# 双CustomZombie系统测试指南

## 问题修复总结

### 已修复的关键问题

1. **返回类型不匹配**：
   - 修复了UserCustomZombie方法返回boolean而DualZombieSystemManager需要Zombie实体的问题
   - 现在所有方法都正确返回Zombie实体或null

2. **配置文件设置**：
   - 启用了用户自定义系统：`use_user_custom_settings: true`
   - 禁用了默认系统：`use_default_settings: false`
   - 设置优先级策略为：`priority_strategy: "user_first"`
   - 启用了调试模式：`debug_mode: true`

3. **生成逻辑优化**：
   - 修复了DualZombieSystemManager中的调用逻辑
   - 确保正确处理Zombie实体的返回和传递

## 测试步骤

### 第一步：验证配置加载

1. **启动服务器**，查看控制台日志：
   ```
   [INFO] UserCustomZombie系统初始化完成
   [INFO] 双CustomZombie系统管理器初始化完成
   [INFO] 已加载id1的覆盖配置
   [INFO] 已加载id2的覆盖配置
   [INFO] 已加载id3的覆盖配置
   [INFO] 已加载id4的覆盖配置
   ```

2. **检查系统状态**：
   ```bash
   /dzs dualsystem status
   ```
   
   期望输出：
   ```
   §a双CustomZombie系统状态:
   §7- 默认系统启用: §c否
   §7- 用户自定义启用: §a是
   §7- 优先级策略: §euser_first
   §7- 支持的僵尸ID: §eID1-ID4
   ```

### 第二步：测试僵尸生成

1. **测试ID1僵尸**：
   ```bash
   /dzs dualsystem test id1
   ```
   
   期望结果：
   - 生成一个名为"§a§l强化普通僵尸"的僵尸
   - 生命值：25.0（而不是默认的20.0）
   - 武器：石斧（而不是默认的木斧）
   - 控制台输出详细的调试信息

2. **测试ID2僵尸**：
   ```bash
   /dzs dualsystem test id2
   ```
   
   期望结果：
   - 生成一个名为"§b§l强化小僵尸"的僵尸
   - 生命值：15.0（而不是默认的10.0）
   - 武器：石剑（而不是默认的木棍）
   - 有速度效果

3. **测试ID3和ID4**：
   ```bash
   /dzs dualsystem test id3
   /dzs dualsystem test id4
   ```

### 第三步：验证配置覆盖

1. **检查僵尸属性**：
   - 右键点击生成的僵尸查看名称
   - 观察僵尸的装备和外观
   - 测试僵尸的攻击力和生命值

2. **查看调试日志**：
   ```
   [INFO] 尝试生成用户自定义僵尸: id1 在位置: world(100.0,64.0,100.0)
   [INFO] 使用用户自定义系统成功生成: id1
   [INFO] 成功生成用户自定义僵尸: id1
   ```

## 故障排除

### 问题1：系统状态显示未启用

**症状**：`/dzs dualsystem status` 显示用户自定义系统未启用

**解决方案**：
1. 检查zombie.yml配置文件：
   ```yaml
   system_settings:
     use_user_custom_settings: true
   ```

2. 重载配置：
   ```bash
   /dzs dualsystem reload
   ```

### 问题2：生成的僵尸没有自定义属性

**症状**：僵尸生成了但是属性没有改变

**可能原因**：
1. 配置文件中`enabled: false`
2. 配置文件格式错误
3. 配置文件路径问题

**解决方案**：
1. 检查特定僵尸ID的配置：
   ```yaml
   user_custom_overrides:
     specific_overrides:
       id1:
         enabled: true  # 确保这里是true
   ```

2. 检查配置文件语法（YAML格式敏感）

3. 重载配置并查看日志

### 问题3：控制台没有调试信息

**症状**：没有看到详细的生成日志

**解决方案**：
1. 确保调试模式启用：
   ```yaml
   system_settings:
     debug_mode: true
     verbose_logging: true
   ```

2. 重载配置：
   ```bash
   /dzs dualsystem reload
   ```

### 问题4：命令无法执行

**症状**：`/dzs dualsystem` 命令不存在

**可能原因**：
1. 插件未正确加载
2. 权限不足

**解决方案**：
1. 检查插件是否加载：`/plugins`
2. 确保有管理员权限：`deathzombiev4.admin`
3. 重启服务器

## 配置文件示例

### 启用用户自定义系统的完整配置

```yaml
system_settings:
  use_default_settings: false
  use_user_custom_settings: true
  priority_strategy: "user_first"
  debug_mode: true
  verbose_logging: true

user_custom_overrides:
  specific_overrides:
    id1:
      enabled: true
      health_override: 25.0
      damage_override: 6.0
      custom_name_override: "§a§l强化普通僵尸"
      weapon_override: "STONE_AXE"
      speed_multiplier: 1.2
```

## 高级测试

### 测试不同优先级策略

1. **测试user_first策略**：
   ```yaml
   priority_strategy: "user_first"
   use_default_settings: true
   use_user_custom_settings: true
   ```

2. **测试default_first策略**：
   ```yaml
   priority_strategy: "default_first"
   use_default_settings: true
   use_user_custom_settings: true
   ```

3. **测试both策略**（实验性）：
   ```yaml
   priority_strategy: "both"
   use_default_settings: true
   use_user_custom_settings: true
   ```

### 性能测试

1. **批量生成测试**：
   ```bash
   # 快速连续执行多次
   /dzs dualsystem test id1
   /dzs dualsystem test id2
   /dzs dualsystem test id3
   /dzs dualsystem test id4
   ```

2. **观察服务器性能**：
   - 检查TPS
   - 观察内存使用
   - 查看控制台错误

## 成功标志

当看到以下情况时，说明双系统正常工作：

1. ✅ 控制台显示系统初始化成功
2. ✅ `/dzs dualsystem status` 显示正确状态
3. ✅ 生成的僵尸有自定义名称和属性
4. ✅ 调试日志显示详细的生成过程
5. ✅ 配置重载功能正常工作

## 下一步扩展

成功验证ID1-ID4后，可以：

1. **添加更多僵尸ID支持**（ID5-ID17）
2. **扩展到IDC系列实体**
3. **添加更多自定义属性**
4. **实现技能参数覆盖**
5. **添加游戏级别的配置开关**

---

**注意**：如果遇到任何问题，请先查看控制台日志，大部分问题都会有详细的错误信息和调试输出。
