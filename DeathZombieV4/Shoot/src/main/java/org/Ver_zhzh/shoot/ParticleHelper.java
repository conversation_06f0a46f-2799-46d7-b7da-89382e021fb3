package org.Ver_zhzh.shoot;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import org.bukkit.Bukkit;
import org.bukkit.Location;
import org.bukkit.Particle;
import org.bukkit.World;
import org.bukkit.entity.Player;
import org.bukkit.plugin.Plugin;
import org.bukkit.scheduler.BukkitRunnable;
import org.bukkit.scheduler.BukkitTask;
import org.bukkit.util.Vector;

/**
 * 粒子效果辅助类，使用原生Bukkit API实现粒子效果 提供与原ParticleEffect类似的接口以及更多高级效果
 */
public class ParticleHelper {

    private final Plugin plugin;
    private final List<BukkitTask> activeTasks = new ArrayList<>();

    public ParticleHelper(Plugin plugin) {
        this.plugin = plugin;
    }

    /**
     * 发送基础粒子效果
     *
     * @param location 粒子生成位置
     * @param offsetX X轴偏移
     * @param offsetY Y轴偏移
     * @param offsetZ Z轴偏移
     * @param speed 粒子速度
     * @param count 粒子数量
     * @param particle 粒子类型
     */
    public void displayParticle(Location location, float offsetX, float offsetY, float offsetZ, float speed, int count, Particle particle) {
        if (location.getWorld() == null) {
            return;
        }

        try {
            // 检查是否是需要额外数据的粒子类型
            if (particle.getDataType() == org.bukkit.block.data.BlockData.class) {
                // 对于需要BlockData的粒子类型，使用默认的石头方块数据
                location.getWorld().spawnParticle(
                        particle,
                        location,
                        count,
                        offsetX,
                        offsetY,
                        offsetZ,
                        speed,
                        org.bukkit.Bukkit.createBlockData(org.bukkit.Material.STONE)
                );
            } else {
                // 对于普通粒子类型，直接生成
                location.getWorld().spawnParticle(
                        particle,
                        location,
                        count,
                        offsetX,
                        offsetY,
                        offsetZ,
                        speed
                );
            }
        } catch (Exception e) {
            // 如果出错，则尝试使用不需要额外数据的粒子类型
            try {
                location.getWorld().spawnParticle(Particle.FLAME, location, count, offsetX, offsetY, offsetZ, speed);
            } catch (Exception ex) {
                // 记录异常信息，但不阻止程序继续运行
                System.out.println("粒子效果显示失败: " + ex.getMessage());
            }
        }
    }

    /**
     * 向指定玩家发送基础粒子效果
     *
     * @param player 玩家
     * @param location 粒子生成位置
     * @param offsetX X轴偏移
     * @param offsetY Y轴偏移
     * @param offsetZ Z轴偏移
     * @param speed 粒子速度
     * @param count 粒子数量
     * @param particle 粒子类型
     */
    public void displayParticle(Player player, Location location, float offsetX, float offsetY, float offsetZ, float speed, int count, Particle particle) {
        player.spawnParticle(
                particle,
                location,
                count,
                offsetX,
                offsetY,
                offsetZ,
                speed
        );
    }

    /**
     * 向多个玩家显示粒子效果(兼容原ParticleEffect.send方法)
     *
     * @param players 玩家集合
     * @param location 粒子生成位置
     * @param offsetX X轴偏移
     * @param offsetY Y轴偏移
     * @param offsetZ Z轴偏移
     * @param speed 粒子速度
     * @param count 数量
     * @param particle 粒子类型
     */
    public void displayParticle(Collection<? extends Player> players, Location location, float offsetX, float offsetY, float offsetZ, float speed, int count, Particle particle) {
        for (Player player : players) {
            displayParticle(player, location, offsetX, offsetY, offsetZ, speed, count, particle);
        }
    }

    /**
     * 创建爆炸效果
     *
     * @param location 爆炸位置
     * @param particle 粒子类型
     * @param amount 粒子数量
     */
    public void displayExplosion(Location location, Particle particle, int amount) {
        if (location.getWorld() == null) {
            return;
        }

        // 创建一个爆炸效果，粒子从中心向外扩散
        World world = location.getWorld();

        // 从中心向外爆炸的效果
        BukkitTask task = new BukkitRunnable() {
            double radius = 0.2;
            int iteration = 0;
            final int maxIterations = 10;

            @Override
            public void run() {
                if (iteration >= maxIterations) {
                    cancel();
                    activeTasks.remove(this);
                    return;
                }

                // 在球体表面生成粒子
                for (int i = 0; i < amount / maxIterations; i++) {
                    double phi = Math.random() * Math.PI * 2;
                    double theta = Math.random() * Math.PI;

                    double x = radius * Math.sin(theta) * Math.cos(phi);
                    double y = radius * Math.sin(theta) * Math.sin(phi);
                    double z = radius * Math.cos(theta);

                    Location particleLoc = location.clone().add(x, y, z);
                    world.spawnParticle(particle, particleLoc, 1, 0, 0, 0, 0);
                }

                radius += 0.3;
                iteration++;
            }
        }.runTaskTimer(plugin, 0, 2);

        activeTasks.add(task);
    }

    /**
     * 创建线效果
     *
     * @param location 起始位置
     * @param target 目标位置
     * @param particle 粒子类型
     */
    public void displayLine(Location location, Location target, Particle particle) {
        if (location.getWorld() == null || target.getWorld() == null || !location.getWorld().equals(target.getWorld())) {
            return;
        }

        World world = location.getWorld();
        Vector direction = target.toVector().subtract(location.toVector());
        double length = direction.length();
        direction.normalize();

        // 在两点之间创建线
        double step = 0.2; // 粒子间隔
        for (double i = 0; i < length; i += step) {
            Vector position = direction.clone().multiply(i);
            Location particleLoc = location.clone().add(position);
            world.spawnParticle(particle, particleLoc, 1, 0, 0, 0, 0);
        }
    }

    /**
     * 创建追踪效果
     *
     * @param location 起始位置
     * @param target 目标位置
     * @param particle 粒子类型
     */
    public void displayTrace(Location location, Location target, Particle particle) {
        if (location.getWorld() == null || target.getWorld() == null || !location.getWorld().equals(target.getWorld())) {
            return;
        }

        World world = location.getWorld();
        Vector direction = target.toVector().subtract(location.toVector());
        double length = direction.length();
        direction.normalize();

        // 创建持续追踪的效果
        BukkitTask task = new BukkitRunnable() {
            double progress = 0;
            final double step = 0.5; // 每次移动的距离

            @Override
            public void run() {
                if (progress >= length) {
                    cancel();
                    activeTasks.remove(this);
                    return;
                }

                Vector position = direction.clone().multiply(progress);
                Location particleLoc = location.clone().add(position);

                // 在移动位置生成粒子
                world.spawnParticle(particle, particleLoc, 5, 0.05, 0.05, 0.05, 0.01);

                progress += step;
            }
        }.runTaskTimer(plugin, 0, 1);

        activeTasks.add(task);
    }

    /**
     * 创建球体效果
     *
     * @param location 中心位置
     * @param radius 半径
     * @param density 密度
     * @param particle 粒子类型
     */
    public void displaySphere(Location location, float radius, int density, Particle particle) {
        if (location.getWorld() == null) {
            return;
        }

        World world = location.getWorld();

        // 在球表面生成粒子
        for (int i = 0; i < density; i++) {
            double phi = Math.random() * Math.PI * 2;
            double theta = Math.random() * Math.PI;

            double x = radius * Math.sin(theta) * Math.cos(phi);
            double y = radius * Math.sin(theta) * Math.sin(phi);
            double z = radius * Math.cos(theta);

            Location particleLoc = location.clone().add(x, y, z);
            world.spawnParticle(particle, particleLoc, 1, 0, 0, 0, 0);
        }
    }

    /**
     * 创建立方体效果
     *
     * @param location 中心位置
     * @param size 边长
     * @param particle 粒子类型
     */
    public void displayCube(Location location, float size, Particle particle) {
        if (location.getWorld() == null) {
            return;
        }

        World world = location.getWorld();
        float half = size / 2;

        // 创建立方体的12条边
        for (float x = -half; x <= half; x += size) {
            for (float y = -half; y <= half; y += size) {
                for (float z = -half; z <= half; z += size) {
                    if ((Math.abs(x) == half ? 1 : 0) + (Math.abs(y) == half ? 1 : 0) + (Math.abs(z) == half ? 1 : 0) >= 2) {
                        Location particleLoc = location.clone().add(x, y, z);
                        world.spawnParticle(particle, particleLoc, 1, 0, 0, 0, 0);
                    }
                }
            }
        }
    }

    /**
     * 创建圆形效果
     *
     * @param location 中心位置
     * @param radius 半径
     * @param particle 粒子类型
     */
    public void displayCircle(Location location, float radius, Particle particle) {
        if (location.getWorld() == null) {
            return;
        }

        World world = location.getWorld();

        // 生成圆形
        for (int i = 0; i < 36; i++) {
            double angle = 2 * Math.PI * i / 36;
            double x = radius * Math.cos(angle);
            double z = radius * Math.sin(angle);

            Location particleLoc = location.clone().add(x, 0, z);
            world.spawnParticle(particle, particleLoc, 1, 0, 0, 0, 0);
        }
    }

    /**
     * 创建云效果
     *
     * @param location 中心位置
     * @param size 大小
     * @param particle 粒子类型
     */
    public void displayCloud(Location location, float size, Particle particle) {
        if (location.getWorld() == null) {
            return;
        }

        // 生成云状效果
        for (int i = 0; i < 50; i++) {
            double x = (Math.random() - 0.5) * size;
            double y = (Math.random() - 0.5) * size * 0.5;
            double z = (Math.random() - 0.5) * size;

            Location particleLoc = location.clone().add(x, y, z);
            location.getWorld().spawnParticle(particle, particleLoc, 1, 0, 0, 0, 0);
        }
    }

    /**
     * 创建火焰效果
     *
     * @param location 位置
     * @param height 高度
     */
    public void displayFlame(Location location, float height) {
        if (location.getWorld() == null) {
            return;
        }

        // 持续的火焰效果
        BukkitTask task = new BukkitRunnable() {
            int ticks = 0;
            final int maxTicks = 20; // 持续1秒

            @Override
            public void run() {
                if (ticks >= maxTicks) {
                    cancel();
                    activeTasks.remove(this);
                    return;
                }

                // 在底部生成火焰粒子
                for (int i = 0; i < 5; i++) {
                    double x = (Math.random() - 0.5) * 0.5;
                    double z = (Math.random() - 0.5) * 0.5;
                    double y = Math.random() * height;

                    Location particleLoc = location.clone().add(x, y, z);
                    location.getWorld().spawnParticle(Particle.FLAME, particleLoc, 1, 0, 0, 0, 0);

                    // 添加一些烟雾
                    if (Math.random() > 0.7) {
                        location.getWorld().spawnParticle(Particle.SMOKE, particleLoc, 1, 0, 0, 0, 0);
                    }
                }

                ticks++;
            }
        }.runTaskTimer(plugin, 0, 1);

        activeTasks.add(task);
    }

    /**
     * 下面是特定粒子效果的便捷方法
     */
    /**
     * 模拟原ParticleEffect.FLAME.send方法
     */
    public void displayFlameParticle(Collection<? extends Player> players, Location location, float offsetX, float offsetY, float offsetZ, float speed, int count) {
        displayParticle(players, location, offsetX, offsetY, offsetZ, speed, count, Particle.FLAME);
    }

    /**
     * 模拟原ParticleEffect.EXPLOSION_HUGE.send方法
     */
    public void displayHugeExplosionParticle(Collection<? extends Player> players, Location location, float offsetX, float offsetY, float offsetZ, float speed, int count) {
        displayParticle(players, location, offsetX, offsetY, offsetZ, speed, count, Particle.EXPLOSION_EMITTER);
    }

    /**
     * 模拟原ParticleEffect.SMOKE_NORMAL.send方法
     */
    public void displaySmokeParticle(Collection<? extends Player> players, Location location, float offsetX, float offsetY, float offsetZ, float speed, int count) {
        displayParticle(players, location, offsetX, offsetY, offsetZ, speed, count, Particle.SMOKE);
    }

    /**
     * 模拟原ParticleEffect.SMOKE_LARGE.send方法
     */
    public void displaySmokeLargeParticle(Collection<? extends Player> players, Location loc, float offsetX, float offsetY, float offsetZ, float speed, int count) {
        displayParticle(players, loc, offsetX, offsetY, offsetZ, speed, count, Particle.LARGE_SMOKE);
    }

    /**
     * 模拟原ParticleEffect.EXPLOSION_NORMAL.send方法
     */
    public void displayNormalExplosionParticle(Collection<? extends Player> players, Location loc, float offsetX, float offsetY, float offsetZ, float speed, int count) {
        displayParticle(players, loc, offsetX, offsetY, offsetZ, speed, count, Particle.POOF);
    }

    /**
     * 清理所有效果，在插件禁用时调用
     */
    public void dispose() {
        // 取消所有活动任务
        for (BukkitTask task : activeTasks) {
            task.cancel();
        }
        activeTasks.clear();

        Bukkit.getLogger().info("§a[ParticleHelper] 已清理所有粒子效果任务");
    }
}
