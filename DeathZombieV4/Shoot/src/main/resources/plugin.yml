name: Shoot
version: 1.5
main: org.Ver_zhzh.Shoot
api-version: '1.21'
authors: [Ver_zhzh]
description: 一个强大的枪械插件，提供多种武器和特效
depend: [Citizens]
softdepend: [DecentHolograms]

commands:
  shoot:
    description: 枪械插件主命令
    usage: |
      §6=== Shoot 插件帮助 ===
      §e/shoot help §7- 查看帮助信息
      §e/shoot get <id> §7- 获取一把枪
      §e/shoot up §7- 补充当前武器的子弹
      §e/shoot display <start|hit> <on|off> §7- 控制标题显示
      §e/shoot gui §7- 打开枪支和弹药GUI
    aliases: [sh]
    permission: shoot.use
    permission-message: §c你没有权限使用此命令！

  byr:
    description: 购买点管理系统
    usage: |
      §6=== Buy Point 系统 ===
      §e/byr check §7- 查询自己的金钱余额
      §e/byr help §7- 查看帮助信息
      §c管理员命令:
      §e/byr set <类型> <物品ID> <名称> <价格> §7- 创建购买点
      §e/byr remove §7- 移除购买点
      §e/byr buy §7- 打开购买测试界面
      §e/byr list §7- 列出所有购买点
      §e/byr add <金额> [玩家名] §7- 为玩家增加金钱
      §7类型: ar(护甲) | wp(武器) | it(物品) | sp(特殊功能)
    aliases: [buypoint, buy]
    permission: shoot.use
    permission-message: §c你没有权限使用此命令！

permissions:
  shoot.use:
    description: 允许使用shoot命令
    default: true
  shoot.admin:
    description: 管理员权限
    default: op
    children:
      shoot.use: true
