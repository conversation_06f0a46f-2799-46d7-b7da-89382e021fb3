#=========================
#config.yml:

# 错误提示设置
error_messages:
  # 是否开启错误提示（移动、切换到副手、丢弃时的错误信息）
  # true: 显示错误提示信息，false: 不显示错误提示信息，给玩家更好的游戏体验
  enabled: false

guns:
  id1:
    name: "手枪"
    material: WOODEN_SHOVEL
    ammo: 300   #（总子弹数量）
    clip_size: 5  #（弹夹容量）
    reload_time: 2 #换弹时间，秒）（换弹时间，秒）
    cooldown: 1 #（射击冷却时间，秒）
    damage: 15 #（伤害）
    special_damage: ""  #（特殊伤害，留空则无）
    shoot_sound_start: "minecraft:entity.firework_rocket.blast"  # 射击时播放的音效
    shoot_sound_hit: "minecraft:block.stone.break"    # 命中目标时播放的音效
    particle: "CRIT"                          # 射击粒子效果

  id2:
    name: "步枪"
    material: STONE_HOE
    ammo: 300
    clip_size: 10
    reload_time: 3
    cooldown: 0.5
    damage: 50
    special_damage: ""
    shoot_sound_start: "minecraft:entity.generic.explode"  # 射击时播放的音效
    shoot_sound_hit: "minecraft:block.stone.break"    # 命中目标时播放的音效
    particle: "FLAME"                         # 射击粒子效果

  id3:
    name: "霰弹枪"
    material: IRON_HOE
    ammo: 80
    clip_size: 4
    reload_time: 3
    cooldown: 2
    damage: 70
    special_damage: ""
    shoot_sound_start: "minecraft:entity.dragon_fireball.explode"  # 射击时播放的音效
    shoot_sound_hit: "minecraft:entity.generic.explode"  # 命中目标时播放的音效
    particle: "SMOKE_LARGE"  # 射击粒子效果

  id4:
    name: "机枪"
    material: BOW
    ammo: 500   #（总子弹数量）
    clip_size: 50  #（弹夹容量）
    reload_time: 2 #（换弹时间，秒）
    cooldown: 0.25 #（射击冷却时间，秒）
    damage: 40 #（伤害）
    special_damage: ""  #（特殊伤害，留空则无）
    shoot_sound_start: "minecraft:entity.firework_rocket.large_blast"  # 射击时播放的音效
    shoot_sound_hit: "minecraft:block.stone.break"    # 命中目标时播放的音效
    particle: "CRIT"  # 射击粒子效果

  id5:
    name: "火箭筒"
    material: IRON_SHOVEL  # 枪的材质为铁铲
    ammo: 50   #（总子弹数量）
    clip_size: 5  #（弹夹容量）
    reload_time: 4 #（换弹时间，秒）
    cooldown: 2 #（射击冷却时间，秒）
    damage: 100 #（子弹伤害）
    special_damage: "400"  #（特殊伤害，烈焰弹造成400点伤害）
    shoot_sound_start: "minecraft:entity.firework_rocket.launch"  # 射击时播放的音效
    shoot_sound_hit: "minecraft:entity.generic.explode"    # 命中目标时播放的音效
    particle: "FLAME"  # 射击粒子效果

  id6:
    name: "电击枪"
    material: "IRON_PICKAXE"
    ammo: 130
    damage: 100
    special_damage: 75
    particle: "SPELL_WITCH"
    cooldown: 0.5
    shoot_sound_start: "minecraft:block.beacon.power_select"
    shoot_sound_hit: "minecraft:entity.lightning_bolt.impact"

  id7:
    name: "狙击步枪"
    material: GOLDEN_SHOVEL
    ammo: 60
    damage: 800
    particle: HEART
    special_damage: 0
    cooldown: 2
    shoot_sound_start: "minecraft:entity.dragon_fireball.explode"
    shoot_sound_hit: "minecraft:block.anvil.land"

  id8:
    name: "冷冻枪"
    material: DIAMOND_SHOVEL
    ammo: 125
    clip_size: 5
    reload_time: 3 #（换弹时间，秒）
    cooldown: 1 #（射击冷却时间，秒）
    damage: 10 #（子弹伤害）
    special_damage: "90"  #（特殊伤害，冻结后造成90点伤害）
    shoot_sound_start: "minecraft:block.glass.break"  # 射击时播放的音效
    shoot_sound_hit: "minecraft:block.glass.break"    # 命中目标时播放的音效
    particle: "SPELL_INSTANT"  # 射击粒子效果

  id9:
    name: "雷击枪"
    material: DIAMOND_PICKAXE
    ammo: 50   #（总子弹数量）
    clip_size: 5  #（弹夹容量）
    reload_time: 3 #（换弹时间，秒）
    cooldown: 1 #（射击冷却时间，秒）
    damage: 800 #（子弹伤害）
    special_damage: "1000"  #（特殊伤害，雷击造成25点伤害）
    shoot_sound_start: "minecraft:entity.lightning_bolt.thunder"  # 射击时播放的音效
    shoot_sound_hit: "minecraft:entity.lightning_bolt.impact"    # 命中目标时播放的音效
    particle: "CRIT_GREEN"  # 射击粒子效果

  id10:
    name: "压强枪"
    material: DIAMOND_AXE  # 枪的材质为钻石斧
    ammo: 60   #（总子弹数量）
    clip_size: 10  #（弹夹容量）
    reload_time: 3 #（换弹时间，秒）
    cooldown: 1.5 #（射击冷却时间，秒）
    damage: 80 #（子弹伤害）
    special_damage: "150"  #（特殊伤害，烟雾击退造成15点伤害）
    shoot_sound_start: "minecraft:entity.zombie.break_wooden_door"  # 射击时播放的音效
    shoot_sound_hit: "minecraft:entity.generic.explode"    # 命中目标时播放的音效
    particle: "CLOUD"  # 射击粒子效果，模拟烟雾

  id11:
    name: "突击步枪"
    material: IRON_AXE  # 枪的材质为铁斧
    ammo: 400   #（总子弹数量）
    clip_size: 30  #（弹夹容量）
    reload_time: 2 #（换弹时间，秒）
    cooldown: 0.15 #（射击冷却时间，秒）
    damage: 45 #（子弹伤害）
    special_damage: ""  #（特殊伤害，留空则无）
    shoot_sound_start: "minecraft:entity.firework_rocket.blast_far"  # 射击时播放的音效
    shoot_sound_hit: "minecraft:block.stone.break"    # 命中目标时播放的音效
    particle: "CRIT"  # 射击粒子效果

  id12:
    name: "冲锋枪"
    material: GOLDEN_AXE  # 枪的材质为金斧
    ammo: 350   #（总子弹数量）
    clip_size: 25  #（弹夹容量）
    reload_time: 1.5 #（换弹时间，秒）
    cooldown: 0.1 #（射击冷却时间，秒）
    damage: 30 #（子弹伤害）
    special_damage: ""  #（特殊伤害，留空则无）
    shoot_sound_start: "minecraft:entity.firework_rocket.twinkle"  # 射击时播放的音效
    shoot_sound_hit: "minecraft:block.stone.break"    # 命中目标时播放的音效
    particle: "CRIT"  # 射击粒子效果

  id13:
    name: "等离子枪"
    material: NETHERITE_HOE  # 使用下界合金锄作为材质
    ammo: 200   #（总子弹数量）
    clip_size: 20  #（弹夹容量）
    reload_time: 3 #（换弹时间，秒）
    cooldown: 0.3 #（射击冷却时间，秒）
    damage: 120 #（子弹伤害）
    special_damage: "180"  #（特殊伤害，等离子灼烧伤害）
    shoot_sound_start: "minecraft:block.beacon.activate"  # 射击时播放的音效
    shoot_sound_hit: "minecraft:block.respawn_anchor.deplete"    # 命中目标时播放的音效
    particle: "SOUL_FIRE_FLAME"  # 射击粒子效果，使用灵魂火焰

  id14:
    name: "死神收割者"
    material: NETHERITE_SWORD  # 使用下界合金剑作为材质
    ammo: 150   #（总子弹数量）
    clip_size: 15  #（弹夹容量）
    reload_time: 4 #（换弹时间，秒）
    cooldown: 0.5 #（射击冷却时间，秒）
    damage: 200 #（子弹伤害）
    special_damage: "300"  #（特殊伤害，死亡收割伤害）
    shoot_sound_start: "minecraft:entity.wither.shoot"  # 射击时播放的音效
    shoot_sound_hit: "minecraft:entity.wither.death"    # 命中目标时播放的音效
    particle: "DRAGON_BREATH"  # 射击粒子效果，使用龙息

  id15:
    name: "毁灭者"
    material: NETHERITE_AXE
    ammo: 100   #（总子弹数量）
    clip_size: 10  #（弹夹容量）
    reload_time: 5 #（换弹时间，秒）
    cooldown: 1 #（射击冷却时间，秒）
    damage: 300 #（子弹伤害）
    special_damage: "500"  #（特殊伤害，毁灭性爆炸伤害）
    shoot_sound_start: "minecraft:entity.ender_dragon.growl"  # 射击时播放的音效
    shoot_sound_hit: "minecraft:entity.generic.explode"    # 命中目标时播放的音效
    particle: "EXPLOSION_HUGE"  # 射击粒子效果

  id16:
    name: "超级激光炮"
    material: NETHERITE_HOE
    ammo: 50
    clip_size: 5
    reload_time: 6
    cooldown: 1.5
    damage: 500
    special_damage: "800"
    shoot_sound_start: "minecraft:block.beacon.power_select"
    shoot_sound_hit: "minecraft:entity.lightning_bolt.thunder"
    particle: "REDSTONE"

  id17:
    name: "黑洞吞噬者"
    material: NETHERITE_SWORD
    ammo: 80
    clip_size: 8
    reload_time: 5
    cooldown: 1.2
    damage: 400
    special_damage: "600"
    shoot_sound_start: "minecraft:entity.wither.spawn"
    shoot_sound_hit: "minecraft:entity.wither.death"
    particle: "SMOKE_NORMAL"

  id18:
    name: "音波步枪"
    material: GOLDEN_HOE
    ammo: 300
    clip_size: 30
    reload_time: 2
    cooldown: 0.3
    damage: 80
    special_damage: "120"
    shoot_sound_start: "minecraft:block.note_block.bell"
    shoot_sound_hit: "minecraft:block.note_block.bass"
    particle: "NOTE"

  id19:
    name: "能量脉冲枪"
    material: DIAMOND_HOE
    ammo: 250
    clip_size: 25
    reload_time: 3
    cooldown: 0.4
    damage: 100
    special_damage: "150"
    shoot_sound_start: "minecraft:block.beacon.ambient"
    shoot_sound_hit: "minecraft:block.beacon.deactivate"
    particle: "SPELL_WITCH"

  id20:
    name: "彩虹喷射器"
    material: GOLDEN_SWORD
    ammo: 400
    clip_size: 40
    reload_time: 2
    cooldown: 0.2
    damage: 60
    special_damage: "90"
    shoot_sound_start: "minecraft:block.amethyst_block.chime"
    shoot_sound_hit: "minecraft:block.amethyst_block.break"
    particle: "REDSTONE"

  id21:
    name: "传送枪"
    material: DIAMOND_SWORD
    ammo: 150
    clip_size: 15
    reload_time: 4
    cooldown: 1
    damage: 50
    special_damage: ""
    shoot_sound_start: "minecraft:entity.enderman.teleport"
    shoot_sound_hit: "minecraft:entity.enderman.death"
    particle: "PORTAL"

  id22:
    name: "星辉主宰者"
    material: "GOLDEN_HOE"
    damage: 250
    special_damage: 350
    ammo: 30
    clip_size: 10
    reload_time: 3.0
    cooldown: 1.5
    particle: "END_ROD"
    shoot_sound_start: "ENTITY_FIREWORK_ROCKET_BLAST"
    shoot_sound_hit: "ENTITY_GENERIC_EXPLODE"
    description:
      - "§7一把具有强大能量的星辉武器"
      - "§7发射闪耀的星辉能量，造成范围爆炸伤害"
      - "§c基础伤害: 250"
      - "§c爆炸伤害: 350"
      - "§e射程: 75格"
      - "§b弹夹容量: 10/30"
      - "§a冷却时间: 1.5秒"
      - "§6特殊效果: 命中时造成范围爆炸，周围生物受到50%伤害"

  id23:
    name: "虚空星尘使者"
    material: "NETHERITE_HOE"
    ammo: 40   #（总子弹数量）
    clip_size: 8  #（弹夹容量）
    reload_time: 4 #（换弹时间，秒）
    cooldown: 1.2 #（射击冷却时间，秒）
    damage: 200 #（基础伤害）
    special_damage: "450"  #（特殊伤害：3秒内累计造成450点虚空伤害）
    shoot_sound_start: "minecraft:entity.ender_dragon.growl"  # 射击时播放的音效
    shoot_sound_hit: "minecraft:entity.enderman.scream"    # 命中目标时播放的音效
    particle: "END_ROD,PORTAL,DRAGON_BREATH"  # 三种粒子效果组合
    description:
      - "§5一把来自虚空维度的神秘武器"
      - "§7发射星光能量，并引发虚空撕裂"
      - "§c基础伤害: 200"
      - "§c虚空伤害: 450 (3秒内持续)"
      - "§e射程: 65格"
      - "§b弹夹容量: 8/40"
      - "§a冷却时间: 1.2秒"
      - "§6特殊效果: 三重粒子效果，持续虚空伤害"

  id24:
    material: DIAMOND_HOE
    name: "循声炮"
    ammo: 50
    clip_size: 5
    reload_time: 3
    damage: 300
    special_damage: "400"
    cooldown: 1.5
    particle: SONIC_BOOM
    shoot_sound_start: "ENTITY_WARDEN_SONIC_BOOM"
    shoot_sound_hit: "ENTITY_WARDEN_SONIC_BOOM"
    description:
      - "§b一把利用声波能量的强力武器"
      - "§7发射强大的音波冲击，造成大范围伤害"
      - "§c基础伤害: 300"
      - "§c音波伤害: 400"
      - "§e射程: 80格"
      - "§b弹夹容量: 5/50"
      - "§a冷却时间: 1.5秒"
      - "§6特殊效果: 大范围音波冲击"

  id25:
    name: "火焰喷射器"
    material: NETHERITE_PICKAXE
    ammo: 100   #（总子弹数量）
    clip_size: 10  #（弹夹容量）
    reload_time: 3 #（换弹时间，秒）
    cooldown: 0.5 #（射击冷却时间，秒）
    damage: 25 #（基础伤害）
    special_damage: ""  #（特殊伤害，留空则无）
    fire_ticks: 60  #（着火时间，3秒）
    shoot_sound_start: "minecraft:block.fire.ambient"  # 射击时播放的音效
    shoot_sound_hit: "minecraft:entity.generic.burn"    # 命中目标时播放的音效
    particle: "FLAME"  # 射击粒子效果
    description:
      - "§c强大的火焰喷射武器"
      - "§7可以喷射高温火焰，点燃敌人并造成持续伤害"
      - "§c基础伤害: 25/tick"
      - "§e射程: 10格"
      - "§b弹夹容量: 10/100"
      - "§a冷却时间: 0.5秒"
      - "§6特殊效果: 点燃目标3秒"

