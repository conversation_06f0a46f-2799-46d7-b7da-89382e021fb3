<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <groupId>org.Ver_zhzh</groupId>
  <artifactId>shoot</artifactId>
  <version>1.0-SNAPSHOT</version>
  <packaging>jar</packaging>

  <name>shoot</name>

  <properties>
    <java.version>21</java.version>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
  </properties>

  <build>
    <defaultGoal>clean package</defaultGoal>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-compiler-plugin</artifactId>
        <version>3.13.0</version>
        <configuration>
          <source>${java.version}</source>
          <target>${java.version}</target>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-shade-plugin</artifactId>
        <version>3.5.3</version>
        <executions>
          <execution>
            <phase>package</phase>
            <goals>
              <goal>shade</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
    </plugins>
    <resources>
      <resource>
        <directory>src/main/resources</directory>
        <filtering>true</filtering>
      </resource>
    </resources>
  </build>

  <repositories>
      <repository>
        <id>papermc-repo</id>
        <url>https://repo.papermc.io/repository/maven-public/</url>
      </repository>
      <repository>
        <id>sonatype</id>
        <url>https://oss.sonatype.org/content/groups/public/</url>
      </repository>
      <!-- Citizens仓库已移除，使用本地jar文件 -->
      <repository>
        <id>codemc-repo</id>
        <url>https://repo.codemc.org/repository/maven-public/</url>
      </repository>
  </repositories>

  <dependencies>
    <dependency>
      <groupId>io.papermc.paper</groupId>
      <artifactId>paper-api</artifactId>
      <version>1.21.4-R0.1-SNAPSHOT</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>net.citizensnpcs</groupId>
      <artifactId>citizens-main</artifactId>
      <version>2.0.35-b3598</version>
      <scope>system</scope>
      <systemPath>${project.basedir}/../lib/Citizens-2.0.35-b3598.jar</systemPath>
    </dependency>
    <dependency>
      <groupId>com.gmail.filoghost.holographicdisplays</groupId>
      <artifactId>holographicdisplays-api</artifactId>
      <version>2.4.9</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>com.avaje</groupId>
      <artifactId>ebean</artifactId>
      <version>2.8.1</version>
      <scope>system</scope>
      <systemPath>${project.basedir}/../lib/ebean-2.8.1.jar</systemPath>
    </dependency>

  </dependencies>
</project>
