# UserCustomEntity系统 - idc1适配说明

## 概述

本文档说明了UserCustomEntity系统的实现以及idc1（变异僵尸01）的完整适配过程。

## 系统架构

### 核心组件

1. **UserCustomEntity.java** - 主要的实体管理类
2. **EntityOverrideConfig.java** - 实体配置覆盖类
3. **AdvancedEntitySkillHandler.java** - 高级技能处理器
4. **DualEntitySystemManager.java** - 双系统管理器
5. **UserCustomEntityIntegration.java** - 系统集成类
6. **UserCustomEntityCommand.java** - 命令处理器
7. **UserCustomEntityTest.java** - 测试类

### 配置文件

- **entity.yml** - 实体配置文件，包含idc1的完整配置

## idc1适配详情

### 基础属性

- **实体类型**: ZOMBIFIED_PIGLIN（僵尸猪人）
- **生命值**: 120.0（可配置覆盖）
- **伤害**: 10.0（可配置覆盖）
- **速度倍数**: 1.3（30%速度提升）
- **名称**: "§c§l§k|§r§c§l强化变异僵尸§k|"

### 装备配置

#### 武器
- **主武器**: 铁剑
- **附魔**: 锋利2 + 火焰附加1

#### 护甲
- **全套皮革装备**
- **附魔**: 全部保护1

### 特殊能力

#### 剧毒攻击
- **启用状态**: 是
- **剧毒等级**: 2级（配置中为1，0-based）
- **持续时间**: 4秒（80tick）
- **触发概率**: 80%

#### 自愈能力
- **启用状态**: 是
- **再生等级**: 1级
- **触发间隔**: 5秒（100tick）

#### 抗性能力
- **火焰抗性**: 永久
- **击退抗性**: 30%

#### 攻击增强
- **额外攻击伤害**: +2.0
- **暴击概率**: 15%
- **暴击倍数**: 1.5倍

### 药水效果

- **速度2**: 永久效果
- **火焰抗性**: 永久效果

## 配置示例

```yaml
idc1:
  enabled: true
  health_override: 120.0
  damage_override: 10.0
  speed_multiplier: 1.3
  custom_name_override: "§c§l§k|§r§c§l强化变异僵尸§k|"
  entity_type_override: "ZOMBIFIED_PIGLIN"
  
  weapon_override: "IRON_SWORD"
  weapon_enchantments:
    SHARPNESS: 2
    FIRE_ASPECT: 1
    
  special_abilities:
    poison_enabled: true
    poison_level: 1
    poison_duration: 80
    poison_chance: 0.8
    
    regeneration_enabled: true
    regeneration_level: 0
    regeneration_interval: 100
    
    fire_resistance: true
    knockback_resistance: 0.3
    
    attack_damage_bonus: 2.0
    critical_chance: 0.15
    critical_multiplier: 1.5
```

## 使用方法

### 命令系统

使用 `/uce` 命令来管理和测试系统：

```
/uce status          - 查看系统状态
/uce reload          - 重载配置
/uce test            - 运行全面测试
/uce test idc1       - 测试idc1生成
/uce spawn idc1      - 生成idc1实体
/uce info idc1       - 查看idc1配置信息
/uce list            - 列出支持的实体
```

### 编程接口

```java
// 初始化系统
UserCustomEntityIntegration integration = new UserCustomEntityIntegration(plugin);
integration.initialize(originalEntitySpawner);

// 生成实体
LivingEntity entity = integration.spawnCustomEntity(location, "idc1");

// 检查支持
boolean supported = integration.isSupportedEntity("idc1");

// 获取状态
String status = integration.getSystemStatus();
```

## 测试验证

### 自动测试

系统包含完整的自动测试功能：

1. **配置重载测试**
2. **实体生成测试**
3. **属性验证测试**
4. **双系统管理器测试**

### 验证项目

- ✅ 实体类型正确（ZOMBIFIED_PIGLIN）
- ✅ 生命值设置正确（120.0）
- ✅ 名称显示正确
- ✅ 元数据标记完整
- ✅ 装备配置正确
- ✅ 特殊技能启用

## 系统特性

### 双系统兼容

- **优先级策略**: 支持user_first、default_first、both三种策略
- **回退机制**: 用户自定义失败时自动回退到原有系统
- **配置覆盖**: 可以覆盖原有系统的任何属性

### 配置灵活性

- **热重载**: 支持配置文件热重载
- **细粒度控制**: 每个属性都可以独立配置
- **扩展性**: 易于添加新的实体和能力

### 调试支持

- **详细日志**: 完整的调试日志输出
- **状态监控**: 实时系统状态查看
- **测试工具**: 内置测试和验证工具

## 下一步计划

1. **idc2适配**: 变异僵尸02（骷髅形态）
2. **idc3适配**: 变异烈焰人
3. **技能系统增强**: 更复杂的技能机制
4. **Web界面集成**: 在zombie-spawn-vue.js中添加支持
5. **性能优化**: 大量实体生成时的性能优化

## 注意事项

1. **权限要求**: 需要 `deathzombie.admin` 权限使用命令
2. **配置备份**: 修改配置前请备份原文件
3. **测试环境**: 建议先在测试服务器上验证
4. **版本兼容**: 确保Bukkit/Spigot版本兼容

## 故障排除

### 常见问题

1. **实体生成失败**
   - 检查配置文件格式
   - 验证实体类型是否有效
   - 查看控制台错误日志

2. **配置不生效**
   - 确认enabled设置为true
   - 执行配置重载命令
   - 检查配置文件路径

3. **技能不工作**
   - 验证special_abilities配置
   - 检查元数据是否正确设置
   - 确认技能处理器正常运行

### 调试步骤

1. 启用调试模式（debug_mode: true）
2. 查看详细日志输出
3. 使用测试命令验证功能
4. 检查实体元数据和属性

## 贡献指南

如需添加新的实体或功能：

1. 在EntityOverrideConfig中添加新的配置选项
2. 在UserCustomEntity中添加生成逻辑
3. 在AdvancedEntitySkillHandler中添加技能处理
4. 更新entity.yml配置文件
5. 添加相应的测试用例
6. 更新文档说明

---

**版本**: 1.0  
**作者**: Ver_zhzh  
**最后更新**: 2025-01-30
