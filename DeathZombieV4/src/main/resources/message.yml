# DeathZombieV4 消息配置文件
# 支持颜色代码 (&a, &b, &c 等) 和变量替换

# 消息功能开关
message_enabled:
  # 是否启用射击命中消息
  shooting: true
  # 是否启用击杀消息
  kill: true
  # 是否启用惩罚消息
  penalty: true
  # 是否启用奖励消息
  reward: true
  # 是否启用窗户修复消息
  window_repair: true
  # 是否启用友伤提示消息
  friendly_fire: true

# 射击命中消息
shooting:
  # 子消息开关
  enabled:
    hit_message: true        # 是否启用命中消息（关闭后自动使用爆头和身体命中消息）

  # 普通命中消息（当hit_message启用时使用，关闭后自动使用下面的爆头和身体消息）
  # 可用变量: {hit_type} - 命中类型(命中/爆头), {entity_name} - 实体名称, {amount} - 金钱数量
  hit_message: "&a{hit_type} {entity_name} +&6{amount}&a 金钱"

  # 爆头命中消息 (当hit_message关闭时自动使用)
  headshot_message: "&c爆头 &a{entity_name} +&6{amount}&a 金钱"

  # 身体命中消息 (当hit_message关闭时自动使用)
  bodyshot_message: "&a命中 {entity_name} +&6{amount}&a 金钱"

# 击杀消息
kill:
  # 子消息开关
  enabled:
    kill_message: true                    # 是否启用普通击杀消息
    kill_in_shoot_mode_message: true     # 是否启用射击模式击杀消息

  # 击杀消息
  # 可用变量: {entity_name} - 实体名称, {amount} - 金钱数量
  kill_message: "&e击杀 {entity_name} +&6{amount}&e 金钱"

  # 射击模式下的击杀消息 (通常金额较少或为0)
  kill_in_shoot_mode_message: "&7击杀 {entity_name} +&6{amount}&7 金钱"

# 惩罚消息
penalty:
  # 子消息开关
  enabled:
    player_killed_penalty: true          # 是否启用玩家被击杀惩罚消息

  # 玩家被僵尸击杀的惩罚消息
  # 可用变量: {amount} - 扣除的金钱数量
  player_killed_penalty: "&c你被僵尸击杀，失去了 &6{amount}&c 金钱!"

# 奖励消息 (注意：round_completion和game_completion暂无实际逻辑，保留供未来使用)
reward:
  # 子消息开关
  enabled:
    round_completion: false              # 是否启用回合完成奖励消息 (暂无逻辑)
    game_completion: false               # 是否启用游戏完成奖励消息 (暂无逻辑)

  # 回合完成奖励 (暂无实际逻辑)
  # 可用变量: {round} - 回合数, {amount} - 奖励金钱数量
  round_completion: "&e完成第 {round} 回合 +&6{amount}&e 金钱"

  # 游戏完成奖励 (暂无实际逻辑)
  # 可用变量: {amount} - 奖励金钱数量
  game_completion: "&6游戏胜利 +&6{amount}&6 金钱!"

# 窗户修复消息
window_repair:
  # 子消息开关
  enabled:
    repair_success: true                 # 是否启用修复成功获得金钱的消息
    fully_repaired: true                 # 是否启用窗户完全修复消息
    already_repaired: true               # 是否启用窗户已修复完毕时的提示消息
    monsters_nearby: false                # 是否启用附近有怪物无法修复的消息

  # 修复成功获得金钱的消息
  # 可用变量: {amount} - 奖励金钱数量
  repair_success: "&6+{amount} &a金钱！"

  # 窗户完全修复消息
  fully_repaired: "&a恭喜！你已经完全修复了这个窗户！"

  # 窗户已完全修复时玩家尝试修复的提示消息
  already_repaired: "&a这个窗户已经完全修复了，无需再次修复！"

  # 附近有怪物，无法修复
  monsters_nearby: "&c附近有怪物，无法修复窗户！"

# 友伤提示消息
friendly_fire:
  # 子消息开关
  enabled:
    disabled_message: false               # 是否启用友伤被禁用时的提示消息

  # 友伤被禁用时的提示消息
  disabled_message: "&c友伤已禁用，你不能攻击队友！"

# 实体类型显示名称映射
entity_names:
  # 普通僵尸
  zombie: "僵尸"
  # 特殊僵尸
  special: "特殊僵尸"
  # 变异怪物
  mutant: "变异怪物"
  # 感染者NPC
  npc: "感染者"
  # 其他怪物
  monster: "怪物"

  # CustomZombie插件 - 普通僵尸系列 (id1-id26)
  id1: "普通僵尸"
  id2: "小僵尸"
  id3: "路障僵尸"
  id4: "钻斧僵尸"
  id5: "剧毒僵尸"
  id6: "双生僵尸"
  id7: "骷髅僵尸"
  id8: "武装僵尸"
  id9: "肥胖僵尸"
  id10: "法师僵尸"
  id11: "自爆僵尸"
  id12: "毒箭僵尸"
  id13: "电击僵尸"
  id14: "冰冻僵尸"
  id15: "暗影僵尸"
  id16: "毁灭僵尸"
  id17: "雷霆僵尸"
  id18: "变异科学家"
  id19: "变异法师"
  id20: "气球僵尸"
  id21: "迷雾僵尸"
  id22: "变异雷霆僵尸"
  id23: "终极毁灭僵尸"
  id24: "变异暗影僵尸"
  id25: "变异博士"
  id26: "尖叫僵尸"

  # CustomZombie插件 - 变异实体系列 (idc1-idc22)
  idc1: "变异僵尸01"
  idc2: "变异僵尸02"
  idc3: "变异烈焰人"
  idc4: "变异爬行者"
  idc5: "变异末影螨"
  idc6: "变异蜘蛛"
  idc7: "灾厄卫道士"
  idc8: "灾厄唤魔者"
  idc9: "灾厄劫掠兽"
  idc10: "变异僵尸马"
  idc11: "变异岩浆怪"
  idc12: "变异尸壳"
  idc13: "变异僵尸3"
  idc14: "变异僵尸04"
  idc15: "鲜血猪灵"
  idc16: "暗影潜影贝"
  idc17: "变异雪傀儡"
  idc18: "变异铁傀儡"
  idc19: "变异僵尸Max"
  idc20: "灵魂坚守者"
  idc21: "凋零领主"
  idc22: "异变之王"

  # CustomZombie插件 - NPC系列 (idn1-idn5)
  idn1: "感染者史蒂夫"
  idn2: "感染者艾利克斯"
  idn3: "感染者农民"
  idn4: "感染者居民"
  idn5: "感染猪"

  # 原版实体类型
  skeleton: "骷髅"
  creeper: "苦力怕"
  spider: "蜘蛛"
  enderman: "末影人"
  witch: "女巫"
  vindicator: "卫道士"
  pillager: "掠夺者"
  ravager: "劫掠兽"
  evoker: "唤魔者"
  vex: "恼鬼"
  drowned: "溺尸"
  husk: "尸壳"
  zombie_villager: "僵尸村民"
  phantom: "幻翼"
  stray: "流浪者"
  wither_skeleton: "凋灵骷髅"
  blaze: "烈焰人"
  ghast: "恶魂"
  magma_cube: "岩浆怪"
  slime: "史莱姆"
  silverfish: "蠹虫"
  cave_spider: "洞穴蜘蛛"
  guardian: "守卫者"
  elder_guardian: "远古守卫者"
  shulker: "潜影贝"
  piglin: "猪灵"
  piglin_brute: "猪灵蛮兵"
  zombified_piglin: "僵尸猪灵"
  hoglin: "疣猪兽"
  zoglin: "僵尸疣猪兽"
  strider: "炽足兽"
  warden: "监守者"

# 命中类型显示名称
hit_types:
  head: "爆头"
  body: "命中"

# Title显示配置
title:
  # 回合Title配置 (所有回合使用同一个title)
  round_title:
    enabled: true
    title: "&c第 {round} 回合"
    subtitle: ""
    fade_in: 10      # 淡入时间 (tick)
    stay: 70         # 停留时间 (tick)
    fade_out: 20     # 淡出时间 (tick)

  # 加入游戏Title配置
  join_game_title:
    enabled: true
    title: "&c僵尸末日"
    subtitle: "&c 可怜之人并有可恨之处...  &f{player_count}/{max_players}"
    fade_in: 10      # 淡入时间 (tick)
    stay: 60         # 停留时间 (tick)
    fade_out: 20     # 淡出时间 (tick)

  # 游戏结束Title配置
  game_end_title:
    # 游戏胜利Title配置
    victory:
      enabled: true
      title: "&a&l游戏胜利！"
      subtitle: "&e耗时: &f{duration} &e坚持: &f{rounds} &e回合"
      fade_in: 10    # 淡入时间 (tick)
      stay: 100      # 停留时间 (tick)
      fade_out: 20   # 淡出时间 (tick)

    # 游戏失败Title配置
    defeat:
      enabled: true
      title: "&c&全员阵亡！"
      subtitle: "&e耗时: &f{duration} &e坚持: &f{rounds} &e回合"
      fade_in: 10    # 淡入时间 (tick)
      stay: 100      # 停留时间 (tick)
      fade_out: 20   # 淡出时间 (tick)

    # 时间用尽Title配置
    timeout:
      enabled: true
      title: "&c&l时间用尽"
      subtitle: "&e耗时: &f{duration} &e坚持: &f{rounds} &e回合"
      fade_in: 10    # 淡入时间 (tick)
      stay: 100      # 停留时间 (tick)
      fade_out: 20   # 淡出时间 (tick)

# 消息格式设置
format:
  # 是否启用动作条显示 (除了聊天消息外，还在动作条显示)
  action_bar_enabled: false

  # 是否启用音效
  sound_enabled: true

  # 命中音效
  hit_sound: "entity.experience_orb.pickup"

  # 爆头音效
  headshot_sound: "entity.player.levelup"

  # 击杀音效
  kill_sound: "entity.experience_orb.pickup"
