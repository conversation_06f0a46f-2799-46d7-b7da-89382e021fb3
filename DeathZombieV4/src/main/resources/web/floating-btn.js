// 浮动保存按钮控制代码
document.addEventListener('DOMContentLoaded', function() {
  // 获取浮动保存按钮
  const floatingBtn = document.getElementById('floating-save-btn');
  
  // 监听回合模式模态框的显示和隐藏
  const roundModeModal = document.getElementById('round-mode-modal');
  roundModeModal.addEventListener('shown.bs.modal', function() {
    floatingBtn.style.display = 'block';
  });
  
  roundModeModal.addEventListener('hidden.bs.modal', function() {
    floatingBtn.style.display = 'none';
  });
  
  // 添加浮动按钮点击事件 - 触发模态框中的保存按钮
  floatingBtn.addEventListener('click', function() {
    // 查找并触发模态框中的保存按钮点击事件
    const confirmBtn = document.getElementById('confirm-round-mode');
    if (confirmBtn) {
      confirmBtn.click();
    }
  });

  // 检查页面中是否有按钮不能点击的问题并修复
  fixButtonClickIssues();
});

// 修复按钮点击问题
function fixButtonClickIssues() {
  // 确保所有按钮都可点击
  document.querySelectorAll('button, .btn, input[type="button"], input[type="submit"]').forEach(btn => {
    // 移除可能阻止点击的CSS样式
    btn.style.pointerEvents = 'auto';
    btn.style.cursor = 'pointer';
    
    // 重新绑定点击事件
    const newBtn = btn.cloneNode(true);
    btn.parentNode.replaceChild(newBtn, btn);
    
    // 添加调试反馈
    newBtn.addEventListener('click', function(e) {
      console.log('按钮被点击:', this.id || this.className);
    });
  });
}
