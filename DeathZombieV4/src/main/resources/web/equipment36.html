<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DeathZombie - 初始装备设置</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #ffffff;
            color: #333;
            margin: 0;
            padding: 0;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f8f8;
            border-radius: 8px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }

        h1, h2, h3 {
            color: #8b0000;
            text-align: center;
        }

        #game-selector {
            margin: 20px 0;
            text-align: center;
        }

        #game-select {
            padding: 8px;
            margin-right: 10px;
            border-radius: 4px;
            border: 1px solid #ccc;
        }

        button {
            background-color: #8b0000;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            transition: background-color 0.3s;
        }

        button:hover {
            background-color: #a52a2a;
        }

        .hidden {
            display: none;
        }

        .inventory-grid {
            display: grid;
            grid-template-columns: repeat(9, 1fr);
            gap: 5px;
            margin: 20px auto;
            max-width: 720px;
        }

        .slot {
            width: 70px;
            height: 70px;
            background-color: #ddd;
            border: 2px solid #aaa;
            border-radius: 4px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            position: relative;
            transition: all 0.2s;
        }

        .slot:hover {
            border-color: #8b0000;
            transform: scale(1.05);
        }

        .slot.selected {
            border-color: #8b0000;
            background-color: #ffeeee;
        }

        .item-image {
            max-width: 50px;
            max-height: 50px;
        }

        .slot-label {
            font-size: 10px;
            margin-top: 2px;
            text-align: center;
        }

        #item-selector {
            margin-top: 20px;
            padding: 15px;
            background-color: #f0f0f0;
            border-radius: 8px;
            border: 1px solid #ccc;
        }

        .item-grid {
            display: grid;
            grid-template-columns: repeat(5, 1fr);
            gap: 10px;
            margin: 15px 0;
        }

        .item {
            width: 60px;
            height: 60px;
            background-color: #eee;
            border: 1px solid #ccc;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.2s;
        }

        .item:hover {
            border-color: #8b0000;
            transform: scale(1.05);
        }

        .item img {
            max-width: 50px;
            max-height: 50px;
        }

        .button-row {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-top: 30px;
        }

        .item-tooltip {
            position: absolute;
            background-color: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 5px 10px;
            border-radius: 4px;
            font-size: 12px;
            z-index: 100;
            pointer-events: none;
            white-space: nowrap;
        }

        .slot-with-item {
            position: relative;
        }

        .slot-with-item::after {
            content: attr(data-item-id);
            position: absolute;
            bottom: 2px;
            right: 2px;
            background-color: rgba(0, 0, 0, 0.7);
            color: white;
            font-size: 10px;
            padding: 2px 4px;
            border-radius: 2px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>DeathZombie - 初始装备设置</h1>
        <div id="game-selector">
            <label for="game-select">选择游戏:</label>
            <select id="game-select"></select>
            <button id="load-game">加载</button>
        </div>

        <div id="equipment-container" class="hidden">
            <h2>初始装备设置 - <span id="game-name"></span></h2>

            <div class="inventory-grid">
                <!-- 36个槽位将通过JavaScript动态生成 -->
            </div>

            <div id="item-selector" class="hidden">
                <h3>选择物品 - <span id="selected-slot"></span></h3>
                <div class="item-grid">
                    <!-- 物品将通过JavaScript动态添加 -->
                </div>
                <button id="remove-item">移除物品</button>
                <button id="cancel-selection">取消</button>
            </div>

            <div class="button-row">
                <button id="save-equipment">保存装备设置</button>
                <button id="back-to-main">返回主页</button>
            </div>
        </div>
    </div>

    <!-- 引入身份验证脚本 -->
    <script src="js/auth.js"></script>
    <script>
        // 身份验证相关函数
        function checkAuth() {
            const token = localStorage.getItem('auth_token');
            if (!token) {
                console.log('未找到身份验证令牌，需要登录');
                // 如果当前不在登录页面，则重定向到登录页面
                if (!window.location.href.includes('login.html')) {
                    window.location.href = 'login.html';
                }
                return false;
            }

            console.log('找到身份验证令牌');
            return true;
        }

        // 带身份验证的fetch请求
        function fetchWithAuth(url, options = {}) {
            const token = localStorage.getItem('auth_token');

            if (!token) {
                throw new Error('未登录，无法发送请求');
            }

            // 合并选项
            const authOptions = {
                ...options,
                headers: {
                    ...options.headers,
                    'Authorization': `Bearer ${token}`
                }
            };

            return fetch(url, authOptions)
                .then(response => {
                    // 如果返回401未授权，可能是令牌过期
                    if (response.status === 401) {
                        localStorage.removeItem('auth_token');
                        window.location.href = 'login.html?error=session_expired';
                        throw new Error('会话已过期，请重新登录');
                    }

                    return response;
                })
                .catch(error => {
                    console.error('请求出错:', error);
                    throw error;
                });
        }

        document.addEventListener('DOMContentLoaded', function() {
            // 检查身份验证状态
            if (!checkAuth()) {
                return;
            }
            // 元素引用
            const gameSelect = document.getElementById('game-select');
            const loadGameBtn = document.getElementById('load-game');
            const equipmentContainer = document.getElementById('equipment-container');
            const gameNameSpan = document.getElementById('game-name');
            const inventoryGrid = document.querySelector('.inventory-grid');
            const itemSelector = document.getElementById('item-selector');
            const selectedSlotSpan = document.getElementById('selected-slot');
            const itemGrid = document.querySelector('.item-grid');
            const removeItemBtn = document.getElementById('remove-item');
            const cancelSelectionBtn = document.getElementById('cancel-selection');
            const saveEquipmentBtn = document.getElementById('save-equipment');
            const backToMainBtn = document.getElementById('back-to-main');

            // 状态变量
            let currentGame = '';
            let currentSlot = null;
            let equipmentData = {};

            // 物品数据
            const items = [
                // 武器类型 (id1-id24)
                { id: 'id1', name: '手枪', image: 'https://mc-heads.net/item/wooden_hoe' },
                { id: 'id2', name: '步枪', image: 'https://mc-heads.net/item/wooden_hoe' },
                { id: 'id3', name: '霰弹枪', image: 'https://mc-heads.net/item/wooden_hoe' },
                { id: 'id4', name: '机枪', image: 'https://mc-heads.net/item/wooden_hoe' },
                { id: 'id5', name: '火箭筒', image: 'https://mc-heads.net/item/wooden_hoe' },
                { id: 'id6', name: '电击枪', image: 'https://mc-heads.net/item/wooden_hoe' },
                { id: 'id7', name: '狙击步枪', image: 'https://mc-heads.net/item/wooden_hoe' },
                { id: 'id8', name: '冷冻枪', image: 'https://mc-heads.net/item/wooden_hoe' },
                { id: 'id9', name: '雷击枪', image: 'https://mc-heads.net/item/wooden_hoe' },
                { id: 'id10', name: '压强枪', image: 'https://mc-heads.net/item/wooden_hoe' },
                { id: 'id11', name: '突击步枪', image: 'https://mc-heads.net/item/wooden_hoe' },

                // 物品类型 (id38-id67)
                { id: 'id38', name: '速度buff1药水', image: 'https://mc-heads.net/item/potion' },
                { id: 'id39', name: '跳跃buff1药水', image: 'https://mc-heads.net/item/potion' },
                { id: 'id40', name: '手枪弹药', image: 'https://mc-heads.net/item/iron_nugget' },
                { id: 'id50', name: '金苹果', image: 'https://mc-heads.net/item/golden_apple' },
                { id: 'id65', name: '熟牛排', image: 'https://mc-heads.net/item/cooked_beef' },

                // 特殊物品 (id67-id70)
                { id: 'id67', name: '武器槽位', image: 'https://mc-heads.net/item/barrier' },
                { id: 'id68', name: '空物品槽', image: 'https://mc-heads.net/item/barrier' },
                { id: 'id69', name: '锁定槽位', image: 'https://mc-heads.net/item/barrier' },
                { id: 'id70', name: '铁剑', image: 'https://mc-heads.net/item/iron_sword' }
            ];

            // 初始化
            createInventoryGrid();
            loadGames();

            // 事件监听器
            loadGameBtn.addEventListener('click', loadGameEquipment);
            removeItemBtn.addEventListener('click', removeItemFromSlot);
            cancelSelectionBtn.addEventListener('click', cancelSelection);
            saveEquipmentBtn.addEventListener('click', saveEquipment);
            backToMainBtn.addEventListener('click', () => window.location.href = 'index.html');

            // 创建36槽位的物品栏
            function createInventoryGrid() {
                inventoryGrid.innerHTML = '';

                for (let i = 1; i <= 36; i++) {
                    const slot = document.createElement('div');
                    slot.className = 'slot';
                    slot.setAttribute('data-slot', i);

                    const img = document.createElement('img');
                    // 使用玻璃板代替屏障作为锁定槽位
                    img.src = 'https://mc-heads.net/item/glass_pane';
                    img.alt = '空槽';
                    img.className = 'item-image';

                    const label = document.createElement('div');
                    label.className = 'slot-label';
                    label.textContent = i;

                    slot.appendChild(img);
                    slot.appendChild(label);

                    slot.addEventListener('click', () => selectSlot(slot));

                    inventoryGrid.appendChild(slot);
                }
            }

            // 加载游戏列表
            function loadGames() {
                fetchWithAuth('/api/games')
                    .then(response => response.json())
                    .then(data => {
                        gameSelect.innerHTML = '';
                        data.games.forEach(game => {
                            const option = document.createElement('option');
                            option.value = game.name;
                            option.textContent = game.name;
                            gameSelect.appendChild(option);
                        });
                    })
                    .catch(error => {
                        console.error('加载游戏列表失败:', error);
                        alert('加载游戏列表失败，请刷新页面重试');
                    });
            }

            // 加载游戏装备
            function loadGameEquipment() {
                currentGame = gameSelect.value;
                if (!currentGame) {
                    alert('请选择一个游戏');
                    return;
                }

                fetchWithAuth(`/api/game?name=${currentGame}`)
                    .then(response => response.json())
                    .then(data => {
                        gameNameSpan.textContent = currentGame;
                        equipmentData = data.initialEquipment || {};

                        // 重置所有槽位
                        document.querySelectorAll('.slot').forEach(slot => {
                            const slotNumber = slot.getAttribute('data-slot');
                            const itemId = equipmentData[slotNumber];

                            // 获取图片元素
                            const img = slot.querySelector('.item-image');

                            if (itemId) {
                                // 如果有物品，更新图片
                                const item = items.find(i => i.id === itemId);
                                if (item) {
                                    img.src = item.image;
                                    img.alt = item.name;
                                } else {
                                    // 如果找不到预定义的物品，尝试使用MC-Heads API
                                    const mcHeadsUrl = `https://mc-heads.net/item/${itemId}`;
                                    img.src = mcHeadsUrl;
                                    img.alt = itemId;
                                }
                                slot.classList.add('slot-with-item');
                                slot.setAttribute('data-item-id', itemId);
                            } else {
                                // 如果没有物品，恢复默认图片
                                if (slotNumber === '1') {
                                    img.src = 'https://mc-heads.net/item/iron_sword';
                                    img.alt = '剑槽';
                                    // 设置默认值为id70（铁剑）
                                    equipmentData[slotNumber] = 'id70';
                                } else if (slotNumber === '2') {
                                    img.src = 'https://mc-heads.net/item/wooden_hoe';
                                    img.alt = '枪槽';
                                    // 设置默认值为id1（手枪）
                                    equipmentData[slotNumber] = 'id1';
                                } else {
                                    img.src = 'https://mc-heads.net/item/glass_pane';
                                    img.alt = '空槽';
                                    slot.classList.remove('slot-with-item');
                                    slot.removeAttribute('data-item-id');
                                }
                            }
                        });

                        equipmentContainer.classList.remove('hidden');
                    })
                    .catch(error => {
                        console.error('加载游戏装备失败:', error);
                        alert('加载游戏装备失败，请重试');
                    });
            }

            // 选择槽位
            function selectSlot(slot) {
                // 移除之前的选择
                document.querySelectorAll('.slot').forEach(s => s.classList.remove('selected'));

                // 选中当前槽位
                slot.classList.add('selected');
                currentSlot = slot.getAttribute('data-slot');
                selectedSlotSpan.textContent = `槽位 ${currentSlot}`;

                // 生成物品选择器
                generateItemGrid();

                // 显示物品选择器
                itemSelector.classList.remove('hidden');
            }

            // 生成物品选择器
            function generateItemGrid() {
                itemGrid.innerHTML = '';

                // 根据槽位筛选物品
                let filteredItems = items;
                if (currentSlot === '1') {
                    // 剑槽显示铁剑(id70)和武器类物品
                    filteredItems = items.filter(item =>
                        item.id === 'id70' || item.id === 'iron_sword'
                    );
                } else if (currentSlot === '2') {
                    // 枪槽显示枪类武器
                    filteredItems = items.filter(item =>
                        item.id.startsWith('id') &&
                        parseInt(item.id.substring(2)) >= 1 &&
                        parseInt(item.id.substring(2)) <= 24
                    );
                }

                // 创建物品元素
                filteredItems.forEach(item => {
                    const itemElement = document.createElement('div');
                    itemElement.className = 'item';
                    itemElement.setAttribute('data-id', item.id);

                    const img = document.createElement('img');
                    img.src = item.image;
                    img.alt = item.name;

                    itemElement.appendChild(img);
                    itemElement.addEventListener('click', () => selectItem(item));

                    // 添加悬停提示
                    itemElement.addEventListener('mouseenter', (e) => {
                        const tooltip = document.createElement('div');
                        tooltip.className = 'item-tooltip';
                        tooltip.textContent = `${item.name} (${item.id})`;
                        tooltip.style.top = `${e.pageY - 30}px`;
                        tooltip.style.left = `${e.pageX + 10}px`;
                        document.body.appendChild(tooltip);
                        itemElement.tooltip = tooltip;
                    });

                    itemElement.addEventListener('mousemove', (e) => {
                        if (itemElement.tooltip) {
                            itemElement.tooltip.style.top = `${e.pageY - 30}px`;
                            itemElement.tooltip.style.left = `${e.pageX + 10}px`;
                        }
                    });

                    itemElement.addEventListener('mouseleave', () => {
                        if (itemElement.tooltip) {
                            itemElement.tooltip.remove();
                            itemElement.tooltip = null;
                        }
                    });

                    itemGrid.appendChild(itemElement);
                });
            }

            // 选择物品
            function selectItem(item) {
                if (!currentSlot) return;

                // 更新数据
                equipmentData[currentSlot] = item.id;

                // 更新UI
                const slot = document.querySelector(`.slot[data-slot="${currentSlot}"]`);
                const img = slot.querySelector('.item-image');
                img.src = item.image;
                img.alt = item.name;
                slot.classList.add('slot-with-item');
                slot.setAttribute('data-item-id', item.id);

                // 隐藏物品选择器
                itemSelector.classList.add('hidden');
                slot.classList.remove('selected');
                currentSlot = null;
            }

            // 从槽位移除物品
            function removeItemFromSlot() {
                if (!currentSlot) return;

                // 更新数据
                delete equipmentData[currentSlot];

                // 更新UI
                const slot = document.querySelector(`.slot[data-slot="${currentSlot}"]`);
                const img = slot.querySelector('.item-image');

                // 恢复默认图片和设置默认值
                if (currentSlot === '1') {
                    img.src = 'https://mc-heads.net/item/iron_sword';
                    img.alt = '剑槽';
                    // 设置默认值为id70（铁剑）
                    equipmentData[currentSlot] = 'id70';
                    slot.classList.add('slot-with-item');
                    slot.setAttribute('data-item-id', 'id70');
                } else if (currentSlot === '2') {
                    img.src = 'https://mc-heads.net/item/wooden_hoe';
                    img.alt = '枪槽';
                    // 设置默认值为id1（手枪）
                    equipmentData[currentSlot] = 'id1';
                    slot.classList.add('slot-with-item');
                    slot.setAttribute('data-item-id', 'id1');
                } else {
                    img.src = 'https://mc-heads.net/item/glass_pane';
                    img.alt = '空槽';
                    slot.classList.remove('slot-with-item');
                    slot.removeAttribute('data-item-id');
                }

                // 隐藏物品选择器
                itemSelector.classList.add('hidden');
                slot.classList.remove('selected');
                currentSlot = null;
            }

            // 取消选择
            function cancelSelection() {
                if (!currentSlot) return;

                const slot = document.querySelector(`.slot[data-slot="${currentSlot}"]`);
                slot.classList.remove('selected');
                currentSlot = null;

                // 隐藏物品选择器
                itemSelector.classList.add('hidden');
            }

            // 保存装备设置
            function saveEquipment() {
                if (!currentGame) {
                    alert('请先选择一个游戏');
                    return;
                }

                // 准备请求数据
                const requestData = {
                    name: currentGame,
                    initialEquipment: equipmentData
                };

                // 发送保存请求
                fetchWithAuth('/api/save-game', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(requestData)
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('初始装备设置已保存');
                    } else {
                        alert('保存失败: ' + (data.message || '未知错误'));
                    }
                })
                .catch(error => {
                    console.error('保存装备设置失败:', error);
                    alert('保存装备设置失败，请重试');
                });
            }
        });
    </script>
</body>
</html>
