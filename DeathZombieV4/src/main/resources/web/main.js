// 怪物类型和ID定义
const monsterTypes = {
    zombie: {
        name: "僵尸",
        ids: {
            id1: { name: "普通僵尸", description: "无特殊功能。20点血，手持木斧，无防具" },
            id2: { name: "小僵尸", description: "速度2buff。10点血，手持木棍，无防具" },
            id3: { name: "路障僵尸", description: "无特殊功能，30点血，手持木斧，带着保护附魔1的铁头盔" },
            id4: { name: "钻斧僵尸", description: "无特殊功能，20点血，手持钻石斧，带着保护附魔1的铁护腿" },
            id5: { name: "剧毒僵尸", description: "特殊功能:被剧毒僵尸攻击后获得剧毒1buff效果，持续三秒，10点血，穿全身皮革套" },
            id6: { name: "双生僵尸", description: "特殊功能:每5分钟生成一个普通僵尸，20点血，手持铁剑，穿着铁胸甲" },
            id7: { name: "骷髅僵尸", description: "特殊功能:可以向玩家发射箭矢，30点血，手持钻石剑，带着铁护腿，穿着铁盔甲" },
            id8: { name: "武装僵尸", description: "无特殊功能，100点血，手持钻石剑，带着铁套，自带速度1buff" },
            id9: { name: "肥胖僵尸", description: "无特殊功能，100点血，无武器，无装备，体型是普通僵尸的两倍" },
            id10: { name: "法师僵尸", description: "特殊功能:每10秒召唤两个双生僵尸，50点血，手持木斧，装备全套连锁套" },
            id11: { name: "自爆僵尸", description: "特殊功能:靠近玩家后发生自爆，30点血，无武器，带着保护附魔1的皮革头盔" },
            id12: { name: "毒箭僵尸", description: "特殊功能:每隔3秒射出剧毒箭矢，50点血，手持弓，带着铁护腿，穿着铁盔甲" },
            id13: { name: "电击僵尸", description: "特殊功能:每3秒对自己10*10的范围内释放电流，50点血，手持木棍，带着铁套" },
            id14: { name: "冰冻僵尸", description: "特殊功能:每3秒对自己10*10的范围内冻结，70点血，手持钻石，带着锁链套" },
            id15: { name: "暗影僵尸", description: "特殊功能：隐身效果和隐匿攻击，60点血，手持铁剑，佩戴暗影头盔" },
            id16: { name: "毁灭僵尸", description: "特殊功能：力量效果持续增加攻击力，150点血，手持钻石剑，全套铁护甲" },
            id17: { name: "雷霆僵尸", description: "特殊功能：雷电攻击，80点血，手持弓，全套链甲" },
            id18: { name: "变异科学家", description: "特殊功能：多种高级能力，600点血，手持附魔火焰附加二钻石剑，全套附魔保护三钻石甲" },
            id19: { name: "变异法师", description: "特殊功能：闪电攻击、失明减速效果和召唤能力，400点血，木棍，全套附魔保护三皮革甲" },
            id20: { name: "气球僵尸", description: "特殊功能：持续漂浮效果，25点血，手持气球棒" },
            id21: { name: "迷雾僵尸", description: "特殊功能：生成迷雾降低玩家视野和移动速度，40点血，手持迷雾生成器" },
            id22: { name: "变异雷霆僵尸", description: "特殊功能：多种强力雷电和召唤能力，800点血，手持弓，全套附魔保护2锁链套" },
            id23: { name: "终极毁灭僵尸", description: "特殊功能：多种强力能力，1000点血，全套附魔装备" },
            id24: { name: "变异暗影僵尸", description: "特殊功能：高级隐身和攻击能力，500点血，手持锋利5钻石剑" },
            id25: { name: "变异博士", description: "终极BOSS：多种强力特殊能力，2048点血，手持附魔火焰附加二钻石剑，全套附魔保护三钻石甲" }
        }
    },
    entity: {
        name: "实体",
        ids: {
            idc1: { name: "变异僵尸01", description: "僵尸猪人形态，每次攻击对玩家施加剧毒效果，100点血，手持附魔锋利1铁剑，全套皮革套" },
            idc2: { name: "变异僵尸02", description: "骷髅形态，100点血，手持附魔冲击2的弓，带着苦力怕头颅" },
            idc3: { name: "变异烈焰人", description: "烈焰人形态，特殊能力：发射烈焰粒子和烈焰弹，100点血" },
            idc4: { name: "变异爬行者", description: "闪电苦力怕形态，特殊能力：闪电伤害，50点血" },
            idc5: { name: "变异末影螨", description: "末影螨形态，特殊能力：释放电流，30点血" },
            idc6: { name: "变异蜘蛛", description: "蜘蛛形态，特殊能力：发射烈焰弹，50点血" },
            idc7: { name: "灾厄卫道士", description: "掠夺者形态，特殊能力：每5秒召唤两个仆从，发射粒子球，540点血，手持下界合金斧" },
            idc8: { name: "灾厄唤魔者", description: "唤魔者形态，特殊能力：每10秒召唤两个掠夺者，释放DNA螺旋法术，600点血，速度2效果" },
            idc9: { name: "灾厄劫掠兽", description: "劫掠兽形态，特殊能力：强力冲撞和跳跃，1000点血，跳跃提升3和速度1效果" },
            idc10: { name: "变异僵尸马", description: "僵尸马形态，特殊能力：每10秒召唤4个武装僵尸，冲撞玩家，300点血，速度5效果" },
            idc11: { name: "变异岩浆怪", description: "岩浆怪形态，特殊能力：生成火焰粒子环，造成火焰伤害，100点血，速度3效果" },
            idc12: { name: "变异骷髅", description: "凋零骷髅形态，特殊能力：发射凋零箭，200点血，手持附魔弓" },
            idc13: { name: "变异僵尸3", description: "溺尸形态，特殊能力：每20秒召唤4个变异岩浆怪，200点血，穿着黑色皮革鞋子" },
            idc14: { name: "变异僵尸04", description: "僵尸形态，特殊能力：每15秒召唤2个变异僵尸01，150点血，手持铁剑" },
            idc15: { name: "鲜血猪灵", description: "猪灵形态，特殊能力：每次攻击造成流血效果，250点血，手持金剑" },
            idc16: { name: "暗影潜影贝", description: "潜影贝形态，特殊能力：隐身效果和打乱玩家物品栏，200点血" },
            idc17: { name: "变异雪傀儡", description: "雪傀儡形态，特殊能力：发射冰霜弹，造成减速效果，300点血" },
            idc18: { name: "变异铁傀儡", description: "铁傀儡形态，特殊能力：远程声波攻击和高速方块投射，2000点血" },
            idc19: { name: "变异僵尸Max", description: "僵尸形态，特殊能力：附魔三叉戟，瞬移能力和隐身周期，2000点血" },
            idc20: { name: "灵魂坚守者", description: "监守者形态，特殊能力：声波攻击和高速冲撞，1000点血" },
            idc21: { name: "凋零领主", description: "凋零形态，特殊能力：每4秒随机召唤变异生物，高速发射凋零头颅，黑曜石方块攻击和下界栅栏攻击，6999点血" },
            idc22: { name: "异变之王", description: "末影龙形态，特殊能力：末影人粒子场，黑曜石方块攻击，下界栅栏攻击，随机召唤怪物，10999点血，速度3效果" }
        }
    },
    npc: {
        name: "感染者(npc)",
        ids: {
            idn1: { name: "感染者1史蒂夫", description: "会自动攻击玩家直到自己死亡" },
            idn2: { name: "感染者2艾利克斯", description: "会自动攻击玩家直到自己死亡" },
            idn3: { name: "感染者农民", description: "特殊能力：死亡时召唤3个路障僵尸，50点血，武器：木锄，防具：皮革套" },
            idn4: { name: "感染者居民", description: "特殊能力：死亡时召唤3个普通僵尸，70点血，武器：木剑，防具：皮革套装" },
            idn5: { name: "感染猪", description: "特殊能力：死亡时对10*10范围内玩家造成剧毒II效果持续30秒，200点血" }
        }
    }
};

// 特殊NPC类型
const npcTypes = {
    idn1: { name: "感染者1史蒂夫", description: "会自动攻击玩家直到自己死亡" },
    idn2: { name: "感染者2艾利克斯", description: "会自动攻击玩家直到自己死亡" },
    idn3: { name: "感染者农民", description: "特殊能力：死亡时召唤3个路障僵尸，50点血，武器：木锄，防具：皮革套" },
    idn4: { name: "感染者居民", description: "特殊能力：死亡时召唤3个普通僵尸，70点血，武器：木剑，防具：皮革套装" },
    idn5: { name: "感染猪", description: "特殊能力：死亡时对10*10范围内玩家造成剧毒II效果持续30秒，200点血" }
};

// 检查身份验证状态
function checkAuth() {
    const token = localStorage.getItem('auth_token');
    if (!token) {
        // 没有令牌，重定向到登录页面
        window.location.href = 'login.html';
        return false;
    }
    return true;
}

// 添加身份验证头到fetch请求
function fetchWithAuth(url, options = {}) {
    const token = localStorage.getItem('auth_token');
    if (!token) {
        // 没有令牌，重定向到登录页面
        window.location.href = 'login.html';
        return Promise.reject(new Error('未授权'));
    }

    // 确保headers存在
    if (!options.headers) {
        options.headers = {};
    }

    // 添加Authorization头
    options.headers['Authorization'] = `Bearer ${token}`;

    return fetch(url, options)
        .then(response => {
            if (response.status === 401) {
                // 令牌无效或过期，重定向到登录页面
                localStorage.removeItem('auth_token');
                window.location.href = 'login.html';
                throw new Error('未授权');
            }
            return response;
        });
}

// DOM加载完成后执行
document.addEventListener('DOMContentLoaded', () => {
    // 检查身份验证状态
    if (!checkAuth()) {
        return;
    }

    // 显示加载动画
    const loadingOverlay = document.getElementById('loading-overlay');

    // 延时隐藏加载动画，给用户一个视觉上的加载过程
    setTimeout(() => {
        loadingOverlay.classList.add('hidden');
        // 完全消失后移除元素
        setTimeout(() => {
            loadingOverlay.style.display = 'none';
        }, 500);
    }, 1800);

    // 初始化Bootstrap组件
    const createGameModal = new bootstrap.Modal(document.getElementById('create-game-modal'));
    const addDoorModal = new bootstrap.Modal(document.getElementById('add-door-modal'));
    const editDoorModal = new bootstrap.Modal(document.getElementById('edit-door-modal'));
    const roundModeModal = new bootstrap.Modal(document.getElementById('round-mode-modal'));
    const addWindowModal = new bootstrap.Modal(document.getElementById('add-window-modal'));
    const editWindowModal = new bootstrap.Modal(document.getElementById('edit-window-modal'));

    // 初始化事件监听 - 采用事件代理模式，避免事件绑定丢失
    document.body.addEventListener('click', function(e) {
        // 创建游戏按钮
        if (e.target.matches('#create-game-btn') || e.target.closest('#create-game-btn')) {
            createGameModal.show();
        }

        // 确认创建游戏
        if (e.target.matches('#confirm-create-game') || e.target.closest('#confirm-create-game')) {
            createGame();
        }

        // 重新加载配置
        if (e.target.matches('#reload-config-btn') || e.target.closest('#reload-config-btn')) {
            const restoreBtn = showAnimatedLoading('reload-config-btn');

            fetchWithAuth('/api/reload')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showToast('成功', '配置已成功刷新', 'success');
                        loadGameList();
                    } else {
                        showToast('错误', data.message || '刷新配置失败', 'danger');
                    }
                })
                .catch(error => {
                    console.error('刷新配置失败:', error);
                    showToast('错误', '刷新配置请求失败，请检查网络连接', 'danger');
                })
                .finally(() => {
                    if (restoreBtn) restoreBtn();
                });
        }

        // 确认添加门
        if (e.target.matches('#confirm-add-door') || e.target.closest('#confirm-add-door')) {
            addDoor();
        }

        // 确认编辑门
        if (e.target.matches('#confirm-edit-door') || e.target.closest('#confirm-edit-door')) {
            saveDoor();
        }

        // 删除门
        if (e.target.matches('#delete-door-btn') || e.target.closest('#delete-door-btn')) {
            deleteDoor();
        }

        // 从调试棒选择区域读取（添加门）
        if (e.target.matches('#get-region-btn') || e.target.closest('#get-region-btn')) {
            getRegionFromWand('add');
        }

        // 从调试棒选择区域读取（编辑门）
        if (e.target.matches('#edit-get-region-btn') || e.target.closest('#edit-get-region-btn')) {
            getRegionFromWand('edit');
        }

        // 添加窗户按钮
        if (e.target.matches('#add-window-btn') || e.target.closest('#add-window-btn')) {
            // 重置窗户区域信息
            document.getElementById('window-region-info').classList.add('d-none');
            window.tempWindowRegion = null;
            addWindowModal.show();
        }

        // 从调试棒选择区域读取（添加窗户）
        if (e.target.matches('#get-window-region-btn') || e.target.closest('#get-window-region-btn')) {
            getWindowRegionFromWand('add');
        }

        // 从调试棒选择区域读取（编辑窗户）
        if (e.target.matches('#edit-window-region-btn') || e.target.closest('#edit-window-region-btn')) {
            getWindowRegionFromWand('edit');
        }

        // 确认添加窗户
        if (e.target.matches('#confirm-add-window') || e.target.closest('#confirm-add-window')) {
            addWindow();
        }

        // 确认编辑窗户
        if (e.target.matches('#confirm-edit-window') || e.target.closest('#confirm-edit-window')) {
            saveWindow();
        }

        // 删除窗户
        if (e.target.matches('#delete-window-btn') || e.target.closest('#delete-window-btn')) {
            deleteWindow();
        }

        // 获取全局电源按钮位置
        if (e.target.matches('#get-power-button-location-btn') || e.target.closest('#get-power-button-location-btn')) {
            getPowerButtonLocation();
        }

        // 退出登录按钮
        if (e.target.matches('#logout-btn') || e.target.closest('#logout-btn')) {
            // 清除令牌
            localStorage.removeItem('auth_token');
            // 重定向到登录页面
            window.location.href = 'login.html';
        }
    });

    // 门锁定状态变化监听
    document.getElementById('door-locked').addEventListener('change', function() {
        const priceContainer = document.getElementById('price-container');
        if (this.checked) {
            priceContainer.style.display = 'block';
        } else {
            priceContainer.style.display = 'none';
        }
    });

    document.getElementById('edit-door-locked').addEventListener('change', function() {
        const priceContainer = document.getElementById('edit-price-container');
        if (this.checked) {
            priceContainer.style.display = 'block';
        } else {
            priceContainer.style.display = 'none';
        }
    });

    // 初始化读取区域按钮
    initRegionReadButtons();

    // 加载游戏列表
    loadGameList();

    // 在DOM加载完成后检查和修复按钮事件绑定
    fixAllClickableElements();
});

// 修复所有可点击元素
function fixAllClickableElements() {
    // 防止点击穿透
    document.querySelectorAll('.modal-content, .card, .list-group-item').forEach(el => {
        el.style.position = 'relative';
        el.style.zIndex = '1';
    });

    // 确保所有按钮都可点击
    document.querySelectorAll('button, .btn, input[type="button"], input[type="submit"]').forEach(btn => {
        ensureClickable(btn);
    });

    // 确保表单控件正常工作
    document.querySelectorAll('select, input, textarea').forEach(input => {
        input.style.pointerEvents = 'auto';
        input.style.zIndex = '2';
    });

    console.log('所有可点击元素已修复');
}

// 确保元素可点击
function ensureClickable(element) {
    // 添加必要的样式确保可点击
    element.style.position = 'relative';
    element.style.zIndex = '2';
    element.style.pointerEvents = 'auto';
    element.style.cursor = 'pointer';

    // 添加点击反馈效果
    element.addEventListener('click', function(e) {
        this.classList.add('btn-clicked');
        setTimeout(() => {
            this.classList.remove('btn-clicked');
        }, 300);

        console.log('元素被点击:', this.id || this.className);
    });
}

// 从调试棒选择的区域读取
function getRegionFromWand(mode) {
    const gameName = document.getElementById('edit-game-name').value;

    // 显示加载中
    const btnId = mode === 'add' ? 'get-region-btn' : 'edit-get-region-btn';
    const originalBtnContent = document.getElementById(btnId).innerHTML;
    document.getElementById(btnId).innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 读取中...';
    document.getElementById(btnId).disabled = true;

    // 发送请求获取当前玩家选择的区域
    fetchWithAuth(`/api/region?game=${encodeURIComponent(gameName)}`)
        .then(response => response.json())
        .then(data => {
            // 恢复按钮状态
            document.getElementById(btnId).innerHTML = originalBtnContent;
            document.getElementById(btnId).disabled = false;

            if (data.success) {
                showToast('成功', '成功从调试棒读取区域', 'success');

                // 如果是在编辑门模式下，我们需要存储区域数据
                if (mode === 'edit') {
                    const index = parseInt(document.getElementById('edit-door-index').value);
                    const doors = getCurrentDoorsData();
                    if (index >= 0 && index < doors.length) {
                        doors[index].region = data.region;
                        // 区域信息可能在UI上不直接显示，但会保存到服务器
                    }
                } else {
                    // 在添加门模式下，我们会暂存这个区域数据
                    window.tempDoorRegion = data.region;
                }
            } else {
                showToast('错误', data.message || '读取区域失败，请确保您已使用调试棒选择了一个区域', 'danger');
            }
        })
        .catch(error => {
            console.error('读取区域失败:', error);
            document.getElementById(btnId).innerHTML = originalBtnContent;
            document.getElementById(btnId).disabled = false;
            showToast('错误', '请求失败，请检查网络连接', 'danger');
        });
}

// 加载游戏列表
function loadGameList() {
    // 显示加载动画
    const gameList = document.getElementById('game-list');
    if (!gameList) {
        console.error('未找到游戏列表元素');
        return;
    }

    showAnimatedLoading(gameList);

    // 清空游戏选择器
    const gameSelector = document.getElementById('game-selector');
    if (gameSelector) {
        gameSelector.innerHTML = '<option value="">-- 选择游戏 --</option>';
    }

    // 发送请求获取游戏列表
    fetchWithAuth('/api/games')
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            console.log('获取到游戏列表数据:', data);

            // 清空游戏列表
            gameList.innerHTML = '';

            // 检查数据格式
            let games = [];
            if (data.games && Array.isArray(data.games)) {
                games = data.games;
            } else if (Array.isArray(data)) {
                games = data;
            } else {
                console.error('无效的游戏列表数据格式:', data);
                throw new Error('无效的游戏列表数据格式');
            }

            if (games.length === 0) {
                gameList.innerHTML = '<div class="alert alert-info">没有找到游戏，请创建一个新游戏。</div>';
                return;
            }

            // 排序游戏列表
            games.sort((a, b) => {
                const nameA = typeof a === 'string' ? a : (a.name || '');
                const nameB = typeof b === 'string' ? b : (b.name || '');
                return nameA.localeCompare(nameB);
            });

            // 生成游戏列表HTML
            let html = '';
            games.forEach((game) => {
                const gameName = typeof game === 'string' ? game : (game.name || '未命名游戏');
                const isComplete = typeof game === 'object' && game.complete === true;

                html += `
                <div class="game-item card mb-3" data-game="${gameName}">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="card-title mb-0">${gameName}</h5>
                            <span class="badge ${isComplete ? 'bg-success' : 'bg-warning'}">${isComplete ? '配置完成' : '配置中'}</span>
                        </div>
                        <div class="mt-3 d-flex">
                            <button class="btn btn-primary btn-sm me-2 edit-game-btn" data-game="${gameName}">
                                <i class="bi bi-gear-fill"></i> 编辑
                            </button>
                            <button class="btn btn-info btn-sm me-2 equipment-vue-btn" data-game="${gameName}">
                                <i class="bi bi-grid-3x3"></i> 装备
                            </button>
                            <button class="btn btn-danger btn-sm delete-game-btn" data-game="${gameName}">
                                <i class="bi bi-trash"></i> 删除
                            </button>
                        </div>
                    </div>
                </div>
                `;

                // 将游戏添加到选择器
                if (gameSelector) {
                    gameSelector.innerHTML += `<option value="${gameName}">${gameName}</option>`;
                }
            });

            gameList.innerHTML = html;

            // 重新绑定事件
            bindGameEvents();
        })
        .catch(error => {
            console.error('加载游戏列表失败:', error);
            gameList.innerHTML = `
            <div class="alert alert-danger">
                加载游戏列表失败: ${error.message}
                <button class="btn btn-sm btn-outline-danger ms-3 reload-games-btn">
                    <i class="fas fa-sync-alt"></i> 重试
                </button>
            </div>`;

            // 绑定重试按钮事件
            document.querySelectorAll('.reload-games-btn').forEach(btn => {
                btn.addEventListener('click', loadGameList);
            });
        });
}

// 绑定游戏列表事件
function bindGameEvents() {
    // 编辑游戏按钮事件
    document.querySelectorAll('.edit-game-btn').forEach(button => {
        button.addEventListener('click', function() {
            const gameName = this.getAttribute('data-game');
            if (gameName) {
                loadGameDetails(gameName);

                // 高亮显示选中的游戏
                document.querySelectorAll('.game-item').forEach(item => {
                    item.classList.remove('active');
                });
                this.closest('.game-item').classList.add('active');
            }
        });
    });

    // 装备设置按钮事件 (Vue版本)
    document.querySelectorAll('.equipment-vue-btn').forEach(button => {
        button.addEventListener('click', function() {
            const gameName = this.getAttribute('data-game');
            if (gameName) {
                window.open(`equipment-vue.html?game=${encodeURIComponent(gameName)}`, '_blank');
            }
        });
    });

    // 删除游戏按钮事件
    document.querySelectorAll('.delete-game-btn').forEach(button => {
        button.addEventListener('click', function() {
            const gameName = this.getAttribute('data-game');
            if (gameName && confirm(`确定要删除游戏 "${gameName}" 吗？此操作不可恢复！`)) {
                deleteGame(gameName);
            }
        });
    });
}

// 删除游戏
function deleteGame(gameName) {
    if (!gameName) return;

    // 显示加载状态
    const gameList = document.getElementById('game-list');
    const restore = showAnimatedLoading(gameList, '删除中...');

    // 发送删除请求
    fetchWithAuth(`/api/games/${encodeURIComponent(gameName)}`, {
        method: 'DELETE'
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP error ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        restore();

        if (data.success) {
            showToast('成功', `游戏 "${gameName}" 已删除`, 'success');

            // 重新加载游戏列表
            loadGameList();

            // 清空游戏详情
            const gameDetailsContainer = document.getElementById('game-details');
            if (gameDetailsContainer) {
                gameDetailsContainer.innerHTML = '<div class="alert alert-info">请从左侧选择一个游戏或创建新游戏</div>';
            }
        } else {
            showToast('错误', data.message || '删除游戏失败', 'danger');
        }
    })
    .catch(error => {
        restore();
        console.error('删除游戏失败:', error);
        showToast('错误', '删除游戏失败: ' + error.message, 'danger');
    });
}

// 加载游戏详情
function loadGameDetails(gameName) {
    // 显示加载动画
    const detailsDiv = document.getElementById('game-details');
    detailsDiv.innerHTML = '<div class="text-center p-3"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">加载中...</span></div><div class="mt-2 text-muted">加载游戏详情...</div></div>';

    fetchWithAuth(`/api/game?name=${encodeURIComponent(gameName)}`)
        .then(response => {
            if (!response.ok) {
                throw new Error(response.status === 404 ? '游戏不存在' : '无法加载游戏详情');
            }
            return response.json();
        })
        .then(game => {
            console.log('游戏详情数据:', game); // 调试信息，可以在控制台查看

            // 保存游戏详情到全局变量
            window.currentGameDetails = game;

            // 创建游戏详情表单
            let html = `
                <h3 class="mb-4 animate-left"><i class="bi bi-joystick me-2"></i>${game.name || gameName}</h3>
                <form id="game-edit-form" class="mb-3">
                    <input type="hidden" id="edit-game-name" value="${game.name || gameName}">

                    <div class="game-config-section animate-left delay-1">
                        <h4><i class="bi bi-gear me-2"></i>基本设置</h4>
                        <div class="mb-3">
                            <label for="max-players" class="form-label">最大玩家数</label>
                            <input type="number" class="form-control" id="max-players" value="${game.maxPlayers || 10}" min="1">
                        </div>
                    </div>

                    <div class="game-config-section animate-left delay-2">
                        <h4><i class="bi bi-geo-alt me-2"></i>出生点</h4>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="spawn-world" class="form-label">世界名</label>
                                <input type="text" class="form-control" id="spawn-world" value="${game.spawn?.world || ''}">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="spawn-x" class="form-label">X 坐标</label>
                                <input type="number" step="0.01" class="form-control" id="spawn-x" value="${game.spawn?.x || 0}">
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="spawn-y" class="form-label">Y 坐标</label>
                                <input type="number" step="0.01" class="form-control" id="spawn-y" value="${game.spawn?.y || 0}">
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="spawn-z" class="form-label">Z 坐标</label>
                                <input type="number" step="0.01" class="form-control" id="spawn-z" value="${game.spawn?.z || 0}">
                            </div>
                            <div class="col-md-2 mb-3">
                                <label for="spawn-yaw" class="form-label">偏航角</label>
                                <input type="number" step="0.01" class="form-control" id="spawn-yaw" value="${game.spawn?.yaw || 0}">
                            </div>
                            <div class="col-md-2 mb-3">
                                <label for="spawn-pitch" class="form-label">俯仰角</label>
                                <input type="number" step="0.01" class="form-control" id="spawn-pitch" value="${game.spawn?.pitch || 0}">
                            </div>
                        </div>
                    </div>

                    <div class="game-config-section animate-left delay-3">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h4><i class="bi bi-shield-fill me-2"></i>初始装备</h4>
                            <div>
                                <a href="equipment-vue.html?game=${encodeURIComponent(game.name || gameName)}" class="btn btn-success btn-sm me-2">
                                    <i class="bi bi-grid-3x3-gap-fill me-1"></i>图形化设置
                                </a>
                                <button type="button" class="btn btn-primary btn-sm" id="add-equipment-slot-btn">
                                    <i class="bi bi-plus-circle me-1"></i>添加槽位
                                </button>
                            </div>
                        </div>
                        <div class="alert alert-info">
                            <i class="bi bi-info-circle me-2"></i>配置玩家游戏开始时的初始装备，可以添加任意槽位（1-36）。点击"图形化设置"按钮使用更直观的物品栏界面进行设置。
                        </div>
                        <div id="equipment-slots-container">
                            <!-- 所有槽位将通过renderAllEquipmentSlots函数动态生成 -->
                            ${renderAllEquipmentSlots(game.initialEquipment || {})}
                        </div>
                    </div>

                    <div class="game-config-section animate-left delay-4">
                        <h4><i class="bi bi-clock-history me-2"></i>回合设置</h4>
                        <div class="mb-3">
                            <label for="game-rounds" class="form-label">总回合数</label>
                            <input type="number" class="form-control" id="game-rounds" value="${game.rounds || 5}" min="1">
                        </div>

                        <div id="zombie-count-container" class="mb-3">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h5><i class="bi bi-bug me-2"></i>僵尸数量设置</h5>
                                <div class="d-flex">
                                    <div class="input-group me-2" style="width: 200px;">
                                        <input type="number" class="form-control" id="zombie-round-search" placeholder="回合数" min="1">
                                        <button class="btn btn-outline-primary" type="button" id="search-zombie-round-btn">
                                            <i class="bi bi-search"></i>
                                        </button>
                                    </div>
                                    <button class="btn btn-sm btn-outline-primary" type="button" data-bs-toggle="collapse" data-bs-target="#zombieCountDetailsList" aria-expanded="false" aria-controls="zombieCountDetailsList">
                                        <i class="bi bi-list me-1"></i>查看详情
                                    </button>
                                </div>
                            </div>

                            <div class="zombie-count-summary card mb-3">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <h6 class="mb-0">回合僵尸数量概览</h6>
                                            <p class="text-muted small mb-0" id="zombie-count-summary-text">加载中...</p>
                                        </div>
                                        <div>
                                            <button type="button" class="btn btn-sm btn-outline-primary me-2" id="batch-set-zombie-count-btn">
                                                <i class="bi bi-pencil-square me-1"></i>批量设置
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="collapse" id="zombieCountDetailsList">
                                <div class="card card-body">
                                    <div class="row" id="zombie-count-inputs">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="game-config-section animate-left delay-4">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h4><i class="bi bi-door-closed me-2"></i>门设置</h4>
                            <button type="button" class="btn btn-primary btn-sm add-door-btn" id="add-door-btn">
                                <i class="bi bi-plus-circle me-1"></i>添加门
                            </button>
                        </div>
                        <div id="door-list-container" class="door-list-container">
                            <div id="door-list">
                                <!-- 门列表将被动态加载 -->
                            </div>
                        </div>
                    </div>

                    <div class="game-config-section animate-left delay-5">
                        <h4><i class="bi bi-bug me-2"></i>僵尸生成点</h4>
                        <div class="alert alert-info">
                            <i class="bi bi-info-circle-fill me-2"></i>僵尸生成点需要在游戏中使用<code>/dzs setZombieSpawn</code>命令设置，然后使用<code>/dzs with</code>命令将生成点关联到门。
                        </div>
                        <div id="zombie-spawns-container" class="zombie-spawns-container">
                            <div id="zombie-spawns-list">
                                <!-- 僵尸生成点列表将被动态加载 -->
                            </div>
                        </div>
                    </div>

                    <div class="game-config-section animate-left delay-6">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h4><i class="bi bi-sliders me-2"></i>回合模式设置</h4>
                            <div class="d-flex">
                                <button type="button" class="btn btn-primary btn-sm me-2" id="add-round-mode-btn">
                                    <i class="bi bi-plus-circle me-1"></i>设置第x回合
                                </button>
                                <button class="btn btn-sm btn-outline-primary" type="button" data-bs-toggle="collapse" data-bs-target="#roundModesDetailsList" aria-expanded="true" aria-controls="roundModesDetailsList">
                                    <i class="bi bi-list me-1"></i>查看详情
                                </button>
                            </div>
                        </div>

                        <!-- 回合模式概览卡片 -->
                        <div class="round-modes-summary card mb-3">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="mb-0">回合模式概览</h6>
                                        <p class="text-muted small mb-0" id="round-modes-summary-text">加载中...</p>
                                    </div>
                                    <div>
                                        <i class="bi bi-sliders text-primary" style="font-size: 1.5rem;"></i>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="alert alert-info">
                            <i class="bi bi-info-circle-fill me-2"></i>回合模式用于设置每个回合的怪物生成规则。每个回合的每个生成点可以配置不同的怪物类型和数量。
                        </div>

                        <div class="collapse show" id="roundModesDetailsList">
                            <div class="card card-body">
                                <div id="round-modes-container" class="round-modes-container">
                                    <div id="round-modes-list">
                                        <!-- 回合模式列表将被动态加载 -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="game-config-section animate-left delay-7">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h4><i class="bi bi-window me-2"></i>窗户设置</h4>
                            <button type="button" class="btn btn-primary btn-sm" id="add-window-btn">
                                <i class="bi bi-plus-circle me-1"></i>添加窗户
                            </button>
                        </div>
                        <div class="alert alert-info">
                            <i class="bi bi-info-circle-fill me-2"></i>窗户是可以被僵尸破坏、玩家修复的木栅栏区域。
                        </div>
                        <div id="windows-container" class="windows-container">
                            <div id="windows-list">
                                <!-- 窗户列表将被动态加载 -->
                            </div>
                        </div>
                    </div>

                    <div class="game-config-section animate-left delay-8">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h4><i class="bi bi-lightning me-2"></i>全局电源按钮</h4>
                        </div>
                        <div class="alert alert-info">
                            <i class="bi bi-info-circle-fill me-2"></i>全局电源按钮需要玩家解锁后才能购买全局增益效果。
                        </div>
                        <div class="card mb-3">
                            <div class="card-body">
                                <div class="form-check form-switch mb-3">
                                    <input class="form-check-input" type="checkbox" id="power-button-enabled">
                                    <label class="form-check-label" for="power-button-enabled">启用全局电源按钮</label>
                                </div>
                                <div id="power-button-settings" class="mt-3">
                                    <div class="mb-3">
                                        <label for="power-button-price" class="form-label">解锁价格</label>
                                        <input type="number" class="form-control" id="power-button-price" min="0" value="1000">
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">按钮位置</label>
                                        <div class="input-group mb-2">
                                            <span class="input-group-text">世界</span>
                                            <input type="text" class="form-control" id="power-button-world">
                                        </div>
                                        <div class="input-group mb-2">
                                            <span class="input-group-text">X</span>
                                            <input type="number" class="form-control" id="power-button-x" step="0.01">
                                            <span class="input-group-text">Y</span>
                                            <input type="number" class="form-control" id="power-button-y" step="0.01">
                                            <span class="input-group-text">Z</span>
                                            <input type="number" class="form-control" id="power-button-z" step="0.01">
                                        </div>
                                        <button type="button" class="btn btn-outline-primary" id="get-power-button-location-btn">
                                            <i class="bi bi-geo-alt me-1"></i>从当前位置获取
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="d-grid gap-2 animate-left delay-9">
                        <button type="button" id="save-game-btn" class="btn btn-success btn-lg">
                            <i class="bi bi-save me-2"></i>保存游戏设置
                        </button>
                    </div>
                </form>
            `;

            detailsDiv.innerHTML = html;

            // 渲染僵尸数量输入框
            renderZombieCountInputs(game.rounds || 5, game.zombieCount || {});

            // 渲染门列表
            console.log('渲染门列表数据:', game.doors);
            renderDoorsList(game.doors || []);

            // 渲染僵尸生成点列表
            console.log('渲染僵尸生成点数据:', game.zombieSpawns);
            renderZombieSpawnsList(game.zombieSpawns || []);

            // 渲染回合模式列表
            console.log('渲染回合模式数据:', game.roundModes);
            renderRoundModesList(game.roundModes || {});

            // 渲染窗户列表
            console.log('渲染窗户数据:', game.windows);
            renderWindowsList(game.windows || []);

            // 渲染全局电源按钮设置
            if (game.powerButton) {
                console.log('渲染全局电源按钮数据:', game.powerButton);
                document.getElementById('power-button-enabled').checked = game.powerButton.enabled || false;

                if (game.powerButton.enabled) {
                    document.getElementById('power-button-price').value = game.powerButton.price || 1000;
                    document.getElementById('power-button-world').value = game.powerButton.world || '';
                    document.getElementById('power-button-x').value = game.powerButton.x || 0;
                    document.getElementById('power-button-y').value = game.powerButton.y || 0;
                    document.getElementById('power-button-z').value = game.powerButton.z || 0;
                    document.getElementById('power-button-settings').style.display = 'block';
                } else {
                    document.getElementById('power-button-settings').style.display = 'none';
                }

                // 添加电源按钮启用状态变化监听
                document.getElementById('power-button-enabled').addEventListener('change', function() {
                    if (this.checked) {
                        document.getElementById('power-button-settings').style.display = 'block';
                    } else {
                        document.getElementById('power-button-settings').style.display = 'none';
                    }
                });
            } else {
                // 默认设置
                document.getElementById('power-button-enabled').checked = false;
                document.getElementById('power-button-settings').style.display = 'none';

                // 添加电源按钮启用状态变化监听
                document.getElementById('power-button-enabled').addEventListener('change', function() {
                    if (this.checked) {
                        document.getElementById('power-button-settings').style.display = 'block';
                    } else {
                        document.getElementById('power-button-settings').style.display = 'none';
                    }
                });
            }

            // 监听回合数变化
            document.getElementById('game-rounds').addEventListener('change', (e) => {
                const rounds = parseInt(e.target.value);
                if (rounds > 0) {
                    renderZombieCountInputs(rounds, getZombieCountValues());
                }
            });

            // 添加保存按钮事件
            document.getElementById('save-game-btn').addEventListener('click', () => {
                saveGameConfig();
            });

            // 添加装备槽位按钮事件
            document.getElementById('add-equipment-slot-btn').addEventListener('click', () => {
                addEquipmentSlot();
            });

            // 绑定已有的删除槽位按钮事件
            document.querySelectorAll('.remove-slot-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    const slot = this.getAttribute('data-slot');
                    if (slot) {
                        removeEquipmentSlot(slot);
                    }
                });
            });

            // 绑定批量操作按钮事件
            const addHotbarBtn = document.getElementById('add-hotbar-slots-btn');
            if (addHotbarBtn) {
                addHotbarBtn.addEventListener('click', () => {
                    addHotbarSlots();
                });
            }

            const addRowBtn = document.getElementById('add-row-slots-btn');
            if (addRowBtn) {
                addRowBtn.addEventListener('click', () => {
                    addRowSlots();
                });
            }

            const addDefaultBtn = document.getElementById('add-default-slots-btn');
            if (addDefaultBtn) {
                addDefaultBtn.addEventListener('click', () => {
                    addDefaultSlots();
                });
            }

            const clearAllBtn = document.getElementById('clear-all-slots-btn');
            if (clearAllBtn) {
                clearAllBtn.addEventListener('click', () => {
                    if (confirm('确定要清空所有槽位吗？此操作不可恢复！')) {
                        clearAllSlots();
                    }
                });
            }

            // 添加门按钮事件
            document.getElementById('add-door-btn').addEventListener('click', () => {
                // 重置添加门表单
                document.getElementById('door-name').value = '';
                document.getElementById('door-locked').checked = true;
                document.getElementById('door-price').value = '500';
                document.getElementById('price-container').style.display = 'block';

                // 重置临时区域数据
                window.tempDoorRegion = null;

                // 显示模态框
                const addDoorModal = new bootstrap.Modal(document.getElementById('add-door-modal'));
                addDoorModal.show();
            });

            // 添加窗户按钮事件
            document.getElementById('add-window-btn').addEventListener('click', () => {
                // 重置临时区域数据
                window.tempWindowRegion = null;

                // 隐藏区域信息
                const regionInfo = document.getElementById('window-region-info');
                if (regionInfo) {
                    regionInfo.classList.add('d-none');
                }

                // 显示模态框
                const addWindowModal = new bootstrap.Modal(document.getElementById('add-window-modal'));
                addWindowModal.show();
            });

            // 添加窗户确认按钮事件
            document.getElementById('confirm-add-window').addEventListener('click', () => {
                addWindow();
            });

            // 添加窗户区域选择按钮事件
            document.getElementById('get-window-region-btn').addEventListener('click', () => {
                getWindowRegionFromWand('add');
            });

            // 编辑窗户确认按钮事件
            document.getElementById('confirm-edit-window').addEventListener('click', () => {
                saveWindow();
            });

            // 删除窗户按钮事件
            document.getElementById('delete-window-btn').addEventListener('click', () => {
                deleteWindow();
            });

            // 编辑窗户区域选择按钮事件
            document.getElementById('edit-window-region-btn').addEventListener('click', () => {
                getWindowRegionFromWand('edit');
            });
        })
        .catch(error => {
            console.error('加载游戏详情失败:', error);
            detailsDiv.innerHTML =
                `<div class="alert alert-danger animate-left"><i class="bi bi-exclamation-triangle-fill me-2"></i>加载游戏详情失败: ${error.message}</div>
                <div class="d-grid gap-2 mt-3">
                    <button id="retry-load-details" class="btn btn-outline-primary">
                        <i class="bi bi-arrow-repeat me-2"></i>重试
                    </button>
                </div>`;

            // 添加重试按钮事件
            const retryBtn = document.getElementById('retry-load-details');
            if (retryBtn) {
                retryBtn.addEventListener('click', () => {
                    loadGameDetails(gameName);
                });
            }
        });
}

// 渲染僵尸数量输入框
function renderZombieCountInputs(rounds, zombieCount) {
    const container = document.getElementById('zombie-count-inputs');
    container.innerHTML = '';

    for (let i = 1; i <= rounds; i++) {
        const countKey = `round${i}`;
        const value = zombieCount[countKey] || 10; // 默认值10

        const col = document.createElement('div');
        col.className = 'col-md-4 mb-2';
        col.innerHTML = `
            <label for="zombie-count-${i}" class="form-label">第 ${i} 回合</label>
            <div class="input-group">
                <span class="input-group-text"><i class="bi bi-bug"></i></span>
                <input type="number" class="form-control zombie-count-input"
                       id="zombie-count-${i}"
                       data-round="${i}"
                       value="${value}" min="1">
            </div>
        `;

        container.appendChild(col);
    }

    // 更新概览信息
    updateZombieCountSummary(rounds, zombieCount);

    // 绑定搜索和批量设置事件
    bindZombieCountEvents(rounds);
}

// 更新僵尸数量概览信息
function updateZombieCountSummary(rounds, zombieCount) {
    const summaryText = document.getElementById('zombie-count-summary-text');
    if (!summaryText) return;

    // 计算平均值、最大值、最小值
    let total = 0;
    let max = 0;
    let min = Infinity;
    let count = 0;

    for (let i = 1; i <= rounds; i++) {
        const countKey = `round${i}`;
        const value = zombieCount[countKey] || 10; // 默认值10

        total += value;
        max = Math.max(max, value);
        min = Math.min(min, value);
        count++;
    }

    const avg = count > 0 ? Math.round(total / count) : 0;

    // 更新概览文本
    summaryText.innerHTML = `
        总回合数: <strong>${rounds}</strong> |
        平均每回合: <strong>${avg}</strong> 只僵尸 |
        最多: <strong>${max}</strong> 只 (第 ${getMaxZombieRound(zombieCount, rounds)} 回合) |
        最少: <strong>${min}</strong> 只 (第 ${getMinZombieRound(zombieCount, rounds)} 回合)
    `;
}

// 获取僵尸数量最多的回合
function getMaxZombieRound(zombieCount, rounds) {
    let maxRound = 1;
    let maxValue = 0;

    for (let i = 1; i <= rounds; i++) {
        const countKey = `round${i}`;
        const value = zombieCount[countKey] || 10; // 默认值10

        if (value > maxValue) {
            maxValue = value;
            maxRound = i;
        }
    }

    return maxRound;
}

// 获取僵尸数量最少的回合
function getMinZombieRound(zombieCount, rounds) {
    let minRound = 1;
    let minValue = Infinity;

    for (let i = 1; i <= rounds; i++) {
        const countKey = `round${i}`;
        const value = zombieCount[countKey] || 10; // 默认值10

        if (value < minValue) {
            minValue = value;
            minRound = i;
        }
    }

    return minRound;
}

// 绑定僵尸数量相关事件
function bindZombieCountEvents(rounds) {
    // 绑定搜索按钮事件
    const searchBtn = document.getElementById('search-zombie-round-btn');
    if (searchBtn) {
        searchBtn.addEventListener('click', () => {
            searchZombieRound();
        });
    }

    // 绑定回车键搜索
    const searchInput = document.getElementById('zombie-round-search');
    if (searchInput) {
        searchInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                searchZombieRound();
            }
        });
    }

    // 绑定批量设置按钮事件
    const batchSetBtn = document.getElementById('batch-set-zombie-count-btn');
    if (batchSetBtn) {
        batchSetBtn.addEventListener('click', (e) => {
            e.preventDefault(); // 阻止默认行为，防止页面刷新
            showBatchSetZombieCountModal(rounds);
        });
    }

    // 监听僵尸数量输入变化，实时更新概览
    document.querySelectorAll('.zombie-count-input').forEach(input => {
        input.addEventListener('change', () => {
            updateZombieCountSummary(rounds, getZombieCountValues());
        });
    });
}

// 搜索特定回合的僵尸数量
function searchZombieRound() {
    const searchInput = document.getElementById('zombie-round-search');
    if (!searchInput) return;

    const roundNumber = parseInt(searchInput.value);
    if (isNaN(roundNumber) || roundNumber < 1) {
        showToast('错误', '请输入有效的回合数', 'danger');
        return;
    }

    // 获取总回合数
    const totalRounds = parseInt(document.getElementById('game-rounds').value) || 5;

    if (roundNumber > totalRounds) {
        showToast('错误', `回合数超出范围，当前总回合数为 ${totalRounds}`, 'danger');
        return;
    }

    // 展开详情面板
    const detailsCollapse = bootstrap.Collapse.getInstance(document.getElementById('zombieCountDetailsList'));
    if (detailsCollapse) {
        detailsCollapse.show();
    } else {
        // 如果实例不存在，手动添加show类
        document.getElementById('zombieCountDetailsList').classList.add('show');
    }

    // 滚动到对应的输入框
    const targetInput = document.getElementById(`zombie-count-${roundNumber}`);
    if (targetInput) {
        // 高亮显示目标输入框
        targetInput.closest('.col-md-4').classList.add('highlight-input');

        // 滚动到目标位置
        targetInput.scrollIntoView({ behavior: 'smooth', block: 'center' });

        // 聚焦输入框
        setTimeout(() => {
            targetInput.focus();
            targetInput.select();

            // 3秒后移除高亮
            setTimeout(() => {
                targetInput.closest('.col-md-4').classList.remove('highlight-input');
            }, 3000);
        }, 500);
    } else {
        showToast('错误', `未找到第 ${roundNumber} 回合的设置`, 'danger');
    }
}

// 显示批量设置僵尸数量的模态框
function showBatchSetZombieCountModal(rounds) {
    // 检查模态框是否已存在
    let modal = document.getElementById('batch-zombie-count-modal');

    // 如果不存在，创建模态框
    if (!modal) {
        const modalHTML = `
            <div class="modal fade" id="batch-zombie-count-modal" tabindex="-1" aria-labelledby="batchZombieCountModalLabel" aria-hidden="true">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="batchZombieCountModalLabel">批量设置僵尸数量</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <div class="mb-3">
                                <label for="batch-zombie-count-value" class="form-label">僵尸数量</label>
                                <div class="input-group">
                                    <input type="number" class="form-control" id="batch-zombie-count-value" min="1" value="10">
                                    <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-expanded="false">快速选择</button>
                                    <ul class="dropdown-menu dropdown-menu-end">
                                        <li><a class="dropdown-item" href="#" data-value="5">5只</a></li>
                                        <li><a class="dropdown-item" href="#" data-value="10">10只</a></li>
                                        <li><a class="dropdown-item" href="#" data-value="15">15只</a></li>
                                        <li><a class="dropdown-item" href="#" data-value="20">20只</a></li>
                                        <li><a class="dropdown-item" href="#" data-value="30">30只</a></li>
                                        <li><a class="dropdown-item" href="#" data-value="40">40只</a></li>
                                        <li><a class="dropdown-item" href="#" data-value="50">50只</a></li>
                                    </ul>
                                </div>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">应用范围</label>
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="batch-zombie-range" id="batch-zombie-all" value="all" checked>
                                    <label class="form-check-label" for="batch-zombie-all">
                                        所有回合
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="batch-zombie-range" id="batch-zombie-range" value="range">
                                    <label class="form-check-label" for="batch-zombie-range">
                                        指定范围
                                    </label>
                                </div>
                                <div class="input-group mt-2" id="batch-zombie-range-inputs" style="display: none;">
                                    <span class="input-group-text">从</span>
                                    <input type="number" class="form-control" id="batch-zombie-start" min="1" value="1">
                                    <span class="input-group-text">到</span>
                                    <input type="number" class="form-control" id="batch-zombie-end" min="1" value="${rounds}">
                                </div>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">高级设置</label>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="batch-zombie-pattern" value="pattern">
                                    <label class="form-check-label" for="batch-zombie-pattern">
                                        使用递增/递减模式
                                    </label>
                                </div>
                                <div id="batch-zombie-pattern-inputs" class="mt-2" style="display: none;">
                                    <div class="input-group mb-2">
                                        <span class="input-group-text">起始数量</span>
                                        <input type="number" class="form-control" id="batch-zombie-start-count" min="1" value="5">
                                    </div>
                                    <div class="input-group">
                                        <span class="input-group-text">每回合增加</span>
                                        <input type="number" class="form-control" id="batch-zombie-increment" value="2">
                                        <span class="input-group-text">只</span>
                                    </div>
                                    <div class="form-text text-muted">
                                        注意：增量可以为负数，表示递减
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                            <button type="button" class="btn btn-primary" id="confirm-batch-zombie-count">应用设置</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // 添加到文档中
        document.body.insertAdjacentHTML('beforeend', modalHTML);
        modal = document.getElementById('batch-zombie-count-modal');

        // 绑定范围选择事件
        document.querySelectorAll('input[name="batch-zombie-range"]').forEach(radio => {
            radio.addEventListener('change', function() {
                const rangeInputs = document.getElementById('batch-zombie-range-inputs');
                if (this.value === 'range') {
                    rangeInputs.style.display = 'flex';
                } else {
                    rangeInputs.style.display = 'none';
                }
            });
        });

        // 绑定模式选择事件
        document.getElementById('batch-zombie-pattern').addEventListener('change', function() {
            const patternInputs = document.getElementById('batch-zombie-pattern-inputs');
            if (this.checked) {
                patternInputs.style.display = 'block';
            } else {
                patternInputs.style.display = 'none';
            }
        });

        // 绑定快速选择下拉菜单事件
        document.querySelectorAll('#batch-zombie-count-modal .dropdown-item').forEach(item => {
            item.addEventListener('click', function(e) {
                e.preventDefault();
                const value = this.getAttribute('data-value');
                document.getElementById('batch-zombie-count-value').value = value;
            });
        });

        // 绑定确认按钮事件
        document.getElementById('confirm-batch-zombie-count').addEventListener('click', function() {
            applyBatchZombieCount(rounds);
        });
    }

    // 显示模态框
    const bsModal = new bootstrap.Modal(modal);
    bsModal.show();
}

// 应用批量设置僵尸数量
function applyBatchZombieCount(rounds) {
    // 检查是否使用递增/递减模式
    const usePattern = document.getElementById('batch-zombie-pattern').checked;

    // 获取范围
    const rangeType = document.querySelector('input[name="batch-zombie-range"]:checked').value;
    let startRound = 1;
    let endRound = rounds;

    if (rangeType === 'range') {
        startRound = parseInt(document.getElementById('batch-zombie-start').value);
        endRound = parseInt(document.getElementById('batch-zombie-end').value);

        if (isNaN(startRound) || startRound < 1 || isNaN(endRound) || endRound < startRound || endRound > rounds) {
            showToast('错误', '请输入有效的回合范围', 'danger');
            return;
        }
    }

    // 应用设置
    if (usePattern) {
        // 使用递增/递减模式
        const startCount = parseInt(document.getElementById('batch-zombie-start-count').value);
        const increment = parseInt(document.getElementById('batch-zombie-increment').value);

        if (isNaN(startCount) || startCount < 1) {
            showToast('错误', '请输入有效的起始数量', 'danger');
            return;
        }

        if (isNaN(increment)) {
            showToast('错误', '请输入有效的增量', 'danger');
            return;
        }

        // 计算每个回合的僵尸数量
        let currentCount = startCount;
        for (let i = startRound; i <= endRound; i++) {
            const input = document.getElementById(`zombie-count-${i}`);
            if (input) {
                // 确保数量不小于1
                const roundCount = Math.max(1, currentCount);
                input.value = roundCount;
                currentCount += increment;
            }
        }

        // 显示成功提示
        const patternType = increment >= 0 ? '递增' : '递减';
        showToast('成功', `已将第 ${startRound} 到第 ${endRound} 回合的僵尸数量设置为${patternType}模式，起始数量 ${startCount}，每回合${increment >= 0 ? '增加' : '减少'} ${Math.abs(increment)} 只`, 'success');
    } else {
        // 使用固定数量模式
        const countValue = parseInt(document.getElementById('batch-zombie-count-value').value);
        if (isNaN(countValue) || countValue < 1) {
            showToast('错误', '请输入有效的僵尸数量', 'danger');
            return;
        }

        // 应用固定数量
        for (let i = startRound; i <= endRound; i++) {
            const input = document.getElementById(`zombie-count-${i}`);
            if (input) {
                input.value = countValue;
            }
        }

        // 显示成功提示
        showToast('成功', `已将第 ${startRound} 到第 ${endRound} 回合的僵尸数量设置为 ${countValue}`, 'success');
    }

    // 更新概览信息
    updateZombieCountSummary(rounds, getZombieCountValues());

    // 关闭模态框
    const modal = bootstrap.Modal.getInstance(document.getElementById('batch-zombie-count-modal'));
    if (modal) {
        modal.hide();
    }

    // 显示保存按钮
    showFloatingSaveButton();
}

// 渲染门列表
function renderDoorsList(doors = []) {
    const container = document.getElementById('door-list');
    if (!container) {
        console.error('门列表容器不存在');
        return;
    }

    console.log('收到门数据进行渲染:', doors);
    container.innerHTML = '';

    // 如果doors不是数组，尝试转换
    let doorsArray = doors;
    if (!Array.isArray(doors)) {
        if (typeof doors === 'object' && doors !== null) {
            // 可能是{key: value}格式，将其转换为数组
            doorsArray = Object.keys(doors).map(key => {
                const door = doors[key];
                const doorObj = {
                    name: key,
                    locked: door.locked !== undefined ? door.locked : true,
                    price: door.price || door.unlockPrice || 500,
                    region: door.region || null
                };

                // 处理关联的生成点，支持多选
                if (door.linkedSpawns && Array.isArray(door.linkedSpawns) && door.linkedSpawns.length > 0) {
                    doorObj.linkedSpawns = door.linkedSpawns;
                    // 保持向后兼容性
                    doorObj.linkedSpawn = door.linkedSpawns[0];
                } else if (door.linkedSpawn) {
                    doorObj.linkedSpawn = door.linkedSpawn;
                }

                return doorObj;
            });
            console.log('转换后的门数据:', doorsArray);
        } else {
            doorsArray = [];
        }
    }

    if (doorsArray.length === 0) {
        container.innerHTML = '<div class="text-center p-3 text-muted"><i class="bi bi-door-open me-2"></i>暂无门设置，请添加门</div>';
        return;
    }

    // 转换门数据格式（支持从服务器端接收的不同格式）
    const processedDoors = [];

    for (let i = 0; i < doorsArray.length; i++) {
        let door = doorsArray[i];

        // 处理可能的不同数据格式
        if (typeof door !== 'object' || door === null) {
            console.warn('跳过无效门数据:', door);
            continue;
        }

        // 创建规范化的门对象
        let processedDoor = {
            name: door.name || '未命名门' + (i + 1),
            locked: door.locked !== undefined ? door.locked : true,
            price: door.price || door.unlockPrice || 500
        };

        // 处理关联的生成点，支持多选
        if (door.linkedSpawns && Array.isArray(door.linkedSpawns) && door.linkedSpawns.length > 0) {
            processedDoor.linkedSpawns = door.linkedSpawns;
            // 保持向后兼容性
            processedDoor.linkedSpawn = door.linkedSpawns[0];
        } else if (door.linkedSpawn) {
            processedDoor.linkedSpawn = door.linkedSpawn;
        }

        // 保留区域数据（如果存在）
        if (door.region) {
            processedDoor.region = door.region;
        }

        processedDoors.push(processedDoor);
    }

    // 保存标准化的门数据到全局变量（用于后续保存）
    window.currentGameDoors = processedDoors;

    console.log('处理后的门数据:', processedDoors);

    // 渲染每个门条目
    processedDoors.forEach((door, index) => {
        const doorItem = document.createElement('div');
        doorItem.className = 'door-item animate-left';
        doorItem.style.animationDelay = `${0.1 * (index + 1)}s`;

        const locked = door.locked !== undefined ? door.locked : true;

        let statusHTML = '';
        if (locked) {
            statusHTML = `<span class="door-status locked">需解锁</span> <span class="door-price">${door.price || 0} 金币</span>`;
        } else {
            statusHTML = `<span class="door-status unlocked">自动开启</span>`;
        }

        let regionHTML = '';
        if (door.region) {
            regionHTML = `<span class="door-region-info" title="已设置区域"><i class="bi bi-check-circle-fill text-success"></i></span>`;
        }

        let spawnHTML = '';
        // 处理多个关联的生成点
        if (door.linkedSpawns && Array.isArray(door.linkedSpawns) && door.linkedSpawns.length > 0) {
            const spawnNames = door.linkedSpawns.join(', ');
            spawnHTML = `<span class="door-spawn-info ms-2" title="关联僵尸生成点: ${spawnNames}"><i class="bi bi-bug-fill text-warning"></i> ${door.linkedSpawns.length}</span>`;
        } else if (door.linkedSpawn) {
            // 向后兼容：单个生成点
            spawnHTML = `<span class="door-spawn-info ms-2" title="关联僵尸生成点: ${door.linkedSpawn}"><i class="bi bi-bug-fill text-warning"></i></span>`;
        }

        doorItem.innerHTML = `
            <span class="door-name"><i class="bi ${locked ? 'bi-door-closed' : 'bi-door-open'} me-2"></i>${door.name}</span>
            <div class="door-controls">
                ${statusHTML}
                ${regionHTML}
                ${spawnHTML}
                <button class="btn btn-sm btn-outline-primary ms-2 edit-door-btn" data-index="${index}">
                    <i class="bi bi-pencil"></i>
                </button>
            </div>
        `;

        container.appendChild(doorItem);
    });

    // 绑定编辑按钮事件 - 使用事件委托
    document.removeEventListener('click', doorEditHandler); // 移除之前的处理器避免重复
    document.addEventListener('click', doorEditHandler);
}

// 门编辑按钮点击事件处理函数
function doorEditHandler(e) {
    // 首先确保找到的是编辑按钮或其子元素
    const editBtn = e.target.closest('.edit-door-btn');
    if (!editBtn) return;

    // 阻止默认行为（如果按钮在<a>标签内）
    e.preventDefault();

    // 获取门索引并调用编辑门函数
    const index = parseInt(editBtn.getAttribute('data-index'));
    if (!isNaN(index)) {
        editDoor(index);
    } else {
        console.error('无效的门索引');
    }

    // 阻止事件冒泡，防止触发其他事件处理器
    e.stopPropagation();
}

// 编辑门
function editDoor(index) {
    if (!window.currentGameDoors || !Array.isArray(window.currentGameDoors) || index < 0 || index >= window.currentGameDoors.length) {
        showToast('错误', '门数据无效或索引超出范围', 'danger');
        return;
    }

    const door = window.currentGameDoors[index];
    console.log('编辑门：', door);

    // 填充表单
    document.getElementById('edit-door-index').value = index;
    document.getElementById('edit-door-name').value = door.name || '';
    document.getElementById('edit-door-locked').checked = door.locked !== undefined ? door.locked : true;
    document.getElementById('edit-door-price').value = door.price || 500;

    // 显示/隐藏价格输入框
    const priceContainer = document.getElementById('edit-price-container');
    priceContainer.style.display = door.locked ? 'block' : 'none';

    // 显示区域坐标信息
    const regionInfoContainer = document.getElementById('edit-region-info-container');
    if (regionInfoContainer) {
        if (door.region) {
            // 区域数据格式可能不同（可能包含x1,y1,z1或minX,minY,minZ等字段）
            const region = door.region;
            const minX = region.x1 !== undefined ? region.x1 : (region.minX !== undefined ? region.minX : 'N/A');
            const minY = region.y1 !== undefined ? region.y1 : (region.minY !== undefined ? region.minY : 'N/A');
            const minZ = region.z1 !== undefined ? region.z1 : (region.minZ !== undefined ? region.minZ : 'N/A');
            const maxX = region.x2 !== undefined ? region.x2 : (region.maxX !== undefined ? region.maxX : 'N/A');
            const maxY = region.y2 !== undefined ? region.y2 : (region.maxY !== undefined ? region.maxY : 'N/A');
            const maxZ = region.z2 !== undefined ? region.z2 : (region.maxZ !== undefined ? region.maxZ : 'N/A');
            const world = region.world || 'world';

            regionInfoContainer.innerHTML = `
                <div class="alert alert-info mt-2">
                    <h6><i class="bi bi-geo-alt-fill me-2"></i>门区域坐标信息</h6>
                    <div class="small">
                        <strong>世界：</strong>${world}<br>
                        <strong>起点：</strong>(${minX}, ${minY}, ${minZ})<br>
                        <strong>终点：</strong>(${maxX}, ${maxY}, ${maxZ})
                    </div>
                </div>
            `;
            regionInfoContainer.style.display = 'block';
        } else {
            regionInfoContainer.innerHTML = `
                <div class="alert alert-warning mt-2">
                    <i class="bi bi-exclamation-triangle-fill me-2"></i>该门尚未设置区域坐标
                </div>
            `;
            regionInfoContainer.style.display = 'block';
        }
    }

    // 添加关联僵尸生成点的选择器
    const linkedSpawnContainer = document.getElementById('edit-linked-spawn-container');
    if (!linkedSpawnContainer) {
        // 如果容器不存在，创建一个
        const container = document.createElement('div');
        container.id = 'edit-linked-spawn-container';
        container.className = 'mb-3';

        // 将容器插入到区域信息容器后面
        if (regionInfoContainer) {
            regionInfoContainer.parentNode.insertBefore(container, regionInfoContainer.nextSibling);
        } else {
            // 如果没有区域信息容器，则插入到价格容器后面
            const priceContainer = document.getElementById('edit-price-container');
            if (priceContainer) {
                priceContainer.parentNode.insertBefore(container, priceContainer.nextSibling);
            }
        }

        // 处理关联的生成点，支持多选
        let linkedSpawns = [];
        if (door.linkedSpawns && Array.isArray(door.linkedSpawns)) {
            // 如果有linkedSpawns数组，使用它
            linkedSpawns = door.linkedSpawns;
        } else if (door.linkedSpawn) {
            // 向后兼容：如果只有单个linkedSpawn，转换为数组
            linkedSpawns = [door.linkedSpawn];
        }
        renderLinkedSpawnSelector(linkedSpawns);
    } else {
        // 处理关联的生成点，支持多选
        let linkedSpawns = [];
        if (door.linkedSpawns && Array.isArray(door.linkedSpawns)) {
            // 如果有linkedSpawns数组，使用它
            linkedSpawns = door.linkedSpawns;
        } else if (door.linkedSpawn) {
            // 向后兼容：如果只有单个linkedSpawn，转换为数组
            linkedSpawns = [door.linkedSpawn];
        }
        renderLinkedSpawnSelector(linkedSpawns);
    }

    // 显示模态框
    const editDoorModal = new bootstrap.Modal(document.getElementById('edit-door-modal'));
    editDoorModal.show();
}

// 渲染关联僵尸生成点选择器
function renderLinkedSpawnSelector(selectedSpawns) {
    const container = document.getElementById('edit-linked-spawn-container');
    if (!container) return;

    // 获取当前加载的游戏
    const gameName = document.getElementById('edit-game-name').value;
    if (!gameName) {
        container.innerHTML = '<div class="alert alert-warning">无法加载僵尸生成点：游戏名称未知</div>';
        return;
    }

    // 处理selectedSpawns参数，确保它是数组
    let selectedSpawnsArray = [];
    if (selectedSpawns) {
        if (Array.isArray(selectedSpawns)) {
            selectedSpawnsArray = selectedSpawns;
        } else if (typeof selectedSpawns === 'string') {
            // 如果是单个字符串，转换为数组
            selectedSpawnsArray = [selectedSpawns];
        }
    }

    // 获取僵尸生成点数据
    const zombieSpawns = window.currentGameZombieSpawns || [];

    // 渲染多选框
    container.innerHTML = `
        <label for="linked-spawn-select" class="form-label">关联僵尸生成点（可多选）</label>
        <select class="form-select" id="linked-spawn-select" multiple size="5">
            ${zombieSpawns.map(spawn => `
                <option value="${spawn.name}" ${selectedSpawnsArray.includes(spawn.name) ? 'selected' : ''}>
                    ${spawn.name} (${spawn.type || '未知类型'})
                </option>
            `).join('')}
        </select>
        <div class="form-text">门解锁后，关联的僵尸生成点将自动启用。按住Ctrl键可选择多个生成点。</div>
    `;
}

// 添加门
function addDoor() {
    const gameName = document.getElementById('edit-game-name').value.trim();
    if (!gameName) {
        showToast('错误', '游戏数据无效，请先选择一个游戏', 'danger');
        return;
    }

    const doorName = document.getElementById('door-name').value.trim();
    if (!doorName) {
        showToast('错误', '门名称不能为空', 'danger');
        return;
    }

    const locked = document.getElementById('door-locked').checked;
    const price = parseInt(document.getElementById('door-price').value) || 0;

    // 获取当前所有门数据
    const doors = getCurrentDoorsData();

    // 添加新门
    const newDoor = {
        name: doorName,
        locked: locked,
        price: locked ? price : 0
    };

    // 如果有从调试棒读取的区域，添加到门数据中
    if (window.tempDoorRegion) {
        newDoor.region = window.tempDoorRegion;
        window.tempDoorRegion = null; // 使用后清除
    }

    doors.push(newDoor);

    // 更新全局变量
    window.currentGameDoors = doors;

    // 更新UI
    renderDoorsList(doors);

    // 关闭模态框
    const modal = bootstrap.Modal.getInstance(document.getElementById('add-door-modal'));
    if (modal) modal.hide();

    // 提示用户保存更改
    showToast('提示', '门已添加，请点击"保存游戏设置"按钮保存更改', 'info');
}

// 保存门编辑
function saveDoor() {
    const index = parseInt(document.getElementById('edit-door-index').value);
    if (isNaN(index)) {
        showToast('错误', '门索引无效', 'danger');
        return;
    }

    const doorName = document.getElementById('edit-door-name').value.trim();
    if (!doorName) {
        showToast('错误', '门名称不能为空', 'danger');
        return;
    }

    const locked = document.getElementById('edit-door-locked').checked;
    const price = parseInt(document.getElementById('edit-door-price').value) || 0;

    // 获取关联的僵尸生成点（多选）
    const linkedSpawnSelect = document.getElementById('linked-spawn-select');
    let linkedSpawns = [];

    if (linkedSpawnSelect) {
        // 获取所有选中的选项
        Array.from(linkedSpawnSelect.selectedOptions).forEach(option => {
            if (option.value) {
                linkedSpawns.push(option.value);
            }
        });
    }

    // 获取当前所有门数据
    const doors = getCurrentDoorsData();

    if (!Array.isArray(doors) || index < 0 || index >= doors.length) {
        showToast('错误', '门数据无效或索引超出范围', 'danger');
        return;
    }

    // 保留原有的区域数据
    const originalRegion = doors[index].region;

    // 更新门数据
    doors[index] = {
        name: doorName,
        locked: locked,
        price: locked ? price : 0
    };

    // 保留原有的区域数据或使用最新从调试棒读取的区域
    if (originalRegion) {
        doors[index].region = originalRegion;
    }

    // 添加关联的僵尸生成点（多选）
    if (linkedSpawns && linkedSpawns.length > 0) {
        // 如果只有一个生成点，保持向后兼容性
        if (linkedSpawns.length === 1) {
            doors[index].linkedSpawn = linkedSpawns[0];
        } else {
            // 多个生成点，使用数组
            doors[index].linkedSpawns = linkedSpawns;
            // 同时保留单个linkedSpawn字段以保持向后兼容性
            doors[index].linkedSpawn = linkedSpawns[0];
        }
    }

    // 更新全局变量
    window.currentGameDoors = doors;

    // 更新UI
    renderDoorsList(doors);

    // 关闭模态框
    const modal = bootstrap.Modal.getInstance(document.getElementById('edit-door-modal'));
    if (modal) modal.hide();

    // 提示用户保存更改
    showToast('提示', '门设置已更新，请点击"保存游戏设置"按钮保存更改', 'info');
}

// 删除门
function deleteDoor() {
    if (!confirm('确定要删除这个门吗？')) return;

    const index = parseInt(document.getElementById('edit-door-index').value);
    if (isNaN(index)) {
        showToast('错误', '门索引无效', 'danger');
        return;
    }

    // 获取当前所有门数据
    const doors = getCurrentDoorsData();

    if (!Array.isArray(doors) || index < 0 || index >= doors.length) {
        showToast('错误', '门数据无效或索引超出范围', 'danger');
        return;
    }

    // 删除门
    doors.splice(index, 1);

    // 更新全局变量
    window.currentGameDoors = doors;

    // 更新UI
    renderDoorsList(doors);

    // 关闭模态框
    const modal = bootstrap.Modal.getInstance(document.getElementById('edit-door-modal'));
    if (modal) modal.hide();

    // 提示用户保存更改
    showToast('提示', '门已删除，请记得点击"保存游戏设置"按钮保存所有更改', 'info');
}

// 获取当前所有门数据
function getCurrentDoorsData() {
    // 首先检查全局变量
    if (Array.isArray(window.currentGameDoors)) {
        return window.currentGameDoors;
    }

    // 如果全局变量不存在，尝试从页面中获取
    const gameName = document.getElementById('edit-game-name').value.trim();
    if (!gameName && !window.currentGameDetails) {
        console.warn("无法获取当前游戏门数据：游戏名称为空且没有加载游戏详情");
        return [];
    }

    // 从当前游戏详情中获取门数据
    const gameDetails = window.currentGameDetails || {};
    let doors = [];

    if (!gameDetails.doors) {
        console.warn("游戏详情中没有门数据");
        return [];
    }

    // 处理不同格式的门数据
    if (Array.isArray(gameDetails.doors)) {
        // 已经是数组格式
        doors = gameDetails.doors;
    } else if (typeof gameDetails.doors === 'object' && gameDetails.doors !== null) {
        // 对象格式，需要转换为数组
        doors = Object.keys(gameDetails.doors).map(key => {
            const door = gameDetails.doors[key];
            return {
                name: key,
                locked: door.locked !== undefined ? door.locked : true,
                price: door.price || door.unlockPrice || 500,
                region: door.region || null
            };
        });
    }

    console.log("获取到门数据:", doors);

    // 更新全局变量
    window.currentGameDoors = doors;
    return doors;
}

// 获取当前僵尸数量设置值
function getZombieCountValues() {
    const zombieCount = {};
    const inputs = document.querySelectorAll('.zombie-count-input');

    inputs.forEach(input => {
        const round = input.getAttribute('data-round');
        const value = parseInt(input.value);
        if (!isNaN(value) && value > 0) {
            zombieCount[`round${round}`] = value;
        }
    });

    return zombieCount;
}

// 渲染所有装备槽位
function renderAllEquipmentSlots(initialEquipment) {
    if (!initialEquipment || typeof initialEquipment !== 'object') {
        return '';
    }

    // 获取所有槽位并排序
    const allSlots = Object.keys(initialEquipment).sort((a, b) => parseInt(a) - parseInt(b));

    // 特殊槽位的标签
    const slotLabels = {
        '1': '槽位1 (铁剑槽位)',
        '2': '槽位2 (枪支槽位)',
        '3': '槽位3',
        '4': '槽位4',
        '7': '槽位7 (物品槽位)',
        '8': '槽位8 (物品槽位)',
        '9': '槽位9 (物品槽位)'
    };

    // 如果没有槽位，显示提示信息
    if (allSlots.length === 0) {
        return `
            <div class="alert alert-info">尚未设置初始装备，请点击"添加槽位"按钮添加装备</div>
            <div class="equipment-batch-actions mt-3">
                <button type="button" class="btn btn-outline-primary btn-sm me-2" id="add-row-slots-btn">
                    <i class="bi bi-list-ul me-1"></i>添加一行
                </button>
                <button type="button" class="btn btn-outline-primary btn-sm me-2" id="add-column-slots-btn">
                    <i class="bi bi-list me-1"></i>添加一列
                </button>
                <button type="button" class="btn btn-outline-secondary btn-sm" id="add-default-slots-btn">
                    <i class="bi bi-lightning me-1"></i>添加默认配置
                </button>
            </div>
        `;
    }

    // 将槽位分组
    const hotbarSlots = allSlots.filter(slot => parseInt(slot) >= 1 && parseInt(slot) <= 9);
    const inventorySlots = allSlots.filter(slot => parseInt(slot) > 9);

    // 计算已配置的槽位数量
    const totalSlots = allSlots.length;

    // 创建折叠式菜单
    let html = `
        <div class="equipment-summary card mb-3">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="mb-0">已配置 ${totalSlots} 个槽位</h6>
                        <p class="text-muted small mb-0">快捷栏: ${hotbarSlots.length}个, 背包栏: ${inventorySlots.length}个</p>
                    </div>
                    <button class="btn btn-sm btn-outline-primary" type="button" data-bs-toggle="collapse" data-bs-target="#equipmentDetailsList" aria-expanded="false" aria-controls="equipmentDetailsList">
                        <i class="bi bi-list me-1"></i>查看详情
                    </button>
                </div>
            </div>
        </div>

        <div class="equipment-batch-actions mb-3">
            <button type="button" class="btn btn-outline-primary btn-sm me-2" id="add-row-slots-btn">
                <i class="bi bi-list-ul me-1"></i>添加一行
            </button>
            <button type="button" class="btn btn-outline-primary btn-sm me-2" id="add-column-slots-btn">
                <i class="bi bi-list me-1"></i>添加一列
            </button>
            <button type="button" class="btn btn-outline-secondary btn-sm" id="clear-all-slots-btn">
                <i class="bi bi-trash me-1"></i>清空所有槽位
            </button>
        </div>

        <div class="collapse" id="equipmentDetailsList">
            <div class="card card-body">
                <div class="equipment-slots-list">
    `;

    // 遍历所有槽位
    allSlots.forEach(slot => {
        const itemId = initialEquipment[slot];
        const slotLabel = slotLabels[slot] || `槽位${slot}`;

        // 物品图标在getItemIcon函数中处理

        // 创建列表项
        html += `
            <div class="equipment-slot-item card mb-2" data-slot="${slot}">
                <div class="card-body p-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="mb-0">${slotLabel}</h6>
                            <div class="d-flex align-items-center mt-2">
                                <div class="item-icon me-2">
                                    ${getItemIcon(itemId)}
                                </div>
                                <select class="form-select equipment-slot-select" id="initial-slot${slot}" data-slot="${slot}">
                                    <option value="">-- 选择物品 --</option>
                                    ${generateItemOptions(itemId)}
                                </select>
                            </div>
                        </div>
                        <button type="button" class="btn btn-danger btn-sm remove-slot-btn" data-slot="${slot}">
                            <i class="bi bi-trash"></i>
                        </button>
                    </div>
                </div>
            </div>
        `;
    });

    html += `
                </div>
            </div>
        </div>
    `;

    return html;
}

// 获取物品图标
function getItemIcon(itemId) {
    if (!itemId) return '<i class="bi bi-question-circle text-muted"></i>';

    // 根据物品ID返回对应的图标
    const iconMap = {
        'id1': '<i class="bi bi-shield-fill text-primary"></i>', // 铁剑
        'id2': '<i class="bi bi-bullseye text-danger"></i>', // 手枪
        'id3': '<i class="bi bi-lightning-fill text-warning"></i>', // 霰弹枪
        'white_dye': '<i class="bi bi-square-fill text-light"></i>', // 白色染料
        // 可以添加更多物品的图标
    };

    return iconMap[itemId] || '<i class="bi bi-box text-secondary"></i>';
}

// 获取所有物品选项
function getAllItemOptions() {
    // 武器选项
    const weaponOptions = [
        { id: 'id1', name: '铁剑' },
        { id: 'id2', name: '手枪' },
        { id: 'id3', name: '霰弹枪' },
        { id: 'id4', name: '重型机枪' },
        { id: 'id5', name: '火箭筒' },
        { id: 'id6', name: '电击枪' },
        { id: 'id7', name: '狙击步枪' },
        { id: 'id8', name: '冷冻枪' },
        { id: 'id9', name: '雷击枪' },
        { id: 'id10', name: '压强枪' },
        { id: 'id11', name: '手枪' },
        { id: 'id13', name: '等离子枪' },
        { id: 'id14', name: '死神收割者' },
        { id: 'id15', name: '毁灭者' },
        { id: 'id16', name: '超级激光炮' },
        { id: 'id17', name: '黑洞吞噬者' },
        { id: 'id18', name: '音波步枪' }
    ];

    // 弹药选项
    const ammoOptions = [
        { id: 'id3', name: '手枪弹药' },
        { id: 'id4', name: '步枪弹药' },
        { id: 'id5', name: '霰弹枪弹药' },
        { id: 'id6', name: '重型机枪弹药' }
    ];

    // 物品选项
    const itemOptions = [
        { id: 'id7', name: '医疗包' },
        { id: 'id8', name: '弹药包' },
        { id: 'id9', name: '手雷' },
        { id: 'white_dye', name: '白色染料(占位符)' }
    ];

    // 合并所有选项
    return [...weaponOptions, ...ammoOptions, ...itemOptions];
}

// 渲染动态装备槽位 (保留此函数以兼容旧代码)
function renderDynamicEquipmentSlots(initialEquipment) {
    // 现在直接调用renderAllEquipmentSlots函数
    return renderAllEquipmentSlots(initialEquipment);
}

// 生成物品选项
function generateItemOptions(selectedItemId) {
    // 武器选项
    const weaponOptions = [
        { id: 'id1', name: '铁剑' },
        { id: 'id2', name: '手枪' },
        { id: 'id3', name: '霰弹枪' },
        { id: 'id4', name: '重型机枪' },
        { id: 'id5', name: '火箭筒' },
        { id: 'id6', name: '电击枪' },
        { id: 'id7', name: '狙击步枪' },
        { id: 'id8', name: '冷冻枪' },
        { id: 'id9', name: '雷击枪' },
        { id: 'id10', name: '压强枪' },
        { id: 'id11', name: '手枪' },
        { id: 'id13', name: '等离子枪' },
        { id: 'id14', name: '死神收割者' },
        { id: 'id15', name: '毁灭者' },
        { id: 'id16', name: '超级激光炮' },
        { id: 'id17', name: '黑洞吞噬者' },
        { id: 'id18', name: '音波步枪' }
    ];

    // 弹药选项
    const ammoOptions = [
        { id: 'id3', name: '手枪弹药' },
        { id: 'id4', name: '步枪弹药' },
        { id: 'id5', name: '霰弹枪弹药' },
        { id: 'id6', name: '重型机枪弹药' },
        { id: 'id7', name: '火箭弹' },
        { id: 'id8', name: '电击枪弹药' },
        { id: 'id9', name: '狙击步枪弹药' },
        { id: 'id10', name: '冷冻枪弹药' },
        { id: 'id11', name: '雷击枪弹药' },
        { id: 'id12', name: '压强枪弹药' }
    ];

    // 特殊物品选项
    const specialOptions = [
        { id: 'white_dye', name: '白色染料（占位符）' }
    ];

    // 合并所有选项
    const allOptions = [...weaponOptions, ...ammoOptions, ...specialOptions];

    // 生成HTML
    return allOptions.map(option =>
        `<option value="${option.id}" ${selectedItemId === option.id ? 'selected' : ''}>${option.name}</option>`
    ).join('');
}

// 添加装备槽位
function addEquipmentSlot() {
    // 创建添加槽位的模态框
    const modalHtml = `
    <div class="modal fade" id="add-slot-modal" tabindex="-1" aria-labelledby="add-slot-modal-label" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="add-slot-modal-label">添加装备槽位</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="slot-type" class="form-label">槽位类型</label>
                        <select class="form-select" id="slot-type">
                            <option value="weapon">武器槽位</option>
                            <option value="ammo">弹药槽位</option>
                            <option value="item">物品槽位</option>
                            <option value="custom">自定义槽位</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="slot-position" class="form-label">物品栏位置 (1-36)</label>
                        <input type="number" class="form-control" id="slot-position" min="1" max="36" value="1">
                        <div class="form-text">
                            推荐位置：<br>
                            1-9: 快捷栏 (1=左侧第一格)<br>
                            10-36: 背包栏 (10=背包第一行第一格)
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="slot-item" class="form-label">默认物品</label>
                        <div class="d-flex align-items-center">
                            <div class="item-icon me-2" id="selected-item-icon">
                                <i class="bi bi-question-circle text-muted"></i>
                            </div>
                            <select class="form-select" id="slot-item">
                                <option value="">-- 选择物品 --</option>
                                ${generateItemOptions()}
                            </select>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="confirm-add-slot">添加</button>
                </div>
            </div>
        </div>
    </div>
    `;

    // 如果模态框已存在，先移除
    const existingModal = document.getElementById('add-slot-modal');
    if (existingModal) {
        existingModal.remove();
    }

    // 添加模态框到DOM
    document.body.insertAdjacentHTML('beforeend', modalHtml);

    // 初始化模态框
    const modal = new bootstrap.Modal(document.getElementById('add-slot-modal'));
    modal.show();

    // 更新物品图标
    document.getElementById('slot-item').addEventListener('change', function() {
        const itemId = this.value;
        document.getElementById('selected-item-icon').innerHTML = getItemIcon(itemId);
    });

    // 绑定确认按钮事件
    document.getElementById('confirm-add-slot').addEventListener('click', function() {
        const slotType = document.getElementById('slot-type').value;
        const slotPosition = parseInt(document.getElementById('slot-position').value);
        const slotItem = document.getElementById('slot-item').value;

        // 验证输入
        if (isNaN(slotPosition) || slotPosition < 1 || slotPosition > 36) {
            showToast('错误', '槽位位置必须在1-36之间', 'danger');
            return;
        }

        // 检查槽位是否已存在
        const existingSlots = [];
        document.querySelectorAll('[id^="initial-slot"]').forEach(select => {
            const slotMatch = select.id.match(/initial-slot(\d+)/);
            if (slotMatch) {
                existingSlots.push(parseInt(slotMatch[1]));
            }
        });

        if (existingSlots.includes(slotPosition)) {
            showToast('错误', `槽位 ${slotPosition} 已存在`, 'danger');
            return;
        }

        // 获取当前游戏详情
        if (!window.currentGameDetails) {
            window.currentGameDetails = {};
        }

        if (!window.currentGameDetails.initialEquipment) {
            window.currentGameDetails.initialEquipment = {};
        }

        // 添加新槽位到初始装备数据
        if (slotItem) {
            window.currentGameDetails.initialEquipment[slotPosition.toString()] = slotItem;
        }

        // 重新渲染所有槽位
        const container = document.getElementById('equipment-slots-container');
        container.innerHTML = renderAllEquipmentSlots(window.currentGameDetails.initialEquipment);

        // 重新绑定删除按钮事件
        document.querySelectorAll('.remove-slot-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const slot = this.getAttribute('data-slot');
                if (slot) {
                    removeEquipmentSlot(slot);
                }
            });
        });

        // 重新绑定装备选择事件
        document.querySelectorAll('.equipment-slot-select').forEach(select => {
            select.addEventListener('change', function() {
                const slot = this.getAttribute('data-slot');
                const value = this.value;

                // 更新游戏详情中的装备数据
                if (window.currentGameDetails && window.currentGameDetails.initialEquipment) {
                    if (value) {
                        window.currentGameDetails.initialEquipment[slot] = value;
                    } else {
                        delete window.currentGameDetails.initialEquipment[slot];
                    }
                }

                // 显示保存按钮
                showFloatingSaveButton();
            });
        });

        // 关闭模态框
        modal.hide();

        // 根据槽位类型设置标签
        let slotLabel = `槽位${slotPosition}`;
        switch (slotType) {
            case 'weapon':
                slotLabel = `武器槽位 (${slotPosition})`;
                break;
            case 'ammo':
                slotLabel = `弹药槽位 (${slotPosition})`;
                break;
            case 'item':
                slotLabel = `物品槽位 (${slotPosition})`;
                break;
            case 'custom':
                slotLabel = `自定义槽位 (${slotPosition})`;
                break;
        }

        showToast('成功', `已添加${slotLabel}`, 'success');

        // 显示保存按钮
        showFloatingSaveButton();
    });
}

// 删除装备槽位
function removeEquipmentSlot(slot) {
    const slotItem = document.querySelector(`.equipment-slot-item[data-slot="${slot}"]`);
    if (!slotItem) return;

    // 从游戏详情中删除该槽位
    if (window.currentGameDetails && window.currentGameDetails.initialEquipment) {
        delete window.currentGameDetails.initialEquipment[slot];

        // 重新渲染所有槽位
        const container = document.getElementById('equipment-slots-container');
        container.innerHTML = renderAllEquipmentSlots(window.currentGameDetails.initialEquipment);

        // 重新绑定删除按钮事件
        document.querySelectorAll('.remove-slot-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const slotToRemove = this.getAttribute('data-slot');
                if (slotToRemove) {
                    removeEquipmentSlot(slotToRemove);
                }
            });
        });

        // 重新绑定装备选择事件
        document.querySelectorAll('.equipment-slot-select').forEach(select => {
            select.addEventListener('change', function() {
                const slotId = this.getAttribute('data-slot');
                const value = this.value;

                // 更新游戏详情中的装备数据
                if (window.currentGameDetails && window.currentGameDetails.initialEquipment) {
                    if (value) {
                        window.currentGameDetails.initialEquipment[slotId] = value;
                    } else {
                        delete window.currentGameDetails.initialEquipment[slotId];
                    }
                }

                // 显示保存按钮
                showFloatingSaveButton();
            });
        });

        // 重新绑定批量操作按钮事件
        bindBatchActionButtons();
    } else {
        // 如果没有游戏详情，只移除DOM元素
        slotItem.remove();
    }

    showToast('提示', `已移除槽位 ${slot}`, 'info');

    // 显示保存按钮
    showFloatingSaveButton();
}

// 绑定批量操作按钮事件
function bindBatchActionButtons() {
    // 绑定批量操作按钮事件
    const addRowBtn = document.getElementById('add-row-slots-btn');
    if (addRowBtn) {
        addRowBtn.addEventListener('click', () => {
            addRowSlots();
        });
    }

    const addColumnBtn = document.getElementById('add-column-slots-btn');
    if (addColumnBtn) {
        addColumnBtn.addEventListener('click', () => {
            addColumnSlots();
        });
    }

    const addDefaultBtn = document.getElementById('add-default-slots-btn');
    if (addDefaultBtn) {
        addDefaultBtn.addEventListener('click', () => {
            addDefaultSlots();
        });
    }

    const clearAllBtn = document.getElementById('clear-all-slots-btn');
    if (clearAllBtn) {
        clearAllBtn.addEventListener('click', () => {
            if (confirm('确定要清空所有槽位吗？此操作不可恢复！')) {
                clearAllSlots();
            }
        });
    }
}

// 添加快捷栏槽位(1-9)
function addHotbarSlots() {
    if (!window.currentGameDetails) {
        window.currentGameDetails = {};
    }

    if (!window.currentGameDetails.initialEquipment) {
        window.currentGameDetails.initialEquipment = {};
    }

    // 默认物品配置
    const defaultItems = {
        '1': 'id70', // 铁剑
        '2': 'id1',  // 手枪
        '7': 'id38', // 物品槽1
        '8': 'id38', // 物品槽2
        '9': 'id38'  // 物品槽3
    };

    // 添加快捷栏槽位
    for (let i = 1; i <= 9; i++) {
        // 如果槽位已存在，跳过
        if (window.currentGameDetails.initialEquipment[i]) {
            continue;
        }

        // 设置默认物品或空物品
        if (defaultItems[i]) {
            window.currentGameDetails.initialEquipment[i] = defaultItems[i];
        } else {
            window.currentGameDetails.initialEquipment[i] = 'LIGHT_GRAY_DYE'; // 默认空物品
        }
    }

    // 重新渲染所有槽位
    const container = document.getElementById('equipment-slots-container');
    container.innerHTML = renderAllEquipmentSlots(window.currentGameDetails.initialEquipment);

    // 重新绑定事件
    rebindEquipmentEvents();

    showToast('成功', '已添加快捷栏槽位(1-9)', 'success');

    // 显示保存按钮
    showFloatingSaveButton();
}

// 添加一行槽位(9个)
function addRowSlots() {
    // 显示批量添加行模态框
    showBatchAddRowModal();
}

// 显示批量添加行模态框
function showBatchAddRowModal() {
    // 检查模态框是否已存在
    let modal = document.getElementById('batch-add-row-modal');

    // 如果不存在，创建模态框
    if (!modal) {
        const modalHTML = `
            <div class="modal fade" id="batch-add-row-modal" tabindex="-1" aria-labelledby="batchAddRowModalLabel" aria-hidden="true">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="batchAddRowModalLabel">批量添加一行槽位</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <div class="mb-3">
                                <label for="batch-row-item" class="form-label">选择物品</label>
                                <select class="form-select" id="batch-row-item">
                                    <option value="LIGHT_GRAY_DYE">-- 空物品 --</option>
                                    ${generateItemOptions()}
                                </select>
                            </div>
                            <div class="mb-3">
                                <label for="batch-row-number" class="form-label">选择行号</label>
                                <select class="form-select" id="batch-row-number">
                                    <option value="1">第1行 (槽位1-9)</option>
                                    <option value="2">第2行 (槽位10-18)</option>
                                    <option value="3">第3行 (槽位19-27)</option>
                                    <option value="4">第4行 (槽位28-36)</option>
                                </select>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                            <button type="button" class="btn btn-primary" id="confirm-add-row">添加行</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // 添加到文档中
        document.body.insertAdjacentHTML('beforeend', modalHTML);
        modal = document.getElementById('batch-add-row-modal');

        // 绑定确认按钮事件
        document.getElementById('confirm-add-row').addEventListener('click', function() {
            applyAddRowSlots();
        });
    }

    // 显示模态框
    const bsModal = new bootstrap.Modal(modal);
    bsModal.show();
}

// 应用添加一行槽位
function applyAddRowSlots() {
    if (!window.currentGameDetails) {
        window.currentGameDetails = {};
    }

    if (!window.currentGameDetails.initialEquipment) {
        window.currentGameDetails.initialEquipment = {};
    }

    // 获取选择的物品和行号
    const itemId = document.getElementById('batch-row-item').value;
    const rowNumber = parseInt(document.getElementById('batch-row-number').value);

    // 计算起始槽位
    let startSlot = (rowNumber - 1) * 9 + 1;

    // 添加一行槽位
    for (let i = 0; i < 9; i++) {
        const slot = startSlot + i;
        // 设置物品
        window.currentGameDetails.initialEquipment[slot] = itemId;
    }

    // 重新渲染所有槽位
    const container = document.getElementById('equipment-slots-container');
    container.innerHTML = renderAllEquipmentSlots(window.currentGameDetails.initialEquipment);

    // 重新绑定事件
    rebindEquipmentEvents();

    // 关闭模态框
    const modal = bootstrap.Modal.getInstance(document.getElementById('batch-add-row-modal'));
    if (modal) {
        modal.hide();
    }

    showToast('成功', `已添加第${rowNumber}行槽位(${startSlot}-${startSlot+8})`, 'success');

    // 显示保存按钮
    showFloatingSaveButton();
}

// 添加一列槽位
function addColumnSlots() {
    // 显示批量添加列模态框
    showBatchAddColumnModal();
}

// 显示批量添加列模态框
function showBatchAddColumnModal() {
    // 检查模态框是否已存在
    let modal = document.getElementById('batch-add-column-modal');

    // 如果不存在，创建模态框
    if (!modal) {
        const modalHTML = `
            <div class="modal fade" id="batch-add-column-modal" tabindex="-1" aria-labelledby="batchAddColumnModalLabel" aria-hidden="true">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="batchAddColumnModalLabel">批量添加一列槽位</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <div class="mb-3">
                                <label for="batch-column-item" class="form-label">选择物品</label>
                                <select class="form-select" id="batch-column-item">
                                    <option value="LIGHT_GRAY_DYE">-- 空物品 --</option>
                                    ${generateItemOptions()}
                                </select>
                            </div>
                            <div class="mb-3">
                                <label for="batch-column-number" class="form-label">选择列号</label>
                                <select class="form-select" id="batch-column-number">
                                    <option value="1">第1列 (槽位1,10,19,28)</option>
                                    <option value="2">第2列 (槽位2,11,20,29)</option>
                                    <option value="3">第3列 (槽位3,12,21,30)</option>
                                    <option value="4">第4列 (槽位4,13,22,31)</option>
                                    <option value="5">第5列 (槽位5,14,23,32)</option>
                                    <option value="6">第6列 (槽位6,15,24,33)</option>
                                    <option value="7">第7列 (槽位7,16,25,34)</option>
                                    <option value="8">第8列 (槽位8,17,26,35)</option>
                                    <option value="9">第9列 (槽位9,18,27,36)</option>
                                </select>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                            <button type="button" class="btn btn-primary" id="confirm-add-column">添加列</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // 添加到文档中
        document.body.insertAdjacentHTML('beforeend', modalHTML);
        modal = document.getElementById('batch-add-column-modal');

        // 绑定确认按钮事件
        document.getElementById('confirm-add-column').addEventListener('click', function() {
            applyAddColumnSlots();
        });
    }

    // 显示模态框
    const bsModal = new bootstrap.Modal(modal);
    bsModal.show();
}

// 应用添加一列槽位
function applyAddColumnSlots() {
    if (!window.currentGameDetails) {
        window.currentGameDetails = {};
    }

    if (!window.currentGameDetails.initialEquipment) {
        window.currentGameDetails.initialEquipment = {};
    }

    // 获取选择的物品和列号
    const itemId = document.getElementById('batch-column-item').value;
    const columnNumber = parseInt(document.getElementById('batch-column-number').value);

    // 添加一列槽位
    for (let row = 0; row < 4; row++) {
        const slot = row * 9 + columnNumber;
        // 设置物品
        window.currentGameDetails.initialEquipment[slot] = itemId;
    }

    // 重新渲染所有槽位
    const container = document.getElementById('equipment-slots-container');
    container.innerHTML = renderAllEquipmentSlots(window.currentGameDetails.initialEquipment);

    // 重新绑定事件
    rebindEquipmentEvents();

    // 关闭模态框
    const modal = bootstrap.Modal.getInstance(document.getElementById('batch-add-column-modal'));
    if (modal) {
        modal.hide();
    }

    showToast('成功', `已添加第${columnNumber}列槽位`, 'success');

    // 显示保存按钮
    showFloatingSaveButton();
}

// 添加默认配置
function addDefaultSlots() {
    if (!window.currentGameDetails) {
        window.currentGameDetails = {};
    }

    if (!window.currentGameDetails.initialEquipment) {
        window.currentGameDetails.initialEquipment = {};
    }

    // 默认物品配置
    const defaultConfig = {
        '1': 'id70', // 铁剑
        '2': 'id1',  // 手枪
        '3': 'LIGHT_GRAY_DYE',
        '4': 'LIGHT_GRAY_DYE',
        '5': 'LIGHT_GRAY_DYE',
        '6': 'LIGHT_GRAY_DYE',
        '7': 'id38', // 物品槽1
        '8': 'id38', // 物品槽2
        '9': 'id38'  // 物品槽3
    };

    // 添加默认配置
    for (const slot in defaultConfig) {
        window.currentGameDetails.initialEquipment[slot] = defaultConfig[slot];
    }

    // 重新渲染所有槽位
    const container = document.getElementById('equipment-slots-container');
    container.innerHTML = renderAllEquipmentSlots(window.currentGameDetails.initialEquipment);

    // 重新绑定事件
    rebindEquipmentEvents();

    showToast('成功', '已添加默认配置', 'success');

    // 显示保存按钮
    showFloatingSaveButton();
}

// 清空所有槽位
function clearAllSlots() {
    if (!window.currentGameDetails) {
        return;
    }

    // 清空初始装备
    window.currentGameDetails.initialEquipment = {};

    // 重新渲染所有槽位
    const container = document.getElementById('equipment-slots-container');
    container.innerHTML = renderAllEquipmentSlots(window.currentGameDetails.initialEquipment);

    // 重新绑定事件
    rebindEquipmentEvents();

    showToast('提示', '已清空所有槽位', 'info');

    // 显示保存按钮
    showFloatingSaveButton();
}

// 重新绑定装备相关事件
function rebindEquipmentEvents() {
    // 重新绑定删除按钮事件
    document.querySelectorAll('.remove-slot-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const slotToRemove = this.getAttribute('data-slot');
            if (slotToRemove) {
                removeEquipmentSlot(slotToRemove);
            }
        });
    });

    // 重新绑定装备选择事件
    document.querySelectorAll('.equipment-slot-select').forEach(select => {
        select.addEventListener('change', function() {
            const slotId = this.getAttribute('data-slot');
            const value = this.value;

            // 更新游戏详情中的装备数据
            if (window.currentGameDetails && window.currentGameDetails.initialEquipment) {
                if (value) {
                    window.currentGameDetails.initialEquipment[slotId] = value;
                } else {
                    delete window.currentGameDetails.initialEquipment[slotId];
                }
            }

            // 显示保存按钮
            showFloatingSaveButton();
        });
    });

    // 重新绑定批量操作按钮事件
    bindBatchActionButtons();
}

// 创建游戏
function createGame() {
    const gameName = document.getElementById('game-name').value.trim();
    if (!gameName) {
        showToast('错误', '游戏名称不能为空', 'danger');
        return;
    }

    // 显示加载动画
    const confirmBtn = document.getElementById('confirm-create-game');
    const restore = showAnimatedLoading(confirmBtn, '创建中...');

    fetchWithAuth('/api/games/create', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({ name: gameName })
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP error ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        restore();

        if (data.success) {
            // 确保正确获取模态框元素并隐藏
            try {
                const modalElement = document.getElementById('create-game-modal');
                const modal = bootstrap.Modal.getInstance(modalElement);

                if (modal) {
                    modal.hide();
                    console.log('成功关闭创建游戏模态框');
                } else {
                    console.warn('无法获取模态框实例，尝试创建新实例并关闭');
                    new bootstrap.Modal(modalElement).hide();
                }
            } catch (e) {
                console.error('关闭模态框时出错:', e);
            }

            // 清空输入框
            document.getElementById('game-name').value = '';

            // 显示成功提示
            showToast('成功', data.message || `游戏"${gameName}"创建成功！`, 'success');

            // 重新加载游戏列表
            loadGameList();

            // 等待游戏列表加载完成后，自动选择新创建的游戏
            setTimeout(() => {
                const editButtons = document.querySelectorAll('.edit-game-btn');
                for (const button of editButtons) {
                    if (button.getAttribute('data-game') === gameName) {
                        button.click();
                        break;
                    }
                }
            }, 1000);
        } else {
            showToast('错误', data.message || '创建游戏失败', 'danger');
        }
    })
    .catch(error => {
        restore();
        console.error('创建游戏失败:', error);
        showToast('错误', '创建游戏请求失败: ' + error.message, 'danger');
    });
}

// 保存游戏配置
function saveGameConfig() {
    // 获取游戏名称
    const gameName = document.getElementById('edit-game-name').value;
    if (!gameName) {
        showToast('错误', '游戏名称不能为空', 'danger');
        return;
    }

    // 显示加载动画
    const saveBtn = document.getElementById('save-game-btn');
    saveBtn.disabled = true;
    saveBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 保存中...';

    try {
        // 收集表单数据
        const maxPlayers = parseInt(document.getElementById('max-players').value) || 10;
        const rounds = parseInt(document.getElementById('game-rounds').value) || 5;

        // 获取出生点数据
        const spawnWorld = document.getElementById('spawn-world').value;
        const spawnX = parseFloat(document.getElementById('spawn-x').value) || 0;
        const spawnY = parseFloat(document.getElementById('spawn-y').value) || 0;
        const spawnZ = parseFloat(document.getElementById('spawn-z').value) || 0;
        const spawnYaw = parseFloat(document.getElementById('spawn-yaw').value) || 0;
        const spawnPitch = parseFloat(document.getElementById('spawn-pitch').value) || 0;

        // 获取僵尸数量数据
        const zombieCount = getZombieCountValues();

        // 获取门数据
        const doors = getCurrentDoorsData();

        // 获取回合模式数据
        const roundModes = getCurrentRoundModesData();

        // 获取初始装备数据
        const initialEquipment = {};

        // 获取所有槽位选择器
        const slotSelectors = document.querySelectorAll('[id^="initial-slot"]');

        // 遍历所有槽位选择器
        slotSelectors.forEach(selector => {
            const slotMatch = selector.id.match(/initial-slot(\d+)/);
            if (slotMatch) {
                const slot = slotMatch[1];
                const value = selector.value;

                // 只保存有选择物品的槽位
                if (value) {
                    initialEquipment[slot] = value;
                }
            }
        });

        // 获取窗户数据并确保每个窗户都有名称
        const windows = getCurrentWindowsData().map((window, index) => {
            // 如果窗户没有名称，生成一个默认名称
            if (!window.name) {
                window.name = window.name || `窗户${index + 1}`;
            }
            return window;
        });

        // 获取全局电源按钮数据
        const powerButtonEnabled = document.getElementById('power-button-enabled').checked;
        const powerButtonPrice = parseInt(document.getElementById('power-button-price').value) || 1000;
        const powerButtonWorld = document.getElementById('power-button-world').value;
        const powerButtonX = parseFloat(document.getElementById('power-button-x').value) || 0;
        const powerButtonY = parseFloat(document.getElementById('power-button-y').value) || 0;
        const powerButtonZ = parseFloat(document.getElementById('power-button-z').value) || 0;

        // 构建保存数据
        const saveData = {
            name: gameName,
            maxPlayers: maxPlayers,
            rounds: rounds,
            zombieCount: zombieCount,
            doors: doors,
            roundModes: roundModes,
            initialEquipment: initialEquipment,
            windows: windows
        };

        // 仅当出生点世界名不为空时添加出生点数据
        if (spawnWorld && spawnWorld.trim() !== '') {
            saveData.spawn = {
                world: spawnWorld,
                x: spawnX,
                y: spawnY,
                z: spawnZ,
                yaw: spawnYaw,
                pitch: spawnPitch
            };
        }

        // 添加全局电源按钮数据
        if (powerButtonEnabled) {
            saveData.powerButton = {
                enabled: true,
                price: powerButtonPrice,
                world: powerButtonWorld,
                x: powerButtonX,
                y: powerButtonY,
                z: powerButtonZ
            };
        } else {
            saveData.powerButton = {
                enabled: false
            };
        }

        console.log('保存的游戏数据:', saveData);

        // 发送保存请求
        fetchWithAuth('/api/save', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(saveData)
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`服务器响应错误: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            if (data.success) {
                // 重新加载游戏列表以更新状态
                loadGameList();

                // 显示成功消息
                showToast('成功', '游戏配置保存成功', 'success');
            } else {
                showToast('错误', data.error || '保存失败，请检查错误日志', 'danger');
            }
        })
        .catch(error => {
            console.error('保存游戏配置失败:', error);
            showToast('错误', '保存请求失败: ' + error.message, 'danger');
        })
        .finally(() => {
            // 恢复按钮状态
            saveBtn.disabled = false;
            saveBtn.innerHTML = '<i class="bi bi-save me-2"></i>保存游戏设置';
        });
    } catch (error) {
        console.error('准备保存数据时出错:', error);
        showToast('错误', '准备保存数据时出错: ' + error.message, 'danger');

        // 恢复按钮状态
        saveBtn.disabled = false;
        saveBtn.innerHTML = '<i class="bi bi-save me-2"></i>保存游戏设置';
    }
}

// 显示浮动保存按钮
function showFloatingSaveButton() {
    // 获取保存按钮
    const saveBtn = document.getElementById('save-game-btn');
    if (saveBtn) {
        // 高亮保存按钮
        saveBtn.classList.remove('btn-secondary');
        saveBtn.classList.add('btn-warning');
        saveBtn.innerHTML = '<i class="bi bi-save me-2"></i>保存更改';

        // 添加闪烁效果
        saveBtn.classList.add('btn-pulse');

        // 3秒后移除闪烁效果
        setTimeout(() => {
            saveBtn.classList.remove('btn-pulse');
        }, 3000);
    }
}

// 显示Toast通知
function showToast(title, message, type = 'info') {
    // 检查是否已存在Toast容器
    let toastContainer = document.querySelector('.toast-container');
    if (!toastContainer) {
        toastContainer = document.createElement('div');
        toastContainer.className = 'toast-container position-fixed bottom-0 end-0 p-3';
        document.body.appendChild(toastContainer);
    }

    // 创建唯一ID
    const id = 'toast-' + Date.now();

    // 构建Toast HTML
    const html = `
        <div id="${id}" class="toast" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="toast-header ${type === 'info' ? 'bg-info' : type === 'success' ? 'bg-success' : 'bg-danger'}">
                <strong class="me-auto text-white">${title}</strong>
                <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
            <div class="toast-body">
                ${message}
            </div>
        </div>
    `;

    // 添加到容器
    toastContainer.innerHTML += html;

    // 初始化Toast
    const toastEl = document.getElementById(id);
    const toast = new bootstrap.Toast(toastEl, { delay: 5000 });

    // 显示Toast
    toast.show();

    // 自动删除
    toastEl.addEventListener('hidden.bs.toast', function () {
        toastEl.remove();
    });
}

// 显示加载动画
function showAnimatedLoading(elementId, text = '加载中...') {
    const element = document.getElementById(elementId);
    if (!element) {
        console.warn('未找到元素:', elementId);
        return null;
    }

    // 保存原始状态
    const originalHTML = element.innerHTML;
    const isButton = element.tagName === 'BUTTON';

    if (isButton) {
        // 按钮加载动画
        element.disabled = true;
        element.innerHTML = `<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> ${text}`;
    } else {
        // 容器加载动画
        element.innerHTML = `
            <div class="text-center py-3">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">${text}</span>
                </div>
                <p class="mt-2 text-muted">${text}</p>
            </div>
        `;
    }

    // 返回恢复函数
    return function restoreElement() {
        if (isButton) {
            element.disabled = false;
        }
        element.innerHTML = originalHTML;
    };
}

// 从调试棒读取区域
function readRegionFromDebugStick() {
    const gameName = document.getElementById('edit-game-name').value.trim();
    if (!gameName) {
        showToast('错误', '游戏数据无效，请先选择一个游戏', 'danger');
        return;
    }

    // 显示加载状态
    const button = document.querySelector('.read-region-btn');
    const restore = showAnimatedLoading(button, '读取中...');

    // 调用API获取区域数据
    fetchWithAuth(`/api/games/read-region?game=${encodeURIComponent(gameName)}`)
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            restore();

            if (data.success) {
                // 保存区域数据到临时变量
                window.tempDoorRegion = data.region;

                // 显示区域数据
                const regionInfoElement = document.querySelector('.region-info');
                if (regionInfoElement) {
                    regionInfoElement.innerHTML = `<div class="alert alert-success">
                        <strong>已读取区域：</strong><br>
                        世界：${data.region.world}<br>
                        第一点：(${data.region.x1}, ${data.region.y1}, ${data.region.z1})<br>
                        第二点：(${data.region.x2}, ${data.region.y2}, ${data.region.z2})
                    </div>`;
                    regionInfoElement.classList.remove('d-none');
                }

                showToast('成功', '已成功读取区域数据', 'success');
            } else {
                showToast('错误', data.message || '读取区域数据失败', 'danger');
            }
        })
        .catch(error => {
            restore();
            console.error('读取区域数据失败:', error);
            showToast('错误', '读取区域数据失败：' + error.message, 'danger');
        });
}

// 初始化读取区域按钮事件
function initRegionReadButtons() {
    // 为添加门模态框中的读取区域按钮添加事件
    document.querySelectorAll('.read-region-btn').forEach(button => {
        button.addEventListener('click', function() {
            readRegionFromDebugStick();
        });
    });
}

// 渲染僵尸生成点列表
function renderZombieSpawnsList(spawns = []) {
    const container = document.getElementById('zombie-spawns-list');
    if (!container) {
        console.error('僵尸生成点列表容器不存在');
        return;
    }

    console.log('收到僵尸生成点数据进行渲染:', spawns);
    container.innerHTML = '';

    // 如果spawns不是数组，尝试转换
    let spawnsArray = spawns;
    if (!Array.isArray(spawns)) {
        if (typeof spawns === 'object' && spawns !== null) {
            // 可能是{key: value}格式，将其转换为数组
            spawnsArray = Object.keys(spawns).map(key => {
                const spawn = spawns[key];
                return {
                    name: key,
                    type: spawn.type || 'id',
                    world: spawn.world || 'world',
                    x: spawn.x || 0,
                    y: spawn.y || 0,
                    z: spawn.z || 0,
                    enabled: spawn.enabled || false
                };
            });
            console.log('转换后的生成点数据:', spawnsArray);
        } else {
            spawnsArray = [];
        }
    }

    if (spawnsArray.length === 0) {
        container.innerHTML = '<div class="text-center p-3 text-muted"><i class="bi bi-bug me-2"></i>暂无僵尸生成点设置，请在游戏中使用/dzs setZombieSpawn命令添加</div>';
        return;
    }

    // 保存标准化的生成点数据到全局变量（用于后续保存和选择）
    window.currentGameZombieSpawns = spawnsArray;

    // 渲染每个生成点条目
    spawnsArray.forEach((spawn, index) => {
        const spawnItem = document.createElement('div');
        spawnItem.className = 'spawn-item animate-left';
        spawnItem.style.animationDelay = `${0.1 * (index + 1)}s`;

        const typeLabels = {
            'id': '普通僵尸',
            'idn': '特殊僵尸',
            'idc': '僵尸猪灵'
        };

        const typeLabel = typeLabels[spawn.type] || '未知类型';
        const statusClass = spawn.enabled ? 'text-success' : 'text-secondary';
        const statusLabel = spawn.enabled ? '已启用' : '未启用';

        spawnItem.innerHTML = `
            <span class="spawn-name"><i class="bi bi-bug me-2"></i>${spawn.name}</span>
            <div class="spawn-info">
                <span class="badge bg-info me-2">${typeLabel}</span>
                <span class="badge ${statusClass}">${statusLabel}</span>
                <span class="spawn-location small text-muted ms-2" title="生成点位置">
                    (${spawn.x.toFixed(1)}, ${spawn.y.toFixed(1)}, ${spawn.z.toFixed(1)})
                </span>
            </div>
        `;

        container.appendChild(spawnItem);
    });
}

// 获取当前回合模式数据
function getCurrentRoundModesData() {
    // 首先检查全局变量
    if (window.currentGameRoundModes && typeof window.currentGameRoundModes === 'object') {
        return window.currentGameRoundModes;
    }

    // 如果全局变量不存在，从游戏详情中获取
    const gameDetails = window.currentGameDetails || {};
    if (!gameDetails.roundModes) {
        console.warn("游戏详情中没有回合模式数据");
        return {};
    }

    // 更新全局变量并返回
    window.currentGameRoundModes = gameDetails.roundModes;
    return gameDetails.roundModes;
}

// 从调试棒选择的区域读取（窗户）
function getWindowRegionFromWand(mode) {
    const gameName = document.getElementById('edit-game-name').value;

    // 显示加载中
    const btnId = mode === 'add' ? 'get-window-region-btn' : 'edit-window-region-btn';
    const originalBtnContent = document.getElementById(btnId).innerHTML;
    document.getElementById(btnId).innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 读取中...';
    document.getElementById(btnId).disabled = true;

    // 发送请求获取当前玩家选择的区域
    fetch(`/api/region?game=${encodeURIComponent(gameName)}`)
        .then(response => response.json())
        .then(data => {
            // 恢复按钮状态
            document.getElementById(btnId).innerHTML = originalBtnContent;
            document.getElementById(btnId).disabled = false;

            if (data.success) {
                showToast('成功', '成功从调试棒读取区域', 'success');

                // 如果是在编辑窗户模式下，我们需要存储区域数据
                if (mode === 'edit') {
                    const index = parseInt(document.getElementById('edit-window-index').value);
                    const windows = getCurrentWindowsData();
                    if (index >= 0 && index < windows.length) {
                        windows[index] = data.region;

                        // 显示区域信息
                        displayWindowRegionInfo('edit-window-region-info', data.region);
                    }
                } else {
                    // 在添加窗户模式下，我们会暂存这个区域数据
                    window.tempWindowRegion = data.region;

                    // 显示区域信息
                    displayWindowRegionInfo('window-region-info', data.region);
                    document.getElementById('window-region-info').classList.remove('d-none');
                }
            } else {
                showToast('错误', data.message || '读取区域失败，请确保您已使用调试棒选择了一个区域', 'danger');
            }
        })
        .catch(error => {
            console.error('读取区域失败:', error);
            document.getElementById(btnId).innerHTML = originalBtnContent;
            document.getElementById(btnId).disabled = false;
            showToast('错误', '请求失败，请检查网络连接', 'danger');
        });
}

// 显示窗户区域信息
function displayWindowRegionInfo(containerId, region) {
    const container = document.getElementById(containerId);
    if (!container) return;

    const world = region.world || 'world';
    const minX = region.minX || region.x1 || 0;
    const minY = region.minY || region.y1 || 0;
    const minZ = region.minZ || region.z1 || 0;
    const maxX = region.maxX || region.x2 || 0;
    const maxY = region.maxY || region.y2 || 0;
    const maxZ = region.maxZ || region.z2 || 0;

    container.innerHTML = `
        <h6><i class="bi bi-geo-alt-fill me-2"></i>窗户区域坐标信息</h6>
        <div class="small">
            <strong>世界：</strong>${world}<br>
            <strong>起点：</strong>(${minX}, ${minY}, ${minZ})<br>
            <strong>终点：</strong>(${maxX}, ${maxY}, ${maxZ})
        </div>
    `;
}

// 获取当前窗户数据
function getCurrentWindowsData() {
    // 首先检查全局变量
    if (window.currentGameWindows && Array.isArray(window.currentGameWindows)) {
        return window.currentGameWindows;
    }

    // 如果全局变量不存在，从游戏详情中获取
    const gameDetails = window.currentGameDetails || {};
    if (!gameDetails.windows || !Array.isArray(gameDetails.windows)) {
        console.warn("游戏详情中没有窗户数据");
        return [];
    }

    // 更新全局变量并返回
    window.currentGameWindows = gameDetails.windows;
    return gameDetails.windows;
}

// 添加窗户
function addWindow() {
    if (!window.tempWindowRegion) {
        showToast('错误', '请先从调试棒选择区域', 'danger');
        return;
    }

    // 获取当前窗户数据
    const windows = getCurrentWindowsData();

    // 为新窗户添加名称
    const newWindow = Object.assign({}, window.tempWindowRegion);
    if (!newWindow.name) {
        newWindow.name = `窗户${windows.length + 1}`;
    }

    // 添加新窗户
    windows.push(newWindow);

    // 更新全局变量
    window.currentGameWindows = windows;

    // 关闭模态框
    const modal = bootstrap.Modal.getInstance(document.getElementById('add-window-modal'));
    if (modal) {
        modal.hide();
    }

    // 重新渲染窗户列表
    renderWindowsList(windows);

    // 显示成功消息
    showToast('成功', '窗户添加成功', 'success');

    // 显示保存按钮
    showFloatingSaveButton();
}

// 保存窗户
function saveWindow() {
    const index = parseInt(document.getElementById('edit-window-index').value);
    const windows = getCurrentWindowsData();

    if (isNaN(index) || index < 0 || index >= windows.length) {
        showToast('错误', '无效的窗户索引', 'danger');
        return;
    }

    // 关闭模态框
    const modal = bootstrap.Modal.getInstance(document.getElementById('edit-window-modal'));
    if (modal) {
        modal.hide();
    }

    // 重新渲染窗户列表
    renderWindowsList(windows);

    // 显示成功消息
    showToast('成功', '窗户保存成功', 'success');

    // 显示保存按钮
    showFloatingSaveButton();
}

// 删除窗户
function deleteWindow() {
    const index = parseInt(document.getElementById('edit-window-index').value);
    const windows = getCurrentWindowsData();

    if (isNaN(index) || index < 0 || index >= windows.length) {
        showToast('错误', '无效的窗户索引', 'danger');
        return;
    }

    // 删除窗户
    windows.splice(index, 1);

    // 更新全局变量
    window.currentGameWindows = windows;

    // 关闭模态框
    const modal = bootstrap.Modal.getInstance(document.getElementById('edit-window-modal'));
    if (modal) {
        modal.hide();
    }

    // 重新渲染窗户列表
    renderWindowsList(windows);

    // 显示成功消息
    showToast('成功', '窗户删除成功', 'success');

    // 显示保存按钮
    showFloatingSaveButton();
}

// 渲染窗户列表
function renderWindowsList(windows = []) {
    const container = document.getElementById('windows-list');
    if (!container) {
        console.error('窗户列表容器不存在');
        return;
    }

    container.innerHTML = '';

    if (!Array.isArray(windows) || windows.length === 0) {
        container.innerHTML = '<div class="text-center text-muted py-3">暂无窗户设置</div>';
        return;
    }

    // 渲染每个窗户条目
    windows.forEach((window, index) => {
        const windowItem = document.createElement('div');
        windowItem.className = 'window-item animate-left';
        windowItem.style.animationDelay = `${0.1 * (index + 1)}s`;

        const world = window.world || 'world';
        const minX = window.minX || window.x1 || 0;
        const minY = window.minY || window.y1 || 0;
        const minZ = window.minZ || window.z1 || 0;

        const windowName = window.name || `窗户${index + 1}`;

        windowItem.innerHTML = `
            <span class="window-name"><i class="bi bi-window me-2"></i>${windowName}</span>
            <div class="window-controls">
                <span class="window-location small text-muted" title="窗户位置">
                    ${world} (${minX.toFixed(1)}, ${minY.toFixed(1)}, ${minZ.toFixed(1)})
                </span>
                <button class="btn btn-sm btn-outline-primary ms-2 edit-window-btn" data-index="${index}">
                    <i class="bi bi-pencil"></i>
                </button>
            </div>
        `;

        container.appendChild(windowItem);
    });

    // 绑定编辑按钮事件 - 使用事件委托
    document.removeEventListener('click', windowEditHandler); // 移除之前的处理器避免重复
    document.addEventListener('click', windowEditHandler);
}

// 窗户编辑按钮点击事件处理函数
function windowEditHandler(e) {
    // 首先确保找到的是编辑按钮或其子元素
    const editBtn = e.target.closest('.edit-window-btn');
    if (!editBtn) return;

    // 阻止默认行为（如果按钮在<a>标签内）
    e.preventDefault();

    // 获取窗户索引并调用编辑窗户函数
    const index = parseInt(editBtn.getAttribute('data-index'));
    if (!isNaN(index)) {
        editWindow(index);
    } else {
        console.error('无效的窗户索引');
    }

    // 阻止事件冒泡，防止触发其他事件处理器
    e.stopPropagation();
}

// 编辑窗户
function editWindow(index) {
    const windows = getCurrentWindowsData();

    if (!Array.isArray(windows) || index < 0 || index >= windows.length) {
        showToast('错误', '窗户数据无效或索引超出范围', 'danger');
        return;
    }

    const window = windows[index];

    // 填充表单
    document.getElementById('edit-window-index').value = index;

    // 显示区域信息
    displayWindowRegionInfo('edit-window-region-info', window);

    // 显示模态框
    const editWindowModal = new bootstrap.Modal(document.getElementById('edit-window-modal'));
    editWindowModal.show();
}

// 获取全局电源按钮位置
function getPowerButtonLocation() {
    const gameName = document.getElementById('edit-game-name').value;

    // 显示加载中
    const btn = document.getElementById('get-power-button-location-btn');
    const originalBtnContent = btn.innerHTML;
    btn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 获取中...';
    btn.disabled = true;

    // 发送请求获取当前玩家位置
    fetchWithAuth(`/api/player-location?game=${encodeURIComponent(gameName)}`)
        .then(response => response.json())
        .then(data => {
            // 恢复按钮状态
            btn.innerHTML = originalBtnContent;
            btn.disabled = false;

            if (data.success) {
                // 填充位置信息
                document.getElementById('power-button-world').value = data.world || '';
                document.getElementById('power-button-x').value = data.x || 0;
                document.getElementById('power-button-y').value = data.y || 0;
                document.getElementById('power-button-z').value = data.z || 0;

                showToast('成功', '成功获取当前位置', 'success');

                // 显示保存按钮
                showFloatingSaveButton();
            } else {
                showToast('错误', data.message || '获取位置失败', 'danger');
            }
        })
        .catch(error => {
            console.error('获取位置失败:', error);
            btn.innerHTML = originalBtnContent;
            btn.disabled = false;
            showToast('错误', '请求失败，请检查网络连接', 'danger');
        });
}

// 显示浮动保存按钮
function showFloatingSaveButton() {
    const saveBtn = document.getElementById('floating-save-btn');
    if (saveBtn) {
        saveBtn.style.display = 'block';

        // 添加点击事件
        saveBtn.onclick = function() {
            saveGameConfig();
            this.style.display = 'none';
        };
    }
}

// 更新回合模式概览信息
function updateRoundModesSummary(roundModes = {}) {
    const summaryText = document.getElementById('round-modes-summary-text');
    if (!summaryText) return;

    const roundKeys = Object.keys(roundModes);
    const totalRounds = roundKeys.length;

    if (totalRounds === 0) {
        summaryText.innerHTML = '还没有配置任何回合模式';
        return;
    }

    // 统计生成点和配置数量
    let totalSpawnPoints = 0;
    let totalConfigs = 0;
    const roundNumbers = [];

    roundKeys.forEach(roundKey => {
        const round = parseInt(roundKey.replace('round', ''));
        roundNumbers.push(round);

        const spawns = roundModes[roundKey];
        if (spawns && typeof spawns === 'object') {
            const spawnNames = Object.keys(spawns);
            totalSpawnPoints += spawnNames.length;

            spawnNames.forEach(spawnName => {
                const spawnConfigs = spawns[spawnName];
                if (Array.isArray(spawnConfigs)) {
                    totalConfigs += spawnConfigs.length;
                } else if (typeof spawnConfigs === 'object') {
                    // 对象格式，计算配置数量
                    const configKeys = Object.keys(spawnConfigs).filter(key =>
                        key.startsWith('刷怪逻辑') || !isNaN(parseInt(key))
                    );
                    totalConfigs += configKeys.length;
                }
            });
        }
    });

    // 排序回合数
    roundNumbers.sort((a, b) => a - b);
    const roundRange = roundNumbers.length > 1 ?
        `第${roundNumbers[0]}-${roundNumbers[roundNumbers.length - 1]}回合` :
        `第${roundNumbers[0]}回合`;

    summaryText.innerHTML = `
        已配置 <strong>${totalRounds}</strong> 个回合 (${roundRange}) |
        涉及 <strong>${totalSpawnPoints}</strong> 个生成点 |
        共 <strong>${totalConfigs}</strong> 个怪物配置
    `;
}

// 渲染回合模式列表
function renderRoundModesList(roundModes = {}) {
    const container = document.getElementById('round-modes-list');
    if (!container) return;

    container.innerHTML = '';
    console.log('正在渲染回合模式列表:', roundModes);

    // 更新回合模式概览信息
    updateRoundModesSummary(roundModes);

    // 没有任何模式时显示提示
    if (Object.keys(roundModes).length === 0) {
        container.innerHTML = '<div class="alert alert-info">还没有配置任何回合模式，点击"设置单回合"按钮创建。</div>';
        return;
    }

    // 按回合数排序
    const sortedRounds = Object.keys(roundModes)
        .sort((a, b) => {
            const roundA = parseInt(a.replace('round', ''));
            const roundB = parseInt(b.replace('round', ''));
            return roundA - roundB;
        });

    for (const roundKey of sortedRounds) {
        const round = parseInt(roundKey.replace('round', ''));
        const spawns = roundModes[roundKey];

        if (!spawns) {
            console.warn(`回合 ${round} 的配置为空或无效`);
            continue;
        }

        // 创建回合卡片
        const roundCard = document.createElement('div');
        roundCard.className = 'card mb-3 round-mode-card';

        // 回合卡片头部 - 修改样式，使其更简洁
        const roundCardHeader = document.createElement('div');
        roundCardHeader.className = 'card-header d-flex justify-content-between align-items-center';
        // 修改标题样式，使用普通文本而非高亮
        roundCardHeader.innerHTML = `<h5 class="mb-0 text-secondary">第 ${round} 回合</h5>`;
        roundCard.appendChild(roundCardHeader);

        // 回合卡片内容
        const roundCardBody = document.createElement('div');
        roundCardBody.className = 'card-body';

        // 创建生成点配置列表
        const spawnTable = document.createElement('div');
        spawnTable.className = 'table-responsive';
        spawnTable.innerHTML = `
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th scope="col">生成点</th>
                        <th scope="col">怪物配置</th>
                        <th scope="col">操作</th>
                    </tr>
                </thead>
                <tbody id="spawn-config-${round}">
                </tbody>
            </table>
        `;
        roundCardBody.appendChild(spawnTable);

        // 移除"添加刷怪逻辑"按钮，该功能已被"设置单回合"按钮取代
        roundCard.appendChild(roundCardBody);
        container.appendChild(roundCard);

        // 添加各生成点的配置
        const spawnTableBody = document.getElementById(`spawn-config-${round}`);

        for (const spawn in spawns) {
            try {
                // 检查spawn是否为有效对象属性
                if (!spawns.hasOwnProperty(spawn)) {
                    console.warn(`跳过无效的生成点属性: ${spawn}`);
                    continue;
                }

                // 获取生成点配置数据
                const spawnConfigsData = spawns[spawn];

                // 兼容性处理：处理不同的配置格式
                console.log(`处理生成点 ${spawn} 的配置:`, spawnConfigsData);
                let spawnConfigs = [];

                // 检查配置格式并提取配置项
                if (Array.isArray(spawnConfigsData)) {
                    // 如果是数组格式，直接使用
                    spawnConfigs = spawnConfigsData;
                } else if (typeof spawnConfigsData === 'object') {
                    // 如果是对象格式，检查是否有命名键或数字索引
                    const hasNamedKeys = Object.keys(spawnConfigsData).some(key =>
                        key.startsWith('刷怪逻辑'));
                    const hasNumericKeys = Object.keys(spawnConfigsData).some(key =>
                        !isNaN(parseInt(key)));

                    if (hasNamedKeys || hasNumericKeys) {
                        // 将对象中的配置项转换为数组
                        for (const key in spawnConfigsData) {
                            if (spawnConfigsData.hasOwnProperty(key)) {
                                // 保存键名为显示用
                                const configItem = Object.assign({}, spawnConfigsData[key]);
                                configItem._configKey = key; // 保存原始键名
                                spawnConfigs.push(configItem);
                            }
                        }
                    } else {
                        // 如果是普通对象，将其作为单个配置
                        const configItem = Object.assign({}, spawnConfigsData);
                        configItem._configKey = '刷怪逻辑1'; // 为旧格式赋予默认键名
                        spawnConfigs.push(configItem);
                    }
                }

                if (!spawnConfigs || spawnConfigs.length === 0) {
                    console.warn(`生成点 ${spawn} 的配置为空或无效`);
                    continue;
                }

                console.log(`生成点 ${spawn} 有 ${spawnConfigs.length} 个怪物配置`);

                // 为每个配置创建一行
                spawnConfigs.forEach((config, index) => {
                    if (!config) {
                        console.warn(`生成点 ${spawn} 的第 ${index+1} 个配置为空或无效`);
                        return;
                    }

                    const tr = document.createElement('tr');

                    // 生成点名称（只在第一个配置时显示）
                    const spawnNameCell = document.createElement('td');
                    if (index === 0) {
                        spawnNameCell.rowSpan = spawnConfigs.length;
                        spawnNameCell.innerText = spawn;
                    } else {
                        spawnNameCell.style.display = 'none';
                    }
                    tr.appendChild(spawnNameCell);

                    // 怪物配置信息
                    const configInfoCell = document.createElement('td');
                    const monsterType = config.monsterType || '未知';
                    const monsterId = config.monsterId || '未知';
                    const count = config.count || '未知';
                    const configKey = config._configKey || `刷怪逻辑${index + 1}`; // 显示配置名称

                    // 查找怪物信息
                    let monsterName = monsterId;
                    if (monsterTypes[monsterType] && monsterTypes[monsterType].ids[monsterId]) {
                        monsterName = monsterTypes[monsterType].ids[monsterId].name || monsterName;
                    }

                    configInfoCell.innerHTML = `
                        <div class="monster-config-info">
                            <span class="badge bg-secondary me-2">${configKey}</span>
                            <span class="monster-name">${monsterName}</span>
                            <span class="monster-count badge ${count === 'random' ? 'bg-warning' : 'bg-info'} ms-2">
                                ${count === 'random' ? '随机数量' : `${count}个`}
                            </span>
                        </div>
                    `;
                    tr.appendChild(configInfoCell);

                    // 操作按钮
                    const actionCell = document.createElement('td');
                    // 使用静态HTML和data属性，避免重复添加事件监听器
                    const editBtn = document.createElement('button');
                    editBtn.type = 'button'; // 明确设置按钮类型为button，防止表单提交
                    editBtn.className = 'btn btn-sm btn-outline-primary me-1 edit-spawn-config-btn';
                    editBtn.innerHTML = '<i class="bi bi-pencil"></i>';
                    editBtn.setAttribute('data-round', round);
                    editBtn.setAttribute('data-spawn', spawn);
                    editBtn.setAttribute('data-index', index);
                    // 给编辑按钮添加直接点击事件
                    editBtn.onclick = function(e) {
                        // 阻止默认行为和事件冒泡，防止表单提交或页面刷新
                        e.preventDefault();
                        e.stopPropagation();

                        const roundNum = this.getAttribute('data-round');
                        const spawnName = this.getAttribute('data-spawn');
                        const configIndex = parseInt(this.getAttribute('data-index'));
                        console.log(`点击编辑按钮: 回合=${roundNum}, 生成点=${spawnName}, 索引=${configIndex}`);
                        openRoundModeModal(roundNum, spawnName, configIndex);

                        // 确保返回false以进一步阻止默认行为
                        return false;
                    };
                    actionCell.appendChild(editBtn);
                    tr.appendChild(actionCell);

                    spawnTableBody.appendChild(tr);
                });
            } catch (error) {
                console.error(`处理生成点 ${spawn} 时出错:`, error);
                // 继续处理下一个生成点
            }
        }
    }

    // 保存到全局变量
    window.currentGameRoundModes = roundModes;

    // 初始化折叠按钮事件监听器
    initializeRoundModesCollapseButton();
}

// 初始化回合模式折叠按钮
function initializeRoundModesCollapseButton() {
    const collapseButton = document.querySelector('[data-bs-target="#roundModesDetailsList"]');
    const collapseTarget = document.getElementById('roundModesDetailsList');

    if (!collapseButton || !collapseTarget) return;

    // 更新按钮文本和图标
    const updateButtonText = (isCollapsed) => {
        const icon = collapseButton.querySelector('i');
        const buttonText = collapseButton.childNodes[collapseButton.childNodes.length - 1];

        if (isCollapsed) {
            icon.className = 'bi bi-eye me-1';
            buttonText.textContent = '查看详情';
        } else {
            icon.className = 'bi bi-eye-slash me-1';
            buttonText.textContent = '隐藏详情';
        }
    };

    // 监听折叠状态变化
    collapseTarget.addEventListener('shown.bs.collapse', () => {
        updateButtonText(false);
    });

    collapseTarget.addEventListener('hidden.bs.collapse', () => {
        updateButtonText(true);
    });

    // 初始化按钮状态
    const isCollapsed = !collapseTarget.classList.contains('show');
    updateButtonText(isCollapsed);
}

// 打开回合模式设置模态框
function openRoundModeModal(round, spawnName, monsterIndex = 0) {
    console.log("打开回合模式设置对话框", { round, spawnName, monsterIndex });

    // 初始化表单
    const roundInput = document.getElementById('edit-round-number');
    const spawnInput = document.getElementById('edit-spawn-name');

    // 设置回合隐藏字段
    roundInput.value = round;
    console.log(`设置回合隐藏字段: ${round}`);

    // 设置生成点隐藏字段（确保有初始值）
    if (spawnName && spawnName !== '') {
        spawnInput.value = spawnName;
        console.log(`设置生成点隐藏字段: ${spawnName}`);
    } else {
        // 如果没有提供生成点名称，先清空
        spawnInput.value = '';
        console.log("未提供生成点名称，初始清空隐藏字段");
    }

    // 获取游戏配置
    const gameDetails = window.currentGameDetails || {};
    const roundModes = getCurrentRoundModesData();

    // 填充回合选择框
    const roundSelect = document.getElementById('round-mode-round');
    roundSelect.innerHTML = '';

    const totalRounds = gameDetails.rounds || 5;
    for (let i = 1; i <= totalRounds; i++) {
        const option = document.createElement('option');
        option.value = i;
        option.textContent = `第 ${i} 回合`;
        roundSelect.appendChild(option);
    }

    // 设置当前回合
    roundSelect.value = round;

    // 填充生成点选择框
    const spawnSelect = document.getElementById('round-mode-spawn');
    spawnSelect.innerHTML = '';

    const zombieSpawns = window.currentGameZombieSpawns || [];
    zombieSpawns.forEach(spawn => {
        const option = document.createElement('option');
        option.value = spawn.name;
        option.textContent = spawn.name;
        spawnSelect.appendChild(option);
    });

    // 如果没有生成点，显示提示
    if (zombieSpawns.length === 0) {
        const option = document.createElement('option');
        option.value = '';
        option.textContent = '-- 请先添加生成点 --';
        option.disabled = true;
        spawnSelect.appendChild(option);
    }

    // 设置当前生成点（增强处理逻辑）
    console.log("准备设置生成点下拉框值", {
        spawnName: spawnName,
        hasExistingSpawns: zombieSpawns.length > 0,
        availableSpawns: zombieSpawns.map(s => s.name)
    });

    if (spawnName && spawnName !== '') {
        spawnSelect.value = spawnName;
        console.log(`将生成点下拉框值设置为: ${spawnName}`);
    } else if (zombieSpawns.length > 0) {
        const firstSpawnName = zombieSpawns[0].name;
        spawnSelect.value = firstSpawnName;
        spawnInput.value = firstSpawnName; // 确保隐藏字段也被设置
        console.log(`首次设置生成点: ${firstSpawnName}`);
    }

    // 确保隐藏字段与选择框同步
    spawnInput.value = spawnSelect.value;
    console.log(`同步生成点隐藏字段与选择框: ${spawnInput.value}`);

    // 添加change事件监听器，确保选择变更时同步更新隐藏字段
    spawnSelect.addEventListener('change', function() {
        spawnInput.value = this.value;
        console.log(`生成点选择变更: ${this.value}`);
    });

    // 为回合选择器也添加change事件监听器
    roundSelect.addEventListener('change', function() {
        roundInput.value = this.value;
        console.log(`回合选择变更: ${this.value}`);
    });

    // 填充怪物ID选择框（根据当前选择的怪物类型）
    updateMonsterIdOptions();

    // 如果是编辑现有配置，填充表单
    if (spawnName && roundModes[`round${round}`] && roundModes[`round${round}`][spawnName]) {
        const spawnConfigsData = roundModes[`round${round}`][spawnName];

        // 兼容性处理：检查是否为数组，如果不是数组（旧格式），则转换为数组
        const spawnConfigs = Array.isArray(spawnConfigsData)
            ? spawnConfigsData
            : [spawnConfigsData]; // 如果是旧数据，将对象包装成数组

        // 确保选择有效的配置索引
        const validIndex = monsterIndex < spawnConfigs.length ? monsterIndex : 0;

        // 增加怪物配置选择器（如果有多个配置）
        if (spawnConfigs.length > 1) {
            // 创建或清空怪物配置选择器
            let configSelector = document.getElementById('monster-config-selector');
            if (!configSelector) {
                // 如果不存在，创建配置选择器
                const monsterTypeDiv = document.querySelector('#round-mode-monster-type').closest('.col-md-4');
                const row = monsterTypeDiv.closest('.row');

                // 创建新的行
                const newRow = document.createElement('div');
                newRow.className = 'row mb-3';

                // 创建配置选择器容器
                const selectorDiv = document.createElement('div');
                selectorDiv.className = 'col-12';

                // 创建标签
                const label = document.createElement('label');
                label.className = 'form-label';
                label.textContent = '当前出生点的怪物配置';

                // 创建选择器
                configSelector = document.createElement('select');
                configSelector.className = 'form-select';
                configSelector.id = 'monster-config-selector';

                // 添加事件监听器
                configSelector.addEventListener('change', function() {
                    const selectedIndex = this.value;
                    openRoundModeModal(round, spawnName, parseInt(selectedIndex));
                });

                // 将元素添加到DOM中
                selectorDiv.appendChild(label);
                selectorDiv.appendChild(configSelector);
                newRow.appendChild(selectorDiv);

                // 将新行插入到现有行之前
                row.parentNode.insertBefore(newRow, row);
            } else {
                // 清空现有选项
                configSelector.innerHTML = '';
            }

            // 添加配置选项
            spawnConfigs.forEach((config, index) => {
                const monsterInfo = monsterTypes[config.monsterType]?.ids[config.monsterId];
                const displayName = monsterInfo ?
                    `${monsterTypes[config.monsterType].name} - ${monsterInfo.name}` :
                    `${config.monsterType} - ${config.monsterId}`;

                const option = document.createElement('option');
                option.value = index;
                option.textContent = `配置 ${index + 1}: ${displayName} (${config.count === 'random' ? '随机数量' : config.count + '个'})`;
                configSelector.appendChild(option);
            });

            // 设置当前选择的配置
            configSelector.value = validIndex;
        } else {
            // 如果只有一个配置，移除选择器
            const configSelector = document.getElementById('monster-config-selector');
            if (configSelector) {
                const row = configSelector.closest('.row');
                if (row) {
                    row.remove();
                }
            }
        }

        // 填充当前选择的配置
        const modeData = spawnConfigs[validIndex];

        document.getElementById('round-mode-monster-type').value = modeData.monsterType || 'zombie';
        updateMonsterIdOptions(); // 更新ID选项
        document.getElementById('round-mode-monster-id').value = modeData.monsterId || 'id1';
        document.getElementById('round-mode-count').value = modeData.count || '5';

        // 确保创建并正确初始化配置索引隐藏字段
        let configIndexField = document.getElementById('edit-config-index');
        if (!configIndexField) {
            console.log("创建配置索引隐藏字段");
            configIndexField = document.createElement('input');
            configIndexField.type = 'hidden';
            configIndexField.id = 'edit-config-index';
            document.getElementById('round-mode-form').appendChild(configIndexField);
        }
        // 设置配置索引为当前选择的索引（编辑现有配置）
        configIndexField.value = validIndex;
        console.log("设置配置索引为" + validIndex + "（编辑现有配置）");

        // 更新怪物信息
        updateMonsterInfo();

        // 显示删除按钮
        document.getElementById('delete-round-mode-btn').style.display = 'block';
    } else {
        // 新建配置，设置默认值
        document.getElementById('round-mode-monster-type').value = 'zombie';
        updateMonsterIdOptions();
        document.getElementById('round-mode-monster-id').value = 'id1';
        document.getElementById('round-mode-count').value = '5';

        // 移除怪物配置选择器（如果存在）
        const configSelector = document.getElementById('monster-config-selector');
        if (configSelector) {
            const row = configSelector.closest('.row');
            if (row) {
                row.remove();
            }
        }

        // 设置配置索引为-1（新建配置）
        let configIndexField = document.getElementById('edit-config-index');
        if (!configIndexField) {
            configIndexField = document.createElement('input');
            configIndexField.type = 'hidden';
            configIndexField.id = 'edit-config-index';
            document.getElementById('round-mode-form').appendChild(configIndexField);
        }
        configIndexField.value = -1;

        // 更新怪物信息
        updateMonsterInfo();

        // 隐藏删除按钮
        document.getElementById('delete-round-mode-btn').style.display = 'none';
    }

    // 增强的模态框关闭逻辑
    console.log("准备关闭模态框...");
    const modal = new bootstrap.Modal(document.getElementById('round-mode-modal'));
    modal.show();
}

// 更新怪物ID选项
function updateMonsterIdOptions() {
    try {
        const monsterTypeSelect = document.getElementById('round-mode-monster-type');
        const monsterIdSelect = document.getElementById('round-mode-monster-id');

        if (!monsterTypeSelect || !monsterIdSelect) {
            console.error("找不到怪物类型或ID选择框");
            return;
        }

        const selectedType = monsterTypeSelect.value;
        console.log("更新怪物ID选项，选择的类型:", selectedType);

        // 清空现有选项
        monsterIdSelect.innerHTML = '';

        // 检查选择的类型是否有效
        if (!monsterTypes[selectedType]) {
            console.warn(`未找到类型 ${selectedType} 的怪物列表`);
            const defaultOption = document.createElement('option');
            defaultOption.value = "id1";
            defaultOption.textContent = "普通僵尸 (默认)";
            monsterIdSelect.appendChild(defaultOption);
            return;
        }

        // 添加该类型的所有怪物ID
        const ids = monsterTypes[selectedType].ids || {};

        for (const id in ids) {
            if (ids.hasOwnProperty(id)) {
                const option = document.createElement('option');
                option.value = id;
                option.textContent = ids[id].name || id;
                monsterIdSelect.appendChild(option);
            }
        }

        // 如果没有选项，添加默认选项
        if (monsterIdSelect.options.length === 0) {
            const defaultOption = document.createElement('option');
            defaultOption.value = "id1";
            defaultOption.textContent = "普通僵尸 (默认)";
            monsterIdSelect.appendChild(defaultOption);
        }

        // 更新怪物信息显示
        updateMonsterInfo();

    } catch (error) {
        console.error("更新怪物ID选项时出错:", error);
    }
}

// 更新怪物信息显示
function updateMonsterInfo() {
    const monsterType = document.getElementById('round-mode-monster-type').value;
    const monsterId = document.getElementById('round-mode-monster-id').value;
    const infoContainer = document.getElementById('monster-info-container');

    // 查找怪物信息
    let monsterInfo = null;
    if (monsterTypes[monsterType] && monsterTypes[monsterType].ids[monsterId]) {
        monsterInfo = monsterTypes[monsterType].ids[monsterId];
    }

    if (monsterInfo) {
        infoContainer.innerHTML = `
            <h6 class="mb-2">${monsterInfo.name}</h6>
            <p class="mb-0">${monsterInfo.description}</p>
        `;
    } else {
        infoContainer.innerHTML = '<p class="mb-0">没有该怪物的详细信息</p>';
    }
}

// 保存回合模式设置
function saveRoundMode() {
    console.log("开始保存回合模式...");

    // 获取表单元素
    const roundInput = document.getElementById('edit-round-number');
    const spawnInput = document.getElementById('edit-spawn-name');
    const monsterTypeSelect = document.getElementById('round-mode-monster-type');
    const monsterIdSelect = document.getElementById('round-mode-monster-id');
    const countInput = document.getElementById('round-mode-count');
    const configIndexInput = document.getElementById('edit-config-index');

    // 记录表单元素状态
    console.log("表单元素状态:", {
        round: roundInput?.value,
        spawn: spawnInput?.value,
        monsterType: monsterTypeSelect?.value,
        monsterId: monsterIdSelect?.value,
        count: countInput?.value,
        configIndex: configIndexInput?.value
    });

    // 验证必要的表单元素是否存在
    if (!roundInput) {
        console.error("找不到回合输入框");
        showToast("错误", "找不到回合输入框", "danger");
        return;
    }

    if (!spawnInput) {
        console.error("找不到生成点输入框");
        showToast("错误", "找不到生成点输入框", "danger");
        return;
    }

    if (!monsterTypeSelect) {
        console.error("找不到怪物类型选择框");
        showToast("错误", "找不到怪物类型选择框", "danger");
        return;
    }

    if (!monsterIdSelect) {
        console.error("找不到怪物ID选择框");
        showToast("错误", "找不到怪物ID选择框", "danger");
        return;
    }

    if (!countInput) {
        console.error("找不到数量输入框");
        showToast("错误", "找不到数量输入框", "danger");
        return;
    }

    // 获取表单值 - 严格获取隐藏字段的值，而不是从下拉框获取
    let round = roundInput.value;
    let spawnName = spawnInput.value;
    const monsterType = monsterTypeSelect.value;
    const monsterId = monsterIdSelect.value;
    let count = countInput.value;
    let configIndex = configIndexInput ? configIndexInput.value : null;

    console.log("最终使用的表单值: ", {
        round, spawnName, monsterType, monsterId, count, configIndex
    });

    let missingFields = [];
    if (!round) missingFields.push("回合数");
    if (!spawnName || spawnName === '') missingFields.push("生成点");
    if (!monsterType) missingFields.push("怪物类型");
    if (!monsterId) missingFields.push("怪物ID");
    if (!count) missingFields.push("生成数量");

    if (missingFields.length > 0) {
        const errorMsg = `请填写以下字段: ${missingFields.join(", ")}`;
        console.error(errorMsg);
        showToast("错误", errorMsg, "danger");
        return;
    }

    // 特殊处理count字段，确保即使是"0"也视为有效值
    if (count === "0") {
        count = "1"; // 将0视为1个，避免出现0个怪物的情况
        console.log("将生成数量从0调整为1");
    }

    // 确保configIndex是数字或null，而不是空字符串
    if (configIndex === "" || configIndex === "-1") {
        console.log("将空的configIndex或-1转换为null（新配置）");
        configIndex = null;
    }

    try {
        // 准备数据 - 严格使用隐藏字段的round值
        const roundKey = `round${round}`;

        // 获取当前回合模式数据
        const roundModes = getCurrentRoundModesData() || {};
        console.log("当前回合模式数据:", roundModes);

        // 确保回合和生成点存在
        if (!roundModes[roundKey]) {
            roundModes[roundKey] = {};
        }

        // 检查现有配置格式并转换
        if (roundModes[roundKey][spawnName]) {
            // 如果是旧格式（直接对象），转换为新格式（命名键值对）
            if (!hasNamedKeysFormat(roundModes[roundKey][spawnName])) {
                console.log("检测到非命名键格式的配置，正在转换为命名键格式");
                // 保存当前对象
                const currentConfig = Object.assign({}, roundModes[roundKey][spawnName]);

                // 重置为空对象
                roundModes[roundKey][spawnName] = {};

                // 添加为第一个刷怪逻辑
                if (Array.isArray(currentConfig)) {
                    // 如果是数组，转换每一项到命名键格式
                    currentConfig.forEach((item, index) => {
                        roundModes[roundKey][spawnName][`刷怪逻辑${index + 1}`] = item;
                    });
                } else {
                    // 如果是单个对象，添加为第一个刷怪逻辑
                    roundModes[roundKey][spawnName]['刷怪逻辑1'] = currentConfig;
                }
                console.log("已将非命名键格式配置转换为命名键格式");
            }
        } else {
            // 如果没有现有配置，创建新对象
            roundModes[roundKey][spawnName] = {};
        }

        // 创建新的配置对象
        const configData = {
            monsterType: monsterType,
            monsterId: monsterId,
            count: count,
            type: getMonsterTypeCode(monsterType),
            number: getMonsterIdNumber(monsterId)
        };

        console.log("刷怪逻辑配置详情:", configData);

        // 更新或添加配置
        if (configIndex !== null && configIndex !== "-1" && configIndex >= 0) {
            // 更新现有配置
            const index = parseInt(configIndex);

            // 对于命名键格式，需要特别处理
            if (typeof roundModes[roundKey][spawnName] === 'object' &&
                !Array.isArray(roundModes[roundKey][spawnName])) {

                // 获取所有配置键（刷怪逻辑1，刷怪逻辑2等）
                const configKeys = Object.keys(roundModes[roundKey][spawnName])
                    .filter(key => key.startsWith('刷怪逻辑'));

                // 排序配置键，确保按数字顺序而不是字符串顺序
                configKeys.sort((a, b) => {
                    const numA = parseInt(a.replace('刷怪逻辑', ''));
                    const numB = parseInt(b.replace('刷怪逻辑', ''));
                    return numA - numB;
                });

                console.log(`配置键列表: ${configKeys.join(', ')}`);
                console.log(`准备更新索引 ${index} 的配置`);

                if (index >= 0 && index < configKeys.length) {
                    // 使用排序后的索引找到正确的键
                    const key = configKeys[index];
                    console.log(`更新配置键: ${key}, 索引: ${index}`);
                    console.log(`更新前: ${JSON.stringify(roundModes[roundKey][spawnName][key])}`);
                    console.log(`更新后: ${JSON.stringify(configData)}`);

                    // 更新配置
                    roundModes[roundKey][spawnName][key] = configData;
                } else {
                    // 索引无效，添加为新配置
                    addNewConfigWithNamedKey(roundModes[roundKey][spawnName], configData);
                    console.log(`索引 ${index} 超出范围，添加为新配置`);
                }
            } else {
                // 数组格式 (旧格式)
                const keys = Object.keys(roundModes[roundKey][spawnName]);

                if (index >= 0 && index < keys.length) {
                    // 找到对应的"刷怪逻辑X"键名
                    const key = keys[index];

                    // 记录更新前的配置信息，用于日志
                    const oldConfig = roundModes[roundKey][spawnName][key];
                    console.log(`更新配置前: ${JSON.stringify(oldConfig)}`);
                    console.log(`更新配置后: ${JSON.stringify(configData)}`);

                    // 更新配置
                    roundModes[roundKey][spawnName][key] = configData;
                } else {
                    // 索引无效，添加为新配置
                    addNewConfigWithNamedKey(roundModes[roundKey][spawnName], configData);
                    console.log(`索引${index}无效，添加为新配置`);
                }
            }
        } else {
            // 添加新配置
            addNewConfigWithNamedKey(roundModes[roundKey][spawnName], configData);
            console.log(`添加新配置: 回合${round}, 生成点${spawnName}, 索引=${configIndex}`);
        }

        // 重新渲染回合模式列表
        renderRoundModesList(roundModes);

        // 确保数据保存到游戏详情中
        if (window.currentGameDetails) {
            window.currentGameDetails.roundModes = roundModes;
            console.log("已将回合模式数据保存到游戏详情中", window.currentGameDetails);
        }

        // 立即强制关闭模态框，不等待Bootstrap的关闭动画
        console.log("保存后准备关闭模态框...");
        forceCloseModal();

        // 提示保存成功
        showToast("成功", "回合模式配置已保存，别忘了点击保存按钮提交更改", "success");

        // 标记配置已更改
        document.getElementById('save-game-btn').classList.remove('btn-secondary');
        document.getElementById('save-game-btn').classList.add('btn-warning');

    } catch (error) {
        console.error("保存回合模式时出错:", error);
        showToast("错误", "保存回合模式时出错: " + error.message, "danger");
    }
}

// 检查对象是否使用命名键格式
function hasNamedKeysFormat(obj) {
    if (!obj || typeof obj !== 'object') return false;

    // 检查是否有任何键以"刷怪逻辑"开头
    return Object.keys(obj).some(key => key.startsWith("刷怪逻辑"));
}

// 添加新配置并使用命名键格式
function addNewConfigWithNamedKey(container, configData) {
    if (!container || typeof container !== 'object') {
        container = {};
    }

    // 找出下一个可用的"刷怪逻辑X"键名
    const keys = Object.keys(container);
    const namedKeys = keys.filter(key => key.startsWith("刷怪逻辑"));

    let nextIndex = 1;
    if (namedKeys.length > 0) {
        // 从现有键名中提取数字部分，找出最大值
        const indices = namedKeys.map(key => {
            const match = key.match(/刷怪逻辑(\d+)/);
            return match ? parseInt(match[1]) : 0;
        });
        nextIndex = Math.max(...indices) + 1;
    }

    const newKey = `刷怪逻辑${nextIndex}`;
    container[newKey] = configData;
    console.log(`添加新配置: 键=${newKey}`);
    return newKey;
}

// 获取怪物类型代码
function getMonsterTypeCode(monsterType) {
    if (!monsterType) return "id";

    switch (monsterType.toLowerCase()) {
        case "zombie":
            return "id";
        case "entity":
            return "idc";
        case "npc":
            return "idn";
        default:
            return "id";
    }
}

// 从怪物ID中提取数字部分
function getMonsterIdNumber(monsterId) {
    if (!monsterId) return "1";

    // 尝试从ID中提取数字部分
    if (monsterId.startsWith("id")) {
        return monsterId.substring(2);
    } else if (monsterId.startsWith("idc")) {
        return monsterId.substring(3);
    } else if (monsterId.startsWith("idn")) {
        return monsterId.substring(3);
    }

    // 如果没有特定前缀，假设整个ID是数字
    return monsterId;
}

// 删除回合模式设置
function deleteRoundMode() {
    if (!confirm('确定要删除此回合模式设置吗？')) return;

    const round = document.getElementById('edit-round-number').value;
    const spawnName = document.getElementById('edit-spawn-name').value;
    const configIndex = parseInt(document.getElementById('edit-config-index')?.value || -1);

    console.log('删除回合模式配置:', { round, spawnName, configIndex });

    if (!round || !spawnName || configIndex < 0) {
        showToast('错误', '无效的回合、生成点或配置索引', 'danger');
        return;
    }

    // 获取回合模式数据
    let roundModes = getCurrentRoundModesData();
    const roundKey = `round${round}`;

    console.log('当前回合模式数据:', roundModes);
    console.log('目标回合键:', roundKey);
    console.log('目标生成点:', spawnName);

    // 检查回合和生成点是否存在
    if (!roundModes[roundKey] || !roundModes[roundKey][spawnName]) {
        console.error('回合或生成点不存在');
        showToast('错误', '找不到指定的回合或生成点配置', 'danger');
        return;
    }

    const spawnConfigsData = roundModes[roundKey][spawnName];
    console.log('生成点配置数据:', spawnConfigsData);

    let deleted = false;

    // 处理数组格式的配置
    if (Array.isArray(spawnConfigsData)) {
        console.log('检测到数组格式配置，配置数量:', spawnConfigsData.length);

        if (configIndex < spawnConfigsData.length) {
            // 删除特定配置
            spawnConfigsData.splice(configIndex, 1);
            console.log('已删除数组索引', configIndex, '的配置');
            deleted = true;

            // 如果该出生点没有任何配置，删除该出生点
            if (spawnConfigsData.length === 0) {
                delete roundModes[roundKey][spawnName];
                console.log('生成点配置为空，已删除生成点');
            }
        }
    }
    // 处理对象格式的配置（命名键格式）
    else if (typeof spawnConfigsData === 'object') {
        console.log('检测到对象格式配置');

        // 获取所有配置键（刷怪逻辑1，刷怪逻辑2等）
        const configKeys = Object.keys(spawnConfigsData)
            .filter(key => key.startsWith('刷怪逻辑'));

        // 排序配置键，确保按数字顺序而不是字符串顺序
        configKeys.sort((a, b) => {
            const numA = parseInt(a.replace('刷怪逻辑', ''));
            const numB = parseInt(b.replace('刷怪逻辑', ''));
            return numA - numB;
        });

        console.log('配置键列表:', configKeys);
        console.log('要删除的配置索引:', configIndex);

        if (configIndex >= 0 && configIndex < configKeys.length) {
            // 使用排序后的索引找到正确的键
            const keyToDelete = configKeys[configIndex];
            console.log('要删除的配置键:', keyToDelete);

            // 删除特定配置
            delete spawnConfigsData[keyToDelete];
            console.log('已删除配置键', keyToDelete);
            deleted = true;

            // 如果该出生点没有任何配置，删除该出生点
            const remainingKeys = Object.keys(spawnConfigsData)
                .filter(key => key.startsWith('刷怪逻辑'));
            if (remainingKeys.length === 0) {
                delete roundModes[roundKey][spawnName];
                console.log('生成点配置为空，已删除生成点');
            }
        }
    }

    if (deleted) {
        // 如果该回合没有任何出生点配置，删除该回合
        if (Object.keys(roundModes[roundKey]).length === 0) {
            delete roundModes[roundKey];
            console.log('回合配置为空，已删除回合');
        }

        // 更新全局变量
        window.currentGameRoundModes = roundModes;

        // 更新UI
        renderRoundModesList(roundModes);

        // 立即强制关闭模态框，不等待Bootstrap的关闭动画
        console.log("删除后准备关闭模态框...");
        forceCloseModal();

        // 提示用户保存更改
        showToast('提示', '回合模式配置已删除，请点击"保存游戏设置"按钮保存更改', 'info');
    } else {
        console.error('删除失败，配置索引超出范围或配置不存在');
        showToast('错误', '找不到指定的回合模式配置', 'danger');
    }
}

// 页面加载后立即执行按钮修复
(function() {
    // 当DOM完全加载后执行
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initButtonFixes);
    } else {
        initButtonFixes();
    }

    // 当页面完全加载后再次执行(包括资源)
    window.addEventListener('load', initButtonFixes);

    // 延迟执行修复，确保所有动态添加的元素都被处理
    setTimeout(initButtonFixes, 2000);

    function initButtonFixes() {
        console.log('执行按钮修复...');
        try {
            // 确保所有按钮可以被点击
            fixAllClickableElements();

            // 确保事件委托正常工作 - 特别处理回合模式相关按钮
            const roundModeBtns = document.querySelectorAll('#add-round-mode-btn, #confirm-round-mode, #delete-round-mode-btn');
            console.log('找到回合模式按钮:', roundModeBtns.length);

            roundModeBtns.forEach(btn => {
                if (btn) {
                    console.log('修复按钮:', btn.id);
                    btn.style.position = 'relative';
                    btn.style.zIndex = '9999';
                    btn.style.pointerEvents = 'auto';
                    btn.style.cursor = 'pointer';

                    // 添加直接点击处理
                    if (btn.id === 'add-round-mode-btn') {
                        console.log('绑定"设置第X回合"按钮事件');
                        btn.onclick = function(e) {
                            console.log('设置第X回合按钮被点击');
                            e.preventDefault();
                            e.stopPropagation();
                            const gameDetails = window.currentGameDetails || {};
                            const rounds = gameDetails.rounds || 5;
                            if (rounds > 0) {
                                openRoundModeModal(1, null);
                            } else {
                                showToast('错误', '请先设置游戏回合数', 'danger');
                            }
                            return false;
                        };
                    } else if (btn.id === 'confirm-round-mode') {
                        btn.onclick = function(e) {
                            console.log('保存回合模式按钮被点击');
                            e.preventDefault();
                            e.stopPropagation();
                            saveRoundMode();
                            return false;
                        };
                    } else if (btn.id === 'delete-round-mode-btn') {
                        btn.onclick = function(e) {
                            console.log('删除回合模式按钮被点击');
                            e.preventDefault();
                            e.stopPropagation();
                            deleteRoundMode();
                            return false;
                        };
                    }
                }
            });

            // 全局事件委托处理回合模式按钮点击
            document.addEventListener('click', function(e) {
                const addRoundModeBtn = e.target.closest('#add-round-mode-btn');
                if (addRoundModeBtn) {
                    console.log('通过事件委托处理"设置第X回合"按钮点击');
                    e.preventDefault();
                    e.stopPropagation();
                    const gameDetails = window.currentGameDetails || {};
                    const rounds = gameDetails.rounds || 5;
                    if (rounds > 0) {
                        openRoundModeModal(1, null);
                    } else {
                        showToast('错误', '请先设置游戏回合数', 'danger');
                    }
                    return false;
                }
            });

            // 为下拉菜单项添加事件处理
            document.querySelectorAll('.dropdown-item[data-value]').forEach(item => {
                item.onclick = function(e) {
                    e.preventDefault(); // 防止默认行为
                    const countInput = document.getElementById('round-mode-count');
                    if (countInput) {
                        // 设置输入框的值
                        const value = this.getAttribute('data-value');
                        countInput.value = value;
                        console.log(`设置生成数量为: ${value}`);

                        // 触发change事件以确保值被注册
                        const event = new Event('change', { bubbles: true });
                        countInput.dispatchEvent(event);

                        // 更新下拉按钮文本
                        const dropdownToggle = countInput.closest('.input-group').querySelector('.dropdown-toggle');
                        if (dropdownToggle) {
                            if (value === 'random') {
                                dropdownToggle.textContent = '随机数量';
                            } else {
                                dropdownToggle.textContent = `${value}个`;
                            }
                        }

                        // 手动添加focus效果
                        countInput.focus();
                        countInput.blur();
                    }

                    // 关闭下拉菜单
                    const dropdown = bootstrap.Dropdown.getInstance(this.closest('.dropdown-menu').previousElementSibling);
                    if (dropdown) {
                        dropdown.hide();
                    }
                };
            });

            // 修复怪物类型选择框的事件
            const monsterTypeSelect = document.getElementById('round-mode-monster-type');
            if (monsterTypeSelect) {
                monsterTypeSelect.onchange = updateMonsterIdOptions;
            }

            const monsterIdSelect = document.getElementById('round-mode-monster-id');
            if (monsterIdSelect) {
                monsterIdSelect.onchange = updateMonsterInfo;
            }

            console.log('按钮修复完成');
        } catch (e) {
            console.error('按钮修复出错:', e);
        }
    }
})();

/**
 * 强制关闭模态框和清理背景遮罩
 */
function forceCloseModal() {
    console.log("执行强制关闭模态框");

    // 1. 移除所有模态框相关类
    const modals = document.querySelectorAll('.modal');
    modals.forEach(modal => {
        modal.classList.remove('show');
        modal.setAttribute('aria-hidden', 'true');
        modal.style.display = 'none';
    });

    // 2. 移除body上的modal-open类
    document.body.classList.remove('modal-open');
    document.body.style.overflow = '';
    document.body.style.paddingRight = '';

    // 3. 移除所有背景遮罩
    const backdrops = document.querySelectorAll('.modal-backdrop');
    backdrops.forEach(backdrop => {
        backdrop.classList.remove('show');
        backdrop.remove();
    });

    console.log("强制关闭模态框完成");
}