/* 主要样式 */
body {
    background-color: #f5f7fa;
    font-family: 'Microsoft YaHei', <PERSON><PERSON>, sans-serif !important;
    font-size: 14px !important;
    color: #333;
    overflow-x: hidden;
    position: relative;
    min-height: 100vh;
}

body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, #1a1a2e, #16213e, #0f3460);
    opacity: 0.1;
    z-index: -1;
}

.container {
    max-width: 1200px;
    background-color: #ffffff;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
    padding: 30px;
    margin-top: 20px;
    border: 1px solid rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

.container:hover {
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
}

/* 加载动画 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: #ffffff;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    transition: opacity 0.5s ease, visibility 0.5s ease;
}

.loading-overlay.hidden {
    opacity: 0;
    visibility: hidden;
}

.loading-spinner {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    border: 6px solid transparent;
    border-top-color: #4f46e5;
    border-bottom-color: #4f46e5;
    animation: spin 1.5s linear infinite;
    position: relative;
}

.loading-spinner::before, .loading-spinner::after {
    content: '';
    position: absolute;
    top: -6px;
    left: -6px;
    right: -6px;
    bottom: -6px;
    border-radius: 50%;
    border: 6px solid transparent;
    animation: spin 3s linear infinite reverse;
}

.loading-spinner::before {
    border-left-color: #f43f5e;
    border-right-color: #f43f5e;
}

.loading-spinner::after {
    top: 4px;
    left: 4px;
    right: 4px;
    bottom: 4px;
    animation: spin 1.5s linear infinite;
    border-top-color: #10b981;
    border-bottom-color: #10b981;
}

.loading-text {
    margin-top: 30px;
    font-size: 22px;
    font-weight: bold;
    color: #4f46e5;
    letter-spacing: 1px;
    animation: pulse 1.5s ease-in-out infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes pulse {
    0%, 100% { opacity: 0.6; transform: scale(1); }
    50% { opacity: 1; transform: scale(1.05); }
}

/* 标题样式 */
h1 {
    color: #4f46e5;
    font-weight: bold;
    position: relative;
    padding-bottom: 10px;
}

h1::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100px;
    height: 3px;
    background: linear-gradient(90deg, #4f46e5, #ffffff);
    animation: expandWidth 1s ease-out forwards;
}

@keyframes expandWidth {
    from { width: 0; }
    to { width: 100px; }
}

/* 卡片样式 */
.card {
    border: none;
    background-color: #ffffff;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    margin-bottom: 20px;
    transform: translateY(0);
    transition: all 0.3s ease;
    border-radius: 16px;
    overflow: hidden;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.card-header {
    font-weight: bold;
    background: linear-gradient(135deg, #f9fafb, #f3f4f6);
    color: #4f46e5;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    position: relative;
    overflow: hidden;
    padding: 15px 20px;
    border-radius: 16px 16px 0 0;
}

.card-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(79, 70, 229, 0.1), transparent);
    transition: all 0.5s ease;
}

.card:hover .card-header::before {
    left: 100%;
}

.card-body {
    padding: 20px;
}

/* 游戏列表样式 */
.game-list-container {
    max-height: 500px;
    overflow-y: auto;
    scrollbar-width: thin;
    scrollbar-color: #cbd5e1 #f1f5f9;
}

.game-list-container::-webkit-scrollbar {
    width: 6px;
}

.game-list-container::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 10px;
}

.game-list-container::-webkit-scrollbar-thumb {
    background-color: #cbd5e1;
    border-radius: 10px;
}

.list-group-item {
    cursor: pointer;
    background-color: #ffffff;
    border: 1px solid rgba(0, 0, 0, 0.05);
    margin-bottom: 8px;
    border-left: 4px solid transparent;
    color: #4b5563;
    transition: all 0.3s ease;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 15px;
    border-radius: 10px;
}

.list-group-item:hover {
    background-color: #f9fafb;
    border-left: 4px solid #4f46e5;
    transform: translateX(5px);
}

.list-group-item.active {
    background-color: rgba(79, 70, 229, 0.05);
    border-left: 4px solid #4f46e5;
    color: #4f46e5;
    box-shadow: 0 0 15px rgba(79, 70, 229, 0.1);
}

/* 表单样式 */
.form-control, .form-select {
    background-color: #ffffff;
    border: 1px solid #e5e7eb;
    color: #4b5563;
    transition: all 0.3s ease;
    border-radius: 10px;
    padding: 10px 15px;
}

.form-control:focus, .form-select:focus {
    border-color: #4f46e5;
    box-shadow: 0 0 0 0.25rem rgba(79, 70, 229, 0.15);
    background-color: #ffffff;
}

.form-label {
    color: #4b5563;
    margin-bottom: 0.5rem;
    font-weight: 500;
}

/* 按钮样式 */
.btn {
    border-radius: 10px;
    font-weight: 500;
    padding: 10px 20px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: all 0.5s ease;
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: #4f46e5;
    border: none;
    box-shadow: 0 4px 6px rgba(79, 70, 229, 0.2);
}

.btn-primary:hover {
    background: #4338ca;
    transform: translateY(-2px);
    box-shadow: 0 6px 10px rgba(79, 70, 229, 0.3);
}

.btn-success {
    background: #10b981;
    border: none;
    box-shadow: 0 4px 6px rgba(16, 185, 129, 0.2);
}

.btn-success:hover {
    background: #059669;
    transform: translateY(-2px);
    box-shadow: 0 6px 10px rgba(16, 185, 129, 0.3);
}

.btn-secondary {
    background: #6b7280;
    border: none;
}

.btn-secondary:hover {
    background: #4b5563;
}

.btn-danger {
    background: #ef4444;
    border: none;
    box-shadow: 0 4px 6px rgba(239, 68, 68, 0.2);
}

.btn-danger:hover {
    background: #dc2626;
    transform: translateY(-2px);
    box-shadow: 0 6px 10px rgba(239, 68, 68, 0.3);
}

/* 游戏配置区域 */
.game-config-section {
    margin-bottom: 20px;
    padding: 20px;
    background-color: #f9fafb;
    border-radius: 16px;
    border: 1px solid rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

.game-config-section:hover {
    background-color: #ffffff;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.game-config-section h4 {
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    padding-bottom: 10px;
    margin-bottom: 20px;
    color: #4f46e5;
    font-weight: 600;
}

/* 配置状态指示器 */
.badge {
    padding: 6px 10px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 500;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
    animation: fadeIn 0.5s ease-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: scale(0.8); }
    to { opacity: 1; transform: scale(1); }
}

.bg-success {
    background: #10b981 !important;
}

.bg-danger {
    background: #ef4444 !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 15px;
        margin-top: 10px;
    }

    h1 {
        font-size: 1.5rem;
    }

    .btn {
        padding: 8px 15px;
    }
}

/* 门设置区域样式 */
.door-list-container {
    background-color: #f9fafb;
    border-radius: 16px;
    padding: 15px;
    margin-bottom: 15px;
}

.door-item {
    background-color: #ffffff;
    border-radius: 10px;
    padding: 12px;
    margin-bottom: 10px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: all 0.3s ease;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.03);
}

.door-item:hover {
    background-color: #f9fafb;
    transform: translateX(5px);
    box-shadow: 0 5px 10px rgba(0, 0, 0, 0.05);
}

.door-item .door-name {
    font-weight: 500;
    color: #4b5563;
}

.door-item .door-status {
    font-size: 0.8rem;
    padding: 3px 8px;
    border-radius: 10px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
    color: #ffffff;
}

.door-item .door-status.locked {
    background: #ef4444;
}

.door-item .door-status.unlocked {
    background: #10b981;
}

.door-item .door-price {
    font-style: italic;
    color: #eab308;
    margin-left: 8px;
}

.add-door-btn {
    margin-top: 10px;
    width: 100%;
}

/* 模态框样式 */
.modal-content {
    background-color: #ffffff;
    border: none;
    box-shadow: 0 15px 25px rgba(0, 0, 0, 0.1);
    border-radius: 20px;
}

.modal-header {
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    padding: 20px;
    border-radius: 20px 20px 0 0;
}

.modal-footer {
    border-top: 1px solid rgba(0, 0, 0, 0.05);
    padding: 20px;
    border-radius: 0 0 20px 20px;
}

.modal-title {
    color: #4f46e5;
}

/* 自定义开关样式 */
.form-switch .form-check-input {
    width: 3em;
    height: 1.5em;
}

.form-check-input {
    background-color: rgba(239, 68, 68, 0.5);
    border-color: rgba(239, 68, 68, 0.7);
}

.form-check-input:checked {
    background-color: rgba(16, 185, 129, 0.8);
    border-color: rgba(16, 185, 129, 0.9);
}

/* 动画效果 */
@keyframes slideInFromLeft {
    0% { transform: translateX(-50px); opacity: 0; }
    100% { transform: translateX(0); opacity: 1; }
}

@keyframes slideInFromRight {
    0% { transform: translateX(50px); opacity: 0; }
    100% { transform: translateX(0); opacity: 1; }
}

.animate-left {
    animation: slideInFromLeft 0.5s ease-out forwards;
}

.animate-right {
    animation: slideInFromRight 0.5s ease-out forwards;
}

.delay-1 { animation-delay: 0.1s; }
.delay-2 { animation-delay: 0.2s; }
.delay-3 { animation-delay: 0.3s; }
.delay-4 { animation-delay: 0.4s; }
.delay-5 { animation-delay: 0.5s; }

/* 选区域工具按钮样式 */
.region-selector-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #4f46e5, #6366f1);
    color: white;
    border-radius: 10px;
    margin-top: 15px;
    box-shadow: 0 4px 6px rgba(79, 70, 229, 0.2);
    transition: all 0.3s ease;
}

.region-selector-btn:hover {
    background: linear-gradient(135deg, #4338ca, #4f46e5);
    transform: translateY(-2px);
    box-shadow: 0 6px 10px rgba(79, 70, 229, 0.3);
}

.region-selector-btn i {
    margin-right: 8px;
    font-size: 1.1em;
}

/* Toast通知样式 */
.toast {
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.toast-header {
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    background-color: rgba(255, 255, 255, 0.85);
}

.toast-body {
    background-color: white;
    border-radius: 0 0 10px 10px;
}

/* 僵尸生成点列表样式 */
.zombie-spawns-container {
    max-height: 300px;
    overflow-y: auto;
    border-radius: 10px;
    background-color: #fafafa;
    padding: 10px;
    margin-bottom: 15px;
    box-shadow: inset 0 0 10px rgba(0, 0, 0, 0.05);
}

.spawn-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 15px;
    background-color: #ffffff;
    border-radius: 8px;
    margin-bottom: 8px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    border-left: 3px solid #10b981;
}

.spawn-item:hover {
    transform: translateX(5px);
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
    background-color: #f0fff4;
}

.spawn-item .spawn-name {
    font-weight: 600;
    color: #1f2937;
}

.spawn-item .spawn-info {
    display: flex;
    align-items: center;
}

.spawn-item .spawn-location {
    margin-left: 8px;
    font-size: 0.85em;
}

/* 回合模式样式 */
.round-mode-card {
    border: 1px solid #ddd;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    transition: all 0.3s ease;
}

.round-mode-card:hover {
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.round-mode-card .card-header {
    background-color: #007bff;
    color: white;
    font-weight: bold;
}

/* 回合模式概览卡片样式 */
.round-modes-summary {
    border: 1px solid #e3f2fd;
    background: linear-gradient(135deg, #f8f9fa 0%, #e3f2fd 100%);
    transition: all 0.3s ease;
}

.round-modes-summary:hover {
    box-shadow: 0 4px 12px rgba(0, 123, 255, 0.15);
    transform: translateY(-2px);
}

.round-modes-summary .card-body {
    padding: 1rem 1.25rem;
}

.round-modes-summary h6 {
    color: #1976d2;
    font-weight: 600;
}

.round-modes-summary .text-muted {
    color: #546e7a !important;
}

.spawn-mode-item {
    padding: 10px;
    margin-bottom: 10px;
    border: 1px solid #eee;
    border-radius: 5px;
    background-color: #f9f9f9;
    transition: all 0.2s ease;
}

.spawn-mode-item:hover {
    background-color: #f0f0f0;
    border-color: #ddd;
}

.spawn-mode-item .spawn-name {
    font-weight: bold;
    color: #333;
}

/* 怪物图标 */
.monster-icon {
    width: 32px;
    height: 32px;
    margin-right: 8px;
    background-size: contain;
    background-repeat: no-repeat;
    display: inline-block;
    vertical-align: middle;
}

.monster-icon.zombie {
    background-image: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCA1MTIgNTEyIj48cGF0aCBmaWxsPSIjNWZhODJlIiBkPSJNNDM0LjcsMTY0LjFMMzE4LjQsNTE4LjJjLTMuMywxMC4zLTEyLjksMTcuMi0yMy42LDE3LjJIMjE3LjJjLTEwLjcsMC0yMC4zLTYuOS0yMy42LTE3LjJMNzcuMywxNjQuMUw0My43LDE1N2MtNS4xLTEuMS04LjktNS42LTguOS0xMC45VjMzYy0wLjMtMi42LDAuNi01LjIsMi40LTcuMWMyLjItMiw1LjItMyw4LjItMi42bDM3OCwyOC4yYzUuMSwwLjQsOSw0LjYsOSwxMC4zdjg0LjNjMCw1LjctNC4zLDEwLjQtOS45LDEwLjhMNDM0LjcsMTY0LjF6Ii8+PHBhdGggZmlsbD0iI2ZmZiIgZD0iTTIzMC4zLDE0OS43Yy0zMS4yLDAtNTYuNS0yNS4zLTU2LjUtNTYuNXMyNS4zLTU2LjUsNTYuNS01Ni41czU2LjUsMjUuMyw1Ni41LDU2LjVTMjYxLjUsMTQ5LjcsMjMwLjMsMTQ5Ljd6IE0yMzAuMyw1NS4yYy0yMC45LDAtMzgsMTctMzgsMzhzMTcsMzgsMzgsMzhzMzgtMTcsMzgtMzhTMjUxLjMsNTUuMiwyMzAuMyw1NS4yeiIvPjxjaXJjbGUgZmlsbD0iIzNlM2QzZCIgY3g9IjIzMC4zIiBjeT0iOTMuMiIgcj0iMTguNSIvPjwvc3ZnPg==');
}

.monster-icon.skeleton {
    background-image: url('data:image/svg+xml;base64,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');
}

.monster-icon.special {
    background-image: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCA1MTIgNTEyIj48cGF0aCBmaWxsPSIjYzQyYTJhIiBkPSJNMzE5LjksMjEyLjNjLTMuMy0xLjktNy40LTEuOS0xMC44LDBsLTQ3LjUsMjcuNWMtMy4zLDEuOS01LjQsNS40LTUuNCw5LjJ2NTVjMCwzLjgsMiw3LjMsNS40LDkuMmw0Ny41LDI3LjVjMy4zLDEuOSw3LjQsMS45LDEwLjgsMGw0Ny41LTI3LjVjMy4zLTEuOSw1LjQtNS40LDUuNC05LjJ2LTU1YzAtMy44LTItNy4zLTUuNC05LjJMMzE5LjksMjEyLjN6Ii8+PHBhdGggZmlsbD0iIzYwN2QzYiIgZD0iTTIwNy45LDU5LjNjLTMuMy0xLjktNy40LTEuOS0xMC44LDBsLTQ3LjUsMjcuNWMtMy4zLDEuOS01LjQsNS40LTUuNCw5LjJ2NTVjMCwzLjgsMiw3LjMsNS40LDkuMmw0Ny41LDI3LjVjMy4zLDEuOSw3LjQsMS45LDEwLjgsMGw0Ny41LTI3LjVjMy4zLTEuOSw1LjQtNS40LDUuNC05LjJ2LTU1YzAtMy44LTItNy4zLTUuNC05LjJMMjA3LjksNTkuM3oiLz48cGF0aCBmaWxsPSIjNjA3ZDhiIiBkPSJNNDMxLjksNTkuM2MtMy4zLTEuOS03LjQtMS45LTEwLjgsMGwtNDcuNSwyNy41Yy0zLjMsMS45LTUuNCw1LjQtNS40LDkuMnY1NWMwLDMuOCwyLDcuMyw1LjQsOS4ybDQ3LjUsMjcuNWMzLjMsMS45LDcuNCwxLjksMTAuOCwwbDQ3LjUtMjcuNWMzLjMtMS45LDUuNC01LjQsNS40LTkuMnYtNTVjMC0zLjgtMi03LjMtNS40LTkuMkw0MzEuOSw1OS4zeiIvPjxwYXRoIGZpbGw9IiM0MjcxYmIiIGQ9Ik0zMS45LDIxMi4zYy0zLjMtMS45LTcuNC0xLjktMTAuOCwwbC00Ny41LDI3LjVjLTMuMywxLjktNS40LDUuNC01LjQsOS4ydjU1YzAsMy44LDIsNy4zLDUuNCw5LjJsNDcuNSwyNy41YzMuMywxLjksNy40LDEuOSwxMC44LDBsNDcuNS0yNy41YzMuMy0xLjksNS40LTUuNCw1LjQtOS4ydi01NWMwLTMuOC0yLTcuMy01LjQtOS4yTDMxLjksMjEyLjN6Ii8+PHBhdGggZmlsbD0iI2Q2YTcyOCIgZD0iTTIwNy45LDM2NS4zYy0zLjMtMS45LTcuNC0xLjktMTAuOCwwbC00Ny41LDI3LjVjLTMuMywxLjktNS40LDUuNC01LjQsOS4ydjU1YzAsMy44LDIsNy4zLDUuNCw5LjJsNDcuNSwyNy41YzMuMywxLjksNy40LDEuOSwxMC44LDBsNDcuNS0yNy41YzMuMy0xLjksNS40LTUuNCw1LjQtOS4ydi01NWMwLTMuOC0yLTcuMy01LjQtOS4yTDIwNy45LDM2NS4zeiIvPjwvc3ZnPg==');
}

.monster-icon.spider {
    background-image: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCA1MTIgNTEyIj48cGF0aCBmaWxsPSIjMzMzIiBkPSJNNDgwLDE5Mi41YzAsNzAuNy01Ny4zLDEyOC0xMjgsMTI4aC0xOTJjLTcwLjcsMC0xMjgtNTcuMy0xMjgtMTI4czU3LjMtMTI4LDEyOC0xMjhoMTkyQzQyMi43LDY0LjUsNDgwLDEyMS44LDQ4MCwxOTIuNXoiLz48cGF0aCBmaWxsPSIjMzMzIiBkPSJNMCwyNTYuNWMwLTM1LjMsMjguNy02NCw2NC02NGgyMjF2MTI4SDY0QzI4LjcsMzIwLjUsMCwyOTEuOCwwLDI1Ni41eiIvPjxwYXRoIGZpbGw9IiMzMzMiIGQ9Ik01MTIsMjU2LjVjMCwzNS4zLTI4LjcsNjQtNjQsNjRIMjI3di0xMjhoMjIxQzQ4My4zLDE5Mi41LDUxMiwyMjEuMiw1MTIsMjU2LjV6Ii8+PHBhdGggZmlsbD0iIzMzMyIgZD0iTTI4OCwzMjAuNXYxOTJoLTY0di0xOTJIMjg4eiIvPjxwYXRoIGZpbGw9IiMzMzMiIGQ9Ik0yODgsNjQuNXYtNjRoLTY0djY0SDI4OHoiLz48cGF0aCBmaWxsPSIjMzMzIiBkPSJNNjQsMTkyLjVjLTM1LjMsMC02NCwyOC43LTY0LDY0czI4LjcsNjQsNjQsNjRWMTkyLjV6Ii8+PHBhdGggZmlsbD0iIzMzMyIgZD0iTTQ0OCwxOTIuNXYxMjhjMzUuMywwLDY0LTI4LjcsNjQtNjRTNDgzLjMsMTkyLjUsNDQ4LDE5Mi41eiIvPjxwYXRoIGZpbGw9IiNmZmYiIGQ9Ik0xNjAsMTkyLjVjMCwxNy43LTE0LjMsMzItMzIsMzJzLTMyLTE0LjMtMzItMzJzMTQuMy0zMiwzMi0zMlMxNjAsMTc0LjgsMTYwLDE5Mi41eiIvPjxwYXRoIGZpbGw9IiNmZmYiIGQ9Ik00MTYsMTkyLjVjMCwxNy43LTE0LjMsMzItMzIsMzJzLTMyLTE0LjMtMzItMzJzMTQuMy0zMiwzMi0zMlM0MTYsMTc0LjgsNDE2LDE5Mi41eiIvPjwvc3ZnPg==');
}

/* 动画效果 */
.animate-left, .animate-right, .animate-top, .animate-bottom {
    opacity: 0;
    animation-fill-mode: forwards;
}

.animate-left {
    animation: slideInLeft 0.5s ease forwards;
}

.animate-right {
    animation: slideInRight 0.5s ease forwards;
}

.animate-top {
    animation: slideInTop 0.5s ease forwards;
}

.animate-bottom {
    animation: slideInBottom 0.5s ease forwards;
}

.delay-1 {
    animation-delay: 0.1s;
}

.delay-2 {
    animation-delay: 0.2s;
}

.delay-3 {
    animation-delay: 0.3s;
}

.delay-4 {
    animation-delay: 0.4s;
}

.delay-5 {
    animation-delay: 0.5s;
}

.delay-6 {
    animation-delay: 0.6s;
}

.delay-7 {
    animation-delay: 0.7s;
}

.delay-8 {
    animation-delay: 0.8s;
}

.delay-9 {
    animation-delay: 0.9s;
}

@keyframes slideInLeft {
    from {
        transform: translateX(-30px);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideInRight {
    from {
        transform: translateX(30px);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideInTop {
    from {
        transform: translateY(-30px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes slideInBottom {
    from {
        transform: translateY(30px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

/* 回合模式模态框按钮样式 */
.modal-footer .btn {
    position: relative;
    z-index: 3;  /* 确保按钮在最上层 */
}

.modal-footer .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s, box-shadow 0.2s;
}

.modal-footer .btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 确保删除按钮的点击区域不被干扰 */
#delete-round-mode-btn {
    pointer-events: auto !important;
    cursor: pointer !important;
    z-index: 5;
}

/* 确保保存按钮的点击区域不被干扰 */
#confirm-round-mode {
    pointer-events: auto !important;
    cursor: pointer !important;
    z-index: 5;
}

/* 模态框内部的表单元素不应影响按钮 */
.modal-content form {
    pointer-events: auto;
}

/* 按钮点击动画效果 */
.btn-clicked {
    animation: btn-click-animation 0.3s forwards;
}

@keyframes btn-click-animation {
    0% { transform: scale(1); }
    50% { transform: scale(0.95); }
    100% { transform: scale(1); }
}

/* 浮动按钮样式 */
.btn-floating {
    position: fixed;
    bottom: 30px;
    right: 30px;
    z-index: 1050;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    border-radius: 50px;
    padding: 15px 25px;
    font-weight: bold;
    transform: scale(1);
    transition: all 0.3s ease;
}

.btn-floating:hover {
    transform: scale(1.05);
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.25);
}

.btn-floating:active {
    transform: scale(0.98);
}

/* 窗户列表样式 */
.window-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 15px;
    background-color: #ffffff;
    border-radius: 10px;
    margin-bottom: 10px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    border-left: 3px solid #4f46e5;
}

.window-item:hover {
    transform: translateX(5px);
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
    background-color: #f0f7ff;
}

.window-item .window-name {
    font-weight: 600;
    color: #1f2937;
}

.window-item .window-controls {
    display: flex;
    align-items: center;
}

/* 装备槽位样式 */
.inventory-grid {
    display: grid;
    grid-template-columns: repeat(9, 1fr);
    gap: 10px;
    padding: 15px;
    background-color: #f0f0f0;
    border-radius: 10px;
    margin-bottom: 20px;
}

.inventory-slot {
    width: 100%;
    aspect-ratio: 1;
    background-color: #d0d0d0;
    border: 2px solid #a0a0a0;
    border-radius: 5px;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    cursor: pointer;
    transition: all 0.2s ease;
}

.inventory-slot:hover {
    background-color: #e0e0e0;
    transform: scale(1.05);
}

.inventory-slot.weapon {
    border-color: #ef4444;
    background-color: rgba(239, 68, 68, 0.1);
}

.inventory-slot.item {
    border-color: #10b981;
    background-color: rgba(16, 185, 129, 0.1);
}

.inventory-slot.special {
    border-color: #f59e0b;
    background-color: rgba(245, 158, 11, 0.1);
}

.slot-number {
    position: absolute;
    top: 2px;
    left: 5px;
    font-size: 0.7rem;
    font-weight: bold;
    color: #6b7280;
}

.slot-icon {
    width: 70%;
    height: 70%;
    background-size: contain;
    background-position: center;
    background-repeat: no-repeat;
}

/* 装备槽位列表样式 */
.equipment-slots-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.equipment-slot-item {
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 10px;
    transition: all 0.3s ease;
    background-color: #ffffff;
}

.equipment-slot-item:hover {
    transform: translateX(5px);
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

.equipment-slot-item h6 {
    color: #4f46e5;
    font-weight: 600;
}

.item-icon {
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f3f4f6;
    border-radius: 6px;
    font-size: 1.2rem;
}

.item-icon i {
    color: #6b7280;
}

.item-icon i.text-primary {
    color: #4f46e5;
}

.item-icon i.text-danger {
    color: #ef4444;
}

.item-icon i.text-warning {
    color: #f59e0b;
}

.item-icon i.text-light {
    color: #e5e7eb;
}

.item-icon i.text-secondary {
    color: #6b7280;
}

/* 装备摘要和批量操作样式 */
.equipment-summary, .zombie-count-summary {
    background: linear-gradient(135deg, #f9fafb, #f3f4f6);
    border-left: 4px solid #4f46e5;
    transition: all 0.3s ease;
}

.equipment-summary:hover, .zombie-count-summary:hover {
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

.equipment-summary h6, .zombie-count-summary h6 {
    color: #4f46e5;
    font-weight: 600;
}

.equipment-summary p, .zombie-count-summary p {
    color: #6b7280;
}

.equipment-batch-actions {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.equipment-batch-actions .btn {
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.equipment-batch-actions .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.equipment-batch-actions .btn i {
    margin-right: 5px;
}

/* 僵尸数量设置样式 */
.highlight-input {
    animation: highlight-pulse 2s ease-in-out;
}

@keyframes highlight-pulse {
    0% { background-color: rgba(79, 70, 229, 0.1); }
    50% { background-color: rgba(79, 70, 229, 0.3); }
    100% { background-color: rgba(79, 70, 229, 0); }
}

/* 批量设置模态框样式 */
#batch-zombie-pattern-inputs {
    background-color: rgba(79, 70, 229, 0.05);
    border-radius: 8px;
    padding: 15px;
    transition: all 0.3s ease;
}

#batch-zombie-count-modal .dropdown-item {
    cursor: pointer;
    transition: all 0.2s ease;
}

#batch-zombie-count-modal .dropdown-item:hover {
    background-color: rgba(79, 70, 229, 0.1);
}

#batch-zombie-count-modal .form-text {
    font-size: 0.8rem;
    margin-top: 5px;
}

#batch-zombie-count-modal .input-group-text {
    background-color: #f8f9fa;
    color: #495057;
}

#batch-zombie-count-modal .form-check-input:checked {
    background-color: #4f46e5;
    border-color: #4f46e5;
}

/* 折叠面板样式 */
.collapse {
    transition: all 0.3s ease;
}

.collapse.show {
    animation: fadeInDown 0.5s ease forwards;
}

@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 按钮闪烁效果 */
@keyframes pulse-button {
    0% {
        box-shadow: 0 0 0 0 rgba(239, 68, 68, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(239, 68, 68, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(239, 68, 68, 0);
    }
}

.btn-pulse {
    animation: pulse-button 1.5s infinite;
}

/* 花体字母样式 */
.fancy-letter {
    font-family: 'Times New Roman', Times, serif;
    font-weight: bold;
    font-size: 1.5em;
    color: #4f46e5;
    margin-right: 8px;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
    display: inline-block;
    background: linear-gradient(135deg, #4f46e5, #10b981);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    padding: 0 5px;
}

/* 修复z-index和点击事件 */
.modal-dialog {
    z-index: 1051 !important;
}

.btn {
    position: relative;
    z-index: 1;
}

button,
a.btn,
input[type="button"],
input[type="submit"] {
    position: relative;
    pointer-events: auto !important;
    cursor: pointer !important;
}

/* 确保所有可点击元素可以正常工作 */
.list-group-item,
.dropdown-item,
.form-check-input,
.form-control {
    pointer-events: auto !important;
}