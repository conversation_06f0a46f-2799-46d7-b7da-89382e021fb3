<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>僵尸末日网页编辑器 - 怪物预设管理</title>
    <!-- 引入Bootstrap CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <!-- 引入图标 -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <!-- 引入Google字体 -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Microsoft+YaHei:wght@300;400;500;600;700&display=swap">
    <!-- 引入Vue 3 -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <!-- 引入身份验证脚本 -->
    <script src="js/auth.js"></script>
    <!-- 引入自定义样式 -->
    <link rel="stylesheet" href="css/monster-preset-vue.css">
    <!-- 引入字体重置样式 (必须在最后) -->
    <link rel="stylesheet" href="css/font-reset.css">
</head>
<body>
    <div id="app">
        <div v-if="loading" class="loading-overlay">
            <div class="loading-spinner"></div>
            <div class="loading-message">{{ loadingMessage }}</div>
        </div>

        <div class="app-container">
            <!-- 侧边栏 -->
            <div class="sidebar">
                <div class="sidebar-header">
                    <div class="sidebar-logo">
                        <span class="fancy-letter">DZ</span>
                        <span>僵尸末日网页编辑器</span>
                    </div>
                </div>

                <div class="sidebar-nav">
                    <div class="nav-item">
                        <a href="index.html" class="nav-link">
                            <i class="bi bi-house"></i>
                            <span>主页</span>
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="equipment-vue.html" class="nav-link">
                            <i class="bi bi-shield"></i>
                            <span>初始装备设置</span>
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="#" class="nav-link active">
                            <i class="bi bi-collection"></i>
                            <span>怪物预设管理</span>
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="zombie-spawn-vue.html" class="nav-link">
                            <i class="bi bi-bug"></i>
                            <span>回合出怪设置</span>
                        </a>
                    </div>
                </div>
            </div>

            <!-- 主内容区域 -->
            <div class="main-content">
                <!-- 顶部导航栏 -->
                <div class="top-navbar">
                    <div class="page-title">
                        <i class="bi bi-collection"></i>
                        <span>僵尸末日网页编辑器 - 怪物预设管理</span>
                    </div>
                    <div class="navbar-actions">
                        <button class="btn btn-outline-danger" @click="logout">
                            <i class="bi bi-box-arrow-right"></i>
                            <span>退出登录</span>
                        </button>
                    </div>
                </div>

                <!-- 预设列表 -->
                <div class="dashboard-card animate-slide-up">
                <div class="card-header">
                    <div class="card-title">
                        <i class="bi bi-list-ul"></i>
                        <span>怪物预设列表</span>
                    </div>
                    <div class="card-actions">
                        <button class="btn btn-primary" @click="openCreatePresetModal">
                            <i class="bi bi-plus-circle"></i>
                            创建新预设
                        </button>
                    </div>
                </div>
                <div class="preset-list">
                    <div v-if="presets.length === 0" class="empty-state">
                        <i class="bi bi-emoji-frown"></i>
                        <p>暂无预设，点击"创建新预设"按钮开始创建</p>
                    </div>
                    <div v-else class="preset-items">
                        <div v-for="(preset, index) in presets" :key="preset.name" class="preset-item">
                            <div class="preset-info">
                                <h3 class="preset-name">{{ preset.name }}</h3>
                                <p class="preset-description">{{ preset.description || '无描述' }}</p>
                                <div class="preset-monsters">
                                    <span class="monster-count">包含 {{ preset.monsters.length }} 种怪物</span>
                                    <div class="monster-list">
                                        <div v-for="monster in preset.monsters.slice(0, 3)" :key="monster.monsterId" class="monster-tag">
                                            {{ getMonsterName(monster.monsterType, monster.monsterId) }} × {{ monster.count }}
                                        </div>
                                        <div v-if="preset.monsters.length > 3" class="monster-tag more">
                                            +{{ preset.monsters.length - 3 }} 种
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="preset-actions">
                                <button class="btn btn-sm btn-outline" @click="viewPresetDetails(preset)">
                                    <i class="bi bi-eye"></i>
                                    查看
                                </button>
                                <button class="btn btn-sm btn-primary" @click="editPreset(preset, index)">
                                    <i class="bi bi-pencil"></i>
                                    编辑
                                </button>
                                <button class="btn btn-sm btn-danger" @click="confirmDeletePreset(preset, index)">
                                    <i class="bi bi-trash"></i>
                                    删除
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        <!-- 创建预设模态框 -->
        <div v-if="showCreateModal" class="modal-overlay">
            <div class="modal-container">
                <div class="modal-header">
                    <h2>{{ isEditing ? '编辑预设' : '创建新预设' }}</h2>
                    <button class="btn-close" @click="closeCreateModal">
                        <i class="bi bi-x-lg"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="form-group">
                        <label for="preset-name">预设名称</label>
                        <input type="text" id="preset-name" v-model="newPreset.name" placeholder="输入预设名称">
                        <small class="form-text text-muted">
                            <i class="bi bi-info-circle"></i>
                            预设名称不能包含以下字符: &lt; &gt; : " | ? * \ /
                        </small>
                    </div>
                    <div class="form-group">
                        <label for="preset-description">预设描述</label>
                        <textarea id="preset-description" v-model="newPreset.description" placeholder="输入预设描述（可选）"></textarea>
                    </div>
                    <div class="form-group">
                        <label>怪物列表</label>
                        <div class="monster-add-form">
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="monster-type">怪物类型</label>
                                    <select id="monster-type" v-model="currentMonster.monsterType">
                                        <option value="zombie">普通僵尸</option>
                                        <option value="entity">实体怪物</option>
                                        <option value="npc">感染者(NPC)</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="monster-id">怪物ID</label>
                                    <select id="monster-id" v-model="currentMonster.monsterId">
                                        <option v-for="monster in getMonsterOptions(currentMonster.monsterType)"
                                                :key="monster.id"
                                                :value="monster.id">
                                            {{ monster.name }}
                                        </option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="monster-count">数量</label>
                                    <input type="number" id="monster-count" v-model="currentMonster.count" min="1">
                                </div>
                                <button class="btn btn-primary" @click="addMonsterToPreset">
                                    <i class="bi bi-plus"></i>
                                    添加
                                </button>
                            </div>
                        </div>
                        <div class="monster-list-container">
                            <div v-if="newPreset.monsters.length === 0" class="empty-state">
                                <p>暂无怪物，请添加至少一种怪物</p>
                            </div>
                            <div v-else class="monster-items">
                                <div v-for="(monster, index) in newPreset.monsters" :key="index" class="monster-item">
                                    <div class="monster-info">
                                        <span class="monster-name">{{ getMonsterName(monster.monsterType, monster.monsterId) }}</span>
                                        <span class="monster-count">× {{ monster.count }}</span>
                                    </div>
                                    <button class="btn-remove" @click="removeMonsterFromPreset(index)">
                                        <i class="bi bi-x"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" @click="closeCreateModal">取消</button>
                    <button class="btn btn-primary" @click="savePreset" :disabled="!canSavePreset">
                        {{ isEditing ? '更新预设' : '保存预设' }}
                    </button>
                </div>
            </div>
        </div>

        <!-- 预设详情模态框 -->
        <div v-if="showDetailsModal" class="modal-overlay">
            <div class="modal-container">
                <div class="modal-header">
                    <h2>预设详情: {{ selectedPreset.name }}</h2>
                    <button class="btn-close" @click="closeDetailsModal">
                        <i class="bi bi-x-lg"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="preset-detail-info">
                        <h3>描述</h3>
                        <p>{{ selectedPreset.description || '无描述' }}</p>
                        <h3>包含怪物</h3>
                        <div class="monster-detail-list">
                            <div v-for="(monster, index) in selectedPreset.monsters" :key="index" class="monster-detail-item">
                                <div class="monster-detail-info">
                                    <span class="monster-detail-name">{{ getMonsterName(monster.monsterType, monster.monsterId) }}</span>
                                    <span class="monster-detail-count">× {{ monster.count }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" @click="closeDetailsModal">关闭</button>
                </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 引入Bootstrap JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- 引入自定义脚本 -->
    <script src="js/monster-preset-vue.js"></script>
</body>
</html>
