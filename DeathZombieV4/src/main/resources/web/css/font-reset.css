/* 全局字体重置 - 确保所有页面字体大小一致 */

/* 重置body和html的字体 */
html, body {
    font-size: 14px !important;
    font-family: 'Microsoft YaHei', Arial, sans-serif !important;
}

/* 重置所有文本元素的字体大小为14px */
p, span, div, a, li, td, th, label, input, select, textarea, button {
    font-size: 14px !important;
    font-family: 'Microsoft YaHei', Arial, sans-serif !important;
}

/* 保持相对大小的元素 */
h1 { font-size: 2rem !important; }
h2 { font-size: 1.75rem !important; }
h3 { font-size: 1.5rem !important; }
h4 { font-size: 1.25rem !important; }
h5 { font-size: 1.125rem !important; }
h6 { font-size: 1rem !important; }

/* 小字体元素 */
small, .small { font-size: 0.875rem !important; }
.text-xs { font-size: 0.75rem !important; }
.text-sm { font-size: 0.875rem !important; }
.text-base { font-size: 1rem !important; }
.text-lg { font-size: 1.125rem !important; }
.text-xl { font-size: 1.25rem !important; }

/* 按钮字体 */
button, .btn {
    font-size: 14px !important;
}

/* 输入框字体 */
input, select, textarea {
    font-size: 14px !important;
}

/* 导航链接字体 */
.nav-link, .nav-link span {
    font-size: 14px !important;
}

/* 卡片标题保持相对大小 */
.card-title {
    font-size: 1.125rem !important;
}

/* 页面标题保持相对大小 */
.page-title {
    font-size: 1.25rem !important;
}

/* 侧边栏logo */
.sidebar-logo {
    font-size: 1rem !important;
}

/* 表单标签 */
label {
    font-size: 14px !important;
}

/* 列表项 */
li, ul, ol {
    font-size: 14px !important;
}

/* 段落 */
p {
    font-size: 14px !important;
}

/* 表格 */
table, td, th {
    font-size: 14px !important;
}

/* 模态框 */
.modal-body, .modal-header, .modal-footer {
    font-size: 14px !important;
}

/* 工具提示 */
.tooltip, .tooltip-inner {
    font-size: 12px !important;
}

/* 徽章 */
.badge {
    font-size: 0.75rem !important;
}

/* 特定组件的字体重置 */
.nav-link, .nav-link span, .nav-item span {
    font-size: 14px !important;
}

.sidebar-logo span {
    font-size: 14px !important;
}

.card-body, .card-body p, .card-body span, .card-body div {
    font-size: 14px !important;
}

.form-group label, .form-group input, .form-group select, .form-group textarea {
    font-size: 14px !important;
}

.btn, .btn span, .btn i {
    font-size: 14px !important;
}

/* Bootstrap组件重置 */
.form-control, .form-select, .form-label {
    font-size: 14px !important;
}

.list-group-item {
    font-size: 14px !important;
}

/* 确保所有子元素都继承14px字体 */
.dashboard-card *, .sidebar *, .main-content *, .modal * {
    font-size: inherit !important;
}

/* 重置可能的rem/em单位 */
.text-sm, .small {
    font-size: 12px !important;
}

.text-lg {
    font-size: 16px !important;
}

.text-xl {
    font-size: 18px !important;
}
