/* 装备设置页面样式 - 现代化界面 */
body {
    background-color: #f8f9fa;
    padding-bottom: 60px;
    font-family: 'Microsoft YaHei', Arial, sans-serif !important;
    font-size: 14px !important;
    color: #374151;
    margin: 0;
}

/* 主布局 */
.app-container {
    display: flex;
    min-height: 100vh;
}

/* 侧边栏样式 */
.sidebar {
    width: 240px;
    background-color: #ffffff;
    border-right: 1px solid #e5e7eb;
    padding: 1.5rem 0;
    position: fixed;
    height: 100vh;
    overflow-y: auto;
    z-index: 30;
}

.sidebar-header {
    padding: 0 1.25rem;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
}

.sidebar-logo {
    font-size: 1.25rem;
    font-weight: 600;
    color: #111827;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

/* 花体字母样式 */
.fancy-letter {
    font-family: 'Times New Roman', Times, serif;
    font-weight: bold;
    font-size: 1.5em;
    color: #4f46e5;
    margin-right: 8px;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
    display: inline-block;
    background: linear-gradient(135deg, #4f46e5, #10b981);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    padding: 0 5px;
}

.sidebar-nav {
    padding: 0 0.75rem;
}

.nav-item {
    margin-bottom: 0.25rem;
}

.nav-link {
    display: flex;
    align-items: center;
    padding: 0.75rem 1rem;
    color: #6b7280;
    border-radius: 0.5rem;
    text-decoration: none;
    transition: all 0.2s;
}

.nav-link:hover {
    background-color: #f3f4f6;
    color: #111827;
}

.nav-link.active {
    background-color: #f3f4f6;
    color: #111827;
    font-weight: 500;
}

.nav-link i {
    margin-right: 0.75rem;
    font-size: 1.25rem;
}

/* 主内容区域 */
.main-content {
    flex: 1;
    margin-left: 240px;
    padding: 1.5rem;
}

/* 顶部导航栏 */
.top-navbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 1.5rem;
    background-color: #ffffff;
    border-bottom: 1px solid #e5e7eb;
    position: sticky;
    top: 0;
    z-index: 20;
}

.page-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: #111827;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.navbar-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}

/* 卡片样式 */
.dashboard-card {
    background-color: #ffffff;
    border-radius: 0.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    padding: 1.5rem;
    margin-bottom: 1.5rem;
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.card-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: #111827;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.stat-card {
    background-color: #ffffff;
    border-radius: 0.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    padding: 1.5rem;
    display: flex;
    flex-direction: column;
}

.stat-icon {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1rem;
}

.stat-icon.green {
    background-color: rgba(16, 185, 129, 0.1);
    color: #10b981;
}

.stat-icon.blue {
    background-color: rgba(59, 130, 246, 0.1);
    color: #3b82f6;
}

.stat-icon.purple {
    background-color: rgba(139, 92, 246, 0.1);
    color: #8b5cf6;
}

.stat-icon.red {
    background-color: rgba(239, 68, 68, 0.1);
    color: #ef4444;
}

.stat-value {
    font-size: 1.875rem;
    font-weight: 600;
    color: #111827;
    margin-bottom: 0.25rem;
}

.stat-label {
    font-size: 0.875rem;
    color: #6b7280;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.stat-change {
    display: inline-flex;
    align-items: center;
    font-size: 0.875rem;
    margin-top: 0.5rem;
    padding: 0.25rem 0.5rem;
    border-radius: 9999px;
}

.stat-change.positive {
    background-color: rgba(16, 185, 129, 0.1);
    color: #10b981;
}

.stat-change.negative {
    background-color: rgba(239, 68, 68, 0.1);
    color: #ef4444;
}

/* 护甲设置容器 */
.armor-settings-container {
    background-color: #ffffff;
    border-radius: 0.5rem;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.armor-info {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem;
    background-color: rgba(59, 130, 246, 0.1);
    border-radius: 0.375rem;
    margin-bottom: 1rem;
    color: #3b82f6;
    font-size: 0.875rem;
}

.armor-info i {
    font-size: 1.25rem;
}

.armor-slots {
    display: flex;
    justify-content: center;
    gap: 2rem;
    flex-wrap: wrap;
    padding: 1rem 0;
}

.armor-slot-group {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
}

.armor-slot-label {
    font-weight: 500;
    color: #4b5563;
    font-size: 0.875rem;
}

.armor-slot {
    width: 64px;
    height: 64px;
    background-color: rgba(139, 92, 246, 0.1);
    border: 2px solid #8b5cf6;
    border-radius: 0.375rem;
    position: relative;
    cursor: pointer;
    transition: all 0.2s;
    display: flex;
    align-items: center;
    justify-content: center;
}

.armor-slot:hover {
    border-color: #6d28d9;
    transform: scale(1.05);
    box-shadow: 0 0 0 2px rgba(139, 92, 246, 0.3);
}

.armor-slot.has-item {
    background-color: rgba(139, 92, 246, 0.2);
}

.armor-slot img {
    max-width: 80%;
    max-height: 80%;
    object-fit: contain;
    transition: all 0.2s;
}

.armor-slot img:hover {
    transform: scale(1.1);
    filter: brightness(1.1);
}

.clear-armor-btn {
    position: absolute;
    top: -8px;
    right: -8px;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background-color: #ef4444;
    color: white;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: 12px;
    opacity: 0;
    transition: all 0.2s;
    z-index: 10;
}

.armor-slot:hover .clear-armor-btn {
    opacity: 1;
}

.clear-armor-btn:hover {
    background-color: #dc2626;
    transform: scale(1.1);
}

/* 物品栏容器 */
.inventory-container {
    background-color: #ffffff;
    border-radius: 0.5rem;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.inventory-grid {
    display: grid;
    grid-template-columns: repeat(9, 1fr);
    gap: 0.5rem;
}

@media (max-width: 1200px) {
    .inventory-grid {
        grid-template-columns: repeat(6, 1fr);
    }
}

@media (max-width: 768px) {
    .inventory-grid {
        grid-template-columns: repeat(4, 1fr);
    }
}

.inventory-slot {
    width: 64px;
    height: 64px;
    background-color: #f3f4f6;
    border: 2px solid #e5e7eb;
    border-radius: 0.375rem;
    position: relative;
    cursor: pointer;
    transition: all 0.2s;
    display: flex;
    align-items: center;
    justify-content: center;
}

.inventory-slot:hover {
    border-color: #3b82f6;
    transform: scale(1.05);
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.3);
}

.inventory-slot.selected {
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.5);
}

.inventory-slot.drag-over {
    border-color: #10b981;
    box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.5);
    background-color: rgba(16, 185, 129, 0.1);
    transform: scale(1.1);
}

.inventory-slot.has-item {
    background-color: rgba(59, 130, 246, 0.05);
}

.inventory-slot img {
    max-width: 80%;
    max-height: 80%;
    object-fit: contain;
    transition: all 0.2s;
}

.inventory-slot img:hover {
    transform: scale(1.1);
    filter: brightness(1.1);
}

.inventory-slot img:active {
    transform: scale(0.95);
}

.inventory-slot img.draggable-item {
    cursor: grab;
}

.inventory-slot img.draggable-item:active {
    cursor: grabbing;
}

.inventory-slot img.dragging {
    opacity: 0.5;
    border: 2px dashed #3b82f6;
    background-color: rgba(59, 130, 246, 0.1);
}

.slot-number {
    position: absolute;
    top: 2px;
    left: 2px;
    background-color: rgba(17, 24, 39, 0.7);
    color: white;
    font-size: 10px;
    padding: 1px 4px;
    border-radius: 3px;
    font-weight: 500;
    z-index: 5;
}

.slot-item-tooltip {
    position: absolute;
    bottom: -30px;
    left: 50%;
    transform: translateX(-50%);
    background-color: rgba(17, 24, 39, 0.9);
    color: white;
    font-size: 12px;
    padding: 4px 8px;
    border-radius: 4px;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: all 0.2s;
    z-index: 10;
    pointer-events: none;
}

.inventory-slot:hover .slot-item-tooltip {
    opacity: 1;
    visibility: visible;
    bottom: -35px;
}

/* 特殊槽位样式 */
.inventory-slot.hotbar {
    background-color: rgba(59, 130, 246, 0.1);
    border-color: #3b82f6;
}

.inventory-slot.armor {
    background-color: rgba(139, 92, 246, 0.1);
    border-color: #8b5cf6;
}

.inventory-slot.offhand {
    background-color: rgba(16, 185, 129, 0.1);
    border-color: #10b981;
}

/* 物品选择器 */
.item-selector-card {
    position: relative;
    z-index: 25;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e7eb;
    scroll-margin-top: 20px;
}

.item-selector {
    max-height: 500px;
    overflow-y: auto;
    border: 1px solid #e5e7eb;
    border-radius: 0.5rem;
    background-color: #ffffff;
    position: relative;
    z-index: 20;
}

.item-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 0.75rem;
    padding: 0.75rem;
}

.item-option {
    cursor: grab;
    padding: 0.75rem;
    border-radius: 0.5rem;
    transition: all 0.2s;
    display: flex;
    align-items: center;
    border: 2px solid #e5e7eb;
    background-color: #f9fafb;
    position: relative;
    margin: 0.25rem;
}

.item-option:hover {
    background-color: #f3f4f6;
    border-color: #3b82f6;
    transform: translateY(-2px);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    z-index: 10;
}

.item-option:active {
    cursor: grabbing;
    transform: scale(0.98);
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.item-option.dragging {
    opacity: 0.7;
    border-style: dashed;
    border-color: #3b82f6;
    background-color: rgba(59, 130, 246, 0.1);
}

.item-option img {
    width: 40px;
    height: 40px;
    margin-right: 0.75rem;
    object-fit: contain;
    pointer-events: none; /* 防止拖动时触发图片的默认行为 */
}

.item-option-details {
    flex: 1;
}

.item-name {
    font-weight: 500;
    color: #111827;
    margin-bottom: 0.25rem;
}

.item-id {
    font-size: 0.75rem;
    color: #6b7280;
}

.item-category {
    grid-column: 1 / -1;
    background-color: #f3f4f6;
    color: #111827;
    font-weight: 600;
    padding: 0.5rem 0.75rem;
    margin-top: 0.5rem;
    border-radius: 0.375rem;
    border-left: 4px solid #3b82f6;
}

.item-search {
    position: sticky;
    top: 0;
    background-color: #ffffff;
    padding: 1rem;
    z-index: 30;
    border-bottom: 1px solid #e5e7eb;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.search-input {
    position: relative;
    margin-bottom: 1rem;
}

.search-input i {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: #3b82f6;
    font-size: 1rem;
}

.search-input input {
    padding-left: 2.5rem;
    width: 100%;
    border-radius: 0.375rem;
    border: 2px solid #e5e7eb;
    padding: 0.75rem 0.75rem 0.75rem 2.5rem;
    font-size: 1rem;
    transition: all 0.2s;
    background-color: #f9fafb;
}

.search-input input:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3);
    background-color: #ffffff;
}

.search-filters {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
    margin-bottom: 0.5rem;
}

.filter-group {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.filter-group label {
    font-size: 0.875rem;
    color: #4b5563;
    font-weight: 500;
    white-space: nowrap;
}

.filter-group select {
    padding: 0.375rem 0.75rem;
    border-radius: 0.375rem;
    border: 1px solid #e5e7eb;
    font-size: 0.875rem;
    background-color: #f9fafb;
    color: #111827;
    transition: all 0.2s;
}

.filter-group select:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
}

.filter-group select:hover {
    border-color: #d1d5db;
    background-color: #f3f4f6;
}

/* 按钮样式 */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border-radius: 0.375rem;
    font-weight: 500;
    font-size: 0.875rem;
    padding: 0.625rem 1rem;
    transition: all 0.2s;
    cursor: pointer;
    gap: 0.5rem;
}

.btn-primary {
    background-color: #3b82f6;
    color: white;
    border: 1px solid #3b82f6;
}

.btn-primary:hover {
    background-color: #2563eb;
    border-color: #2563eb;
}

.btn-success {
    background-color: #10b981;
    color: white;
    border: 1px solid #10b981;
}

.btn-success:hover {
    background-color: #059669;
    border-color: #059669;
}

.btn-danger {
    background-color: #ef4444;
    color: white;
    border: 1px solid #ef4444;
}

.btn-danger:hover {
    background-color: #dc2626;
    border-color: #dc2626;
}

.btn-outline {
    background-color: transparent;
    border: 1px solid #d1d5db;
    color: #6b7280;
}

.btn-outline:hover {
    background-color: #f3f4f6;
    color: #111827;
}

.btn-icon {
    width: 2.5rem;
    height: 2.5rem;
    padding: 0;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border-radius: 9999px;
}

.save-button {
    position: fixed;
    bottom: 1.5rem;
    right: 1.5rem;
    z-index: 1000;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

/* 加载动画 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.9);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    color: #111827;
}

.loading-spinner {
    border: 3px solid #e5e7eb;
    border-top: 3px solid #3b82f6;
    border-radius: 50%;
    width: 48px;
    height: 48px;
    animation: spin 1s linear infinite;
    margin-bottom: 1rem;
}

.loading-message {
    font-weight: 500;
    margin-top: 0.5rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 物品序列容器样式 */
.item-sequence-container {
    padding: 1rem;
    background-color: #ffffff;
    border-radius: 0.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.sequence-section {
    margin-bottom: 1.5rem;
    border: 1px solid #e5e7eb;
    border-radius: 0.5rem;
    overflow: hidden;
}

.sequence-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 1rem;
    background-color: #f9fafb;
    cursor: pointer;
    transition: all 0.2s;
}

.sequence-header:hover {
    background-color: #f3f4f6;
}

.sequence-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: #111827;
    margin: 0;
    padding: 0;
}

.card-actions {
    display: flex;
    gap: 0.5rem;
}

.sequence-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 0.75rem;
}

.sequence-item {
    display: flex;
    flex-direction: column;
    border: 1px solid #e5e7eb;
    border-radius: 0.375rem;
    overflow: hidden;
    transition: all 0.2s;
    background-color: #f9fafb;
    cursor: grab;
}

.sequence-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    border-color: #3b82f6;
}

.sequence-item:active {
    cursor: grabbing;
    transform: scale(0.98);
}

.sequence-item.dragging {
    opacity: 0.7;
    border-style: dashed;
    border-color: #3b82f6;
    background-color: rgba(59, 130, 246, 0.1);
}

.sequence-item-id {
    background-color: #f3f4f6;
    color: #111827;
    font-weight: 600;
    padding: 0.25rem 0.5rem;
    font-size: 0.8rem;
    border-bottom: 1px solid #e5e7eb;
}

.sequence-item-content {
    display: flex;
    align-items: center;
    padding: 0.75rem;
    gap: 0.75rem;
}

.sequence-item-content img {
    width: 40px;
    height: 40px;
    object-fit: contain;
    transition: all 0.2s;
    cursor: grab;
}

.sequence-item-content img:hover {
    transform: scale(1.1);
    filter: brightness(1.1);
}

.sequence-item-content img:active {
    cursor: grabbing;
    transform: scale(0.95);
}

.sequence-item-content span {
    font-size: 0.85rem;
    color: #4b5563;
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* 动画效果 */
.animate-fade-in {
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.animate-slide-up {
    animation: slideUp 0.3s ease-in-out;
}

@keyframes slideUp {
    from { transform: translateY(20px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

.animate-slide-in {
    animation: slideIn 0.3s ease-in-out;
}

@keyframes slideIn {
    from { transform: translateX(20px); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

/* 响应式调整 */
@media (max-width: 768px) {
    .inventory-slot {
        width: 48px;
        height: 48px;
        margin: 2px;
    }

    .slot-number {
        font-size: 8px;
        padding: 1px 3px;
    }
}

/* 暗色主题支持 */
@media (prefers-color-scheme: dark) {
    body {
        background-color: #111827;
        color: #f9fafb;
    }

    .sidebar {
        background-color: #1f2937;
        border-color: #374151;
    }

    .sidebar-logo {
        color: #f9fafb;
    }

    .nav-link {
        color: #9ca3af;
    }

    .nav-link:hover {
        background-color: #374151;
        color: #f9fafb;
    }

    .nav-link.active {
        background-color: #374151;
        color: #f9fafb;
    }

    .top-navbar {
        background-color: #1f2937;
        border-color: #374151;
    }

    .page-title {
        color: #f9fafb;
    }

    .dashboard-card, .stat-card, .inventory-container, .armor-settings-container {
        background-color: #1f2937;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
    }

    .armor-info {
        background-color: rgba(59, 130, 246, 0.2);
        color: #60a5fa;
    }

    .armor-slot-label {
        color: #9ca3af;
    }

    .armor-slot {
        background-color: rgba(139, 92, 246, 0.2);
        border-color: #8b5cf6;
    }

    .card-title {
        color: #f9fafb;
    }

    .inventory-slot {
        background-color: #374151;
        border-color: #4b5563;
    }

    .inventory-slot.hotbar {
        background-color: rgba(59, 130, 246, 0.2);
    }

    .inventory-slot.armor {
        background-color: rgba(139, 92, 246, 0.2);
    }

    .inventory-slot.offhand {
        background-color: rgba(16, 185, 129, 0.2);
    }

    .item-selector {
        background-color: #1f2937;
        border-color: #374151;
    }

    .item-option {
        background-color: #374151;
        border-color: #4b5563;
    }

    .item-option:hover {
        background-color: #4b5563;
        border-color: #6b7280;
    }

    .item-name {
        color: #f9fafb;
    }

    .item-id {
        color: #9ca3af;
    }

    .item-category {
        background-color: #374151;
        color: #f9fafb;
        border-left-color: #3b82f6;
    }

    .item-search {
        background-color: #1f2937;
        border-color: #374151;
    }

    .search-input input {
        background-color: #374151;
        border-color: #4b5563;
        color: #f9fafb;
    }

    .search-input input:focus {
        border-color: #3b82f6;
    }

    .btn-outline {
        border-color: #4b5563;
        color: #9ca3af;
    }

    .btn-outline:hover {
        background-color: #374151;
        color: #f9fafb;
    }

    .loading-overlay {
        background-color: rgba(17, 24, 39, 0.9);
        color: #f9fafb;
    }

    .loading-spinner {
        border-color: #4b5563;
        border-top-color: #3b82f6;
    }
}
