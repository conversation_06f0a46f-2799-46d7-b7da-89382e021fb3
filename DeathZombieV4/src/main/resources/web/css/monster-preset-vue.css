/* 基础样式 */
:root {
    --primary-color: #4f46e5;
    --primary-dark: #3730a3;
    --primary-light: #6366f1;
    --secondary-color: #10b981;
    --secondary-dark: #059669;
    --background: #f8f9fa;
    --surface: #ffffff;
    --error: #ef4444;
    --on-primary: #ffffff;
    --on-secondary: #ffffff;
    --on-background: #374151;
    --on-surface: #111827;
    --on-error: #ffffff;
    --border-radius: 8px;
    --card-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    --transition-speed: 0.3s;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Microsoft YaHei', Arial, sans-serif !important;
    font-size: 14px !important;
    background-color: var(--background);
    color: var(--on-background);
    line-height: 1.6;
    margin: 0;
}

/* 加载动画 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.9);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 5px solid rgba(79, 70, 229, 0.2);
    border-radius: 50%;
    border-top-color: var(--primary-color);
    animation: spin 1s ease-in-out infinite;
}

.loading-message {
    margin-top: 20px;
    color: #4b5563;
    font-size: 18px;
    font-weight: 500;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* 主布局 */
.app-container {
    display: flex;
    min-height: 100vh;
}

/* 侧边栏样式 */
.sidebar {
    width: 240px;
    background-color: #ffffff;
    border-right: 1px solid #e5e7eb;
    padding: 1.5rem 0;
    position: fixed;
    height: 100vh;
    overflow-y: auto;
    z-index: 30;
}

.sidebar-header {
    padding: 0 1.25rem;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
}

.sidebar-logo {
    font-size: 1.25rem;
    font-weight: 600;
    color: #111827;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

/* 花体字母样式 */
.fancy-letter {
    font-family: 'Times New Roman', Times, serif;
    font-weight: bold;
    font-size: 1.5em;
    color: #4f46e5;
    margin-right: 8px;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
    display: inline-block;
    background: linear-gradient(135deg, #4f46e5, #10b981);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    padding: 0 5px;
}

.sidebar-nav {
    padding: 0 0.75rem;
}

.nav-item {
    margin-bottom: 0.25rem;
}

.nav-link {
    display: flex;
    align-items: center;
    padding: 0.75rem 1rem;
    color: #6b7280;
    border-radius: 0.5rem;
    text-decoration: none;
    transition: all 0.2s;
}

.nav-link:hover {
    background-color: #f3f4f6;
    color: #111827;
}

.nav-link.active {
    background-color: #f3f4f6;
    color: #111827;
    font-weight: 500;
}

.nav-link i {
    margin-right: 0.75rem;
    font-size: 1.25rem;
}

/* 主内容区域 */
.main-content {
    flex: 1;
    margin-left: 240px;
    padding: 1.5rem;
}

/* 顶部导航栏 */
.top-navbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 1.5rem;
    background-color: #ffffff;
    border-bottom: 1px solid #e5e7eb;
    position: sticky;
    top: 0;
    z-index: 20;
}

.page-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: #111827;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.navbar-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}

/* 按钮样式 */
.btn {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: var(--border-radius);
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.btn:hover {
    transform: translateY(-2px);
}

.btn:active {
    transform: translateY(0);
}

.btn-primary {
    background-color: var(--primary-color);
    color: var(--on-primary);
}

.btn-primary:hover {
    background-color: var(--primary-dark);
}

.btn-secondary {
    background-color: var(--secondary-color);
    color: var(--on-secondary);
}

.btn-secondary:hover {
    background-color: var(--secondary-dark);
}

.btn-outline {
    background-color: transparent;
    border: 1px solid var(--primary-color);
    color: var(--primary-color);
}

.btn-outline:hover {
    background-color: rgba(79, 70, 229, 0.1);
}

.btn-danger {
    background-color: var(--error);
    color: var(--on-error);
}

.btn-danger:hover {
    background-color: #dc2626;
}

.btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.8rem;
}

.btn-close {
    background: none;
    border: none;
    color: var(--on-surface);
    font-size: 1.2rem;
    cursor: pointer;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

/* 卡片样式 */
.dashboard-card {
    background-color: var(--surface);
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
    margin-bottom: 1.5rem;
    overflow: hidden;
    padding: 1.5rem;
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.card-title {
    display: flex;
    align-items: center;
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--on-surface);
}

.card-title i {
    margin-right: 0.5rem;
    font-size: 1.4rem;
}

.card-actions {
    display: flex;
    gap: 0.5rem;
}

/* 预设列表样式 */
.preset-list {
    padding: 1rem 0;
}

.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 3rem;
    color: #6b7280;
    text-align: center;
}

.empty-state i {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.preset-items {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1rem;
}

@media (max-width: 768px) {
    .preset-items {
        grid-template-columns: 1fr;
    }
}

.preset-item {
    background-color: #f9fafb;
    border: 1px solid #e5e7eb;
    border-radius: var(--border-radius);
    padding: 1rem;
    display: flex;
    flex-direction: column;
    gap: 1rem;
    transition: all 0.2s;
}

.preset-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.preset-info {
    flex: 1;
}

.preset-name {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--primary-color);
    display: block;
}

.preset-description {
    font-size: 0.9rem;
    color: #6b7280;
    margin-bottom: 1rem;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.preset-monsters {
    margin-top: 0.5rem;
}

.monster-count {
    font-size: 0.8rem;
    color: #6b7280;
    margin-bottom: 0.5rem;
    display: block;
}

.monster-list {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.monster-tag {
    background-color: rgba(79, 70, 229, 0.1);
    color: var(--primary-color);
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.8rem;
}

.monster-tag.more {
    background-color: rgba(107, 114, 128, 0.1);
    color: #6b7280;
}

.preset-actions {
    display: flex;
    justify-content: flex-end;
    gap: 0.5rem;
}

/* 模态框样式 */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.modal-container {
    background-color: var(--surface);
    border-radius: var(--border-radius);
    width: 90%;
    max-width: 600px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 1.5rem;
    border-bottom: 1px solid #e5e7eb;
}

.modal-header h2 {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--on-surface);
}

.modal-body {
    padding: 1.5rem;
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
    padding: 1rem 1.5rem;
    border-top: 1px solid #e5e7eb;
}

/* 表单样式 */
.form-group {
    margin-bottom: 1rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #4b5563;
}

.form-row {
    display: flex;
    gap: 1rem;
    align-items: flex-end;
}

@media (max-width: 768px) {
    .form-row {
        flex-direction: column;
        align-items: stretch;
    }
}

input, select, textarea {
    width: 100%;
    padding: 0.75rem;
    background-color: #f9fafb;
    border: 1px solid #e5e7eb;
    border-radius: var(--border-radius);
    color: #374151;
    font-size: 0.9rem;
}

input:focus, select:focus, textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(79, 70, 229, 0.2);
}

textarea {
    min-height: 100px;
    resize: vertical;
}

/* 怪物列表样式 */
.monster-add-form {
    margin-bottom: 1rem;
    padding: 1rem;
    background-color: #f9fafb;
    border: 1px solid #e5e7eb;
    border-radius: var(--border-radius);
}

.monster-list-container {
    max-height: 200px;
    overflow-y: auto;
    border: 1px solid #e5e7eb;
    border-radius: var(--border-radius);
}

.monster-items {
    padding: 0.5rem;
}

.monster-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem;
    background-color: #f9fafb;
    border: 1px solid #e5e7eb;
    border-radius: var(--border-radius);
    margin-bottom: 0.5rem;
}

.monster-info {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.monster-name {
    font-weight: 500;
    color: #374151;
}

.monster-count {
    color: #6b7280;
}

.btn-remove {
    background: none;
    border: none;
    color: var(--error);
    cursor: pointer;
    font-size: 1rem;
}

/* 预设详情样式 */
.preset-detail-info {
    padding: 1rem 0;
}

.preset-detail-info h3 {
    margin-top: 1rem;
    margin-bottom: 0.5rem;
    color: var(--primary-color);
    font-weight: 600;
}

.monster-detail-list {
    margin-top: 1rem;
}

.monster-detail-item {
    padding: 0.75rem;
    background-color: #f9fafb;
    border: 1px solid #e5e7eb;
    border-radius: var(--border-radius);
    margin-bottom: 0.5rem;
}

.monster-detail-info {
    display: flex;
    justify-content: space-between;
}

.monster-detail-name {
    font-weight: 500;
    color: #374151;
}

.monster-detail-count {
    color: #6b7280;
}

/* 动画 */
.animate-slide-up {
    animation: slideUp 0.5s ease-out;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
