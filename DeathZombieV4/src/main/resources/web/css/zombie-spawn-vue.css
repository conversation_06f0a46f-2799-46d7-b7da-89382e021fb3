/* 基础样式 */
:root {
    --primary-color: #4f46e5;
    --primary-dark: #3730a3;
    --primary-light: #6366f1;
    --secondary-color: #10b981;
    --secondary-dark: #059669;
    --background: #f8f9fa;
    --surface: #ffffff;
    --error: #ef4444;
    --on-primary: #ffffff;
    --on-secondary: #ffffff;
    --on-background: #374151;
    --on-surface: #111827;
    --on-error: #ffffff;
    --border-radius: 8px;
    --card-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    --transition-speed: 0.3s;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Microsoft YaHei', Arial, sans-serif !important;
    font-size: 14px !important;
    background-color: var(--background);
    color: var(--on-background);
    line-height: 1.6;
    margin: 0;
}

/* 加载动画 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.9);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 5px solid rgba(79, 70, 229, 0.2);
    border-radius: 50%;
    border-top-color: var(--primary-color);
    animation: spin 1s ease-in-out infinite;
}

.loading-text {
    margin-top: 20px;
    color: #4b5563;
    font-size: 18px;
    font-weight: 500;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* 主布局 */
.app-container {
    display: flex;
    min-height: 100vh;
}

/* 侧边栏样式 */
.sidebar {
    width: 240px;
    background-color: #ffffff;
    border-right: 1px solid #e5e7eb;
    padding: 1.5rem 0;
    position: fixed;
    height: 100vh;
    overflow-y: auto;
    z-index: 30;
}

.sidebar-header {
    padding: 0 1.25rem;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
}

.sidebar-logo {
    font-size: 1.25rem;
    font-weight: 600;
    color: #111827;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

/* 花体字母样式 */
.fancy-letter {
    font-family: 'Times New Roman', Times, serif;
    font-weight: bold;
    font-size: 1.5em;
    color: #4f46e5;
    margin-right: 8px;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
    display: inline-block;
    background: linear-gradient(135deg, #4f46e5, #10b981);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    padding: 0 5px;
}

.sidebar-nav {
    padding: 0 0.75rem;
}

.nav-item {
    margin-bottom: 0.25rem;
}

.nav-link {
    display: flex;
    align-items: center;
    padding: 0.75rem 1rem;
    color: #6b7280;
    border-radius: 0.5rem;
    text-decoration: none;
    transition: all 0.2s;
}

.nav-link:hover {
    background-color: #f3f4f6;
    color: #111827;
}

.nav-link.active {
    background-color: #f3f4f6;
    color: #111827;
    font-weight: 500;
}

.nav-link i {
    margin-right: 0.75rem;
    font-size: 1.25rem;
}

/* 主内容区域 */
.main-content {
    flex: 1;
    margin-left: 240px;
    padding: 1.5rem;
}

/* 顶部导航栏 */
.top-navbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 1.5rem;
    background-color: #ffffff;
    border-bottom: 1px solid #e5e7eb;
    position: sticky;
    top: 0;
    z-index: 20;
}

.page-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: #111827;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.navbar-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}

/* 按钮样式 */
.btn {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: var(--border-radius);
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.btn:hover {
    transform: translateY(-2px);
}

.btn:active {
    transform: translateY(0);
}

.btn-primary {
    background-color: var(--primary-color);
    color: var(--on-primary);
}

.btn-primary:hover {
    background-color: var(--primary-dark);
}

.btn-secondary {
    background-color: var(--secondary-color);
    color: var(--on-secondary);
}

.btn-secondary:hover {
    background-color: var(--secondary-dark);
}

.btn-outline {
    background-color: transparent;
    border: 1px solid var(--primary-color);
    color: var(--primary-color);
}

.btn-outline:hover {
    background-color: rgba(79, 70, 229, 0.1);
}

.btn-outline-primary {
    background-color: transparent;
    border: 1px solid var(--primary-color);
    color: var(--primary-color);
}

.btn-outline-primary:hover {
    background-color: rgba(79, 70, 229, 0.1);
}

.btn-outline-success {
    background-color: transparent;
    border: 1px solid var(--secondary-color);
    color: var(--secondary-color);
}

.btn-outline-success:hover {
    background-color: rgba(16, 185, 129, 0.1);
}

.btn-danger {
    background-color: var(--error);
    color: var(--on-error);
}

.btn-danger:hover {
    background-color: #dc2626;
}

.btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.8rem;
}

.btn-block {
    width: 100%;
}

.btn-close {
    background: none;
    border: none;
    color: var(--on-surface);
    font-size: 1.2rem;
    cursor: pointer;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

/* 卡片样式 */
.dashboard-card {
    background-color: var(--surface);
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
    margin-bottom: 1.5rem;
    overflow: hidden;
    padding: 1.5rem;
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.card-title {
    display: flex;
    align-items: center;
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--on-surface);
}

.card-title i {
    margin-right: 0.5rem;
    font-size: 1.4rem;
}

.card-actions {
    display: flex;
    gap: 0.5rem;
}

.card-body {
    padding: 1rem 0;
}

/* 信息提示框 */
.info-box {
    display: flex;
    align-items: center;
    padding: 1rem;
    background-color: rgba(79, 70, 229, 0.1);
    border-radius: var(--border-radius);
    margin-bottom: 1rem;
    color: var(--primary-color);
}

.info-box i {
    font-size: 1.5rem;
    color: var(--primary-color);
    margin-right: 1rem;
}

/* 回合选择器 */
.round-selector {
    margin-bottom: 1.5rem;
}

.round-selector-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.round-count {
    font-size: 0.9rem;
    color: #6b7280;
}

.round-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.round-button {
    width: 40px;
    height: 40px;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #f3f4f6;
    border: 1px solid #e5e7eb;
    border-radius: var(--border-radius);
    color: #4b5563;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s;
}

.round-button:hover {
    background-color: #e5e7eb;
    transform: translateY(-2px);
}

.round-button.active {
    background-color: var(--primary-color);
    color: var(--on-primary);
    border-color: var(--primary-dark);
}

/* 批量设置 */
.batch-settings {
    margin-bottom: 1.5rem;
    padding: 1rem;
    background-color: #f9fafb;
    border: 1px solid #e5e7eb;
    border-radius: var(--border-radius);
}

.batch-settings-header {
    margin-bottom: 1rem;
}

.form-row {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    margin-bottom: 1rem;
}

@media (max-width: 768px) {
    .form-row {
        flex-direction: column;
    }
}

.form-group {
    flex: 1;
    min-width: 150px;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
    font-weight: 500;
    color: #4b5563;
}

.form-control {
    width: 100%;
    padding: 0.75rem;
    background-color: #ffffff;
    border: 1px solid #e5e7eb;
    border-radius: var(--border-radius);
    color: #374151;
    transition: all 0.2s;
}

.form-control:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(79, 70, 229, 0.2);
}

.form-select {
    width: 100%;
    padding: 0.75rem;
    background-color: #ffffff;
    border: 1px solid #e5e7eb;
    border-radius: var(--border-radius);
    color: #374151;
    appearance: none;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='%234b5563' viewBox='0 0 16 16'%3E%3Cpath d='M7.247 11.14 2.451 5.658C1.885 5.013 2.345 4 3.204 4h9.592a1 1 0 0 1 .753 1.659l-4.796 5.48a1 1 0 0 1-1.506 0z'/%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: right 0.75rem center;
    padding-right: 2.5rem;
    transition: all 0.2s;
}

.form-select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(79, 70, 229, 0.2);
}

/* 回合详情 */
.round-details {
    margin-top: 2rem;
}

.round-details-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

/* 生成点列表 */
.spawn-points-container {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.spawn-point-item {
    background-color: #ffffff;
    border: 1px solid #e5e7eb;
    border-radius: var(--border-radius);
    overflow: hidden;
}

.spawn-point-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    background-color: #f9fafb;
    transition: all 0.2s;
}

.spawn-point-header:hover {
    background-color: #f3f4f6;
}

.spawn-point-info {
    flex: 1;
    cursor: pointer;
}

.spawn-point-info h4 {
    margin: 0 0 0.5rem 0;
    font-size: 1.1rem;
    font-weight: 600;
    color: #111827;
}

.spawn-point-details {
    display: flex;
    flex-wrap: wrap;
    gap: 0.75rem;
    margin-top: 0.5rem;
}

.spawn-detail-item {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    font-size: 0.8rem;
    color: #6b7280;
    padding: 0.25rem 0.5rem;
    background-color: #f3f4f6;
    border-radius: 4px;
    border: 1px solid #e5e7eb;
}

.spawn-detail-item i {
    font-size: 0.85rem;
}

.spawn-detail-item.enabled {
    color: #059669;
    background-color: #d1fae5;
    border-color: #a7f3d0;
}

.spawn-detail-item.disabled {
    color: #dc2626;
    background-color: #fee2e2;
    border-color: #fecaca;
}

.spawn-point-actions {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.spawn-point-actions i {
    cursor: pointer;
    font-size: 1.2rem;
    color: #6b7280;
    transition: color 0.2s;
}

.spawn-point-actions i:hover {
    color: var(--primary-color);
}

.spawn-point-config {
    padding: 1rem;
    background-color: #ffffff;
}

/* 怪物配置 */
.monster-config {
    background-color: #ffffff;
    border: 1px solid #e5e7eb;
    border-radius: var(--border-radius);
    margin-bottom: 1rem;
    overflow: hidden;
}

.monster-config-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 1rem;
    background-color: #f9fafb;
    border-bottom: 1px solid #e5e7eb;
}

.monster-config-actions {
    display: flex;
    gap: 0.5rem;
}

.monster-config-body {
    padding: 1rem;
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
}

.monster-config-body .form-group {
    flex: 1;
    min-width: 150px;
}

.add-monster-config {
    margin-top: 1rem;
}

/* 模态框样式 */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.modal-container {
    background-color: var(--surface);
    border-radius: var(--border-radius);
    width: 90%;
    max-width: 600px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 1.5rem;
    border-bottom: 1px solid #e5e7eb;
}

.modal-header h2 {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--on-surface);
}

.modal-body {
    padding: 1.5rem;
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
    padding: 1rem 1.5rem;
    border-top: 1px solid #e5e7eb;
}

/* 动画 */
.animate-slide-up {
    animation: slideUp 0.5s ease-out;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
