# DeathZombieV4 插件配置文件

# 游戏设置
game:
  # 服务器名称（显示在计分板上）
  server_name: "&6僵尸末日"
  # 是否启用友伤（true为开启友伤，false为关闭友伤）
  friendly_fire: false
  # 是否发送友伤提示消息（当友伤被禁用时，是否向攻击者发送提示消息）
  friendly_fire_message_enabled: true

  # 游戏开始后玩家离开时的处理方式
  # true: 当剩余玩家数少于最小玩家数时直接结束游戏
  # false: 允许剩余玩家继续游戏，失败条件仍为所有玩家倒地或死亡
  end_game_when_below_min_players: true

  # 回合复活设置
  round_revival:
    # 是否启用回合复活功能（每当进入下一个回合时，复活已死亡和已倒下的队友）
    enabled: true
    # 复活后是否传送到出生地位置
    respawn_at_spawn: true
    # 复活时的生命值（百分比，1.0表示满血）
    health_percentage: 1.0
    # 复活时播放的音效
    revival_sound: "entity.player.levelup"
    # 复活时的粒子效果
    revival_particle: "HEART"
    # 复活消息
    revival_message: "&a你已在新回合中复活！"

  # 游戏结束后统计数据显示设置
  post_game_statistics:
    # 是否启用游戏结束后的统计数据显示
    enabled: true
    # 游戏结束后等待时间（秒），在此期间显示统计数据，之后传送玩家到大厅
    wait_time_seconds: 10
    # 统计数据显示消息模板（支持变量：{zombie_kills}, {money_earned}, {doors_opened}, {money_spent}, {windows_repaired}）
    messages:
      header: "&6&l========== &e游戏统计 &6&l=========="
      zombie_kills: "&a击杀僵尸: &f{zombie_kills} 只"
      money_earned: "&a获得金钱: &f{money_earned} 金币"
      doors_opened: "&a开启门数: &f{doors_opened} 扇"
      money_spent: "&a花费金钱: &f{money_spent} 金币"
      windows_repaired: "&a修复窗户: &f{windows_repaired} 扇"
      footer: "&6&l=========================="
      teleport_countdown: "&e{seconds} 秒后传送到大厅..."

  # 计分板设置
  scoreboard:
    # 计分板更新间隔（ticks，20ticks=1秒）
    update_interval: 10
    # 是否启用计分板
    enabled: true
    # 计分板标题
    title: "&e&l僵尸末日"
    # 计分板样式设置
    styles:
      # 等待游戏时的计分板样式
      waiting:
        header: "&c&l===== &6等待游戏 &c&l====="
        map_name: "&a地图: &f{map_name}"
        players_count: "&a玩家: &f{current}/{max}"
        countdown: "&a开始倒计时: &f{countdown}秒"
        footer: "&c&l====================="
      # 游戏中的计分板样式
      in_game:
        header: "&c&l===== &6游戏进行中 &c&l====="
        map_name: "&a地图: &f{map_name}"
        round: "&a回合: &f{current}/{total}"
        time_left: "&a剩余时间: &f{minutes}:{seconds}"
        zombies_left: "&a剩余僵尸: &f{count}"
        game_duration: "&a游戏耗时: &f{duration}"
        footer: "&c&l====================="

  # 游戏结束后的计分板配置
  post_game_scoreboard:
    # 计分板标题
    title: "&6&l游戏结束"
    # 计分板内容行（从上到下显示）
    lines:
      - "&7=================="
      - "&a&l游戏胜利！"
      - "&7"
      - "&e正在统计数据..."
      - "&7"
      - "&c即将返回大厅"
      - "&7=================="

  # 金钱奖励设置
  money:
    # 金钱获取方式: shoot(射击命中) 或 kill(击杀)
    getting_money_way: "shoot"

    # 枪支特定金钱奖励设置
    weapon_specific_rewards:
      # 是否启用枪支特定金钱奖励（true: 使用下面的枪支特定配置，false: 使用全局配置）
      enabled: false

      # 各枪支的金钱奖励配置（与Shoot插件保持一致）
      # 格式：枪支ID -> shoot模式和kill模式的奖励设置
      weapons:
        # 手枪 (id1) - 基础武器，低伤害
        id1:
          # shoot模式下的奖励（射击命中奖励）
          shoot:
            zombie_hit: 1        # 命中普通僵尸
            mutant_hit: 2        # 命中变异怪物
            npc_hit: 3           # 命中感染者NPC
            monster_hit: 1       # 命中其他怪物
          # kill模式下的奖励（击杀奖励）
          kill:
            zombie_kill: 3       # 击杀普通僵尸
            mutant_kill: 6       # 击杀变异怪物
            npc_kill: 10         # 击杀感染者NPC
            monster_kill: 4      # 击杀其他怪物

        # 步枪 (id2) - 中等威力
        id2:
          shoot:
            zombie_hit: 2
            mutant_hit: 4
            npc_hit: 6
            monster_hit: 2
          kill:
            zombie_kill: 5
            mutant_kill: 12
            npc_kill: 18
            monster_kill: 7

        # 霰弹枪 (id3) - 近距离高伤害
        id3:
          shoot:
            zombie_hit: 3
            mutant_hit: 6
            npc_hit: 9
            monster_hit: 3
          kill:
            zombie_kill: 8
            mutant_kill: 18
            npc_kill: 25
            monster_kill: 10

        # 机枪 (id4) - 高射速，低单发伤害
        id4:
          shoot:
            zombie_hit: 1
            mutant_hit: 2
            npc_hit: 3
            monster_hit: 1
          kill:
            zombie_kill: 4
            mutant_kill: 8
            npc_kill: 12
            monster_kill: 5

        # 火箭筒 (id5) - 高爆炸伤害
        id5:
          shoot:
            zombie_hit: 5
            mutant_hit: 10
            npc_hit: 15
            monster_hit: 6
          kill:
            zombie_kill: 15
            mutant_kill: 30
            npc_kill: 45
            monster_kill: 20

        # 电击枪 (id6) - 电击特效
        id6:
          shoot:
            zombie_hit: 4
            mutant_hit: 8
            npc_hit: 12
            monster_hit: 4
          kill:
            zombie_kill: 10
            mutant_kill: 20
            npc_kill: 30
            monster_kill: 12

        # 狙击步枪 (id7) - 超高单发伤害
        id7:
          shoot:
            zombie_hit: 8
            mutant_hit: 16
            npc_hit: 24
            monster_hit: 10
          kill:
            zombie_kill: 25
            mutant_kill: 50
            npc_kill: 75
            monster_kill: 30

        # 冷冻枪 (id8) - 冰冻效果
        id8:
          shoot:
            zombie_hit: 3
            mutant_hit: 6
            npc_hit: 9
            monster_hit: 3
          kill:
            zombie_kill: 8
            mutant_kill: 16
            npc_kill: 24
            monster_kill: 10

        # 雷击枪 (id9) - 雷电高伤害
        id9:
          shoot:
            zombie_hit: 8
            mutant_hit: 16
            npc_hit: 24
            monster_hit: 10
          kill:
            zombie_kill: 25
            mutant_kill: 50
            npc_kill: 75
            monster_kill: 30

        # 压强枪 (id10) - 烟雾击退
        id10:
          shoot:
            zombie_hit: 3
            mutant_hit: 6
            npc_hit: 9
            monster_hit: 3
          kill:
            zombie_kill: 8
            mutant_kill: 16
            npc_kill: 24
            monster_kill: 10

        # 突击步枪 (id11) - 快速连射
        id11:
          shoot:
            zombie_hit: 2
            mutant_hit: 4
            npc_hit: 6
            monster_hit: 2
          kill:
            zombie_kill: 5
            mutant_kill: 10
            npc_kill: 15
            monster_kill: 6

        # 冲锋枪 (id12) - 极快射速
        id12:
          shoot:
            zombie_hit: 1
            mutant_hit: 2
            npc_hit: 3
            monster_hit: 1
          kill:
            zombie_kill: 3
            mutant_kill: 6
            npc_kill: 9
            monster_kill: 4

        # 等离子枪 (id13) - 等离子灼烧
        id13:
          shoot:
            zombie_hit: 5
            mutant_hit: 10
            npc_hit: 15
            monster_hit: 6
          kill:
            zombie_kill: 12
            mutant_kill: 24
            npc_kill: 36
            monster_kill: 15

        # 死神收割者 (id14) - 死亡收割
        id14:
          shoot:
            zombie_hit: 8
            mutant_hit: 16
            npc_hit: 24
            monster_hit: 10
          kill:
            zombie_kill: 20
            mutant_kill: 40
            npc_kill: 60
            monster_kill: 25

        # 毁灭者 (id15) - 毁灭性爆炸
        id15:
          shoot:
            zombie_hit: 12
            mutant_hit: 24
            npc_hit: 36
            monster_hit: 15
          kill:
            zombie_kill: 30
            mutant_kill: 60
            npc_kill: 90
            monster_kill: 40

        # 超级激光炮 (id16) - 超级激光
        id16:
          shoot:
            zombie_hit: 15
            mutant_hit: 30
            npc_hit: 45
            monster_hit: 18
          kill:
            zombie_kill: 40
            mutant_kill: 80
            npc_kill: 120
            monster_kill: 50

        # 黑洞吞噬者 (id17) - 黑洞吞噬
        id17:
          shoot:
            zombie_hit: 12
            mutant_hit: 24
            npc_hit: 36
            monster_hit: 15
          kill:
            zombie_kill: 35
            mutant_kill: 70
            npc_kill: 105
            monster_kill: 42

        # 音波步枪 (id18) - 音波冲击
        id18:
          shoot:
            zombie_hit: 3
            mutant_hit: 6
            npc_hit: 9
            monster_hit: 3
          kill:
            zombie_kill: 8
            mutant_kill: 16
            npc_kill: 24
            monster_kill: 10

        # 能量脉冲枪 (id19) - 能量脉冲
        id19:
          shoot:
            zombie_hit: 4
            mutant_hit: 8
            npc_hit: 12
            monster_hit: 5
          kill:
            zombie_kill: 10
            mutant_kill: 20
            npc_kill: 30
            monster_kill: 12

        # 彩虹喷射器 (id20) - 彩虹效果
        id20:
          shoot:
            zombie_hit: 2
            mutant_hit: 4
            npc_hit: 6
            monster_hit: 2
          kill:
            zombie_kill: 6
            mutant_kill: 12
            npc_kill: 18
            monster_kill: 8

        # 传送枪 (id21) - 传送效果
        id21:
          shoot:
            zombie_hit: 2
            mutant_hit: 4
            npc_hit: 6
            monster_hit: 2
          kill:
            zombie_kill: 5
            mutant_kill: 10
            npc_kill: 15
            monster_kill: 6

        # 星辉主宰者 (id22) - 星辉爆炸
        id22:
          shoot:
            zombie_hit: 10
            mutant_hit: 20
            npc_hit: 30
            monster_hit: 12
          kill:
            zombie_kill: 25
            mutant_kill: 50
            npc_kill: 75
            monster_kill: 30

        # 虚空星尘使者 (id23) - 虚空撕裂
        id23:
          shoot:
            zombie_hit: 8
            mutant_hit: 16
            npc_hit: 24
            monster_hit: 10
          kill:
            zombie_kill: 20
            mutant_kill: 40
            npc_kill: 60
            monster_kill: 25

        # 循声炮 (id24) - 音波冲击
        id24:
          shoot:
            zombie_hit: 12
            mutant_hit: 24
            npc_hit: 36
            monster_hit: 15
          kill:
            zombie_kill: 30
            mutant_kill: 60
            npc_kill: 90
            monster_kill: 35

    # === 击杀奖励设置 ===
    # 击杀普通僵尸获得的金钱
    zombie_kill: 10
    # 击杀特殊僵尸获得的金钱（基础值，会根据僵尸类型有所增减）
    special_zombie_kill: 20
    # 击杀变异怪物获得的金钱
    mutant_kill: 30
    # 击杀感染者NPC获得的金钱
    npc_kill: 50
    # 击杀其他怪物获得的金钱
    monster_kill: 15
    # 击杀玩家获得的金钱
    player_kill: 50
    # 使用近战武器击杀获得额外奖励（百分比增加，0.3表示增加30%）
    melee_weapon_bonus: 0.3
    # 使用远程武器击杀获得额外奖励（百分比增加，0.1表示增加10%）
    ranged_weapon_bonus: 0.1

    # === 射击命中奖励设置 ===
    # 射击命中普通僵尸获得的金钱
    zombie_hit: 2
    # 射击命中特殊僵尸获得的金钱
    special_zombie_hit: 4
    # 射击命中变异怪物获得的金钱
    mutant_hit: 6
    # 射击命中感染者NPC获得的金钱
    npc_hit: 10
    # 射击命中其他怪物获得的金钱
    monster_hit: 3

    # === 高级射击命中设置 ===
    # 头部命中奖励倍数（相对于身体命中）
    headshot_multiplier: 2.0
    # 身体命中奖励倍数（基础倍数）
    bodyshot_multiplier: 1.0

    # === 头部命中检测设置 ===
    # 头部区域占实体高度的百分比（0.15-0.25推荐，0.2表示顶部20%为头部）
    head_region_percentage: 0.2
    # 角度检测的容差（度数，用于验证玩家是否真的在瞄准头部）
    angle_tolerance: 8.0
    # 是否启用头部命中检测调试信息
    headshot_debug: false

    # === 射击模式下的击杀奖励设置（通常设置为0，允许开发者自定义） ===
    # 射击模式下击杀普通僵尸获得的金钱
    zombie_kill_in_shoot_mode: 0
    # 射击模式下击杀特殊僵尸获得的金钱
    special_zombie_kill_in_shoot_mode: 0
    # 射击模式下击杀变异怪物获得的金钱
    mutant_kill_in_shoot_mode: 0
    # 射击模式下击杀感染者NPC获得的金钱
    npc_kill_in_shoot_mode: 0
    # 射击模式下击杀其他怪物获得的金钱
    monster_kill_in_shoot_mode: 0

    # === 通用设置 ===
    # 最小金钱奖励
    min_reward: 1
    # 最大金钱奖励
    max_reward: 100
    # 回合完成奖励
    round_completion_reward: 50
    game_completion_reward: 500
    # 玩家被僵尸击杀的金钱惩罚
    player_killed_penalty: 30
    # 窗户修复奖励
    window_repair: 5

  # 虚空伤害处理设置
  void_handling:
    # 是否启用虚空传送到出生点功能
    teleport_to_spawn: true
    # 虚空伤害是否导致玩家倒下（true: 传送后仍会倒下，false: 传送后不倒下）
    downs_player: false
    # 虚空传送提示消息
    teleport_message: "&c你掉入了虚空，已传送回出生点！"

# 实体名称显示设置
entity_name_display:
  # 是否启用实体名称动态显示功能
  enabled: true
  # 显示名称的最大距离（格）
  display_distance: 5.0
  # 检查间隔（tick，20tick=1秒）
  check_interval: 10

# 调试模式
debug: false

# 错误提示设置
error_messages:
  # 是否开启错误提示（移动、切换到副手、丢弃时的错误信息）
  # true: 显示错误提示信息，false: 不显示错误提示信息，给玩家更好的游戏体验
  enabled: true

# 日志设置
logging:
  # 是否将日志输出到单独的文件
  enabled: true
  # 日志文件夹路径（相对于插件数据文件夹）
  folder: "logs"
  # 日志级别: INFO, WARNING, SEVERE
  level: "INFO"
  # 是否根据日期创建新的日志文件（如果为false，则只使用latest.log）
  daily_files: false
  # 是否同时在控制台显示日志（如果为false，则只输出到日志文件）
  console_output: false

  # 实体死亡日志设置
  entity_death_logging:
    # 批量死亡检测阈值（5秒内死亡实体数量超过此值时触发批量死亡处理）
    batch_death_threshold: 10
    # 批量死亡处理模式
    # "suppress" - 完全抑制批量死亡消息
    # "summary" - 汇总显示批量死亡信息（推荐）
    # "normal" - 正常记录但添加警告标记
    batch_death_handling: "summary"

# 网页服务器设置
web:
  enabled: true
  port: 1777
  # 服务器绑定的IP地址，留空则使用本机地址
  # 示例：
  # host: "*************"  # 绑定到指定IP
  # host: "0.0.0.0"        # 绑定到所有网络接口
  # host: ""               # 留空使用本机地址（默认）
  host: "127.0.0.1"
  password: "admin123"

# 幸运箱设置
luckyBox:
  # 幸运箱显示设置
  display:
    # 抽奖动画持续时间（秒）
    duration: 3
    # 物品切换速度（tick，越小越快）
    switch_speed: 1
    # 抽奖过程音效
    sound: "block.note_block.pling"
    # 得到物品音效
    reward_sound: "ui.toast.challenge.complete"
    # 动画显示样式（gold/white/purple）
    style: "gold"
    # 翻转动画设置
    flip_animation:
      # 是否启用翻转动画
      enabled: true
      # 翻转持续时间（ticks）
      duration: 10
      # 翻转速度
      speed: 0.3
      # 翻转高度效果强度
      height_effect: 0.3
      # 是否显示粒子效果
      particles: true
      # 增强效果设置
      enhanced_effects:
        # 是否启用增强的旋转效果
        enhanced_rotation: true
        # 是否启用动态半径变化
        dynamic_radius: true
        # 是否启用螺旋粒子效果
        spiral_particles: true
        # 是否启用颜色变化效果
        color_transition: true
        # 是否启用额外音效
        extra_sounds: true

  # 幸运箱中可抽取的武器及其概率
  weapons:
    id1:
      probability: 30.0  # 概率权重，数值越大越容易抽到
      material: "WOODEN_HOE"
      name: "手枪"
    id2:
      probability: 25.0
      material: "WOODEN_HOE"
      name: "步枪"
    id3:
      probability: 20.0
      material: "WOODEN_HOE"
      name: "霰弹枪"
    id4:
      probability: 15.0
      material: "WOODEN_HOE"
      name: "机枪"
    id5:
      probability: 10.0
      material: "WOODEN_HOE"
      name: "火箭筒"
    id6:
      probability: 8.0
      material: "WOODEN_HOE"
      name: "电击枪"
    id7:
      probability: 5.0
      material: "WOODEN_HOE"
      name: "狙击步枪"
    id8:
      probability: 3.0
      material: "WOODEN_HOE"
      name: "冷冻枪"
    id9:
      probability: 2.0
      material: "WOODEN_HOE"
      name: "雷击枪"
    id10:
      probability: 1.0
      material: "WOODEN_HOE"
      name: "压强枪"

# 救援系统设置
rescue_system:
  # 救援范围（格）
  rescue_range: 2.0
  # 救援所需时间（秒）
  rescue_time_required: 5
  # 死亡后等待救援的最大时间（秒）
  max_rescue_wait_time: 30

  # 倒下时显示配置
  downed_display:
    # 倒下玩家看到的title
    player_title:
      main: "&c你已倒下"
      subtitle: "&e等待队友救援..."
      fade_in: 10
      stay: 70
      fade_out: 20

    # 倒下玩家看到的聊天消息（支持多行、颜色、变量）
    player_messages:
      - "&c你已倒下，等待队友救援！"
      - "&e剩余救援时间: &f{remaining_time} &e秒"

    # 其他队友看到的title
    team_title:
      main: "&c{player_name} 已倒下"
      subtitle: "&e蹲下 {rescue_time} 秒可以救援他！"
      fade_in: 10
      stay: 70
      fade_out: 20

    # 其他队友看到的聊天消息
    team_messages:
      - "&c{player_name} 已倒下，蹲下 {rescue_time} 秒可以救援他！"

  # 救援过程配置
  rescue_process:
    # 救援者看到的title
    rescuer_title:
      main: "&a正在救援 {target_name}"
      subtitle: "&e进度: {progress}/{total} 秒"
      fade_in: 5
      stay: 25
      fade_out: 5

    # 被救援者看到的title
    target_title:
      main: "&a{rescuer_name} 正在救援你"
      subtitle: "&e进度: {progress}/{total} 秒"
      fade_in: 5
      stay: 25
      fade_out: 5

  # 救援成功配置
  rescue_success:
    # 救援者看到的消息
    rescuer_messages:
      - "&a你成功救援了 {target_name}！"

    # 被救援者看到的title
    target_title:
      main: "&a已复活"
      subtitle: "&e感谢 {rescuer_name} 的救援"
      fade_in: 10
      stay: 70
      fade_out: 20

    # 被救援者看到的消息
    target_messages:
      - "&a你已被 {rescuer_name} 复活！"

  # 救援失败配置
  rescue_failure:
    # 救援者看到的消息
    rescuer_messages:
      stopped_crouching: "&c救援失败，你停止了蹲下"
      left_area: "&c救援失败，你离开了灵魂"
      target_died: "&c救援失败，{target_name} 已永久死亡"

    # 被救援者看到的消息
    target_messages:
      general: "&c救援失败，请等待其他队友的救援"
      timeout: "&c你没有被及时救援，已永久死亡！"

# 声音设置
sounds:
  # 回合切换音效
  round_change: "item.goat.horn.sound.0"
  # 游戏胜利音效
  game_win: "ui.toast.challenge.complete"
  # 窗户破坏音效
  window_break: "block.wood.break"
  # 窗户修复音效
  window_repair: "block.wood.place"
  # 电击僵尸攻击音效
  electric_shock: "entity.lightning_bolt.impact"
  # 游戏开始音效
  game_start: "entity.wither.spawn"
  # 游戏结束音效
  game_end: "entity.ender_dragon.death"