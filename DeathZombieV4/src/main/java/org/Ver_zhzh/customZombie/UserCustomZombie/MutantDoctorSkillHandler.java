package org.Ver_zhzh.customZombie.UserCustomZombie;

import org.Ver_zhzh.customZombie.CustomZombie;
import org.bukkit.Bukkit;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.Particle;
import org.bukkit.Sound;
import org.bukkit.entity.*;
import org.bukkit.metadata.FixedMetadataValue;
import org.bukkit.plugin.Plugin;
import org.bukkit.potion.PotionEffect;
import org.bukkit.potion.PotionEffectType;
import org.bukkit.scheduler.BukkitRunnable;
import org.bukkit.scheduler.BukkitTask;
import org.bukkit.util.Vector;

import java.util.*;
import java.util.logging.Logger;

/**
 * 变异博士技能处理器 - 专门处理ID25变异博士的七重技能系统
 *
 * 七大技能：
 * 1. 随机召唤技能：每30秒从ID1-ID24中随机召唤一个僵尸
 * 2. 自动攻击技能：玩家在20*20范围内时自动发射弓箭或烈焰弹
 * 3. 巨额电击技能：每3分钟对玩家造成一次巨额电击伤害（16血以上）
 * 4. 攻击强化技能：每次攻击使玩家获得虚弱2+击退4格
 * 5. 冻结技能：每4分钟冻结周围所有玩家5秒
 * 6. 电流领域技能：每3秒对10*10范围内释放电流，造成4点伤害
 * 7. 科学家召唤：每收到50点伤害召唤一个变异科学家
 */
public class MutantDoctorSkillHandler {

    private final Plugin plugin;
    private final Logger logger;
    private final CustomZombie originalCustomZombie;
    private final boolean debugMode;

    // 任务管理
    private final Map<Zombie, BukkitTask> randomSummonTasks = new HashMap<>();
    private final Map<Zombie, BukkitTask> autoAttackTasks = new HashMap<>();
    private final Map<Zombie, BukkitTask> electricShockTasks = new HashMap<>();
    private final Map<Zombie, BukkitTask> freezeTasks = new HashMap<>();
    private final Map<Zombie, BukkitTask> electricFieldTasks = new HashMap<>();
    private final Map<Zombie, BukkitTask> particleTasks = new HashMap<>();

    // 科学家召唤伤害追踪
    private final Map<Zombie, Double> scientistSummonDamage = new HashMap<>();

    // 召唤配置追踪
    private final Map<Zombie, Boolean> useUserCustomSummonMap = new HashMap<>();

    // ID1-ID24的僵尸ID列表（用于随机召唤）
    private static final String[] ZOMBIE_IDS = {
        "id1", "id2", "id3", "id4", "id5", "id6", "id7", "id8", "id9", "id10",
        "id11", "id12", "id13", "id14", "id15", "id16", "id17", "id18", "id19", "id20",
        "id21", "id22", "id23", "id24"
    };

    public MutantDoctorSkillHandler(Plugin plugin, Logger logger, CustomZombie originalCustomZombie, boolean debugMode) {
        this.plugin = plugin;
        this.logger = logger;
        this.originalCustomZombie = originalCustomZombie;
        this.debugMode = debugMode;
    }

    /**
     * 启用变异博士的所有技能
     *
     * @param zombie 僵尸实体
     * @param zombieId 僵尸ID
     * @param config 配置
     */
    public void enableSkills(Zombie zombie, String zombieId, UserCustomZombie.ZombieOverrideConfig config) {
        try {
            // 添加变异博士标记
            zombie.setMetadata("mutantDoctorZombie", new FixedMetadataValue(plugin, true));

            if (debugMode) {
                logger.info("启用变异博士技能 - zombieId: " + zombieId);
                logger.info("配置对象: " + (config != null ? "存在" : "null"));
            }

            // 获取配置参数
            int randomSummonInterval = 600; // 默认30秒间隔
            int autoAttackInterval = 20; // 默认1秒检查间隔
            int electricShockInterval = 3600; // 默认3分钟间隔
            int freezeInterval = 4800; // 默认4分钟间隔
            int electricFieldInterval = 60; // 默认3秒间隔

            double scientistSummonThreshold = 50.0; // 默认50点伤害触发科学家召唤
            double electricShockDamage = 16.0; // 默认16点电击伤害
            double electricFieldDamage = 4.0; // 默认4点电流伤害
            double autoAttackRange = 20.0; // 默认20格自动攻击范围
            int weaknessLevel = 1; // 默认虚弱2等级
            int weaknessDuration = 60; // 默认3秒持续时间
            double knockbackDistance = 4.0; // 默认4格击退距离
            int freezeDuration = 100; // 默认5秒冻结时间

            boolean randomSummonEnabled = true; // 默认启用随机召唤
            boolean autoAttackEnabled = true; // 默认启用自动攻击
            boolean electricShockEnabled = true; // 默认启用电击
            boolean attackEnhanceEnabled = true; // 默认启用攻击强化
            boolean freezeEnabled = true; // 默认启用冻结
            boolean electricFieldEnabled = true; // 默认启用电流领域
            boolean scientistSummonEnabled = true; // 默认启用科学家召唤
            boolean particleEnabled = true; // 默认启用粒子效果
            boolean useUserCustomSummon = true; // 默认使用用户配置版本召唤

            // 读取配置参数
            if (config != null) {
                if (config.skillCooldownOverrides != null) {
                    randomSummonInterval = config.skillCooldownOverrides.getOrDefault("random_summon_interval", randomSummonInterval);
                    autoAttackInterval = config.skillCooldownOverrides.getOrDefault("auto_attack_interval", autoAttackInterval);
                    electricShockInterval = config.skillCooldownOverrides.getOrDefault("electric_shock_interval", electricShockInterval);
                    freezeInterval = config.skillCooldownOverrides.getOrDefault("freeze_interval", freezeInterval);
                    electricFieldInterval = config.skillCooldownOverrides.getOrDefault("electric_field_interval", electricFieldInterval);
                    weaknessLevel = config.skillCooldownOverrides.getOrDefault("weakness_level", weaknessLevel);
                    weaknessDuration = config.skillCooldownOverrides.getOrDefault("weakness_duration", weaknessDuration);
                    freezeDuration = config.skillCooldownOverrides.getOrDefault("freeze_duration", freezeDuration);
                }

                if (config.specialAbilities != null) {
                    // 读取技能开关
                    randomSummonEnabled = getBooleanFromConfig(config.specialAbilities, "random_summon_enabled", randomSummonEnabled);
                    autoAttackEnabled = getBooleanFromConfig(config.specialAbilities, "auto_attack_enabled", autoAttackEnabled);
                    electricShockEnabled = getBooleanFromConfig(config.specialAbilities, "electric_shock_enabled", electricShockEnabled);
                    attackEnhanceEnabled = getBooleanFromConfig(config.specialAbilities, "attack_enhance_enabled", attackEnhanceEnabled);
                    freezeEnabled = getBooleanFromConfig(config.specialAbilities, "freeze_enabled", freezeEnabled);
                    electricFieldEnabled = getBooleanFromConfig(config.specialAbilities, "electric_field_enabled", electricFieldEnabled);
                    scientistSummonEnabled = getBooleanFromConfig(config.specialAbilities, "scientist_summon_enabled", scientistSummonEnabled);
                    particleEnabled = getBooleanFromConfig(config.specialAbilities, "particle_enabled", particleEnabled);
                    useUserCustomSummon = getBooleanFromConfig(config.specialAbilities, "use_user_custom_summon", useUserCustomSummon);

                    // 读取数值参数
                    scientistSummonThreshold = getDoubleFromConfig(config.specialAbilities, "scientist_summon_threshold", scientistSummonThreshold);
                    electricShockDamage = getDoubleFromConfig(config.specialAbilities, "electric_shock_damage", electricShockDamage);
                    electricFieldDamage = getDoubleFromConfig(config.specialAbilities, "electric_field_damage", electricFieldDamage);
                    autoAttackRange = getDoubleFromConfig(config.specialAbilities, "auto_attack_range", autoAttackRange);
                    knockbackDistance = getDoubleFromConfig(config.specialAbilities, "knockback_distance", knockbackDistance);
                }
            }

            // 设置元数据
            setMetadata(zombie, attackEnhanceEnabled, scientistSummonEnabled,
                       weaknessLevel, weaknessDuration, knockbackDistance,
                       scientistSummonThreshold);

            // 存储召唤配置
            useUserCustomSummonMap.put(zombie, useUserCustomSummon);

            // 启动技能任务
            startSkillTasks(zombie, randomSummonEnabled, autoAttackEnabled, electricShockEnabled,
                           freezeEnabled, electricFieldEnabled, particleEnabled, useUserCustomSummon,
                           randomSummonInterval, autoAttackInterval, electricShockInterval,
                           freezeInterval, electricFieldInterval, autoAttackRange,
                           electricShockDamage, electricFieldDamage, freezeDuration);

            if (debugMode) {
                logger.info("已为" + zombieId + "启用变异博士终极八重技能系统");
            }

        } catch (Exception e) {
            logger.warning("启用变异博士技能失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 从配置中获取布尔值
     */
    private boolean getBooleanFromConfig(Map<String, Object> abilities, String key, boolean defaultValue) {
        Object obj = abilities.get(key);
        return obj instanceof Boolean ? (Boolean) obj : defaultValue;
    }

    /**
     * 从配置中获取双精度值
     */
    private double getDoubleFromConfig(Map<String, Object> abilities, String key, double defaultValue) {
        Object obj = abilities.get(key);
        return obj instanceof Number ? ((Number) obj).doubleValue() : defaultValue;
    }

    /**
     * 设置僵尸元数据
     */
    private void setMetadata(Zombie zombie, boolean attackEnhanceEnabled, boolean scientistSummonEnabled,
                           int weaknessLevel, int weaknessDuration, double knockbackDistance,
                           double scientistSummonThreshold) {

        if (attackEnhanceEnabled) {
            zombie.setMetadata("doctor_attack_enhance_enabled", new FixedMetadataValue(plugin, true));
            zombie.setMetadata("doctor_weakness_level", new FixedMetadataValue(plugin, weaknessLevel));
            zombie.setMetadata("doctor_weakness_duration", new FixedMetadataValue(plugin, weaknessDuration));
            zombie.setMetadata("doctor_knockback_distance", new FixedMetadataValue(plugin, knockbackDistance));
        }

        if (scientistSummonEnabled) {
            zombie.setMetadata("doctor_scientist_summon_enabled", new FixedMetadataValue(plugin, true));
            zombie.setMetadata("doctor_scientist_summon_threshold", new FixedMetadataValue(plugin, scientistSummonThreshold));
            scientistSummonDamage.put(zombie, 0.0);
        }
    }

    /**
     * 启动技能任务
     */
    private void startSkillTasks(Zombie zombie, boolean randomSummonEnabled, boolean autoAttackEnabled,
                               boolean electricShockEnabled, boolean freezeEnabled, boolean electricFieldEnabled,
                               boolean particleEnabled, boolean useUserCustomSummon, int randomSummonInterval,
                               int autoAttackInterval, int electricShockInterval, int freezeInterval,
                               int electricFieldInterval, double autoAttackRange, double electricShockDamage,
                               double electricFieldDamage, int freezeDuration) {

        if (randomSummonEnabled) {
            startRandomSummonTask(zombie, randomSummonInterval, useUserCustomSummon);
        }
        if (autoAttackEnabled) {
            startAutoAttackTask(zombie, autoAttackInterval, autoAttackRange);
        }
        if (electricShockEnabled) {
            startElectricShockTask(zombie, electricShockInterval, electricShockDamage);
        }
        if (freezeEnabled) {
            startFreezeTask(zombie, freezeInterval, freezeDuration);
        }
        if (electricFieldEnabled) {
            startElectricFieldTask(zombie, electricFieldInterval, electricFieldDamage);
        }
        if (particleEnabled) {
            startParticleTask(zombie);
        }
    }

    /**
     * 启动随机召唤任务 - 技能1
     */
    private void startRandomSummonTask(Zombie zombie, int interval, boolean useUserCustom) {
        BukkitTask task = new BukkitRunnable() {
            @Override
            public void run() {
                if (zombie == null || zombie.isDead()) {
                    cancel();
                    randomSummonTasks.remove(zombie);
                    return;
                }

                // 从ID1-ID24中随机选择一个
                String randomZombieId = ZOMBIE_IDS[(int) (Math.random() * ZOMBIE_IDS.length)];

                // 计算召唤位置
                Location summonLoc = zombie.getLocation().clone();
                double offsetX = (Math.random() - 0.5) * 10;
                double offsetZ = (Math.random() - 0.5) * 10;
                summonLoc.add(offsetX, 0, offsetZ);

                // 确保生成位置有效
                while (!summonLoc.getBlock().getType().isSolid() && summonLoc.getBlockY() > 0) {
                    summonLoc.setY(summonLoc.getY() - 1);
                }
                summonLoc.setY(summonLoc.getY() + 1);

                try {
                    Zombie summonedZombie = null;

                    if (useUserCustom) {
                        // 使用双系统召唤（用户配置版本）
                        if (plugin instanceof org.Ver_zhzh.deathZombieV4.DeathZombieV4) {
                            org.Ver_zhzh.deathZombieV4.DeathZombieV4 dzPlugin = (org.Ver_zhzh.deathZombieV4.DeathZombieV4) plugin;
                            if (dzPlugin.getDualZombieSystemManager() != null) {
                                summonedZombie = dzPlugin.getDualZombieSystemManager().spawnCustomZombieDirect(summonLoc, randomZombieId);
                            }
                        }
                    } else {
                        // 使用原有系统召唤（默认版本）
                        summonedZombie = originalCustomZombie.spawnCustomZombieDirect(summonLoc, randomZombieId);
                    }

                    if (summonedZombie != null) {
                        // 显示召唤效果
                        zombie.getWorld().spawnParticle(Particle.PORTAL, summonLoc, 30, 1.0, 1.0, 1.0, 0.1);
                        zombie.getWorld().playSound(summonLoc, Sound.ENTITY_ENDERMAN_TELEPORT, 1.0f, 0.8f);

                        if (debugMode) {
                            logger.info("变异博士随机召唤了 " + randomZombieId);
                        }
                    }
                } catch (Exception e) {
                    logger.warning("变异博士随机召唤失败: " + e.getMessage());
                }
            }
        }.runTaskTimer(plugin, 600, interval); // 30秒后开始

        randomSummonTasks.put(zombie, task);

        if (debugMode) {
            logger.info("已启动变异博士随机召唤任务，间隔: " + interval + " ticks");
        }
    }

    /**
     * 启动自动攻击任务 - 技能3
     */
    private void startAutoAttackTask(Zombie zombie, int interval, double range) {
        BukkitTask task = new BukkitRunnable() {
            @Override
            public void run() {
                if (zombie == null || zombie.isDead()) {
                    cancel();
                    autoAttackTasks.remove(zombie);
                    return;
                }

                // 检查范围内的玩家
                for (Entity entity : zombie.getNearbyEntities(range, range, range)) {
                    if (entity instanceof Player) {
                        Player player = (Player) entity;

                        // 随机选择攻击方式：弓箭或烈焰弹
                        if (Math.random() < 0.5) {
                            // 发射弓箭
                            Arrow arrow = zombie.getWorld().spawnArrow(
                                zombie.getEyeLocation(),
                                player.getLocation().add(0, 1, 0).subtract(zombie.getEyeLocation()).toVector().normalize(),
                                1.5f, 0.1f
                            );
                            arrow.setShooter(zombie);
                            arrow.setDamage(6.0);

                            // 弓箭音效
                            zombie.getWorld().playSound(zombie.getLocation(), Sound.ENTITY_ARROW_SHOOT, 1.0f, 1.0f);
                        } else {
                            // 发射烈焰弹
                            Fireball fireball = zombie.getWorld().spawn(zombie.getEyeLocation(), Fireball.class);
                            Vector direction = player.getLocation().add(0, 1, 0).subtract(zombie.getEyeLocation()).toVector().normalize();
                            fireball.setDirection(direction);
                            fireball.setShooter(zombie);
                            fireball.setYield(2.0f);

                            // 烈焰弹音效
                            zombie.getWorld().playSound(zombie.getLocation(), Sound.ENTITY_GHAST_SHOOT, 1.0f, 1.0f);
                        }

                        if (debugMode) {
                            logger.info("变异博士对玩家 " + player.getName() + " 进行了自动攻击");
                        }

                        break; // 每次只攻击一个玩家
                    }
                }
            }
        }.runTaskTimer(plugin, 20, interval); // 1秒后开始

        autoAttackTasks.put(zombie, task);

        if (debugMode) {
            logger.info("已启动变异博士自动攻击任务，间隔: " + interval + " ticks，范围: " + range + " 格");
        }
    }

    /**
     * 启动电击任务 - 技能4
     */
    private void startElectricShockTask(Zombie zombie, int interval, double damage) {
        BukkitTask task = new BukkitRunnable() {
            @Override
            public void run() {
                if (zombie == null || zombie.isDead()) {
                    cancel();
                    electricShockTasks.remove(zombie);
                    return;
                }

                // 对所有在线玩家造成巨额电击伤害
                for (Player player : plugin.getServer().getOnlinePlayers()) {
                    // 雷电效果
                    player.getWorld().strikeLightningEffect(player.getLocation());

                    // 造成伤害
                    player.damage(damage, zombie);

                    // 电击粒子效果
                    player.getWorld().spawnParticle(Particle.CRIT, player.getLocation().add(0, 1, 0), 20, 0.5, 1, 0.5, 0.1);
                    player.getWorld().spawnParticle(Particle.ENCHANT, player.getLocation().add(0, 1, 0), 15, 0.5, 1, 0.5, 0.1);

                    // 电击音效
                    player.getWorld().playSound(player.getLocation(), Sound.ENTITY_LIGHTNING_BOLT_THUNDER, 1.0f, 1.0f);

                    if (debugMode) {
                        logger.info("变异博士对玩家 " + player.getName() + " 造成了 " + damage + " 点电击伤害");
                    }
                }
            }
        }.runTaskTimer(plugin, 3600, interval); // 3分钟后开始

        electricShockTasks.put(zombie, task);

        if (debugMode) {
            logger.info("已启动变异博士电击任务，间隔: " + interval + " ticks，伤害: " + damage);
        }
    }

    /**
     * 启动冻结任务 - 技能6
     */
    private void startFreezeTask(Zombie zombie, int interval, int duration) {
        BukkitTask task = new BukkitRunnable() {
            @Override
            public void run() {
                if (zombie == null || zombie.isDead()) {
                    cancel();
                    freezeTasks.remove(zombie);
                    return;
                }

                // 对周围所有玩家施加冻结效果
                for (Entity entity : zombie.getNearbyEntities(20, 20, 20)) {
                    if (entity instanceof Player) {
                        Player player = (Player) entity;

                        // 施加极强的缓慢效果（几乎无法移动）
                        player.addPotionEffect(new PotionEffect(PotionEffectType.SLOWNESS, duration, 9));

                        // 施加极强的挖掘疲劳效果（无法攻击）
                        player.addPotionEffect(new PotionEffect(PotionEffectType.MINING_FATIGUE, duration, 9));

                        // 施加跳跃减弱效果（无法跳跃）
                        player.addPotionEffect(new PotionEffect(PotionEffectType.JUMP_BOOST, duration, -10));

                        // 冻结视觉效果
                        player.getWorld().spawnParticle(Particle.CLOUD, player.getLocation().add(0, 1, 0), 20, 0.5, 1, 0.5, 0.1);
                        player.getWorld().playSound(player.getLocation(), Sound.BLOCK_GLASS_BREAK, 1.0f, 0.5f);

                        if (debugMode) {
                            logger.info("变异博士冻结了玩家 " + player.getName());
                        }
                    }
                }

                // 全局冻结音效
                zombie.getWorld().playSound(zombie.getLocation(), Sound.ENTITY_ENDER_DRAGON_GROWL, 2.0f, 0.5f);
            }
        }.runTaskTimer(plugin, 4800, interval); // 4分钟后开始

        freezeTasks.put(zombie, task);

        if (debugMode) {
            logger.info("已启动变异博士冻结任务，间隔: " + interval + " ticks，持续: " + duration + " ticks");
        }
    }

    /**
     * 启动电流领域任务 - 技能7
     */
    private void startElectricFieldTask(Zombie zombie, int interval, double damage) {
        BukkitTask task = new BukkitRunnable() {
            @Override
            public void run() {
                if (zombie == null || zombie.isDead()) {
                    cancel();
                    electricFieldTasks.remove(zombie);
                    return;
                }

                // 对10*10范围内的玩家造成电流伤害
                for (Entity entity : zombie.getNearbyEntities(10, 10, 10)) {
                    if (entity instanceof Player) {
                        Player player = (Player) entity;

                        // 造成电流伤害
                        player.damage(damage, zombie);

                        // 电流粒子效果
                        player.getWorld().spawnParticle(Particle.CRIT, player.getLocation().add(0, 1, 0), 8, 0.3, 0.5, 0.3, 0.1);
                        player.getWorld().spawnParticle(Particle.ENCHANT, player.getLocation().add(0, 1, 0), 5, 0.3, 0.5, 0.3, 0.1);

                        // 电流音效
                        player.getWorld().playSound(player.getLocation(), Sound.BLOCK_REDSTONE_TORCH_BURNOUT, 0.5f, 1.5f);

                        if (debugMode) {
                            logger.info("变异博士的电流领域对玩家 " + player.getName() + " 造成了 " + damage + " 点伤害");
                        }
                    }
                }
            }
        }.runTaskTimer(plugin, 60, interval); // 3秒后开始

        electricFieldTasks.put(zombie, task);

        if (debugMode) {
            logger.info("已启动变异博士电流领域任务，间隔: " + interval + " ticks，伤害: " + damage);
        }
    }

    /**
     * 启动华丽粒子效果任务
     */
    private void startParticleTask(Zombie zombie) {
        BukkitTask task = new BukkitRunnable() {
            @Override
            public void run() {
                if (zombie == null || zombie.isDead()) {
                    cancel();
                    particleTasks.remove(zombie);
                    return;
                }

                Location location = zombie.getLocation();

                // 1. 华丽的魔法光环
                double time = System.currentTimeMillis() / 1000.0;
                for (int i = 0; i < 12; i++) {
                    double angle = (time + i * Math.PI / 6) % (2 * Math.PI);
                    double radius = 2.0 + 0.5 * Math.sin(time * 3);
                    double x = radius * Math.cos(angle);
                    double z = radius * Math.sin(angle);
                    double y = 1.5 + 0.3 * Math.sin(time * 4 + i);

                    Location particleLoc = location.clone().add(x, y, z);
                    zombie.getWorld().spawnParticle(Particle.ENCHANT, particleLoc, 2, 0.1, 0.1, 0.1, 0.01);
                    zombie.getWorld().spawnParticle(Particle.PORTAL, particleLoc, 1, 0.05, 0.05, 0.05, 0.01);
                }

                // 2. 电流螺旋
                for (int i = 0; i < 8; i++) {
                    double spiralAngle = (time * 2 + i * Math.PI / 4) % (2 * Math.PI);
                    double spiralRadius = 1.0;
                    double spiralX = spiralRadius * Math.cos(spiralAngle);
                    double spiralZ = spiralRadius * Math.sin(spiralAngle);
                    double spiralY = 0.5 + (time * 0.5 + i * 0.2) % 3.0;

                    Location spiralLoc = location.clone().add(spiralX, spiralY, spiralZ);
                    zombie.getWorld().spawnParticle(Particle.CRIT, spiralLoc, 1, 0, 0, 0, 0);
                }

                // 3. 脚底能量波纹
                double waveRadius = (time * 2) % 4.0;
                for (int i = 0; i < 16; i++) {
                    double waveAngle = Math.toRadians(i * 22.5);
                    double waveX = waveRadius * Math.cos(waveAngle);
                    double waveZ = waveRadius * Math.sin(waveAngle);

                    Location waveLoc = location.clone().add(waveX, 0.1, waveZ);
                    zombie.getWorld().spawnParticle(Particle.WITCH, waveLoc, 1, 0, 0, 0, 0.01);
                }
            }
        }.runTaskTimer(plugin, 0, 2); // 立即开始，每0.1秒执行一次

        particleTasks.put(zombie, task);

        if (debugMode) {
            logger.info("已启动变异博士华丽粒子效果任务");
        }
    }

    /**
     * 处理变异博士受到伤害事件 - 用于技能7（科学家召唤）
     */
    public void handleDamage(Zombie zombie, double damage) {
        if (!zombie.hasMetadata("mutantDoctorZombie")) {
            return;
        }

        // 技能7：科学家召唤
        if (zombie.hasMetadata("doctor_scientist_summon_enabled")) {
            double threshold = zombie.getMetadata("doctor_scientist_summon_threshold").get(0).asDouble();
            double currentDamage = scientistSummonDamage.getOrDefault(zombie, 0.0) + damage;
            scientistSummonDamage.put(zombie, currentDamage);

            if (currentDamage >= threshold) {
                // 重置伤害计数
                scientistSummonDamage.put(zombie, 0.0);

                // 召唤变异科学家
                summonMutantScientist(zombie);
            }
        }
    }



    /**
     * 召唤变异科学家 - 技能8的实现
     */
    private void summonMutantScientist(Zombie zombie) {
        try {
            Location summonLoc = zombie.getLocation().clone();
            double offsetX = (Math.random() - 0.5) * 6;
            double offsetZ = (Math.random() - 0.5) * 6;
            summonLoc.add(offsetX, 0, offsetZ);

            // 确保生成位置有效
            while (!summonLoc.getBlock().getType().isSolid() && summonLoc.getBlockY() > 0) {
                summonLoc.setY(summonLoc.getY() - 1);
            }
            summonLoc.setY(summonLoc.getY() + 1);

            // 召唤变异科学家（ID21）- 支持用户配置版本
            Zombie scientist = null;
            boolean useUserCustom = useUserCustomSummonMap.getOrDefault(zombie, true);

            if (useUserCustom) {
                // 使用双系统召唤（用户配置版本）
                if (plugin instanceof org.Ver_zhzh.deathZombieV4.DeathZombieV4) {
                    org.Ver_zhzh.deathZombieV4.DeathZombieV4 dzPlugin = (org.Ver_zhzh.deathZombieV4.DeathZombieV4) plugin;
                    if (dzPlugin.getDualZombieSystemManager() != null) {
                        scientist = dzPlugin.getDualZombieSystemManager().spawnCustomZombieDirect(summonLoc, "id21");
                        if (debugMode) {
                            logger.info("变异博士召唤了用户配置版本的变异科学家");
                        }
                    }
                }
            } else {
                // 使用原有系统召唤（默认版本）
                scientist = originalCustomZombie.spawnCustomZombieDirect(summonLoc, "id21");
                if (debugMode) {
                    logger.info("变异博士召唤了默认版本的变异科学家");
                }
            }

            if (scientist != null) {
                // 科学家召唤效果
                zombie.getWorld().spawnParticle(Particle.PORTAL, summonLoc, 20, 0.5, 0.5, 0.5, 0.1);
                zombie.getWorld().spawnParticle(Particle.WITCH, summonLoc, 15, 0.5, 0.5, 0.5, 0.1);
                zombie.getWorld().playSound(summonLoc, Sound.ENTITY_WITCH_AMBIENT, 1.0f, 0.8f);
            }
        } catch (Exception e) {
            logger.warning("变异博士召唤变异科学家失败: " + e.getMessage());
        }
    }

    /**
     * 清理所有任务
     */
    public void cleanup() {
        randomSummonTasks.values().forEach(BukkitTask::cancel);
        randomSummonTasks.clear();

        autoAttackTasks.values().forEach(BukkitTask::cancel);
        autoAttackTasks.clear();

        electricShockTasks.values().forEach(BukkitTask::cancel);
        electricShockTasks.clear();

        freezeTasks.values().forEach(BukkitTask::cancel);
        freezeTasks.clear();

        electricFieldTasks.values().forEach(BukkitTask::cancel);
        electricFieldTasks.clear();

        particleTasks.values().forEach(BukkitTask::cancel);
        particleTasks.clear();

        scientistSummonDamage.clear();
        useUserCustomSummonMap.clear();
    }
}
