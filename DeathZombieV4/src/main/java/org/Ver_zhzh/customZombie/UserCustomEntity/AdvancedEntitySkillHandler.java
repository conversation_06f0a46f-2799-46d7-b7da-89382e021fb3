package org.Ver_zhzh.customZombie.UserCustomEntity;

import org.bukkit.entity.LivingEntity;
import org.bukkit.plugin.Plugin;

import java.util.logging.Logger;

/**
 * 高级实体技能处理器
 * 处理IDC实体的特殊技能和能力
 *
 * <AUTHOR>
 * @version 1.0
 * 符合阿里巴巴编码规范
 */
public class AdvancedEntitySkillHandler {

    private final Plugin plugin;
    private final Logger logger;
    private final UserCustomEntity userCustomEntity;
    private boolean debugMode = false;

    /**
     * 构造函数
     *
     * @param plugin 插件实例
     * @param userCustomEntity UserCustomEntity实例
     */
    public AdvancedEntitySkillHandler(Plugin plugin, UserCustomEntity userCustomEntity) {
        this.plugin = plugin;
        this.logger = plugin.getLogger();
        this.userCustomEntity = userCustomEntity;
        this.debugMode = userCustomEntity.isDebugMode();
    }

    /**
     * 启用实体的高级技能
     *
     * @param entity 实体
     * @param entityId 实体ID
     * @param config 配置
     */
    public void enableAdvancedSkills(LivingEntity entity, String entityId, EntityOverrideConfig config) {
        try {
            switch (entityId) {
                case "idc1": // 变异僵尸01
                    enableMutantZombie01AdvancedSkills(entity, config);
                    break;
                // 其他实体的高级技能将在后续添加
                default:
                    if (debugMode) {
                        logger.info("实体 " + entityId + " 暂无高级技能");
                    }
                    break;
            }
        } catch (Exception e) {
            logger.warning("启用高级技能失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 启用变异僵尸01的高级技能
     * 包括剧毒攻击的高级配置
     */
    private void enableMutantZombie01AdvancedSkills(LivingEntity entity, EntityOverrideConfig config) {
        if (debugMode) {
            logger.info("开始为变异僵尸01配置高级技能");
        }

        // 检查是否启用剧毒攻击
        boolean poisonEnabled = true;
        if (config.specialAbilities.containsKey("poison_enabled")) {
            poisonEnabled = (Boolean) config.specialAbilities.get("poison_enabled");
        }

        if (poisonEnabled) {
            // 配置剧毒攻击参数
            configurePoisonAttack(entity, config);
        }

        // 检查是否有其他特殊能力
        configureOtherAbilities(entity, config);

        if (debugMode) {
            logger.info("变异僵尸01高级技能配置完成");
        }
    }

    /**
     * 配置剧毒攻击
     */
    private void configurePoisonAttack(LivingEntity entity, EntityOverrideConfig config) {
        // 从配置或技能冷却覆盖中获取参数
        int poisonLevel = getConfigValue(config, "poison_level", "poison_level", 0);
        int poisonDuration = getConfigValue(config, "poison_duration", "poison_duration", 60);
        double poisonChance = getConfigValue(config, "poison_chance", "poison_chance", 1.0);

        // 设置剧毒攻击元数据
        entity.setMetadata("advancedPoisonAttack", new org.bukkit.metadata.FixedMetadataValue(plugin, true));
        entity.setMetadata("poisonLevel", new org.bukkit.metadata.FixedMetadataValue(plugin, poisonLevel));
        entity.setMetadata("poisonDuration", new org.bukkit.metadata.FixedMetadataValue(plugin, poisonDuration));
        entity.setMetadata("poisonChance", new org.bukkit.metadata.FixedMetadataValue(plugin, poisonChance));

        if (debugMode) {
            logger.info("配置剧毒攻击 - 等级: " + poisonLevel + ", 持续时间: " + poisonDuration + ", 概率: " + poisonChance);
        }
    }

    /**
     * 配置其他能力
     */
    private void configureOtherAbilities(LivingEntity entity, EntityOverrideConfig config) {
        // 检查是否有特殊抗性
        if (config.specialAbilities.containsKey("fire_resistance") &&
            (Boolean) config.specialAbilities.get("fire_resistance")) {

            entity.setMetadata("hasFireResistance", new org.bukkit.metadata.FixedMetadataValue(plugin, true));

            if (debugMode) {
                logger.info("配置火焰抗性");
            }
        }
    }

    /**
     * 从配置中获取数值，支持从特殊能力和技能冷却覆盖中获取
     */
    private <T> T getConfigValue(EntityOverrideConfig config, String abilityKey, String cooldownKey, T defaultValue) {
        // 优先从特殊能力中获取
        if (config.specialAbilities.containsKey(abilityKey)) {
            try {
                return (T) config.specialAbilities.get(abilityKey);
            } catch (ClassCastException e) {
                logger.warning("配置值类型错误: " + abilityKey);
            }
        }

        // 然后从技能冷却覆盖中获取
        if (config.skillCooldownOverrides.containsKey(cooldownKey)) {
            try {
                Object value = config.skillCooldownOverrides.get(cooldownKey);
                if (defaultValue instanceof Double && value instanceof Integer) {
                    return (T) Double.valueOf(((Integer) value).doubleValue());
                }
                return (T) value;
            } catch (ClassCastException e) {
                logger.warning("配置值类型错误: " + cooldownKey);
            }
        }

        return defaultValue;
    }

    /**
     * 更新调试模式状态
     */
    public void updateDebugMode() {
        this.debugMode = userCustomEntity.isDebugMode();
    }
}

