package org.Ver_zhzh.deathZombieV4.web;

import java.io.BufferedReader;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.InetAddress;
import java.net.InetSocketAddress;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

import org.Ver_zhzh.deathZombieV4.DeathZombieV4;
import org.Ver_zhzh.deathZombieV4.game.GameManager;
import org.Ver_zhzh.deathZombieV4.utils.MonsterIdMapper;
import org.Ver_zhzh.deathZombieV4.utils.Region;
import org.Ver_zhzh.deathZombieV4.web.api.EquipmentApiHandler;
import org.Ver_zhzh.deathZombieV4.web.api.MonsterPresetApiHandler;
import org.Ver_zhzh.deathZombieV4.web.api.ZombieSpawnApiHandler;
import org.bukkit.Bukkit;
import org.bukkit.Location;
import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.entity.Player;
import org.bukkit.scheduler.BukkitRunnable;
import org.json.simple.JSONArray;
import org.json.simple.JSONObject;
import org.json.simple.parser.JSONParser;
import org.json.simple.parser.ParseException;

import com.sun.net.httpserver.HttpExchange;
import com.sun.net.httpserver.HttpHandler;
import com.sun.net.httpserver.HttpServer;

public class WebServer {

    private final DeathZombieV4 plugin;
    private HttpServer server;
    private int port;
    private boolean isRunning = false;
    private String password;
    private AuthFilter authFilter; // 添加一个全局的AuthFilter实例

    public WebServer(DeathZombieV4 plugin) {
        this.plugin = plugin;
    }

    /**
     * 启动Web服务器
     *
     * @param port 端口号
     * @return 是否成功启动
     */
    public boolean start(int port) {
        return start(port, null);
    }

    /**
     * 启动Web服务器
     *
     * @param port 端口号
     * @param host 绑定的IP地址，为null或空字符串时使用本机地址
     * @return 是否成功启动
     */
    public boolean start(int port, String host) {
        if (isRunning) {
            return false;
        }

        this.port = port;

        // 从配置文件中获取密码
        this.password = plugin.getConfig().getString("web.password", "admin123");

        try {
            // 确定绑定地址
            InetAddress bindAddress;
            if (host == null || host.trim().isEmpty()) {
                // 如果host为空，使用本机地址
                bindAddress = InetAddress.getLocalHost();
                plugin.getLogger().info("Web服务器将绑定到本机地址: " + bindAddress.getHostAddress());
            } else {
                // 使用指定的IP地址
                bindAddress = InetAddress.getByName(host.trim());
                plugin.getLogger().info("Web服务器将绑定到指定地址: " + bindAddress.getHostAddress());
            }

            server = HttpServer.create(new InetSocketAddress(bindAddress, port), 0);

            // 创建全局身份验证过滤器
            this.authFilter = new AuthFilter();

            // 静态资源
            server.createContext("/", new StaticFileHandler());

            // 登录端点 - 不需要身份验证
            server.createContext("/api/login", new LoginHandler());

            // API端点 - 需要身份验证
            server.createContext("/api/games", authFilter.wrap(new GamesApiHandler()));
            server.createContext("/api/game", authFilter.wrap(new GameDetailApiHandler()));
            server.createContext("/api/save", authFilter.wrap(new SaveGameApiHandler()));
            server.createContext("/api/region", authFilter.wrap(new RegionApiHandler()));
            server.createContext("/api/reload", authFilter.wrap(new ReloadConfigApiHandler()));
            server.createContext("/api/games/create", authFilter.wrap(new CreateGameApiHandler()));
            server.createContext("/api/player-location", authFilter.wrap(new PlayerLocationApiHandler()));

            // 装备API端点 - 需要身份验证
            EquipmentApiHandler equipmentApiHandler = new EquipmentApiHandler(plugin);
            server.createContext("/api/equipment", authFilter.wrap(equipmentApiHandler));
            server.createContext("/api/save-game", authFilter.wrap(equipmentApiHandler));
            plugin.getLogger().info("成功注册装备API端点");

            // 怪物预设API端点 - 需要身份验证
            MonsterPresetApiHandler monsterPresetApiHandler = new MonsterPresetApiHandler(plugin);
            server.createContext("/api/monster-presets", authFilter.wrap(monsterPresetApiHandler));
            server.createContext("/api/monster-preset", authFilter.wrap(monsterPresetApiHandler));
            plugin.getLogger().info("成功注册怪物预设API端点");

            // 僵尸生成设置API端点 - 需要身份验证
            ZombieSpawnApiHandler zombieSpawnApiHandler = new ZombieSpawnApiHandler(plugin);
            server.createContext("/api/zombie-spawn", authFilter.wrap(zombieSpawnApiHandler));
            plugin.getLogger().info("成功注册僵尸生成设置API端点");

            // 设置线程池
            server.setExecutor(Executors.newCachedThreadPool());
            server.start();

            // 获取实际绑定的地址
            InetSocketAddress actualAddress = server.getAddress();
            String actualHost = actualAddress.getAddress().getHostAddress();
            int actualPort = actualAddress.getPort();

            plugin.getLogger().info("WebUI服务器已启动（直接使用JAR包中的WebUI文件）");
            plugin.getLogger().info("绑定地址: " + actualHost + ":" + actualPort);
            plugin.getLogger().info("WebUI密码验证已启用，密码: " + this.password);

            // 显示访问地址信息
            if ("0.0.0.0".equals(actualHost)) {
                plugin.getLogger().info("服务器绑定到所有网络接口，可通过以下地址访问:");
                try {
                    String localIp = java.net.InetAddress.getLocalHost().getHostAddress();
                    plugin.getLogger().info("  本机访问: http://127.0.0.1:" + actualPort);
                    plugin.getLogger().info("  局域网访问: http://" + localIp + ":" + actualPort);
                } catch (Exception e) {
                    plugin.getLogger().warning("无法获取本机IP地址: " + e.getMessage());
                    plugin.getLogger().info("  本机访问: http://127.0.0.1:" + actualPort);
                }
            } else {
                plugin.getLogger().info("访问地址: http://" + actualHost + ":" + actualPort);

                // 如果绑定的不是回环地址，也显示本机访问方式
                if (!actualHost.equals("127.0.0.1") && !actualHost.equals("localhost")) {
                    plugin.getLogger().info("本机访问: http://127.0.0.1:" + actualPort + " (如果支持)");
                }
            }

            isRunning = true;
            return true;
        } catch (IOException e) {
            plugin.getLogger().severe("启动WebUI服务器失败: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 停止Web服务器
     */
    public void stop() {
        if (isRunning && server != null) {
            server.stop(0);
            isRunning = false;
            plugin.getLogger().info("WebUI服务器已停止");
        }
    }

    /**
     * 检查服务器是否运行中
     *
     * @return 服务器是否运行
     */
    public boolean isRunning() {
        return isRunning;
    }

    /**
     * 获取服务器端口
     *
     * @return 端口号
     */
    public int getPort() {
        return port;
    }

    /**
     * 处理静态文件请求
     */
    private class StaticFileHandler implements HttpHandler {

        public StaticFileHandler() {
            // 只创建用户可能需要自定义的目录
            File dataFolder = plugin.getDataFolder();
            File webFolder = new File(dataFolder, "web");
            File resourcesFolder = new File(webFolder, "resources");
            File itemsFolder = new File(resourcesFolder, "items");

            // 创建物品图标目录，用户可以在此放置自定义物品图标
            if (!itemsFolder.exists()) {
                itemsFolder.mkdirs();
                plugin.getLogger().info("创建物品图标目录: " + itemsFolder.getAbsolutePath());
            }
        }

        @Override
        public void handle(HttpExchange exchange) throws IOException {
            String path = exchange.getRequestURI().getPath();

            // 如果请求根路径，返回index.html
            if ("/".equals(path)) {
                path = "/index.html";
            }

            InputStream resourceStream = null;

            // 对于物品图标，允许从文件系统加载用户自定义图标
            if (path.startsWith("/resources/items/")) {
                File dataFolder = plugin.getDataFolder();
                File resourceFile = new File(dataFolder, "web" + path);
                if (resourceFile.exists() && resourceFile.isFile()) {
                    try {
                        resourceStream = new FileInputStream(resourceFile);
                        plugin.getLogger().info("加载用户自定义物品图标: " + resourceFile.getName());
                    } catch (Exception e) {
                        plugin.getLogger().warning("加载用户自定义物品图标失败: " + e.getMessage());
                    }
                }
            }

            // 从JAR包加载资源
            if (resourceStream == null) {
                resourceStream = plugin.getResource("web" + path);
                if (resourceStream != null) {
                    plugin.getLogger().info("从JAR包加载资源: web" + path);
                }
            }

            if (resourceStream == null) {
                // 资源不存在
                plugin.getLogger().warning("资源不存在: " + path);
                exchange.sendResponseHeaders(404, 0);
                exchange.getResponseBody().close();
                return;
            }

            // 设置Content-Type
            String contentType = getContentType(path);
            exchange.getResponseHeaders().set("Content-Type", contentType);

            // 读取资源内容
            ByteArrayOutputStream result = new ByteArrayOutputStream();
            byte[] buffer = new byte[1024];
            int length;
            while ((length = resourceStream.read(buffer)) != -1) {
                result.write(buffer, 0, length);
            }
            resourceStream.close();

            // 发送响应
            byte[] response = result.toByteArray();
            exchange.sendResponseHeaders(200, response.length);
            try (OutputStream os = exchange.getResponseBody()) {
                os.write(response);
            }
        }

        /**
         * 根据文件名确定Content-Type
         */
        private String getContentType(String path) {
            if (path.endsWith(".html")) {
                return "text/html; charset=UTF-8";
            } else if (path.endsWith(".css")) {
                return "text/css; charset=UTF-8";
            } else if (path.endsWith(".js")) {
                return "application/javascript; charset=UTF-8";
            } else if (path.endsWith(".json")) {
                return "application/json; charset=UTF-8";
            } else if (path.endsWith(".png")) {
                return "image/png";
            } else if (path.endsWith(".jpg") || path.endsWith(".jpeg")) {
                return "image/jpeg";
            }
            return "application/octet-stream";
        }
    }

    /**
     * 处理获取游戏列表的API请求
     */
    private class GamesApiHandler implements HttpHandler {

        @Override
        public void handle(HttpExchange exchange) throws IOException {
            if (!"GET".equals(exchange.getRequestMethod())) {
                exchange.sendResponseHeaders(405, 0);
                exchange.getResponseBody().close();
                return;
            }

            plugin.getLogger().info("收到获取游戏列表请求");

            // 强制重新加载所有游戏配置，确保数据最新
            plugin.getGameManager().loadAllGames();

            JSONObject response = new JSONObject();
            JSONArray gamesArray = new JSONArray();

            GameManager gameManager = plugin.getGameManager();
            List<String> gameNames = gameManager.getGameNames();

            plugin.getLogger().info("找到 " + gameNames.size() + " 个游戏");

            for (String gameName : gameNames) {
                JSONObject gameObj = new JSONObject();
                gameObj.put("name", gameName);

                // 添加游戏配置状态
                Map<String, Boolean> checkResults = gameManager.checkGameConfig(gameName);
                boolean isComplete = checkResults.getOrDefault("complete", false);
                gameObj.put("complete", isComplete); // 使用"complete"键名，与前端保持一致

                plugin.getLogger().info("游戏 '" + gameName + "' 配置完整: " + isComplete);

                gamesArray.add(gameObj);
            }

            response.put("games", gamesArray);
            plugin.getLogger().info("游戏列表API响应: " + response.toJSONString());
            sendJsonResponse(exchange, response);
        }
    }

    /**
     * 处理获取游戏详情的API请求
     */
    private class GameDetailApiHandler implements HttpHandler {

        @Override
        public void handle(HttpExchange exchange) throws IOException {
            if (!"GET".equals(exchange.getRequestMethod())) {
                exchange.sendResponseHeaders(405, 0);
                exchange.getResponseBody().close();
                return;
            }

            // 获取查询参数
            String query = exchange.getRequestURI().getQuery();
            if (query == null || !query.startsWith("name=")) {
                exchange.sendResponseHeaders(400, 0);
                exchange.getResponseBody().close();
                return;
            }

            String gameName = query.substring(5);
            GameManager gameManager = plugin.getGameManager();

            if (!gameManager.gameExists(gameName)) {
                exchange.sendResponseHeaders(404, 0);
                exchange.getResponseBody().close();
                return;
            }

            FileConfiguration config = gameManager.getGameConfig(gameName);
            JSONObject response = new JSONObject();

            response.put("name", gameName);
            response.put("maxPlayers", config.getInt("maxPlayers", 10));

            // 添加出生点信息
            if (config.contains("spawn")) {
                JSONObject spawnObj = new JSONObject();
                spawnObj.put("world", config.getString("spawn.world"));
                spawnObj.put("x", config.getDouble("spawn.x"));
                spawnObj.put("y", config.getDouble("spawn.y"));
                spawnObj.put("z", config.getDouble("spawn.z"));
                spawnObj.put("yaw", config.getDouble("spawn.yaw"));
                spawnObj.put("pitch", config.getDouble("spawn.pitch"));
                response.put("spawn", spawnObj);
            }

            // 添加窗户区域信息
            if (config.contains("windows")) {
                JSONArray windowsArray = new JSONArray();
                ConfigurationSection windowsSection = config.getConfigurationSection("windows");
                if (windowsSection != null) {
                    for (String windowName : windowsSection.getKeys(false)) {
                        ConfigurationSection windowSection = windowsSection.getConfigurationSection(windowName);
                        if (windowSection != null) {
                            JSONObject windowObj = new JSONObject();
                            windowObj.put("name", windowName);
                            windowObj.put("world", windowSection.getString("world"));
                            windowObj.put("minX", windowSection.getDouble("minX"));
                            windowObj.put("minY", windowSection.getDouble("minY"));
                            windowObj.put("minZ", windowSection.getDouble("minZ"));
                            windowObj.put("maxX", windowSection.getDouble("maxX"));
                            windowObj.put("maxY", windowSection.getDouble("maxY"));
                            windowObj.put("maxZ", windowSection.getDouble("maxZ"));
                            windowsArray.add(windowObj);
                        }
                    }
                }
                response.put("windows", windowsArray);
            }

            // 添加回合信息
            if (config.contains("rounds")) {
                int rounds = config.getInt("rounds");
                response.put("rounds", rounds);

                // 添加僵尸数量信息
                JSONObject zombieCountObj = new JSONObject();
                for (int i = 1; i <= rounds; i++) {
                    String path = "zombieCount.round" + i;
                    if (config.contains(path)) {
                        zombieCountObj.put("round" + i, config.getInt(path));
                    }
                }
                response.put("zombieCount", zombieCountObj);
            }

            // 添加门信息
            if (config.contains("doors")) {
                plugin.getLogger().info("游戏 " + gameName + " 存在门配置，开始处理");
                JSONArray doorsArray = new JSONArray();
                ConfigurationSection doorsSection = config.getConfigurationSection("doors");

                if (doorsSection != null) {
                    for (String doorName : doorsSection.getKeys(false)) {
                        plugin.getLogger().info("处理门: " + doorName);
                        JSONObject doorObj = new JSONObject();
                        doorObj.put("name", doorName);
                        doorObj.put("locked", doorsSection.getBoolean(doorName + ".locked", true));

                        // 只有锁定的门才有解锁价格
                        if (doorsSection.getBoolean(doorName + ".locked", true)) {
                            doorObj.put("price", doorsSection.getInt(doorName + ".unlockPrice", 500));
                        }

                        // 添加区域信息（如果有）
                        if (doorsSection.contains(doorName + ".region")) {
                            JSONObject regionObj = new JSONObject();
                            ConfigurationSection regionSection = doorsSection.getConfigurationSection(doorName + ".region");

                            if (regionSection != null) {
                                regionObj.put("world", regionSection.getString("world", "world"));
                                regionObj.put("x1", regionSection.getDouble("minX", 0));
                                regionObj.put("y1", regionSection.getDouble("minY", 0));
                                regionObj.put("z1", regionSection.getDouble("minZ", 0));
                                regionObj.put("x2", regionSection.getDouble("maxX", 0));
                                regionObj.put("y2", regionSection.getDouble("maxY", 0));
                                regionObj.put("z2", regionSection.getDouble("maxZ", 0));
                                doorObj.put("region", regionObj);
                            }
                        }

                        // 添加关联的僵尸生成点（如果有）
                        if (doorsSection.isList(doorName + ".linkedSpawns")) {
                            // 新的多生成点关联
                            List<String> linkedSpawns = doorsSection.getStringList(doorName + ".linkedSpawns");
                            if (!linkedSpawns.isEmpty()) {
                                JSONArray linkedSpawnsArray = new JSONArray();
                                for (String spawn : linkedSpawns) {
                                    linkedSpawnsArray.add(spawn);
                                }
                                doorObj.put("linkedSpawns", linkedSpawnsArray);

                                // 为了兼容旧版前端，也设置第一个生成点为linkedSpawn
                                if (!linkedSpawns.isEmpty()) {
                                    doorObj.put("linkedSpawn", linkedSpawns.get(0));
                                }
                            }
                        } else if (doorsSection.contains(doorName + ".linkedSpawn")) {
                            // 兼容旧的单一生成点关联
                            String linkedSpawn = doorsSection.getString(doorName + ".linkedSpawn");
                            if (linkedSpawn != null && !linkedSpawn.isEmpty()) {
                                doorObj.put("linkedSpawn", linkedSpawn);

                                // 为了兼容新版前端，也设置为linkedSpawns数组
                                JSONArray linkedSpawnsArray = new JSONArray();
                                linkedSpawnsArray.add(linkedSpawn);
                                doorObj.put("linkedSpawns", linkedSpawnsArray);
                            }
                        }

                        doorsArray.add(doorObj);
                    }
                }

                plugin.getLogger().info("处理完成，找到 " + doorsArray.size() + " 个门");
                response.put("doors", doorsArray);
            } else {
                plugin.getLogger().info("游戏 " + gameName + " 没有门配置");
                response.put("doors", new JSONArray());
            }

            // 添加僵尸生成点信息
            if (config.contains("zombieSpawns")) {
                plugin.getLogger().info("游戏 " + gameName + " 存在僵尸生成点配置，开始处理");
                JSONArray spawnsArray = new JSONArray();
                ConfigurationSection spawnsSection = config.getConfigurationSection("zombieSpawns");

                if (spawnsSection != null) {
                    for (String spawnName : spawnsSection.getKeys(false)) {
                        plugin.getLogger().info("处理僵尸生成点: " + spawnName);
                        JSONObject spawnObj = new JSONObject();
                        spawnObj.put("name", spawnName);
                        spawnObj.put("type", spawnsSection.getString(spawnName + ".type", "id"));
                        spawnObj.put("world", spawnsSection.getString(spawnName + ".world", "world"));
                        spawnObj.put("x", spawnsSection.getDouble(spawnName + ".x", 0));
                        spawnObj.put("y", spawnsSection.getDouble(spawnName + ".y", 0));
                        spawnObj.put("z", spawnsSection.getDouble(spawnName + ".z", 0));
                        spawnObj.put("enabled", spawnsSection.getBoolean(spawnName + ".enabled", false));

                        spawnsArray.add(spawnObj);
                    }
                }

                plugin.getLogger().info("处理完成，找到 " + spawnsArray.size() + " 个僵尸生成点");
                response.put("zombieSpawns", spawnsArray);
            } else {
                plugin.getLogger().info("游戏 " + gameName + " 没有僵尸生成点配置");
                response.put("zombieSpawns", new JSONArray());
            }

            // 添加初始装备信息
            if (config.contains("initialEquipment")) {
                plugin.getLogger().info("游戏 " + gameName + " 存在初始装备配置，开始处理");
                JSONObject initialEquipmentObj = new JSONObject();
                ConfigurationSection equipmentSection = config.getConfigurationSection("initialEquipment");

                if (equipmentSection != null) {
                    for (String slotKey : equipmentSection.getKeys(false)) {
                        String itemId = equipmentSection.getString(slotKey);
                        if (itemId != null && !itemId.isEmpty()) {
                            initialEquipmentObj.put(slotKey, itemId);
                        }
                    }
                }

                plugin.getLogger().info("处理完成，找到 " + initialEquipmentObj.size() + " 个初始装备槽位");
                response.put("initialEquipment", initialEquipmentObj);
            } else {
                plugin.getLogger().info("游戏 " + gameName + " 没有初始装备配置");
                response.put("initialEquipment", new JSONObject());
            }

            // 添加电源按钮信息
            if (config.contains("power_button")) {
                plugin.getLogger().info("游戏 " + gameName + " 存在电源按钮配置，开始处理");
                JSONObject powerButtonObj = new JSONObject();
                ConfigurationSection powerButtonSection = config.getConfigurationSection("power_button");

                if (powerButtonSection != null) {
                    // 检查是否有完整的电源按钮配置
                    boolean hasValidConfig = powerButtonSection.contains("world") &&
                                           powerButtonSection.contains("x") &&
                                           powerButtonSection.contains("y") &&
                                           powerButtonSection.contains("z");

                    if (hasValidConfig) {
                        powerButtonObj.put("enabled", true);
                        powerButtonObj.put("world", powerButtonSection.getString("world", "world"));
                        powerButtonObj.put("x", powerButtonSection.getDouble("x", 0));
                        powerButtonObj.put("y", powerButtonSection.getDouble("y", 0));
                        powerButtonObj.put("z", powerButtonSection.getDouble("z", 0));
                        powerButtonObj.put("price", powerButtonSection.getInt("price", 1000));
                        powerButtonObj.put("unlocked", powerButtonSection.getBoolean("unlocked", false));

                        plugin.getLogger().info("电源按钮配置: 世界=" + powerButtonSection.getString("world") +
                                               ", 位置=(" + powerButtonSection.getDouble("x") + "," +
                                               powerButtonSection.getDouble("y") + "," +
                                               powerButtonSection.getDouble("z") + "), 价格=" +
                                               powerButtonSection.getInt("price", 1000));
                    } else {
                        powerButtonObj.put("enabled", false);
                        plugin.getLogger().warning("电源按钮配置不完整，缺少必要的位置信息");
                    }
                } else {
                    powerButtonObj.put("enabled", false);
                    plugin.getLogger().warning("电源按钮配置区段为空");
                }

                response.put("powerButton", powerButtonObj);
                plugin.getLogger().info("电源按钮配置已添加到响应");
            } else {
                plugin.getLogger().info("游戏 " + gameName + " 没有电源按钮配置");
                JSONObject powerButtonObj = new JSONObject();
                powerButtonObj.put("enabled", false);
                response.put("powerButton", powerButtonObj);
            }

            // 添加回合模式信息
            if (config.contains("roundModes")) {
                plugin.getLogger().info("游戏 " + gameName + " 存在回合模式配置，开始处理");
                JSONObject roundModesObj = new JSONObject();
                ConfigurationSection roundModesSection = config.getConfigurationSection("roundModes");

                if (roundModesSection != null) {
                    // 获取MonsterIdMapper
                    MonsterIdMapper idMapper = plugin.getMonsterIdMapper();

                    plugin.getLogger().info("回合模式配置中的顶级键: " + String.join(", ", roundModesSection.getKeys(false)));

                    for (String roundKey : roundModesSection.getKeys(false)) {
                        // 例如 round1, round2 等
                        JSONObject roundObj = new JSONObject();
                        ConfigurationSection roundSection = roundModesSection.getConfigurationSection(roundKey);

                        if (roundSection != null) {
                            plugin.getLogger().info("回合 " + roundKey + " 的生成点: " + String.join(", ", roundSection.getKeys(false)));

                            for (String spawnName : roundSection.getKeys(false)) {
                                // 记录当前处理的生成点
                                plugin.getLogger().info("处理回合 " + roundKey + " 的生成点: " + spawnName);

                                // 检查该生成点配置的结构并记录
                                ConfigurationSection spawnSection = roundSection.getConfigurationSection(spawnName);
                                if (spawnSection != null) {
                                    plugin.getLogger().info("生成点 " + spawnName + " 的配置键: " + String.join(", ", spawnSection.getKeys(false)));

                                    // 检查是否有多个配置（索引形式存储）
                                    boolean hasMultipleConfigs = false;
                                    boolean hasNamedConfigs = false;
                                    JSONArray spawnConfigs = new JSONArray();

                                    // 检查是否存在命名的刷怪逻辑
                                    for (String key : spawnSection.getKeys(false)) {
                                        if (key.startsWith("刷怪逻辑")) {
                                            hasNamedConfigs = true;
                                            ConfigurationSection configSection = spawnSection.getConfigurationSection(key);
                                            if (configSection != null) {
                                                plugin.getLogger().info("找到命名配置: " + key + ", 键: " + String.join(", ", configSection.getKeys(false)));
                                                JSONObject configObj = processConfigSection(configSection, idMapper);
                                                if (configObj != null) {
                                                    configObj.put("configKey", key); // 保存配置键名
                                                    spawnConfigs.add(configObj);
                                                }
                                            }
                                        }
                                    }

                                    // 如果没有找到命名配置，尝试检查数字索引
                                    if (!hasNamedConfigs) {
                                        plugin.getLogger().info("未找到命名配置，尝试检查数字索引...");

                                        // 尝试获取数字索引的配置（多配置情况）
                                        for (int i = 0; i < 20; i++) { // 设置一个合理的上限以防无限循环
                                            if (spawnSection.contains(String.valueOf(i))) {
                                                hasMultipleConfigs = true;
                                                ConfigurationSection configSection = spawnSection.getConfigurationSection(String.valueOf(i));
                                                JSONObject configObj = processConfigSection(configSection, idMapper);
                                                if (configObj != null) {
                                                    configObj.put("configKey", "刷怪逻辑" + (i + 1)); // 转换为命名键
                                                    spawnConfigs.add(configObj);
                                                    plugin.getLogger().info("找到数字索引配置: " + i + ", 转换为: 刷怪逻辑" + (i + 1));
                                                }
                                            } else if (i > 0) {
                                                // 如果找到至少一个配置后没有更多配置，就跳出循环
                                                break;
                                            }
                                        }
                                    }

                                    // 如果没有命名配置和数字索引配置，尝试直接处理整个生成点部分作为单一配置
                                    if (!hasNamedConfigs && !hasMultipleConfigs) {
                                        plugin.getLogger().info("未找到命名配置或数字索引配置，尝试解析为单一配置");
                                        // 检查是否有标准的怪物配置字段
                                        if (spawnSection.contains("monsterType") || spawnSection.contains("monsterId") || spawnSection.contains("count")) {
                                            JSONObject spawnModeObj = processConfigSection(spawnSection, idMapper);
                                            if (spawnModeObj != null) {
                                                spawnModeObj.put("configKey", "刷怪逻辑1"); // 默认键名
                                                spawnConfigs.add(spawnModeObj);
                                                plugin.getLogger().info("将整个生成点处理为单一配置: 刷怪逻辑1");
                                            }
                                        } else {
                                            plugin.getLogger().warning("生成点 " + spawnName + " 缺少有效的怪物配置字段");
                                        }
                                    }

                                    // 只有找到配置时才添加到响应中
                                    if (!spawnConfigs.isEmpty()) {
                                        // 创建JSON对象来包含配置数组
                                        JSONObject spawnObj = new JSONObject();

                                        // 转换为前端预期的命名键格式
                                        for (int i = 0; i < spawnConfigs.size(); i++) {
                                            JSONObject configItem = (JSONObject) spawnConfigs.get(i);
                                            String configKey = (String) configItem.get("configKey");
                                            configItem.remove("configKey"); // 移除临时键
                                            spawnObj.put(configKey, configItem);
                                        }

                                        roundObj.put(spawnName, spawnObj);
                                        plugin.getLogger().info("为生成点 " + spawnName + " 添加了 " + spawnConfigs.size() + " 个配置");
                                    } else {
                                        plugin.getLogger().warning("生成点 " + spawnName + " 未找到有效配置");
                                    }
                                } else {
                                    plugin.getLogger().warning("生成点 " + spawnName + " 的配置不是有效的配置区段");
                                }
                            }
                        }

                        roundModesObj.put(roundKey, roundObj);
                    }
                }

                response.put("roundModes", roundModesObj);
                plugin.getLogger().info("处理完成，回合模式数据已添加到响应");
            } else {
                plugin.getLogger().info("游戏 " + gameName + " 没有回合模式配置");
                response.put("roundModes", new JSONObject());
            }

            sendJsonResponse(exchange, response);
        }

        /**
         * 处理配置区段为JSON对象
         */
        private JSONObject processConfigSection(ConfigurationSection section, MonsterIdMapper idMapper) {
            if (section == null) {
                return null;
            }

            JSONObject configObj = new JSONObject();
            String monsterType = section.getString("monsterType", "zombie");
            String monsterId = section.getString("monsterId", "id1");

            // 读取新增的type和number字段
            String type = section.getString("type", "id");
            String number = section.getString("number", "1");

            // 确保ID是正确格式，使用MonsterIdMapper
            if (monsterId.contains(":")) {
                String[] parts = monsterId.split(":", 2);
                monsterType = parts[0]; // 更新monsterType
                monsterId = parts[1];
            }

            // 添加标准化的类型和ID
            configObj.put("monsterType", monsterType);
            configObj.put("monsterId", monsterId);
            configObj.put("count", section.getString("count", "5"));

            // 添加新增的类型和编号信息
            configObj.put("type", type);
            configObj.put("number", number);

            return configObj;
        }
    }

    /**
     * 处理保存游戏配置的API请求
     */
    private class SaveGameApiHandler implements HttpHandler {

        @Override
        public void handle(HttpExchange exchange) throws IOException {
            if (!"POST".equals(exchange.getRequestMethod())) {
                exchange.sendResponseHeaders(405, 0);
                exchange.getResponseBody().close();
                return;
            }

            // 读取请求体
            String requestBody = new BufferedReader(
                    new InputStreamReader(exchange.getRequestBody(), StandardCharsets.UTF_8))
                    .lines()
                    .collect(Collectors.joining("\n"));

            try {
                JSONParser parser = new JSONParser();
                JSONObject requestJson = (JSONObject) parser.parse(requestBody);

                String gameName = (String) requestJson.get("name");
                if (gameName == null || gameName.isEmpty()) {
                    exchange.sendResponseHeaders(400, 0);
                    exchange.getResponseBody().close();
                    return;
                }

                GameManager gameManager = plugin.getGameManager();

                // 同步回主线程执行，因为Bukkit API不是线程安全的
                new BukkitRunnable() {
                    @Override
                    public void run() {
                        try {
                            // 如果游戏不存在，创建它
                            if (!gameManager.gameExists(gameName)) {
                                gameManager.createGame(gameName);
                            }

                            // 更新最大玩家数
                            if (requestJson.containsKey("maxPlayers")) {
                                int maxPlayers = ((Number) requestJson.get("maxPlayers")).intValue();
                                gameManager.setMaxPlayers(gameName, maxPlayers);
                            }

                            // 更新出生点
                            if (requestJson.containsKey("spawn")) {
                                JSONObject spawnObj = (JSONObject) requestJson.get("spawn");
                                String worldName = (String) spawnObj.get("world");
                                double x = ((Number) spawnObj.get("x")).doubleValue();
                                double y = ((Number) spawnObj.get("y")).doubleValue();
                                double z = ((Number) spawnObj.get("z")).doubleValue();
                                float yaw = ((Number) spawnObj.get("yaw")).floatValue();
                                float pitch = ((Number) spawnObj.get("pitch")).floatValue();

                                Location location = new Location(Bukkit.getWorld(worldName), x, y, z, yaw, pitch);
                                gameManager.setSpawnLocation(gameName, location);
                            }

                            // 更新回合数
                            if (requestJson.containsKey("rounds")) {
                                int rounds = ((Number) requestJson.get("rounds")).intValue();
                                gameManager.setRounds(gameName, rounds);

                                // 更新僵尸数量
                                if (requestJson.containsKey("zombieCount")) {
                                    JSONObject zombieCountObj = (JSONObject) requestJson.get("zombieCount");
                                    for (int i = 1; i <= rounds; i++) {
                                        String key = "round" + i;
                                        if (zombieCountObj.containsKey(key)) {
                                            int count = ((Number) zombieCountObj.get(key)).intValue();
                                            gameManager.setZombieCount(gameName, i, count);
                                        }
                                    }
                                }
                            }

                            // 更新窗户区域
                            if (requestJson.containsKey("windows")) {
                                // 先清除现有窗户配置
                                FileConfiguration config = gameManager.getGameConfig(gameName);
                                if (config.contains("windows")) {
                                    config.set("windows", null);
                                }

                                JSONArray windowsArray = (JSONArray) requestJson.get("windows");
                                for (Object windowObj : windowsArray) {
                                    JSONObject window = (JSONObject) windowObj;
                                    String windowName = (String) window.get("name");
                                    String worldName = (String) window.get("world");
                                    double minX = ((Number) window.get("minX")).doubleValue();
                                    double minY = ((Number) window.get("minY")).doubleValue();
                                    double minZ = ((Number) window.get("minZ")).doubleValue();
                                    double maxX = ((Number) window.get("maxX")).doubleValue();
                                    double maxY = ((Number) window.get("maxY")).doubleValue();
                                    double maxZ = ((Number) window.get("maxZ")).doubleValue();

                                    // 如果没有窗户名称，生成一个默认名称
                                    if (windowName == null || windowName.isEmpty()) {
                                        windowName = "窗户" + (windowsArray.indexOf(windowObj) + 1);
                                    }

                                    // 使用两个Location对象创建Region
                                    Location loc1 = new Location(Bukkit.getWorld(worldName), minX, minY, minZ);
                                    Location loc2 = new Location(Bukkit.getWorld(worldName), maxX, maxY, maxZ);
                                    Region region = new Region(loc1, loc2);
                                    gameManager.addWindowsRegion(gameName, windowName, region);
                                }

                                // 保存配置
                                try {
                                    File configFile = new File(plugin.getDataFolder() + "/game", gameName + ".yml");
                                    config.save(configFile);
                                    plugin.getLogger().info("已保存窗户配置到文件: " + configFile.getAbsolutePath());
                                } catch (IOException e) {
                                    plugin.getLogger().severe("保存窗户配置时出错: " + e.getMessage());
                                }
                            }

                            // 更新全局电源按钮
                            if (requestJson.containsKey("powerButton")) {
                                FileConfiguration config = gameManager.getGameConfig(gameName);
                                JSONObject powerButtonObj = (JSONObject) requestJson.get("powerButton");
                                boolean enabled = Boolean.TRUE.equals(powerButtonObj.get("enabled"));

                                plugin.getLogger().info("处理电源按钮配置，启用状态: " + enabled);

                                if (enabled) {
                                    // 检查是否有完整的位置信息
                                    if (powerButtonObj.containsKey("world") && powerButtonObj.containsKey("x")
                                            && powerButtonObj.containsKey("y") && powerButtonObj.containsKey("z")) {

                                        String worldName = (String) powerButtonObj.get("world");
                                        double x = ((Number) powerButtonObj.get("x")).doubleValue();
                                        double y = ((Number) powerButtonObj.get("y")).doubleValue();
                                        double z = ((Number) powerButtonObj.get("z")).doubleValue();
                                        int price = powerButtonObj.containsKey("price") ?
                                                   ((Number) powerButtonObj.get("price")).intValue() : 1000;

                                        // 使用正确的power_button格式保存
                                        config.set("power_button.world", worldName);
                                        config.set("power_button.x", x);
                                        config.set("power_button.y", y);
                                        config.set("power_button.z", z);
                                        config.set("power_button.price", price);
                                        config.set("power_button.unlocked", false); // 默认未解锁

                                        plugin.getLogger().info("已设置电源按钮配置: 世界=" + worldName +
                                                               ", 位置=(" + x + "," + y + "," + z + "), 价格=" + price);
                                    } else {
                                        plugin.getLogger().warning("电源按钮启用但缺少位置信息，跳过保存");
                                    }
                                } else {
                                    // 如果禁用电源按钮，移除配置
                                    config.set("power_button", null);
                                    plugin.getLogger().info("已移除电源按钮配置");
                                }

                                // 保存配置
                                try {
                                    File configFile = new File(plugin.getDataFolder() + "/game", gameName + ".yml");
                                    config.save(configFile);
                                    plugin.getLogger().info("已保存全局电源按钮配置到文件: " + configFile.getAbsolutePath());
                                } catch (IOException e) {
                                    plugin.getLogger().severe("保存全局电源按钮配置时出错: " + e.getMessage());
                                }
                            }

                            // 更新门信息
                            if (requestJson.containsKey("doors")) {
                                // 先清除现有门配置
                                FileConfiguration config = gameManager.getGameConfig(gameName);
                                if (config.contains("doors")) {
                                    config.set("doors", null);
                                }

                                JSONArray doorsArray = (JSONArray) requestJson.get("doors");
                                for (Object doorObj : doorsArray) {
                                    JSONObject door = (JSONObject) doorObj;
                                    String doorName = (String) door.get("name");
                                    boolean locked = Boolean.TRUE.equals(door.get("locked"));

                                    // 设置门基本信息
                                    String doorPath = "doors." + doorName;
                                    config.set(doorPath + ".locked", locked); // 无论锁定状态如何，都保存

                                    // 只有锁定的门才设置解锁价格
                                    if (locked && door.containsKey("price")) {
                                        int price = ((Number) door.get("price")).intValue();
                                        config.set(doorPath + ".unlockPrice", price);
                                    }

                                    // 设置门区域信息(如果存在)
                                    if (door.containsKey("region")) {
                                        JSONObject region = (JSONObject) door.get("region");

                                        if (region != null) {
                                            String worldName = (String) region.get("world");

                                            // 处理不同格式的坐标数据
                                            double minX, minY, minZ, maxX, maxY, maxZ;

                                            if (region.containsKey("x1")) {
                                                minX = ((Number) region.get("x1")).doubleValue();
                                                minY = ((Number) region.get("y1")).doubleValue();
                                                minZ = ((Number) region.get("z1")).doubleValue();
                                                maxX = ((Number) region.get("x2")).doubleValue();
                                                maxY = ((Number) region.get("y2")).doubleValue();
                                                maxZ = ((Number) region.get("z2")).doubleValue();
                                            } else {
                                                minX = ((Number) region.get("minX")).doubleValue();
                                                minY = ((Number) region.get("minY")).doubleValue();
                                                minZ = ((Number) region.get("minZ")).doubleValue();
                                                maxX = ((Number) region.get("maxX")).doubleValue();
                                                maxY = ((Number) region.get("maxY")).doubleValue();
                                                maxZ = ((Number) region.get("maxZ")).doubleValue();
                                            }

                                            // 保存区域数据
                                            config.set(doorPath + ".region.world", worldName);
                                            config.set(doorPath + ".region.minX", minX);
                                            config.set(doorPath + ".region.minY", minY);
                                            config.set(doorPath + ".region.minZ", minZ);
                                            config.set(doorPath + ".region.maxX", maxX);
                                            config.set(doorPath + ".region.maxY", maxY);
                                            config.set(doorPath + ".region.maxZ", maxZ);

                                            plugin.getLogger().info("已保存门 '" + doorName + "' 的区域数据");
                                        }
                                    }

                                    // 添加关联的僵尸生成点（如果存在）
                                    if (door.containsKey("linkedSpawns") && door.get("linkedSpawns") instanceof JSONArray) {
                                        // 新的多生成点关联
                                        JSONArray linkedSpawnsArray = (JSONArray) door.get("linkedSpawns");
                                        if (!linkedSpawnsArray.isEmpty()) {
                                            List<String> linkedSpawns = new ArrayList<>();
                                            for (Object spawnObj : linkedSpawnsArray) {
                                                if (spawnObj instanceof String) {
                                                    linkedSpawns.add((String) spawnObj);
                                                }
                                            }

                                            if (!linkedSpawns.isEmpty()) {
                                                // 移除旧的单一关联配置
                                                config.set(doorPath + ".linkedSpawn", null);
                                                // 设置新的多生成点关联
                                                config.set(doorPath + ".linkedSpawns", linkedSpawns);
                                                plugin.getLogger().info("已将门 '" + doorName + "' 与 " + linkedSpawns.size() + " 个僵尸生成点关联");
                                            }
                                        }
                                    } else if (door.containsKey("linkedSpawn")) {
                                        // 兼容旧的单一生成点关联
                                        String linkedSpawn = (String) door.get("linkedSpawn");
                                        if (linkedSpawn != null && !linkedSpawn.isEmpty()) {
                                            // 将单一生成点转换为多生成点关联
                                            List<String> linkedSpawns = new ArrayList<>();
                                            linkedSpawns.add(linkedSpawn);

                                            // 移除旧的单一关联配置
                                            config.set(doorPath + ".linkedSpawn", null);
                                            // 设置新的多生成点关联
                                            config.set(doorPath + ".linkedSpawns", linkedSpawns);
                                            plugin.getLogger().info("已将门 '" + doorName + "' 与僵尸生成点 '" + linkedSpawn + "' 关联（转换为多生成点格式）");
                                        }
                                    }
                                }

                                // 保存门信息并手动保存配置文件
                                // 由于GameManager没有提供直接保存配置的方法，这里我们通过现有方法间接保存
                                // 通过设置一个无关紧要的属性触发配置文件保存
                                int maxPlayers = config.getInt("maxPlayers", 10);
                                gameManager.setMaxPlayers(gameName, maxPlayers);

                                // 强制保存配置文件，确保所有更改被写入
                                try {
                                    File configFile = new File(plugin.getDataFolder() + "/game", gameName + ".yml");
                                    config.save(configFile);
                                    plugin.getLogger().info("已直接保存 '" + gameName + "' 的配置文件: " + configFile.getAbsolutePath());
                                } catch (IOException e) {
                                    plugin.getLogger().severe("保存配置文件时出错: " + e.getMessage());
                                    throw e; // 重新抛出异常以便外层捕获
                                }

                                plugin.getLogger().info("已更新游戏 '" + gameName + "' 的门信息");
                            }

                            // 处理初始装备数据
                            if (requestJson.containsKey("initialEquipment")) {
                                FileConfiguration config = gameManager.getGameConfig(gameName);
                                // 清除现有初始装备配置
                                if (config.contains("initialEquipment")) {
                                    config.set("initialEquipment", null);
                                }

                                JSONObject initialEquipmentObj = (JSONObject) requestJson.get("initialEquipment");
                                for (Object slotObj : initialEquipmentObj.keySet()) {
                                    String slot = (String) slotObj;
                                    String itemId = (String) initialEquipmentObj.get(slot);

                                    if (itemId != null && !itemId.isEmpty()) {
                                        config.set("initialEquipment." + slot, itemId);
                                        plugin.getLogger().info("已设置槽位 " + slot + " 的初始装备为 " + itemId);
                                    }
                                }

                                // 保存配置
                                try {
                                    File configFile = new File(plugin.getDataFolder() + "/game", gameName + ".yml");
                                    config.save(configFile);
                                    plugin.getLogger().info("已保存初始装备配置到文件: " + configFile.getAbsolutePath());
                                } catch (IOException e) {
                                    plugin.getLogger().severe("保存初始装备配置时出错: " + e.getMessage());
                                }
                            }

                            // 处理回合模式数据
                            if (requestJson.containsKey("roundModes")) {
                                // 先清除现有回合模式配置
                                FileConfiguration config = gameManager.getGameConfig(gameName);
                                if (config.contains("roundModes")) {
                                    config.set("roundModes", null);
                                }

                                plugin.getLogger().info("开始处理roundModes数据...");
                                MonsterIdMapper idMapper = plugin.getMonsterIdMapper();

                                JSONObject roundModesObj = (JSONObject) requestJson.get("roundModes");
                                for (Object roundKeyObj : roundModesObj.keySet()) {
                                    String roundKey = (String) roundKeyObj;
                                    JSONObject roundObj = (JSONObject) roundModesObj.get(roundKey);
                                    plugin.getLogger().info("处理回合: " + roundKey);

                                    for (Object spawnNameObj : roundObj.keySet()) {
                                        String spawnName = (String) spawnNameObj;
                                        Object spawnModeValue = roundObj.get(spawnName);
                                        plugin.getLogger().info("处理生成点: " + spawnName + ", 数据类型: " + spawnModeValue.getClass().getName());

                                        // 检查是否包含命名键格式的配置（刷怪逻辑1, 刷怪逻辑2等）
                                        boolean hasNamedKeys = false;
                                        for (Object key : ((JSONObject) spawnModeValue).keySet()) {
                                            String keyStr = (String) key;
                                            if (keyStr.startsWith("刷怪逻辑")) {
                                                hasNamedKeys = true;
                                                plugin.getLogger().info("检测到命名键格式配置: " + keyStr);
                                                break;
                                            }
                                        }

                                        if (hasNamedKeys) {
                                            // 处理命名键格式的配置
                                            for (Object key : ((JSONObject) spawnModeValue).keySet()) {
                                                String keyStr = (String) key;
                                                if (keyStr.startsWith("刷怪逻辑")) {
                                                    Object namedConfig = ((JSONObject) spawnModeValue).get(keyStr);
                                                    if (namedConfig instanceof JSONObject) {
                                                        JSONObject namedConfigObj = (JSONObject) namedConfig;

                                                        // 详细记录每个配置
                                                        plugin.getLogger().info("处理命名配置 " + keyStr + ": " + namedConfigObj.toJSONString());

                                                        // 确保所有必要字段都存在
                                                        if (!namedConfigObj.containsKey("monsterType")) {
                                                            plugin.getLogger().warning("配置 " + keyStr + " 缺少 monsterType 字段，使用默认值 zombie");
                                                            namedConfigObj.put("monsterType", "zombie");
                                                        }
                                                        if (!namedConfigObj.containsKey("monsterId")) {
                                                            plugin.getLogger().warning("配置 " + keyStr + " 缺少 monsterId 字段，使用默认值 id1");
                                                            namedConfigObj.put("monsterId", "id1");
                                                        }
                                                        if (!namedConfigObj.containsKey("count")) {
                                                            plugin.getLogger().warning("配置 " + keyStr + " 缺少 count 字段，使用默认值 1");
                                                            namedConfigObj.put("count", "1");
                                                        }

                                                        String monsterType = (String) namedConfigObj.get("monsterType");
                                                        String monsterId = (String) namedConfigObj.get("monsterId");

                                                        // 使用MonsterIdMapper标准化ID
                                                        String apiId = idMapper.getApiId(monsterId, monsterType);
                                                        plugin.getLogger().info("标准化后的怪物ID: " + apiId);

                                                        // 安全获取count，防止空指针异常
                                                        String count = "1"; // 默认数量为1
                                                        if (namedConfigObj.get("count") != null) {
                                                            count = namedConfigObj.get("count").toString();
                                                        } else {
                                                            plugin.getLogger().warning("生成点配置缺少count属性，使用默认值1");
                                                        }

                                                        // 提取或设置type和number
                                                        String type = "id"; // 默认类型
                                                        String number = "1"; // 默认编号

                                                        // 从JSON中获取type和number（如果存在）
                                                        if (namedConfigObj.containsKey("type") && namedConfigObj.get("type") != null) {
                                                            type = (String) namedConfigObj.get("type");
                                                        } else {
                                                            // 从monsterType推断type
                                                            if ("zombie".equalsIgnoreCase(monsterType)) {
                                                                type = "id";
                                                            } else if ("entity".equalsIgnoreCase(monsterType)) {
                                                                type = "idc";
                                                            } else if ("npc".equalsIgnoreCase(monsterType)) {
                                                                type = "idn";
                                                            }
                                                            plugin.getLogger().info("生成点配置缺少type属性，根据monsterType推断为: " + type);
                                                        }

                                                        if (namedConfigObj.containsKey("number") && namedConfigObj.get("number") != null) {
                                                            number = namedConfigObj.get("number").toString();
                                                        } else {
                                                            // 从monsterId提取number
                                                            if (apiId != null) {
                                                                if (apiId.startsWith("id")) {
                                                                    number = apiId.substring(2);
                                                                } else if (apiId.startsWith("idc")) {
                                                                    number = apiId.substring(3);
                                                                } else if (apiId.startsWith("idn")) {
                                                                    number = apiId.substring(3);
                                                                } else {
                                                                    number = apiId; // 假设是纯数字
                                                                }
                                                            }
                                                            plugin.getLogger().info("生成点配置缺少number属性，从monsterId推断为: " + number);
                                                        }

                                                        // 将值写入配置 - 使用原始的命名键
                                                        String path = "roundModes." + roundKey + "." + spawnName + "." + keyStr;
                                                        config.set(path + ".monsterType", monsterType);
                                                        config.set(path + ".monsterId", apiId);
                                                        config.set(path + ".count", count);

                                                        // 保存新增的类型和编号信息
                                                        config.set(path + ".type", type);
                                                        config.set(path + ".number", number);

                                                        plugin.getLogger().info("保存生成点配置 " + keyStr + ": " + monsterType + ":" + apiId + ", 类型=" + type + ", 编号=" + number + ", 数量=" + count);
                                                    } else {
                                                        plugin.getLogger().warning("配置 " + keyStr + " 不是有效的JSON对象: " + namedConfig);
                                                    }
                                                }
                                            }
                                        } else {
                                            // 原先的处理逻辑 - 处理对象格式数据 (旧格式兼容)
                                            // 不再需要，因为前端始终发送命名键格式
                                            plugin.getLogger().warning("未检测到命名键格式的配置，这可能是前端数据格式错误，将尝试标准化为命名键格式");

                                            // 尝试标准化为命名键格式
                                            JSONObject spawnModeObj = (JSONObject) spawnModeValue;
                                            JSONObject standardizedConfig = new JSONObject();
                                            standardizedConfig.put("刷怪逻辑1", spawnModeObj);

                                            // 使用标准化后的配置继续处理
                                            String keyStr = "刷怪逻辑1";
                                            JSONObject namedConfigObj = spawnModeObj;

                                            plugin.getLogger().info("处理标准化配置 " + keyStr + ": " + namedConfigObj.toJSONString());

                                            // 确保所有必要字段都存在
                                            if (!namedConfigObj.containsKey("monsterType")) {
                                                plugin.getLogger().warning("配置 " + keyStr + " 缺少 monsterType 字段，使用默认值 zombie");
                                                namedConfigObj.put("monsterType", "zombie");
                                            }
                                            if (!namedConfigObj.containsKey("monsterId")) {
                                                plugin.getLogger().warning("配置 " + keyStr + " 缺少 monsterId 字段，使用默认值 id1");
                                                namedConfigObj.put("monsterId", "id1");
                                            }
                                            if (!namedConfigObj.containsKey("count")) {
                                                plugin.getLogger().warning("配置 " + keyStr + " 缺少 count 字段，使用默认值 1");
                                                namedConfigObj.put("count", "1");
                                            }

                                            String monsterType = (String) namedConfigObj.get("monsterType");
                                            String monsterId = (String) namedConfigObj.get("monsterId");

                                            // 使用MonsterIdMapper标准化ID
                                            String apiId = idMapper.getApiId(monsterId, monsterType);
                                            plugin.getLogger().info("标准化后的怪物ID: " + apiId);

                                            // 安全获取count，防止空指针异常
                                            String count = "1"; // 默认数量为1
                                            if (namedConfigObj.get("count") != null) {
                                                count = namedConfigObj.get("count").toString();
                                            } else {
                                                plugin.getLogger().warning("生成点配置缺少count属性，使用默认值1");
                                            }

                                            // 提取或设置type和number
                                            String type = "id"; // 默认类型
                                            String number = "1"; // 默认编号

                                            // 从JSON中获取type和number（如果存在）
                                            if (namedConfigObj.containsKey("type") && namedConfigObj.get("type") != null) {
                                                type = (String) namedConfigObj.get("type");
                                            } else {
                                                // 从monsterType推断type
                                                if ("zombie".equalsIgnoreCase(monsterType)) {
                                                    type = "id";
                                                } else if ("entity".equalsIgnoreCase(monsterType)) {
                                                    type = "idc";
                                                } else if ("npc".equalsIgnoreCase(monsterType)) {
                                                    type = "idn";
                                                }
                                                plugin.getLogger().info("生成点配置缺少type属性，根据monsterType推断为: " + type);
                                            }

                                            if (namedConfigObj.containsKey("number") && namedConfigObj.get("number") != null) {
                                                number = namedConfigObj.get("number").toString();
                                            } else {
                                                // 从monsterId提取number
                                                if (apiId != null) {
                                                    if (apiId.startsWith("id")) {
                                                        number = apiId.substring(2);
                                                    } else if (apiId.startsWith("idc")) {
                                                        number = apiId.substring(3);
                                                    } else if (apiId.startsWith("idn")) {
                                                        number = apiId.substring(3);
                                                    } else {
                                                        number = apiId; // 假设是纯数字
                                                    }
                                                }
                                                plugin.getLogger().info("生成点配置缺少number属性，从monsterId推断为: " + number);
                                            }

                                            // 将值写入配置 - 使用固定的"刷怪逻辑1"作为对象格式的默认路径
                                            String path = "roundModes." + roundKey + "." + spawnName + "." + keyStr;
                                            config.set(path + ".monsterType", monsterType);
                                            config.set(path + ".monsterId", apiId);
                                            config.set(path + ".count", count);

                                            // 保存新增的类型和编号信息
                                            config.set(path + ".type", type);
                                            config.set(path + ".number", number);

                                            plugin.getLogger().info("保存生成点配置 " + keyStr + ": " + monsterType + ":" + apiId + ", 类型=" + type + ", 编号=" + number + ", 数量=" + count);
                                        }
                                    }
                                }

                                // 保存配置文件
                                try {
                                    File configFile = new File(plugin.getDataFolder() + "/game", gameName + ".yml");
                                    config.save(configFile);
                                    plugin.getLogger().info("保存了游戏 '" + gameName + "' 的回合模式配置");
                                } catch (IOException e) {
                                    plugin.getLogger().severe("保存配置文件时出错: " + e.getMessage());
                                    throw e;
                                }
                            }

                            // 发送成功响应
                            JSONObject response = new JSONObject();
                            response.put("success", true);
                            sendJsonResponse(exchange, response);

                        } catch (Exception e) {
                            plugin.getLogger().severe("保存游戏配置时出错: " + e.getMessage());
                            e.printStackTrace();

                            try {
                                JSONObject response = new JSONObject();
                                response.put("success", false);
                                response.put("error", e.getMessage());
                                sendJsonResponse(exchange, response);
                            } catch (IOException ex) {
                                ex.printStackTrace();
                            }
                        }
                    }
                }.runTask(plugin);

                return;

            } catch (ParseException e) {
                plugin.getLogger().severe("解析请求JSON时出错: " + e.getMessage());
                exchange.sendResponseHeaders(400, 0);
                exchange.getResponseBody().close();
            }
        }
    }

    /**
     * 发送JSON响应
     */
    private void sendJsonResponse(HttpExchange exchange, JSONObject response) throws IOException {
        exchange.getResponseHeaders().set("Content-Type", "application/json; charset=UTF-8");
        byte[] responseBytes = response.toJSONString().getBytes(StandardCharsets.UTF_8);
        exchange.sendResponseHeaders(200, responseBytes.length);
        try (OutputStream os = exchange.getResponseBody()) {
            os.write(responseBytes);
        }
    }

    /**
     * 身份验证过滤器，用于保护API端点
     */
    private class AuthFilter {

        // 存储会话令牌
        private Map<String, Long> sessionTokens = new HashMap<>();

        /**
         * 包装HttpHandler，添加身份验证功能
         */
        public HttpHandler wrap(HttpHandler handler) {
            return exchange -> {
                // 检查Authorization头
                String authHeader = exchange.getRequestHeaders().getFirst("Authorization");

                // 如果没有Authorization头，返回401
                if (authHeader == null || !authHeader.startsWith("Bearer ")) {
                    sendUnauthorizedResponse(exchange, "需要身份验证");
                    return;
                }

                // 获取令牌
                String token = authHeader.substring(7);

                // 验证令牌
                if (!isValidToken(token)) {
                    sendUnauthorizedResponse(exchange, "无效的令牌或令牌已过期");
                    return;
                }

                // 令牌有效，继续处理请求
                handler.handle(exchange);
            };
        }

        /**
         * 验证令牌是否有效
         */
        private boolean isValidToken(String token) {
            Long expiry = sessionTokens.get(token);
            if (expiry == null) {
                return false;
            }

            // 检查令牌是否过期（1小时过期）
            if (System.currentTimeMillis() > expiry) {
                sessionTokens.remove(token);
                return false;
            }

            return true;
        }

        /**
         * 创建新的会话令牌
         */
        public String createToken() {
            String token = UUID.randomUUID().toString();
            // 设置令牌有效期为1小时
            sessionTokens.put(token, System.currentTimeMillis() + 3600000);
            return token;
        }

        /**
         * 发送未授权响应
         */
        private void sendUnauthorizedResponse(HttpExchange exchange, String message) throws IOException {
            JSONObject response = new JSONObject();
            response.put("error", "Unauthorized");
            response.put("message", message);

            exchange.getResponseHeaders().set("Content-Type", "application/json; charset=UTF-8");
            exchange.getResponseHeaders().set("WWW-Authenticate", "Bearer realm=\"DeathZombieV4 WebUI\"");

            byte[] responseBytes = response.toJSONString().getBytes(StandardCharsets.UTF_8);
            exchange.sendResponseHeaders(401, responseBytes.length);

            try (OutputStream os = exchange.getResponseBody()) {
                os.write(responseBytes);
            }
        }
    }

    /**
     * 处理登录请求
     */
    private class LoginHandler implements HttpHandler {

        @Override
        public void handle(HttpExchange exchange) throws IOException {
            // 只允许POST请求
            if (!"POST".equals(exchange.getRequestMethod())) {
                exchange.sendResponseHeaders(405, 0);
                exchange.getResponseBody().close();
                return;
            }

            // 读取请求体
            String requestBody = new BufferedReader(
                    new InputStreamReader(exchange.getRequestBody(), StandardCharsets.UTF_8))
                    .lines()
                    .collect(Collectors.joining("\n"));

            try {
                // 解析JSON
                JSONParser parser = new JSONParser();
                JSONObject requestJson = (JSONObject) parser.parse(requestBody);

                // 获取密码
                String password = (String) requestJson.get("password");

                // 验证密码
                if (password == null || !password.equals(WebServer.this.password)) {
                    // 密码错误
                    JSONObject response = new JSONObject();
                    response.put("success", false);
                    response.put("message", "密码错误");

                    exchange.getResponseHeaders().set("Content-Type", "application/json; charset=UTF-8");
                    byte[] responseBytes = response.toJSONString().getBytes(StandardCharsets.UTF_8);
                    exchange.sendResponseHeaders(401, responseBytes.length);

                    try (OutputStream os = exchange.getResponseBody()) {
                        os.write(responseBytes);
                    }
                    return;
                }

                // 密码正确，使用全局AuthFilter创建令牌
                String token = WebServer.this.authFilter.createToken();

                // 返回令牌
                JSONObject response = new JSONObject();
                response.put("success", true);
                response.put("token", token);
                response.put("message", "登录成功");

                sendJsonResponse(exchange, response);

            } catch (ParseException e) {
                // JSON解析错误
                JSONObject response = new JSONObject();
                response.put("success", false);
                response.put("message", "无效的请求格式");

                exchange.getResponseHeaders().set("Content-Type", "application/json; charset=UTF-8");
                byte[] responseBytes = response.toJSONString().getBytes(StandardCharsets.UTF_8);
                exchange.sendResponseHeaders(400, responseBytes.length);

                try (OutputStream os = exchange.getResponseBody()) {
                    os.write(responseBytes);
                }
            }
        }
    }

    /**
     * 处理获取玩家当前选择的区域的API请求
     */
    private class RegionApiHandler implements HttpHandler {

        @Override
        public void handle(HttpExchange exchange) throws IOException {
            if (!"GET".equals(exchange.getRequestMethod())) {
                exchange.sendResponseHeaders(405, 0);
                exchange.getResponseBody().close();
                return;
            }

            // 获取查询参数
            String query = exchange.getRequestURI().getQuery();
            if (query == null || !query.startsWith("game=")) {
                exchange.sendResponseHeaders(400, 0);
                exchange.getResponseBody().close();
                return;
            }

            String gameName = query.substring(5);
            plugin.getLogger().info("收到区域API请求，游戏名称: " + gameName);

            // 同步回主线程执行
            new BukkitRunnable() {
                @Override
                public void run() {
                    try {
                        // 获取选择工具管理器
                        org.Ver_zhzh.deathZombieV4.utils.RegionSelector regionSelector = plugin.getRegionSelector();
                        plugin.getLogger().info("正在检索玩家区域选择...");

                        // 获取当前玩家的区域选择
                        Map<String, Region> selections = regionSelector.getSelections();
                        plugin.getLogger().info("找到 " + selections.size() + " 个区域选择");

                        JSONObject response = new JSONObject();

                        if (selections.isEmpty()) {
                            plugin.getLogger().warning("未找到任何区域选择");
                            response.put("success", false);
                            response.put("message", "未找到选择的区域，请先使用调试棒选择区域");
                        } else {
                            // 使用第一个找到的选择
                            java.util.Map.Entry<String, Region> firstSelection = selections.entrySet().iterator().next();
                            String playerName = firstSelection.getKey();
                            Region region = firstSelection.getValue();

                            plugin.getLogger().info("使用玩家 " + playerName + " 的区域选择");
                            plugin.getLogger().info("区域信息: 世界=" + region.getWorld().getName()
                                    + ", 最小坐标=(" + region.getMinX() + "," + region.getMinY() + "," + region.getMinZ() + ")"
                                    + ", 最大坐标=(" + region.getMaxX() + "," + region.getMaxY() + "," + region.getMaxZ() + ")");

                            JSONObject regionData = new JSONObject();
                            regionData.put("world", region.getWorld().getName());
                            regionData.put("minX", region.getMinX());
                            regionData.put("minY", region.getMinY());
                            regionData.put("minZ", region.getMinZ());
                            regionData.put("maxX", region.getMaxX());
                            regionData.put("maxY", region.getMaxY());
                            regionData.put("maxZ", region.getMaxZ());

                            response.put("success", true);
                            response.put("player", playerName);
                            response.put("region", regionData);
                        }

                        plugin.getLogger().info("区域API响应: " + response.toJSONString());
                        sendJsonResponse(exchange, response);
                    } catch (Exception e) {
                        plugin.getLogger().severe("处理区域请求时出错: " + e.getMessage());
                        e.printStackTrace();

                        try {
                            JSONObject response = new JSONObject();
                            response.put("success", false);
                            response.put("message", "处理区域请求时出错: " + e.getMessage());
                            sendJsonResponse(exchange, response);
                        } catch (IOException ex) {
                            ex.printStackTrace();
                        }
                    }
                }
            }.runTask(plugin);
        }
    }

    /**
     * 准备游戏数据以供Web界面使用
     *
     * @param gameName 游戏名称
     */
    public void prepareGameData(String gameName) {
        // 记录预加载请求
        plugin.getLogger().info("收到游戏数据预加载请求: " + gameName);

        // 这里可以预加载特定游戏的配置
        try {
            GameManager gameManager = plugin.getGameManager();
            if (gameManager.gameExists(gameName)) {
                FileConfiguration config = gameManager.getGameConfig(gameName);
                if (config != null) {
                    plugin.getLogger().info("游戏 '" + gameName + "' 配置已预加载并就绪，等待Web界面获取");
                } else {
                    plugin.getLogger().warning("游戏 '" + gameName + "' 配置加载失败");
                }
            } else {
                plugin.getLogger().warning("游戏 '" + gameName + "' 不存在");
            }
        } catch (Exception e) {
            plugin.getLogger().severe("预加载游戏数据时出错: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 处理获取玩家当前位置的API请求
     */
    private class PlayerLocationApiHandler implements HttpHandler {

        @Override
        public void handle(HttpExchange exchange) throws IOException {
            if (!"GET".equals(exchange.getRequestMethod())) {
                exchange.sendResponseHeaders(405, 0);
                exchange.getResponseBody().close();
                return;
            }

            // 获取查询参数
            String query = exchange.getRequestURI().getQuery();
            if (query == null || !query.startsWith("game=")) {
                exchange.sendResponseHeaders(400, 0);
                exchange.getResponseBody().close();
                return;
            }

            String gameName = query.substring(5);
            plugin.getLogger().info("收到玩家位置API请求，游戏名称: " + gameName);

            // 同步回主线程执行
            new BukkitRunnable() {
                @Override
                public void run() {
                    try {
                        // 获取在线玩家
                        Collection<? extends Player> onlinePlayers = Bukkit.getOnlinePlayers();
                        JSONObject response = new JSONObject();

                        if (onlinePlayers.isEmpty()) {
                            plugin.getLogger().warning("没有在线玩家");
                            response.put("success", false);
                            response.put("message", "没有在线玩家");
                        } else {
                            // 使用第一个在线玩家
                            Player player = onlinePlayers.iterator().next();
                            Location location = player.getLocation();

                            plugin.getLogger().info("使用玩家 " + player.getName() + " 的位置");
                            plugin.getLogger().info("位置信息: 世界=" + location.getWorld().getName()
                                    + ", 坐标=(" + location.getX() + "," + location.getY() + "," + location.getZ() + ")");

                            response.put("success", true);
                            response.put("player", player.getName());
                            response.put("world", location.getWorld().getName());
                            response.put("x", location.getX());
                            response.put("y", location.getY());
                            response.put("z", location.getZ());
                            response.put("yaw", location.getYaw());
                            response.put("pitch", location.getPitch());
                        }

                        plugin.getLogger().info("玩家位置API响应: " + response.toJSONString());
                        sendJsonResponse(exchange, response);
                    } catch (Exception e) {
                        plugin.getLogger().severe("处理玩家位置请求时出错: " + e.getMessage());
                        e.printStackTrace();

                        try {
                            JSONObject response = new JSONObject();
                            response.put("success", false);
                            response.put("message", "处理玩家位置请求时出错: " + e.getMessage());
                            sendJsonResponse(exchange, response);
                        } catch (IOException ex) {
                            ex.printStackTrace();
                        }
                    }
                }
            }.runTask(plugin);
        }
    }

    /**
     * 处理重新加载配置的API请求
     */
    private class ReloadConfigApiHandler implements HttpHandler {

        @Override
        public void handle(HttpExchange exchange) throws IOException {
            // 只允许GET请求
            if (!"GET".equals(exchange.getRequestMethod())) {
                exchange.sendResponseHeaders(405, 0);
                exchange.getResponseBody().close();
                return;
            }

            // 同步回主线程执行
            new BukkitRunnable() {
                @Override
                public void run() {
                    try {
                        plugin.getLogger().info("收到重新加载配置请求");

                        // 重新加载所有游戏配置
                        plugin.getGameManager().loadAllGames();

                        // 重新加载UserCustomEntity系统配置
                        if (plugin.getZombieHelper() != null &&
                            plugin.getZombieHelper().getCustomZombie() != null &&
                            plugin.getZombieHelper().getCustomZombie().getEntityIntegration() != null) {
                            try {
                                plugin.getZombieHelper().getCustomZombie().getEntityIntegration().reloadConfig();
                                plugin.getLogger().info("UserCustomEntity系统配置(entity.yml)已重新加载");
                            } catch (Exception e) {
                                plugin.getLogger().warning("重新加载UserCustomEntity配置时出错: " + e.getMessage());
                            }
                        }

                        JSONObject response = new JSONObject();
                        response.put("success", true);
                        response.put("message", "配置已成功重新加载（包括entity.yml）");

                        plugin.getLogger().info("配置重新加载成功");
                        sendJsonResponse(exchange, response);
                    } catch (Exception e) {
                        plugin.getLogger().severe("重新加载配置时出错: " + e.getMessage());
                        e.printStackTrace();

                        try {
                            JSONObject response = new JSONObject();
                            response.put("success", false);
                            response.put("message", "重新加载配置失败: " + e.getMessage());
                            sendJsonResponse(exchange, response);
                        } catch (IOException ex) {
                            ex.printStackTrace();
                        }
                    }
                }
            }.runTask(plugin);
        }
    }

    /**
     * 处理创建游戏的API请求
     */
    private class CreateGameApiHandler implements HttpHandler {

        @Override
        public void handle(HttpExchange exchange) throws IOException {
            if (!"POST".equals(exchange.getRequestMethod())) {
                exchange.sendResponseHeaders(405, 0);
                exchange.getResponseBody().close();
                return;
            }

            // 读取请求体
            String requestBody = new BufferedReader(
                    new InputStreamReader(exchange.getRequestBody(), StandardCharsets.UTF_8))
                    .lines()
                    .collect(Collectors.joining("\n"));

            plugin.getLogger().info("收到创建游戏请求: " + requestBody);

            try {
                JSONParser parser = new JSONParser();
                JSONObject requestJson = (JSONObject) parser.parse(requestBody);

                if (!requestJson.containsKey("name")) {
                    JSONObject errorResponse = new JSONObject();
                    errorResponse.put("success", false);
                    errorResponse.put("message", "缺少游戏名称参数");
                    sendJsonResponse(exchange, errorResponse);
                    return;
                }

                String gameName = (String) requestJson.get("name");

                // 在主线程中创建游戏
                new BukkitRunnable() {
                    @Override
                    public void run() {
                        try {
                            GameManager gameManager = plugin.getGameManager();

                            // 检查游戏是否已存在
                            if (gameManager.gameExists(gameName)) {
                                JSONObject response = new JSONObject();
                                response.put("success", false);
                                response.put("message", "游戏 '" + gameName + "' 已存在");
                                sendJsonResponse(exchange, response);
                                return;
                            }

                            // 创建新游戏
                            boolean created = gameManager.createGame(gameName);

                            JSONObject response = new JSONObject();
                            if (created) {
                                response.put("success", true);
                                response.put("message", "游戏 '" + gameName + "' 创建成功");
                            } else {
                                response.put("success", false);
                                response.put("message", "创建游戏失败");
                            }

                            sendJsonResponse(exchange, response);
                        } catch (Exception e) {
                            plugin.getLogger().severe("创建游戏时出错: " + e.getMessage());
                            e.printStackTrace();

                            try {
                                JSONObject response = new JSONObject();
                                response.put("success", false);
                                response.put("message", "创建游戏时出错: " + e.getMessage());
                                sendJsonResponse(exchange, response);
                            } catch (IOException ex) {
                                ex.printStackTrace();
                            }
                        }
                    }
                }.runTask(plugin);

            } catch (ParseException e) {
                plugin.getLogger().severe("解析创建游戏请求JSON时出错: " + e.getMessage());

                JSONObject response = new JSONObject();
                response.put("success", false);
                response.put("message", "无效的JSON格式: " + e.getMessage());
                sendJsonResponse(exchange, response);
            }
        }
    }
}
