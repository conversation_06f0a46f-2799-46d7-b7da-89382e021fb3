package org.Ver_zhzh.deathZombieV4.web.api;

import java.io.BufferedReader;
import java.io.File;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;

import org.Ver_zhzh.deathZombieV4.DeathZombieV4;
import org.Ver_zhzh.deathZombieV4.game.GameSessionManager;
import org.bukkit.configuration.file.FileConfiguration;
import org.json.simple.JSONObject;
import org.json.simple.parser.JSONParser;

import com.sun.net.httpserver.HttpExchange;
import com.sun.net.httpserver.HttpHandler;

/**
 * 处理装备相关的API请求
 */
public class EquipmentApiHandler implements HttpHandler {

    private final DeathZombieV4 plugin;

    public EquipmentApiHandler(DeathZombieV4 plugin) {
        this.plugin = plugin;
    }

    @Override
    public void handle(HttpExchange exchange) throws IOException {
        String method = exchange.getRequestMethod();
        String path = exchange.getRequestURI().getPath();
        String query = exchange.getRequestURI().getQuery();

        try {
            // 处理OPTIONS请求（CORS预检请求）
            if (method.equalsIgnoreCase("OPTIONS")) {
                handleCorsHeaders(exchange);
                exchange.sendResponseHeaders(204, -1);
                return;
            }

            // 添加CORS头
            handleCorsHeaders(exchange);

            // 根据路径和方法处理不同的请求
            plugin.getLogger().info("处理装备API请求: " + path + ", 方法: " + method + ", 查询: " + query);

            if (path.equals("/api/equipment") && method.equalsIgnoreCase("GET")) {
                // 处理GET请求，获取装备信息
                handleGetEquipment(exchange, query);
            } else if ((path.equals("/api/equipment") || path.equals("/api/save-game")) && method.equalsIgnoreCase("POST")) {
                // 处理POST请求，保存装备信息
                plugin.getLogger().info("收到保存装备请求，路径: " + path);
                handleSaveEquipment(exchange);
            } else {
                // 未知路径，返回404
                plugin.getLogger().warning("未知的装备API请求路径: " + path);
                sendResponse(exchange, 404, createErrorResponse("未找到请求的资源"));
            }
        } catch (Exception e) {
            plugin.getLogger().severe("处理API请求时出错: " + e.getMessage());
            e.printStackTrace();
            sendResponse(exchange, 500, createErrorResponse("服务器内部错误: " + e.getMessage()));
        }
    }

    /**
     * 处理CORS头
     */
    private void handleCorsHeaders(HttpExchange exchange) {
        exchange.getResponseHeaders().add("Access-Control-Allow-Origin", "*");
        exchange.getResponseHeaders().add("Access-Control-Allow-Methods", "GET, POST, OPTIONS");
        exchange.getResponseHeaders().add("Access-Control-Allow-Headers", "Content-Type, Authorization");
        exchange.getResponseHeaders().add("Access-Control-Max-Age", "3600");
    }

    /**
     * 处理获取游戏装备的请求
     */
    private void handleGetEquipment(HttpExchange exchange, String query) throws IOException {
        // 添加调试日志
        plugin.getLogger().info("处理获取游戏装备请求，查询参数: " + query);

        // 解析查询参数
        Map<String, String> params = parseQueryParams(query);
        String gameName = params.get("name");

        if (gameName == null || gameName.isEmpty()) {
            plugin.getLogger().warning("缺少游戏名称参数");
            sendResponse(exchange, 400, createErrorResponse("缺少游戏名称参数"));
            return;
        }

        plugin.getLogger().info("获取游戏 '" + gameName + "' 的装备信息");

        // 检查游戏是否存在
        if (!plugin.getGameManager().gameExists(gameName)) {
            plugin.getLogger().warning("游戏不存在: " + gameName);
            sendResponse(exchange, 404, createErrorResponse("游戏不存在: " + gameName));
            return;
        }

        // 获取初始装备
        GameSessionManager sessionManager = plugin.getGameSessionManager();
        Map<Integer, String> initialEquipment = sessionManager.getGameInitialEquipment(gameName);

        // 添加调试日志
        if (initialEquipment != null && !initialEquipment.isEmpty()) {
            plugin.getLogger().info("成功获取游戏 '" + gameName + "' 的初始装备，共 " + initialEquipment.size() + " 个槽位");
            for (Map.Entry<Integer, String> entry : initialEquipment.entrySet()) {
                plugin.getLogger().info("槽位 " + entry.getKey() + ": " + entry.getValue());
            }
        } else {
            plugin.getLogger().warning("游戏 '" + gameName + "' 没有初始装备配置或获取失败");
        }

        // 获取护甲设置
        FileConfiguration config = plugin.getGameManager().getGameConfig(gameName);
        JSONObject armorEquipment = new JSONObject();

        // 获取上套装
        String upperArmor = config.getString("armorEquipment.upper");
        if (upperArmor != null) {
            armorEquipment.put("upper", upperArmor);
            plugin.getLogger().info("获取到游戏 " + gameName + " 的上套装: " + upperArmor);
        }

        // 获取下套装
        String lowerArmor = config.getString("armorEquipment.lower");
        if (lowerArmor != null) {
            armorEquipment.put("lower", lowerArmor);
            plugin.getLogger().info("获取到游戏 " + gameName + " 的下套装: " + lowerArmor);
        }

        // 获取GameKitManager
        JSONObject gameKitInfo = new JSONObject();
        if (plugin.getGameKitManager() != null) {
            // 添加武器信息
            JSONObject weapons = new JSONObject();
            for (Map.Entry<String, Map<String, Object>> entry : plugin.getGameKitManager().getAllWeapons().entrySet()) {
                JSONObject weaponInfo = new JSONObject();
                weaponInfo.putAll(entry.getValue());
                weapons.put(entry.getKey(), weaponInfo);
            }
            gameKitInfo.put("weapons", weapons);

            // 添加护甲信息
            JSONObject armors = new JSONObject();
            for (Map.Entry<String, Map<String, Object>> entry : plugin.getGameKitManager().getAllArmors().entrySet()) {
                JSONObject armorInfo = new JSONObject();
                armorInfo.putAll(entry.getValue());
                armors.put(entry.getKey(), armorInfo);
            }
            gameKitInfo.put("armors", armors);

            // 添加物品信息
            JSONObject items = new JSONObject();
            for (Map.Entry<String, Map<String, Object>> entry : plugin.getGameKitManager().getAllItems().entrySet()) {
                JSONObject itemInfo = new JSONObject();
                itemInfo.putAll(entry.getValue());
                items.put(entry.getKey(), itemInfo);
            }
            gameKitInfo.put("items", items);

            // 添加特殊物品信息
            JSONObject specialItems = new JSONObject();
            for (Map.Entry<String, Map<String, Object>> entry : plugin.getGameKitManager().getAllSpecialItems().entrySet()) {
                JSONObject specialItemInfo = new JSONObject();
                specialItemInfo.putAll(entry.getValue());
                specialItems.put(entry.getKey(), specialItemInfo);
            }
            gameKitInfo.put("special", specialItems);

            // 添加其他物品信息
            JSONObject otherItems = new JSONObject();
            for (Map.Entry<String, Map<String, Object>> entry : plugin.getGameKitManager().getAllOtherItems().entrySet()) {
                JSONObject otherItemInfo = new JSONObject();
                otherItemInfo.putAll(entry.getValue());
                otherItems.put(entry.getKey(), otherItemInfo);
            }
            gameKitInfo.put("other", otherItems);
        }

        // 创建响应
        JSONObject response = new JSONObject();
        response.put("success", true);
        response.put("name", gameName);
        response.put("initialEquipment", initialEquipment);
        response.put("armorEquipment", armorEquipment);
        response.put("gameKit", gameKitInfo);

        // 记录响应内容
        plugin.getLogger().info("发送响应: " + response.toJSONString());

        // 发送响应
        sendResponse(exchange, 200, response.toJSONString());
    }

    /**
     * 处理保存游戏装备的请求
     */
    private void handleSaveEquipment(HttpExchange exchange) throws IOException {
        // 读取请求体
        String requestBody = new BufferedReader(new InputStreamReader(exchange.getRequestBody(), StandardCharsets.UTF_8))
                .lines().collect(Collectors.joining("\n"));

        try {
            // 解析JSON
            JSONParser parser = new JSONParser();
            JSONObject requestJson = (JSONObject) parser.parse(requestBody);

            // 记录完整请求内容，帮助调试
            plugin.getLogger().info("收到保存初始装备请求: " + requestBody);

            // 获取游戏名称
            String gameName = (String) requestJson.get("name");
            if (gameName == null || gameName.isEmpty()) {
                sendResponse(exchange, 400, createErrorResponse("缺少游戏名称"));
                return;
            }

            // 检查游戏是否存在
            if (!plugin.getGameManager().gameExists(gameName)) {
                sendResponse(exchange, 404, createErrorResponse("游戏不存在: " + gameName));
                return;
            }

            // 获取初始装备
            JSONObject equipmentJson = (JSONObject) requestJson.get("initialEquipment");
            if (equipmentJson == null) {
                sendResponse(exchange, 400, createErrorResponse("缺少初始装备数据"));
                return;
            }

            // 获取护甲设置
            JSONObject armorEquipmentJson = (JSONObject) requestJson.get("armorEquipment");

            // 记录请求内容，帮助调试
            plugin.getLogger().info("初始装备数据: " + equipmentJson.toJSONString());
            if (armorEquipmentJson != null) {
                plugin.getLogger().info("护甲设置数据: " + armorEquipmentJson.toJSONString());
            }

            // 获取游戏配置
            FileConfiguration config = plugin.getGameManager().getGameConfig(gameName);

            // 清除现有初始装备配置
            if (config.contains("initialEquipment")) {
                config.set("initialEquipment", null);
                plugin.getLogger().info("已清除游戏 " + gameName + " 的现有初始装备配置");
            }

            // 直接将初始装备写入配置文件
            boolean success = true;

            for (Object key : equipmentJson.keySet()) {
                try {
                    int slot = Integer.parseInt(key.toString());
                    String itemId = (String) equipmentJson.get(key);

                    // 验证槽位是否有效 - 只支持1-36（新格式）
                    if (slot < 1 || slot > 36) {
                        plugin.getLogger().warning("跳过无效的槽位编号: " + slot + "，有效范围为1-36");
                        success = false;
                        continue;
                    }

                    plugin.getLogger().info("设置槽位 " + slot + " 的物品为 " + itemId);

                    // 直接写入配置文件，使用新格式（1-36）
                    config.set("initialEquipment." + slot, itemId);
                    plugin.getLogger().info("已设置槽位 " + slot + " 的初始装备为 " + itemId);
                } catch (NumberFormatException e) {
                    success = false;
                    plugin.getLogger().warning("无效的槽位: " + key);
                }
            }

            // 清除现有护甲设置
            if (config.contains("armorEquipment")) {
                config.set("armorEquipment", null);
                plugin.getLogger().info("已清除游戏 " + gameName + " 的现有护甲设置");
            }

            // 保存护甲设置
            if (armorEquipmentJson != null) {
                try {
                    // 保存上套装
                    if (armorEquipmentJson.containsKey("upper")) {
                        String upperArmor = (String) armorEquipmentJson.get("upper");
                        config.set("armorEquipment.upper", upperArmor);
                        plugin.getLogger().info("设置游戏 " + gameName + " 的上套装为 " + upperArmor);
                    }

                    // 保存下套装
                    if (armorEquipmentJson.containsKey("lower")) {
                        String lowerArmor = (String) armorEquipmentJson.get("lower");
                        config.set("armorEquipment.lower", lowerArmor);
                        plugin.getLogger().info("设置游戏 " + gameName + " 的下套装为 " + lowerArmor);
                    }
                } catch (Exception e) {
                    success = false;
                    plugin.getLogger().warning("保存护甲设置失败: " + e.getMessage());
                    e.printStackTrace();
                }
            }

            // 直接保存配置文件
            try {
                // 确保游戏目录存在
                File gameDir = new File(plugin.getDataFolder(), "game");
                if (!gameDir.exists()) {
                    gameDir.mkdirs();
                    plugin.getLogger().info("创建游戏目录: " + gameDir.getAbsolutePath());
                }

                // 创建配置文件
                File configFile = new File(gameDir, gameName + ".yml");
                plugin.getLogger().info("准备保存配置文件: " + configFile.getAbsolutePath());

                // 检查文件是否可写
                if (configFile.exists() && !configFile.canWrite()) {
                    plugin.getLogger().warning("配置文件不可写: " + configFile.getAbsolutePath());
                    success = false;
                } else {
                    // 保存配置
                    config.save(configFile);
                    plugin.getLogger().info("已直接保存初始装备配置到文件: " + configFile.getAbsolutePath());

                    // 提示用户需要重新加载插件或重启服务器
                    plugin.getLogger().info("配置已保存，但可能需要重新加载插件或重启服务器才能生效");
                }
            } catch (IOException e) {
                success = false;
                plugin.getLogger().severe("保存初始装备配置时出错: " + e.getMessage());
                e.printStackTrace();
            }

            // 创建响应
            JSONObject response = new JSONObject();
            response.put("success", success);
            if (!success) {
                response.put("message", "部分装备设置失败，请检查日志");
            }

            // 发送响应
            sendResponse(exchange, 200, response.toJSONString());

        } catch (Exception e) {
            plugin.getLogger().severe("解析请求体时出错: " + e.getMessage());
            e.printStackTrace();
            sendResponse(exchange, 400, createErrorResponse("无效的请求格式: " + e.getMessage()));
        }
    }

    /**
     * 解析查询参数
     */
    private Map<String, String> parseQueryParams(String query) {
        Map<String, String> params = new HashMap<>();
        if (query == null || query.isEmpty()) {
            return params;
        }

        String[] pairs = query.split("&");
        for (String pair : pairs) {
            int idx = pair.indexOf("=");
            if (idx > 0) {
                String key = pair.substring(0, idx);
                String value = pair.substring(idx + 1);
                params.put(key, value);
            }
        }

        return params;
    }

    /**
     * 创建错误响应
     */
    private String createErrorResponse(String message) {
        JSONObject response = new JSONObject();
        response.put("success", false);
        response.put("message", message);
        return response.toJSONString();
    }

    /**
     * 发送HTTP响应
     */
    private void sendResponse(HttpExchange exchange, int statusCode, String response) throws IOException {
        exchange.getResponseHeaders().add("Content-Type", "application/json");
        byte[] responseBytes = response.getBytes(StandardCharsets.UTF_8);
        exchange.sendResponseHeaders(statusCode, responseBytes.length);

        try (OutputStream os = exchange.getResponseBody()) {
            os.write(responseBytes);
        }
    }
}
