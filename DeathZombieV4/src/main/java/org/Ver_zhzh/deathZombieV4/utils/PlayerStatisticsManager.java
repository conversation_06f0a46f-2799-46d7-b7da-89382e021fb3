package org.Ver_zhzh.deathZombieV4.utils;

import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.configuration.file.YamlConfiguration;
import org.bukkit.entity.Player;
import org.Ver_zhzh.deathZombieV4.DeathZombieV4;

import java.io.File;
import java.io.IOException;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.UUID;

/**
 * 玩家统计数据管理器
 * 管理玩家的游戏统计数据，包括游戏次数、击杀数、生存回合等
 */
public class PlayerStatisticsManager {

    private final DeathZombieV4 plugin;
    
    // 存储玩家统计数据: <玩家UUID, 统计数据>
    private final Map<UUID, PlayerStatistics> playerStatistics;
    
    // 玩家数据配置文件
    private File playerDataFile;
    private FileConfiguration playerDataConfig;

    public PlayerStatisticsManager(DeathZombieV4 plugin) {
        this.plugin = plugin;
        this.playerStatistics = new HashMap<>();
        
        // 初始化玩家数据文件
        initPlayerDataFile();
        
        // 加载保存的统计数据
        loadPlayerStatistics();
    }

    /**
     * 初始化玩家数据文件
     */
    private void initPlayerDataFile() {
        playerDataFile = new File(plugin.getDataFolder(), "playerdata.yml");
        if (!playerDataFile.exists()) {
            try {
                playerDataFile.getParentFile().mkdirs();
                playerDataFile.createNewFile();
            } catch (IOException e) {
                plugin.getLogger().severe("无法创建玩家数据文件: " + e.getMessage());
            }
        }
        playerDataConfig = YamlConfiguration.loadConfiguration(playerDataFile);
    }

    /**
     * 加载玩家统计数据
     */
    private void loadPlayerStatistics() {
        if (!playerDataConfig.contains("players")) {
            playerDataConfig.createSection("players");
            return;
        }
        
        ConfigurationSection playersSection = playerDataConfig.getConfigurationSection("players");
        if (playersSection == null) {
            return;
        }
        
        for (String uuidString : playersSection.getKeys(false)) {
            try {
                UUID uuid = UUID.fromString(uuidString);
                PlayerStatistics stats = loadPlayerStatisticsFromConfig(uuidString);
                playerStatistics.put(uuid, stats);
            } catch (IllegalArgumentException e) {
                plugin.getLogger().warning("无效的UUID格式: " + uuidString);
            }
        }
        
        plugin.getLogger().info("已加载 " + playerStatistics.size() + " 个玩家的统计数据");
    }

    /**
     * 从配置文件加载单个玩家的统计数据
     */
    private PlayerStatistics loadPlayerStatisticsFromConfig(String uuidString) {
        PlayerStatistics stats = new PlayerStatistics();
        
        String basePath = "players." + uuidString + ".game_statistics.";
        stats.setTotalGamesPlayed(playerDataConfig.getInt(basePath + "total_games_played", 0));
        stats.setTotalZombieKills(playerDataConfig.getInt(basePath + "total_zombie_kills", 0));
        stats.setTotalRoundsSurvived(playerDataConfig.getInt(basePath + "total_rounds_survived", 0));
        stats.setTotalTeammateRescues(playerDataConfig.getInt(basePath + "total_teammate_rescues", 0));
        stats.setHighestMoneyEarned(playerDataConfig.getDouble(basePath + "highest_money_earned", 0.0));
        
        // 加载地图记录
        ConfigurationSection mapRecordsSection = playerDataConfig.getConfigurationSection(basePath + "map_records");
        if (mapRecordsSection != null) {
            for (String mapName : mapRecordsSection.getKeys(false)) {
                int maxRounds = playerDataConfig.getInt(basePath + "map_records." + mapName + ".max_rounds_survived", 0);
                long fastestTime = playerDataConfig.getLong(basePath + "map_records." + mapName + ".fastest_completion_time", 0);
                stats.setMapRecord(mapName, maxRounds, fastestTime);
            }
        }
        
        return stats;
    }

    /**
     * 保存玩家统计数据
     */
    public void savePlayerStatistics() {
        for (Map.Entry<UUID, PlayerStatistics> entry : playerStatistics.entrySet()) {
            String uuidString = entry.getKey().toString();
            PlayerStatistics stats = entry.getValue();
            savePlayerStatisticsToConfig(uuidString, stats);
        }
        
        try {
            playerDataConfig.save(playerDataFile);
            plugin.getLogger().info("已保存 " + playerStatistics.size() + " 个玩家的统计数据");
        } catch (IOException e) {
            plugin.getLogger().severe("保存玩家统计数据时出错: " + e.getMessage());
        }
    }

    /**
     * 保存单个玩家的统计数据到配置文件
     */
    private void savePlayerStatisticsToConfig(String uuidString, PlayerStatistics stats) {
        String basePath = "players." + uuidString + ".game_statistics.";
        
        playerDataConfig.set(basePath + "total_games_played", stats.getTotalGamesPlayed());
        playerDataConfig.set(basePath + "total_zombie_kills", stats.getTotalZombieKills());
        playerDataConfig.set(basePath + "total_rounds_survived", stats.getTotalRoundsSurvived());
        playerDataConfig.set(basePath + "total_teammate_rescues", stats.getTotalTeammateRescues());
        playerDataConfig.set(basePath + "highest_money_earned", stats.getHighestMoneyEarned());
        
        // 保存地图记录
        for (Map.Entry<String, PlayerStatistics.MapRecord> mapEntry : stats.getMapRecords().entrySet()) {
            String mapName = mapEntry.getKey();
            PlayerStatistics.MapRecord record = mapEntry.getValue();
            playerDataConfig.set(basePath + "map_records." + mapName + ".max_rounds_survived", record.getMaxRoundsSurvived());
            playerDataConfig.set(basePath + "map_records." + mapName + ".fastest_completion_time", record.getFastestCompletionTime());
        }
    }

    /**
     * 获取玩家的统计数据
     * 如果玩家数据不存在，会创建新的数据并保存
     */
    public PlayerStatistics getPlayerStatistics(Player player) {
        UUID uuid = player.getUniqueId();

        // 如果内存中没有数据，尝试从配置文件加载
        if (!playerStatistics.containsKey(uuid)) {
            PlayerStatistics stats = loadPlayerStatisticsFromConfig(uuid.toString());
            playerStatistics.put(uuid, stats);

            // 如果是新玩家（配置文件中也没有数据），保存初始数据
            if (stats.getTotalGamesPlayed() == 0 && stats.getTotalZombieKills() == 0 &&
                stats.getTotalRoundsSurvived() == 0 && stats.getTotalTeammateRescues() == 0 &&
                stats.getHighestMoneyEarned() == 0.0) {

                setPlayerStatistics(player, stats);
                plugin.getLogger().info("为新玩家 " + player.getName() + " 初始化统计数据");
            }
        }

        return playerStatistics.get(uuid);
    }

    /**
     * 获取玩家的统计数据（通过UUID）
     */
    public PlayerStatistics getPlayerStatistics(UUID uuid) {
        return playerStatistics.getOrDefault(uuid, new PlayerStatistics());
    }

    /**
     * 设置玩家的统计数据
     */
    public void setPlayerStatistics(Player player, PlayerStatistics stats) {
        playerStatistics.put(player.getUniqueId(), stats);
        savePlayerStatisticsToConfig(player.getUniqueId().toString(), stats);
        
        try {
            playerDataConfig.save(playerDataFile);
        } catch (IOException e) {
            plugin.getLogger().warning("保存玩家统计数据时出错: " + e.getMessage());
        }
    }

    /**
     * 增加玩家游戏次数
     */
    public void incrementGamesPlayed(Player player) {
        PlayerStatistics stats = getPlayerStatistics(player);
        stats.incrementTotalGamesPlayed();
        setPlayerStatistics(player, stats);
    }

    /**
     * 增加玩家击杀僵尸数
     */
    public void addZombieKills(Player player, int kills) {
        PlayerStatistics stats = getPlayerStatistics(player);
        stats.addTotalZombieKills(kills);
        setPlayerStatistics(player, stats);
    }

    /**
     * 增加玩家生存回合数
     */
    public void addRoundsSurvived(Player player, int rounds) {
        PlayerStatistics stats = getPlayerStatistics(player);
        stats.addTotalRoundsSurvived(rounds);
        setPlayerStatistics(player, stats);
    }

    /**
     * 增加玩家救援队友数
     */
    public void addTeammateRescues(Player player, int rescues) {
        PlayerStatistics stats = getPlayerStatistics(player);
        stats.addTotalTeammateRescues(rescues);
        setPlayerStatistics(player, stats);
    }

    /**
     * 更新玩家最高金钱记录
     */
    public void updateHighestMoney(Player player, double money) {
        PlayerStatistics stats = getPlayerStatistics(player);
        if (money > stats.getHighestMoneyEarned()) {
            stats.setHighestMoneyEarned(money);
            setPlayerStatistics(player, stats);
        }
    }

    /**
     * 更新玩家地图记录
     */
    public void updateMapRecord(Player player, String mapName, int roundsSurvived, long completionTime) {
        PlayerStatistics stats = getPlayerStatistics(player);
        stats.updateMapRecord(mapName, roundsSurvived, completionTime);
        setPlayerStatistics(player, stats);
    }

    /**
     * 获取所有玩家的UUID
     * 从配置文件中读取所有玩家UUID，确保包含所有有记录的玩家
     */
    public Set<UUID> getAllPlayerUuids() {
        Set<UUID> allUuids = new HashSet<>();

        // 添加内存中的UUID
        allUuids.addAll(playerStatistics.keySet());

        // 从配置文件中读取所有UUID，确保不遗漏任何玩家
        if (playerDataConfig.contains("players")) {
            ConfigurationSection playersSection = playerDataConfig.getConfigurationSection("players");
            if (playersSection != null) {
                for (String uuidString : playersSection.getKeys(false)) {
                    try {
                        UUID uuid = UUID.fromString(uuidString);
                        allUuids.add(uuid);

                        // 如果内存中没有这个玩家的数据，加载它
                        if (!playerStatistics.containsKey(uuid)) {
                            PlayerStatistics stats = loadPlayerStatisticsFromConfig(uuidString);
                            playerStatistics.put(uuid, stats);
                        }
                    } catch (IllegalArgumentException e) {
                        plugin.getLogger().warning("无效的UUID格式: " + uuidString);
                    }
                }
            }
        }

        return allUuids;
    }

    /**
     * 获取所有玩家的统计数据
     */
    public Map<UUID, PlayerStatistics> getAllPlayerStatistics() {
        return new HashMap<>(playerStatistics);
    }

    /**
     * 玩家统计数据类
     */
    public static class PlayerStatistics {
        private int totalGamesPlayed = 0;           // 总游戏次数
        private int totalZombieKills = 0;           // 累计击杀僵尸数
        private int totalRoundsSurvived = 0;        // 累计生存回合数
        private int totalTeammateRescues = 0;       // 累计救援队友数
        private double highestMoneyEarned = 0.0;    // 累计最高金钱数
        
        // 单地图最佳记录: <地图名, 记录>
        private final Map<String, MapRecord> mapRecords = new HashMap<>();

        // Getters and Setters
        public int getTotalGamesPlayed() { return totalGamesPlayed; }
        public void setTotalGamesPlayed(int totalGamesPlayed) { this.totalGamesPlayed = totalGamesPlayed; }
        public void incrementTotalGamesPlayed() { this.totalGamesPlayed++; }

        public int getTotalZombieKills() { return totalZombieKills; }
        public void setTotalZombieKills(int totalZombieKills) { this.totalZombieKills = totalZombieKills; }
        public void addTotalZombieKills(int kills) { this.totalZombieKills += kills; }

        public int getTotalRoundsSurvived() { return totalRoundsSurvived; }
        public void setTotalRoundsSurvived(int totalRoundsSurvived) { this.totalRoundsSurvived = totalRoundsSurvived; }
        public void addTotalRoundsSurvived(int rounds) { this.totalRoundsSurvived += rounds; }

        public int getTotalTeammateRescues() { return totalTeammateRescues; }
        public void setTotalTeammateRescues(int totalTeammateRescues) { this.totalTeammateRescues = totalTeammateRescues; }
        public void addTotalTeammateRescues(int rescues) { this.totalTeammateRescues += rescues; }

        public double getHighestMoneyEarned() { return highestMoneyEarned; }
        public void setHighestMoneyEarned(double highestMoneyEarned) { this.highestMoneyEarned = highestMoneyEarned; }

        public Map<String, MapRecord> getMapRecords() { return mapRecords; }

        /**
         * 设置地图记录
         */
        public void setMapRecord(String mapName, int maxRounds, long fastestTime) {
            mapRecords.put(mapName, new MapRecord(maxRounds, fastestTime));
        }

        /**
         * 更新地图记录
         */
        public void updateMapRecord(String mapName, int roundsSurvived, long completionTime) {
            MapRecord record = mapRecords.getOrDefault(mapName, new MapRecord(0, 0));
            
            // 更新最大生存回合数
            if (roundsSurvived > record.getMaxRoundsSurvived()) {
                record.setMaxRoundsSurvived(roundsSurvived);
            }
            
            // 更新最快完成时间（只有胜利时才记录）
            if (completionTime > 0 && (record.getFastestCompletionTime() == 0 || completionTime < record.getFastestCompletionTime())) {
                record.setFastestCompletionTime(completionTime);
            }
            
            mapRecords.put(mapName, record);
        }

        /**
         * 地图记录类
         */
        public static class MapRecord {
            private int maxRoundsSurvived = 0;      // 单地图生存回合最大值
            private long fastestCompletionTime = 0; // 最快单地图游戏耗时(毫秒，需要胜利)

            public MapRecord(int maxRoundsSurvived, long fastestCompletionTime) {
                this.maxRoundsSurvived = maxRoundsSurvived;
                this.fastestCompletionTime = fastestCompletionTime;
            }

            public int getMaxRoundsSurvived() { return maxRoundsSurvived; }
            public void setMaxRoundsSurvived(int maxRoundsSurvived) { this.maxRoundsSurvived = maxRoundsSurvived; }

            public long getFastestCompletionTime() { return fastestCompletionTime; }
            public void setFastestCompletionTime(long fastestCompletionTime) { this.fastestCompletionTime = fastestCompletionTime; }
        }
    }
}
