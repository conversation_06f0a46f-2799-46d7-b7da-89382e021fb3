package org.Ver_zhzh.deathZombieV4.utils;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

import org.Ver_zhzh.deathZombieV4.DeathZombieV4;
import org.bukkit.ChatColor;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.NamespacedKey;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.block.Action;
import org.bukkit.event.player.PlayerInteractEvent;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.persistence.PersistentDataContainer;
import org.bukkit.persistence.PersistentDataType;

public class RegionSelector implements Listener {

    private final DeathZombieV4 plugin;
    private final NamespacedKey wandKey;
    private final NamespacedKey gameNameKey;
    private final Map<UUID, Location> pos1Selections;
    private final Map<UUID, Location> pos2Selections;

    public RegionSelector(DeathZombieV4 plugin) {
        this.plugin = plugin;
        this.wandKey = new NamespacedKey(plugin, "wand");
        this.gameNameKey = new NamespacedKey(plugin, "game_name");
        this.pos1Selections = new HashMap<>();
        this.pos2Selections = new HashMap<>();
        
        plugin.getServer().getPluginManager().registerEvents(this, plugin);
    }

    /**
     * 给玩家一个区域选择棒
     * @param player 玩家
     * @param gameName 游戏名称
     */
    public void giveSelectionWand(Player player, String gameName) {
        ItemStack wand = new ItemStack(Material.BLAZE_ROD);
        ItemMeta meta = wand.getItemMeta();
        
        meta.setDisplayName(ChatColor.GOLD + "区域选择棒");
        meta.setLore(Arrays.asList(
                ChatColor.YELLOW + "左键：选择第一个点",
                ChatColor.YELLOW + "右键：选择第二个点",
                ChatColor.GRAY + "游戏：" + gameName
        ));
        
        // 存储选择工具的标识和游戏名称
        PersistentDataContainer container = meta.getPersistentDataContainer();
        container.set(wandKey, PersistentDataType.BYTE, (byte) 1);
        container.set(gameNameKey, PersistentDataType.STRING, gameName);
        
        wand.setItemMeta(meta);
        player.getInventory().addItem(wand);
    }

    /**
     * 判断一个物品是否是区域选择棒
     * @param item 物品
     * @return 如果是选择棒则返回true
     */
    private boolean isSelectionWand(ItemStack item) {
        if (item == null || !item.hasItemMeta()) {
            return false;
        }
        
        ItemMeta meta = item.getItemMeta();
        PersistentDataContainer container = meta.getPersistentDataContainer();
        
        return container.has(wandKey, PersistentDataType.BYTE);
    }

    /**
     * 从物品中获取游戏名称
     * @param item 物品
     * @return 游戏名称，如果不存在则返回null
     */
    private String getGameNameFromWand(ItemStack item) {
        if (item == null || !item.hasItemMeta()) {
            return null;
        }
        
        ItemMeta meta = item.getItemMeta();
        PersistentDataContainer container = meta.getPersistentDataContainer();
        
        if (container.has(gameNameKey, PersistentDataType.STRING)) {
            return container.get(gameNameKey, PersistentDataType.STRING);
        }
        
        return null;
    }

    /**
     * 处理玩家交互事件
     * @param event 事件
     */
    @EventHandler
    public void onPlayerInteract(PlayerInteractEvent event) {
        Player player = event.getPlayer();
        ItemStack item = event.getItem();
        
        if (item == null || !isSelectionWand(item)) {
            return;
        }
        
        event.setCancelled(true);
        
        if (event.getAction() == Action.LEFT_CLICK_BLOCK) {
            // 设置第一个点
            pos1Selections.put(player.getUniqueId(), event.getClickedBlock().getLocation());
            player.sendMessage(ChatColor.GREEN + "第一个点已设置在 " + 
                    formatLocation(event.getClickedBlock().getLocation()));
        } else if (event.getAction() == Action.RIGHT_CLICK_BLOCK) {
            // 设置第二个点
            pos2Selections.put(player.getUniqueId(), event.getClickedBlock().getLocation());
            player.sendMessage(ChatColor.GREEN + "第二个点已设置在 " + 
                    formatLocation(event.getClickedBlock().getLocation()));
        }
    }

    /**
     * 格式化位置信息
     * @param loc 位置
     * @return 格式化的字符串
     */
    private String formatLocation(Location loc) {
        return String.format("x=%d, y=%d, z=%d", loc.getBlockX(), loc.getBlockY(), loc.getBlockZ());
    }

    /**
     * 检查玩家是否有完整的选择
     * @param player 玩家
     * @return 如果有完整的选择则返回true
     */
    public boolean hasSelection(Player player) {
        UUID playerId = player.getUniqueId();
        return pos1Selections.containsKey(playerId) && pos2Selections.containsKey(playerId);
    }

    /**
     * 获取玩家的区域选择
     * @param player 玩家
     * @return 区域，如果没有完整的选择则返回null
     */
    public Region getSelection(Player player) {
        if (!hasSelection(player)) {
            return null;
        }
        
        UUID playerId = player.getUniqueId();
        return new Region(pos1Selections.get(playerId), pos2Selections.get(playerId));
    }

    /**
     * 清除玩家的选择
     * @param player 玩家
     */
    public void clearSelection(Player player) {
        UUID playerId = player.getUniqueId();
        pos1Selections.remove(playerId);
        pos2Selections.remove(playerId);
    }
    
    /**
     * 获取所有玩家的区域选择
     * @return 以玩家名称为键，Region为值的Map
     */
    public Map<String, Region> getSelections() {
        Map<String, Region> selections = new HashMap<>();
        
        // 遍历所有有pos1和pos2的玩家，构建区域
        for (UUID playerId : pos1Selections.keySet()) {
            if (pos2Selections.containsKey(playerId)) {
                Location pos1 = pos1Selections.get(playerId);
                Location pos2 = pos2Selections.get(playerId);
                
                // 根据UUID获取玩家名称
                String playerName = plugin.getServer().getOfflinePlayer(playerId).getName();
                if (playerName != null) {
                    selections.put(playerName, new Region(pos1, pos2));
                }
            }
        }
        
        return selections;
    }
} 