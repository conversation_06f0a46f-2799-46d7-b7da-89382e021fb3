package org.Ver_zhzh.deathZombieV4.utils;

/**
 * 标题消息封装类
 * 用于封装标题、副标题和显示时间参数
 */
public class TitleMessage {
    
    private final String title;
    private final String subtitle;
    private final int fadeIn;
    private final int stay;
    private final int fadeOut;
    
    /**
     * 构造函数
     *
     * @param title 主标题
     * @param subtitle 副标题
     * @param fadeIn 淡入时间（tick）
     * @param stay 停留时间（tick）
     * @param fadeOut 淡出时间（tick）
     */
    public TitleMessage(String title, String subtitle, int fadeIn, int stay, int fadeOut) {
        this.title = title;
        this.subtitle = subtitle;
        this.fadeIn = fadeIn;
        this.stay = stay;
        this.fadeOut = fadeOut;
    }
    
    /**
     * 获取主标题
     *
     * @return 主标题
     */
    public String getTitle() {
        return title;
    }
    
    /**
     * 获取副标题
     *
     * @return 副标题
     */
    public String getSubtitle() {
        return subtitle;
    }
    
    /**
     * 获取淡入时间
     *
     * @return 淡入时间（tick）
     */
    public int getFadeIn() {
        return fadeIn;
    }
    
    /**
     * 获取停留时间
     *
     * @return 停留时间（tick）
     */
    public int getStay() {
        return stay;
    }
    
    /**
     * 获取淡出时间
     *
     * @return 淡出时间（tick）
     */
    public int getFadeOut() {
        return fadeOut;
    }
    
    @Override
    public String toString() {
        return "TitleMessage{" +
                "title='" + title + '\'' +
                ", subtitle='" + subtitle + '\'' +
                ", fadeIn=" + fadeIn +
                ", stay=" + stay +
                ", fadeOut=" + fadeOut +
                '}';
    }
}
