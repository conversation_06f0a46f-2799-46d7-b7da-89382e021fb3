package org.Ver_zhzh.deathZombieV4.utils;

import org.Ver_zhzh.deathZombieV4.DeathZombieV4;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.World;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.configuration.file.YamlConfiguration;
import org.bukkit.entity.ArmorStand;
import org.bukkit.entity.EntityType;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.scheduler.BukkitRunnable;
import org.bukkit.scheduler.BukkitTask;
import eu.decentsoftware.holograms.api.DHAPI;

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 管理全局电源按钮的悬浮文字和物品旋转效果
 */
public class PowerButtonEffectManager {

    private final DeathZombieV4 plugin;
    private final Map<String, String> powerButtonHolograms = new ConcurrentHashMap<>();
    private final Map<String, ArmorStand> powerButtonArmorStands = new ConcurrentHashMap<>();
    private final Map<String, BukkitTask> rotationTasks = new ConcurrentHashMap<>();

    public PowerButtonEffectManager(DeathZombieV4 plugin) {
        this.plugin = plugin;
    }

    /**
     * 为指定游戏创建全局电源按钮的悬浮文字和物品旋转效果
     *
     * @param gameName 游戏名称
     */
    public void createPowerButtonEffects(String gameName) {
        // 获取游戏配置文件
        File gameFile = new File(plugin.getDataFolder(), "game/" + gameName + ".yml");
        if (!gameFile.exists()) {
            plugin.getLogger().warning("游戏配置文件不存在: " + gameFile.getAbsolutePath());
            return;
        }

        FileConfiguration gameConfig = YamlConfiguration.loadConfiguration(gameFile);

        // 检查是否有电源按钮配置
        if (!gameConfig.isConfigurationSection("power_button")) {
            return;
        }

        // 获取电源按钮位置
        String worldName = gameConfig.getString("power_button.world");
        double x = gameConfig.getDouble("power_button.x");
        double y = gameConfig.getDouble("power_button.y");
        double z = gameConfig.getDouble("power_button.z");
        int price = gameConfig.getInt("power_button.price", 1000);
        boolean unlocked = gameConfig.getBoolean("power_button.unlocked", false);

        if (worldName == null) {
            return;
        }

        World world = Bukkit.getWorld(worldName);
        if (world == null) {
            plugin.getLogger().warning("世界不存在: " + worldName);
            return;
        }

        Location buttonLocation = new Location(world, x, y, z);

        // 创建悬浮文字
        createPowerButtonHologram(gameName, buttonLocation, price, unlocked);

        // 创建物品旋转效果
        createPowerButtonArmorStand(gameName, buttonLocation, unlocked);
    }

    /**
     * 创建全局电源按钮的悬浮文字
     *
     * @param gameName 游戏名称
     * @param location 电源按钮位置
     * @param price 解锁价格
     * @param unlocked 是否已解锁
     */
    private void createPowerButtonHologram(String gameName, Location location, int price, boolean unlocked) {
        if (!plugin.isDecentHologramsAvailable()) {
            return;
        }

        // 移除旧的悬浮文字
        removePowerButtonHologram(gameName);

        // 创建悬浮文字ID
        String holoId = "power_button_" + gameName;

        // 设置悬浮文字位置（在电源按钮上方2格）
        Location holoLocation = location.clone().add(0.5, 2.0, 0.5);

        // 根据解锁状态设置不同的文字内容
        List<String> lines = new ArrayList<>();
        if (unlocked) {
            lines.add(ChatColor.GREEN + "§l全局电源按钮");
            lines.add("#ICON: EMERALD_BLOCK");
            lines.add(ChatColor.YELLOW + "已解锁");
            lines.add(ChatColor.AQUA + "增益效果已激活");
        } else {
            lines.add(ChatColor.RED + "§l全局电源按钮");
            lines.add("#ICON: REDSTONE_BLOCK");
            lines.add(ChatColor.YELLOW + "解锁价格: " + ChatColor.GOLD + price + " 金币");
            lines.add(ChatColor.GRAY + "右键点击解锁");
        }

        // 创建悬浮文字
        boolean success = createHologramDirect(holoId, holoLocation, lines);
        if (success) {
            powerButtonHolograms.put(gameName, holoId);
            plugin.getLogger().info("已为游戏 " + gameName + " 创建全局电源按钮悬浮文字");
        }
    }

    /**
     * 创建全局电源按钮的物品旋转效果
     *
     * @param gameName 游戏名称
     * @param location 电源按钮位置
     * @param unlocked 是否已解锁
     */
    private void createPowerButtonArmorStand(String gameName, Location location, boolean unlocked) {
        // 移除旧的盔甲架
        removePowerButtonArmorStand(gameName);

        // 设置盔甲架位置（在电源按钮上方1格）
        Location armorStandLocation = location.clone().add(0.5, 1.0, 0.5);

        // 创建盔甲架
        ArmorStand armorStand = (ArmorStand) armorStandLocation.getWorld().spawnEntity(armorStandLocation, EntityType.ARMOR_STAND);

        // 设置盔甲架属性
        armorStand.setVisible(false);
        armorStand.setGravity(false);
        armorStand.setCanPickupItems(false);
        armorStand.setCustomNameVisible(false);
        armorStand.setSmall(true);

        // 根据解锁状态设置不同的物品
        ItemStack item;
        if (unlocked) {
            item = new ItemStack(Material.EMERALD_BLOCK);
            ItemMeta meta = item.getItemMeta();
            if (meta != null) {
                meta.setDisplayName(ChatColor.GREEN + "已激活的电源按钮");
                item.setItemMeta(meta);
            }
        } else {
            item = new ItemStack(Material.REDSTONE_BLOCK);
            ItemMeta meta = item.getItemMeta();
            if (meta != null) {
                meta.setDisplayName(ChatColor.RED + "未激活的电源按钮");
                item.setItemMeta(meta);
            }
        }

        // 设置盔甲架手持物品
        armorStand.getEquipment().setItemInMainHand(item);

        // 保存盔甲架引用
        powerButtonArmorStands.put(gameName, armorStand);

        // 创建旋转任务
        createRotationTask(gameName, armorStand);

        plugin.getLogger().info("已为游戏 " + gameName + " 创建全局电源按钮物品旋转效果");
    }

    /**
     * 创建物品旋转任务
     *
     * @param gameName 游戏名称
     * @param armorStand 盔甲架
     */
    private void createRotationTask(String gameName, ArmorStand armorStand) {
        // 停止旧的旋转任务
        stopRotationTask(gameName);

        // 创建新的旋转任务
        BukkitTask task = new BukkitRunnable() {
            private float yaw = 0;

            @Override
            public void run() {
                if (armorStand == null || armorStand.isDead()) {
                    this.cancel();
                    rotationTasks.remove(gameName);
                    return;
                }

                // 更新旋转角度
                yaw += 5; // 每次旋转5度
                if (yaw >= 360) {
                    yaw = 0;
                }

                // 设置盔甲架旋转
                Location newLocation = armorStand.getLocation();
                newLocation.setYaw(yaw);
                armorStand.teleport(newLocation);
            }
        }.runTaskTimer(plugin, 0L, 2L); // 每2tick执行一次（约0.1秒）

        rotationTasks.put(gameName, task);
    }

    /**
     * 更新全局电源按钮效果（当解锁状态改变时）
     *
     * @param gameName 游戏名称
     */
    public void updatePowerButtonEffects(String gameName) {
        // 重新创建效果
        createPowerButtonEffects(gameName);
    }

    /**
     * 直接创建悬浮文字
     *
     * @param holoId 悬浮文字ID
     * @param location 位置
     * @param lines 文字内容
     * @return 是否创建成功
     */
    private boolean createHologramDirect(String holoId, Location location, List<String> lines) {
        try {
            // 检查是否已存在相同ID的悬浮文字，如果有则先删除
            if (DHAPI.getHologram(holoId) != null) {
                plugin.getLogger().warning("发现已存在相同ID的全局电源按钮悬浮文字，先删除: " + holoId);
                DHAPI.removeHologram(holoId);
            }

            // 创建悬浮文字
            DHAPI.createHologram(holoId, location, lines);
            return true;
        } catch (Exception e) {
            plugin.getLogger().severe("创建全局电源按钮悬浮文字失败: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 移除指定游戏的全局电源按钮悬浮文字
     *
     * @param gameName 游戏名称
     */
    public void removePowerButtonHologram(String gameName) {
        if (!powerButtonHolograms.containsKey(gameName)) {
            return;
        }

        String holoId = powerButtonHolograms.get(gameName);
        try {
            DHAPI.removeHologram(holoId);
        } catch (Exception e) {
            plugin.getLogger().warning("移除全局电源按钮悬浮文字失败: " + e.getMessage());
        }

        powerButtonHolograms.remove(gameName);
    }

    /**
     * 移除指定游戏的全局电源按钮物品旋转效果
     *
     * @param gameName 游戏名称
     */
    public void removePowerButtonArmorStand(String gameName) {
        // 停止旋转任务
        stopRotationTask(gameName);

        // 移除盔甲架
        if (powerButtonArmorStands.containsKey(gameName)) {
            ArmorStand armorStand = powerButtonArmorStands.get(gameName);
            if (armorStand != null && !armorStand.isDead()) {
                armorStand.remove();
            }
            powerButtonArmorStands.remove(gameName);
        }
    }

    /**
     * 停止指定游戏的旋转任务
     *
     * @param gameName 游戏名称
     */
    private void stopRotationTask(String gameName) {
        if (rotationTasks.containsKey(gameName)) {
            BukkitTask task = rotationTasks.get(gameName);
            if (task != null && !task.isCancelled()) {
                task.cancel();
            }
            rotationTasks.remove(gameName);
        }
    }

    /**
     * 移除指定游戏的所有全局电源按钮效果
     *
     * @param gameName 游戏名称
     */
    public void removePowerButtonEffects(String gameName) {
        removePowerButtonHologram(gameName);
        removePowerButtonArmorStand(gameName);
        plugin.getLogger().info("已清理游戏 " + gameName + " 的全局电源按钮效果");
    }

    /**
     * 清理所有全局电源按钮效果
     */
    public void cleanupAllEffects() {
        // 清理所有悬浮文字
        for (String gameName : powerButtonHolograms.keySet()) {
            removePowerButtonHologram(gameName);
        }

        // 清理所有盔甲架和旋转任务
        for (String gameName : powerButtonArmorStands.keySet()) {
            removePowerButtonArmorStand(gameName);
        }

        plugin.getLogger().info("已清理所有全局电源按钮效果");
    }

    /**
     * 当游戏结束时清理效果
     *
     * @param gameName 游戏名称
     */
    public void onGameEnd(String gameName) {
        removePowerButtonEffects(gameName);
    }

    /**
     * 获取所有电源按钮悬浮文字
     *
     * @return 电源按钮悬浮文字映射
     */
    public Map<String, String> getPowerButtonHolograms() {
        return new HashMap<>(powerButtonHolograms);
    }

    /**
     * 获取所有电源按钮盔甲架
     *
     * @return 电源按钮盔甲架映射
     */
    public Map<String, ArmorStand> getPowerButtonArmorStands() {
        return new HashMap<>(powerButtonArmorStands);
    }
}
