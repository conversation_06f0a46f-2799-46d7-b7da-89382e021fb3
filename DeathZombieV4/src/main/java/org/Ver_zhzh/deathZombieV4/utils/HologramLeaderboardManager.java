package org.Ver_zhzh.deathZombieV4.utils;

import eu.decentsoftware.holograms.api.DHAPI;
import eu.decentsoftware.holograms.api.holograms.Hologram;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.Location;
import org.bukkit.World;
import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.configuration.file.YamlConfiguration;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.player.PlayerInteractEvent;
import org.bukkit.scheduler.BukkitRunnable;
import org.Ver_zhzh.deathZombieV4.DeathZombieV4;
import org.Ver_zhzh.deathZombieV4.utils.LeaderboardManager.LeaderboardEntry;
import org.Ver_zhzh.deathZombieV4.utils.LeaderboardManager.StatType;

import java.io.File;
import java.io.IOException;
import java.util.*;
import java.util.regex.Pattern;

/**
 * 全息排行榜管理器
 * 使用DecentHolograms创建和管理排行榜全息图
 */
public class HologramLeaderboardManager implements Listener {

    private final DeathZombieV4 plugin;
    private final LeaderboardManager leaderboardManager;

    // 存储全息排行榜信息: <全息图名称, 排行榜信息>
    private final Map<String, HologramLeaderboardInfo> hologramLeaderboards = new HashMap<>();

    // 更新任务
    private BukkitRunnable updateTask;

    // 名称验证正则表达式（只允许英文字母和数字）
    private static final Pattern VALID_NAME_PATTERN = Pattern.compile("^[a-zA-Z0-9]+$");

    // 每页显示的条目数
    private static final int ITEMS_PER_PAGE = 10;

    // 配置文件
    private File configFile;
    private FileConfiguration config;

    public HologramLeaderboardManager(DeathZombieV4 plugin) {
        this.plugin = plugin;
        this.leaderboardManager = new LeaderboardManager(plugin);

        // 初始化配置文件
        initializeConfig();

        // 加载已保存的全息排行榜
        loadHologramLeaderboards();

        // 启动定期更新任务（每5分钟更新一次）
        startUpdateTask();
    }

    /**
     * 全息排行榜信息类
     */
    public static class HologramLeaderboardInfo {
        private final String holoName;
        private StatType currentStatType;
        private final String mapName;
        private final int displayRange;
        private final Location location;
        private final boolean showAllData;
        private int currentPage;
        private int currentStatIndex; // 用于全部数据模式的统计类型切换

        public HologramLeaderboardInfo(String holoName, StatType statType, String mapName,
                                     int displayRange, Location location, boolean showAllData) {
            this.holoName = holoName;
            this.currentStatType = statType;
            this.mapName = mapName;
            this.displayRange = displayRange;
            this.location = location;
            this.showAllData = showAllData;
            this.currentPage = 0;
            this.currentStatIndex = 0;
        }

        public String getHoloName() { return holoName; }
        public StatType getCurrentStatType() { return currentStatType; }
        public void setCurrentStatType(StatType statType) { this.currentStatType = statType; }
        public String getMapName() { return mapName; }
        public int getDisplayRange() { return displayRange; }
        public Location getLocation() { return location; }
        public boolean isShowAllData() { return showAllData; }
        public int getCurrentPage() { return currentPage; }
        public void setCurrentPage(int page) { this.currentPage = Math.max(0, page); }
        public int getCurrentStatIndex() { return currentStatIndex; }
        public void setCurrentStatIndex(int index) { this.currentStatIndex = index; }

        public void nextPage() { this.currentPage++; }
        public void previousPage() { this.currentPage = Math.max(0, this.currentPage - 1); }

        public void nextStatType() {
            if (showAllData) {
                StatType[] types = StatType.values();
                currentStatIndex = (currentStatIndex + 1) % types.length;
                currentStatType = types[currentStatIndex];
            }
        }

        public void previousStatType() {
            if (showAllData) {
                StatType[] types = StatType.values();
                currentStatIndex = (currentStatIndex - 1 + types.length) % types.length;
                currentStatType = types[currentStatIndex];
            }
        }
    }

    /**
     * 验证全息图名称
     */
    public static boolean isValidHologramName(String name) {
        return name != null && VALID_NAME_PATTERN.matcher(name).matches() && name.length() <= 32;
    }

    /**
     * 创建全息排行榜
     */
    public boolean createHologramLeaderboard(Player player, String holoName, StatType statType,
                                           String mapName, int displayRange, boolean showAllData) {
        // 验证名称
        if (!isValidHologramName(holoName)) {
            player.sendMessage(ChatColor.RED + "全息图名称只能包含英文字母和数字，且长度不超过32个字符！");
            return false;
        }

        Location location = player.getLocation().add(0, 2, 0); // 在玩家头顶2格创建

        // 检查是否已存在同名全息图
        if (DHAPI.getHologram(holoName) != null) {
            player.sendMessage(ChatColor.RED + "全息图 '" + holoName + "' 已存在！");
            return false;
        }

        // 创建全息排行榜信息
        HologramLeaderboardInfo info = new HologramLeaderboardInfo(
            holoName, statType, mapName, displayRange, location, showAllData
        );

        // 创建全息图
        if (createHologram(info)) {
            hologramLeaderboards.put(holoName, info);

            // 保存配置
            saveHologramLeaderboards();

            String title = showAllData ? "全部数据" : statType.getDisplayName();
            if (mapName != null && !mapName.isEmpty()) {
                title += " - " + mapName;
            }

            player.sendMessage(ChatColor.GREEN + "成功创建全息排行榜 '" + holoName + "' (" + title + ")");
            player.sendMessage(ChatColor.YELLOW + "提示: 左键切换数据类型，右键翻页");
            player.sendMessage(ChatColor.GRAY + "配置已自动保存，重启后将自动恢复");
            return true;
        } else {
            player.sendMessage(ChatColor.RED + "创建全息排行榜失败！");
            return false;
        }
    }

    /**
     * 移除全息排行榜
     */
    public boolean removeHologramLeaderboard(Player player, String holoName) {
        if (!hologramLeaderboards.containsKey(holoName)) {
            player.sendMessage(ChatColor.RED + "全息排行榜 '" + holoName + "' 不存在！");
            return false;
        }

        // 移除全息图
        DHAPI.removeHologram(holoName);

        // 移除记录
        hologramLeaderboards.remove(holoName);

        // 保存配置
        saveHologramLeaderboards();

        player.sendMessage(ChatColor.GREEN + "成功移除全息排行榜 '" + holoName + "'");
        player.sendMessage(ChatColor.GRAY + "配置已自动保存");
        return true;
    }

    /**
     * 创建全息图
     */
    private boolean createHologram(HologramLeaderboardInfo info) {
        try {
            List<String> lines = generateHologramLines(info);
            DHAPI.createHologram(info.getHoloName(), info.getLocation(), lines);
            return true;
        } catch (Exception e) {
            plugin.getLogger().severe("创建全息排行榜失败: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 生成全息图内容
     */
    private List<String> generateHologramLines(HologramLeaderboardInfo info) {
        List<String> lines = new ArrayList<>();
        
        if (info.isShowAllData()) {
            // 显示全部数据
            lines.addAll(generateAllDataLines(info));
        } else {
            // 显示单项统计
            lines.addAll(generateSingleStatLines(info));
        }
        
        return lines;
    }

    /**
     * 生成全部数据的全息图内容
     */
    private List<String> generateAllDataLines(HologramLeaderboardInfo info) {
        List<String> lines = new ArrayList<>();
        
        // 标题
        String title = info.getMapName() != null ? info.getMapName() : "全服统计";
        lines.add(ChatColor.GOLD + "※ " + title + " ※");
        lines.add("");
        
        // 显示各项统计的前几名
        StatType[] statTypes = {
            StatType.TOTAL_GAMES, StatType.TOTAL_ZOMBIE_KILLS, 
            StatType.TOTAL_ROUNDS_SURVIVED, StatType.HIGHEST_MONEY_EARNED
        };
        
        for (StatType statType : statTypes) {
            lines.add(ChatColor.AQUA + statType.getDisplayName() + ":");
            
            List<LeaderboardEntry> leaderboard = leaderboardManager.getLeaderboard(
                statType, info.getMapName(), 3
            );
            
            if (leaderboard.isEmpty()) {
                lines.add(ChatColor.GRAY + "  暂无数据");
            } else {
                for (int i = 0; i < leaderboard.size(); i++) {
                    LeaderboardEntry entry = leaderboard.get(i);
                    String rankColor = getRankColor(i + 1);
                    String valueStr = formatValue(statType, entry.getValue());
                    lines.add(rankColor + "  " + (i + 1) + ". " + entry.getPlayerName() + 
                             ChatColor.GRAY + " - " + ChatColor.YELLOW + valueStr);
                }
            }
            lines.add("");
        }
        
        // 添加更新时间
        lines.add(ChatColor.GRAY + "点击切换!");
        
        return lines;
    }

    /**
     * 生成单项统计的全息图内容
     */
    private List<String> generateSingleStatLines(HologramLeaderboardInfo info) {
        List<String> lines = new ArrayList<>();

        // 标题
        String title = info.getCurrentStatType().getDisplayName();
        if (info.getMapName() != null && !info.getMapName().isEmpty()) {
            title += " - " + info.getMapName();
        }
        lines.add(ChatColor.GOLD + "※ " + title + " ※");

        // 获取排行榜数据
        List<LeaderboardEntry> fullLeaderboard = leaderboardManager.getLeaderboard(
            info.getCurrentStatType(), info.getMapName(), Integer.MAX_VALUE
        );

        if (fullLeaderboard.isEmpty()) {
            lines.add("");
            lines.add(ChatColor.GRAY + "暂无数据");
        } else {
            // 计算分页
            int totalPages = (int) Math.ceil((double) fullLeaderboard.size() / ITEMS_PER_PAGE);
            int currentPage = Math.min(info.getCurrentPage(), totalPages - 1);
            int startIndex = currentPage * ITEMS_PER_PAGE;
            int endIndex = Math.min(startIndex + ITEMS_PER_PAGE, fullLeaderboard.size());

            // 显示页码信息
            if (totalPages > 1) {
                lines.add(ChatColor.AQUA + "第 " + (currentPage + 1) + "/" + totalPages + " 页");
                lines.add("");
            }

            // 显示当前页的数据
            for (int i = startIndex; i < endIndex; i++) {
                LeaderboardEntry entry = fullLeaderboard.get(i);
                int rank = i + 1;
                String rankColor = getRankColor(rank);
                String valueStr = formatValue(info.getCurrentStatType(), entry.getValue());

                lines.add(rankColor + rank + ". " + ChatColor.WHITE + entry.getPlayerName() +
                         ChatColor.GRAY + " - " + ChatColor.YELLOW + valueStr);
            }
        }

        // 添加操作提示
        lines.add("");
        if (info.isShowAllData()) {
            lines.add(ChatColor.GRAY + "左键: 切换数据类型");
            lines.add(ChatColor.GRAY + "右键: 翻页");
        } else {
            lines.add(ChatColor.GRAY + "左键/右键: 翻页");
        }

        return lines;
    }

    /**
     * 更新所有全息排行榜
     */
    public void updateAllHolograms() {
        for (HologramLeaderboardInfo info : hologramLeaderboards.values()) {
            updateHologram(info);
        }
    }

    /**
     * 更新单个全息图
     */
    private void updateHologram(HologramLeaderboardInfo info) {
        try {
            Hologram hologram = DHAPI.getHologram(info.getHoloName());
            if (hologram != null) {
                List<String> newLines = generateHologramLines(info);
                DHAPI.setHologramLines(hologram, newLines);
            }
        } catch (Exception e) {
            plugin.getLogger().warning("更新全息排行榜失败: " + e.getMessage());
        }
    }

    /**
     * 启动定期更新任务
     */
    private void startUpdateTask() {
        updateTask = new BukkitRunnable() {
            @Override
            public void run() {
                updateAllHolograms();
            }
        };
        
        // 每5分钟更新一次（6000 ticks）
        updateTask.runTaskTimer(plugin, 6000L, 6000L);
    }

    /**
     * 停止更新任务
     */
    public void stopUpdateTask() {
        if (updateTask != null) {
            updateTask.cancel();
            updateTask = null;
        }
    }

    /**
     * 获取排名颜色
     */
    private String getRankColor(int rank) {
        switch (rank) {
            case 1: return ChatColor.GOLD.toString();
            case 2: return ChatColor.GRAY.toString();
            case 3: return ChatColor.YELLOW.toString();
            default: return ChatColor.WHITE.toString();
        }
    }

    /**
     * 格式化数值显示
     */
    private String formatValue(StatType statType, double value) {
        switch (statType) {
            case MAP_FASTEST_TIME:
                if (value == Double.MAX_VALUE) {
                    return "未完成";
                }
                return formatTime((long) value);
            case HIGHEST_MONEY_EARNED:
                return String.format("%.0f", value);
            default:
                return String.valueOf((int) value);
        }
    }

    /**
     * 格式化时间显示
     */
    private String formatTime(long milliseconds) {
        long seconds = milliseconds / 1000;
        long minutes = seconds / 60;
        seconds = seconds % 60;
        
        if (minutes > 0) {
            return String.format("%d:%02d", minutes, seconds);
        } else {
            return String.format("%d秒", seconds);
        }
    }

    /**
     * 处理全息图点击事件
     */
    @EventHandler
    public void onHologramClick(PlayerInteractEvent event) {
        // 这里需要使用DecentHolograms的点击事件
        // 由于DecentHolograms的API限制，我们需要通过其他方式实现点击检测
        // 可以考虑使用玩家右键点击空气时检测附近的全息图
    }

    /**
     * 处理全息图交互
     */
    public void handleHologramInteraction(Player player, String holoName, boolean isLeftClick) {
        HologramLeaderboardInfo info = hologramLeaderboards.get(holoName);
        if (info == null) {
            return;
        }

        if (info.isShowAllData()) {
            if (isLeftClick) {
                // 左键：切换数据类型
                info.nextStatType();
                player.sendMessage(ChatColor.GREEN + "切换到: " + info.getCurrentStatType().getDisplayName());
            } else {
                // 右键：切换全服数据的不同统计类型
                info.previousStatType();
                player.sendMessage(ChatColor.GREEN + "切换到: " + info.getCurrentStatType().getDisplayName());
            }
        } else {
            if (isLeftClick) {
                // 左键：上一页
                info.previousPage();
                player.sendMessage(ChatColor.GREEN + "切换到第 " + (info.getCurrentPage() + 1) + " 页");
            } else {
                // 右键：下一页
                info.nextPage();
                player.sendMessage(ChatColor.GREEN + "切换到第 " + (info.getCurrentPage() + 1) + " 页");
            }
        }

        // 更新全息图显示
        updateHologram(info);
    }

    /**
     * 获取玩家附近的全息排行榜
     */
    public String getNearbyHologram(Player player) {
        Location playerLoc = player.getLocation();

        for (HologramLeaderboardInfo info : hologramLeaderboards.values()) {
            if (info.getLocation().getWorld().equals(playerLoc.getWorld()) &&
                info.getLocation().distance(playerLoc) <= 5.0) {
                return info.getHoloName();
            }
        }

        return null;
    }

    /**
     * 列出所有全息排行榜
     */
    public void listHologramLeaderboards(Player player) {
        if (hologramLeaderboards.isEmpty()) {
            player.sendMessage(ChatColor.YELLOW + "当前没有任何全息排行榜");
            return;
        }

        player.sendMessage(ChatColor.GOLD + "========== 全息排行榜列表 ==========");
        for (HologramLeaderboardInfo info : hologramLeaderboards.values()) {
            String title = info.isShowAllData() ? "全部数据" : info.getCurrentStatType().getDisplayName();
            if (info.getMapName() != null && !info.getMapName().isEmpty()) {
                title += " - " + info.getMapName();
            }

            Location loc = info.getLocation();
            String locationStr = String.format("%.1f, %.1f, %.1f (%s)",
                loc.getX(), loc.getY(), loc.getZ(), loc.getWorld().getName());

            player.sendMessage(ChatColor.AQUA + "• " + ChatColor.WHITE + info.getHoloName() +
                             ChatColor.GRAY + " - " + ChatColor.YELLOW + title);
            player.sendMessage(ChatColor.GRAY + "  位置: " + locationStr +
                             " | 显示范围: " + info.getDisplayRange());
        }
        player.sendMessage(ChatColor.GRAY + "总计: " + hologramLeaderboards.size() + " 个全息排行榜");
    }

    /**
     * 获取所有全息排行榜名称（用于Tab补全）
     */
    public List<String> getAllHologramNames() {
        return new ArrayList<>(hologramLeaderboards.keySet());
    }

    /**
     * 初始化配置文件
     */
    private void initializeConfig() {
        configFile = new File(plugin.getDataFolder(), "gamesessions.yml");

        if (!configFile.exists()) {
            try {
                configFile.getParentFile().mkdirs();
                configFile.createNewFile();
            } catch (IOException e) {
                plugin.getLogger().severe("无法创建 gamesessions.yml 文件: " + e.getMessage());
            }
        }

        config = YamlConfiguration.loadConfiguration(configFile);
    }

    /**
     * 加载已保存的全息排行榜
     */
    private void loadHologramLeaderboards() {
        ConfigurationSection holoSection = config.getConfigurationSection("hologram_leaderboards");
        if (holoSection == null) {
            return;
        }

        for (String holoName : holoSection.getKeys(false)) {
            try {
                ConfigurationSection holoConfig = holoSection.getConfigurationSection(holoName);
                if (holoConfig == null) continue;

                // 读取配置
                String statTypeName = holoConfig.getString("stat_type");
                String mapName = holoConfig.getString("map_name");
                int displayRange = holoConfig.getInt("display_range", 10);
                boolean showAllData = holoConfig.getBoolean("show_all_data", false);

                // 读取位置信息
                String worldName = holoConfig.getString("location.world");
                double x = holoConfig.getDouble("location.x");
                double y = holoConfig.getDouble("location.y");
                double z = holoConfig.getDouble("location.z");
                float yaw = (float) holoConfig.getDouble("location.yaw");
                float pitch = (float) holoConfig.getDouble("location.pitch");

                World world = Bukkit.getWorld(worldName);
                if (world == null) {
                    plugin.getLogger().warning("世界 '" + worldName + "' 不存在，跳过全息排行榜 '" + holoName + "'");
                    continue;
                }

                Location location = new Location(world, x, y, z, yaw, pitch);

                // 解析统计类型
                StatType statType = null;
                if (!showAllData) {
                    statType = StatType.fromDisplayName(statTypeName);
                    if (statType == null) {
                        plugin.getLogger().warning("无效的统计类型 '" + statTypeName + "'，跳过全息排行榜 '" + holoName + "'");
                        continue;
                    }
                }

                // 创建全息排行榜信息
                HologramLeaderboardInfo info = new HologramLeaderboardInfo(
                    holoName, statType, mapName, displayRange, location, showAllData
                );

                // 创建全息图
                if (createHologram(info)) {
                    hologramLeaderboards.put(holoName, info);
                    plugin.getLogger().info("已加载全息排行榜: " + holoName);
                } else {
                    plugin.getLogger().warning("无法创建全息排行榜: " + holoName);
                }

            } catch (Exception e) {
                plugin.getLogger().warning("加载全息排行榜 '" + holoName + "' 时出错: " + e.getMessage());
            }
        }
    }

    /**
     * 保存全息排行榜配置
     */
    private void saveHologramLeaderboards() {
        // 清除现有配置
        config.set("hologram_leaderboards", null);

        for (HologramLeaderboardInfo info : hologramLeaderboards.values()) {
            String path = "hologram_leaderboards." + info.getHoloName();

            // 保存基本信息
            config.set(path + ".stat_type", info.getCurrentStatType() != null ?
                      info.getCurrentStatType().getDisplayName() : "");
            config.set(path + ".map_name", info.getMapName());
            config.set(path + ".display_range", info.getDisplayRange());
            config.set(path + ".show_all_data", info.isShowAllData());

            // 保存位置信息
            Location loc = info.getLocation();
            config.set(path + ".location.world", loc.getWorld().getName());
            config.set(path + ".location.x", loc.getX());
            config.set(path + ".location.y", loc.getY());
            config.set(path + ".location.z", loc.getZ());
            config.set(path + ".location.yaw", loc.getYaw());
            config.set(path + ".location.pitch", loc.getPitch());
        }

        try {
            config.save(configFile);
        } catch (IOException e) {
            plugin.getLogger().severe("无法保存全息排行榜配置: " + e.getMessage());
        }
    }

    /**
     * 清理所有全息排行榜
     */
    public void cleanup() {
        stopUpdateTask();

        for (String holoName : hologramLeaderboards.keySet()) {
            DHAPI.removeHologram(holoName);
        }

        hologramLeaderboards.clear();
    }
}
