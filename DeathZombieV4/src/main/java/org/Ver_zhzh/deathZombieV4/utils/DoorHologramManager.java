package org.Ver_zhzh.deathZombieV4.utils;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

import org.Ver_zhzh.deathZombieV4.DeathZombieV4;
import org.Ver_zhzh.deathZombieV4.game.DoorManager;
import org.Ver_zhzh.deathZombieV4.game.GameManager;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.Location;
import org.bukkit.Sound;
import org.bukkit.entity.Player;
import org.bukkit.event.Listener;

/**
 * 管理门解锁悬浮文字的类，替代原来的DoorNPCManager
 */
public class DoorHologramManager implements Listener {

    private final DeathZombieV4 plugin;
    private final GameManager gameManager;
    private final DoorManager doorManager;
    private final HologramHelper hologramHelper;

    // 存储游戏中的门悬浮文字: <游戏名, <门名, 悬浮文字ID>>
    private final Map<String, Map<String, String>> doorHolograms = new HashMap<>();

    /**
     * 构造函数
     *
     * @param plugin 插件实例
     */
    public DoorHologramManager(DeathZombieV4 plugin) {
        this.plugin = plugin;
        this.gameManager = plugin.getGameManager();
        this.doorManager = plugin.getDoorManager();
        this.hologramHelper = plugin.getHologramHelper();

        // 注册事件监听器
        plugin.getServer().getPluginManager().registerEvents(this, plugin);
    }

    /**
     * 为游戏中的门创建悬浮文字
     *
     * @param gameName 游戏名称
     */
    public void setupDoorHolograms(String gameName) {
        plugin.getLogger().info("为游戏 " + gameName + " 设置门悬浮文字");

        // 获取游戏中的门
        Map<String, Map<String, Object>> doors = gameManager.getDoors(gameName);
        if (doors == null || doors.isEmpty()) {
            plugin.getLogger().info("游戏 " + gameName + " 没有设置任何门");
            return;
        }

        // 清理之前的悬浮文字（如果有）
        cleanupDoorHolograms(gameName);

        // 为每个锁住的门创建悬浮文字
        for (Map.Entry<String, Map<String, Object>> entry : doors.entrySet()) {
            String doorName = entry.getKey();
            Map<String, Object> doorInfo = entry.getValue();

            // 检查门是否锁住
            if (doorInfo.containsKey("locked") && (boolean) doorInfo.get("locked")) {
                // 获取门的区域
                if (doorInfo.containsKey("region")) {
                    @SuppressWarnings("unchecked")
                    Map<String, Object> regionData = (Map<String, Object>) doorInfo.get("region");

                    // 获取解锁价格
                    int unlockPrice = doorInfo.containsKey("unlockPrice") ? (int) doorInfo.get("unlockPrice") : 0;

                    // 创建悬浮文字在门附近
                    createDoorHologram(gameName, doorName, regionData, unlockPrice);
                }
            }
        }
    }

    /**
     * 创建门悬浮文字
     *
     * @param gameName 游戏名称
     * @param doorName 门名称
     * @param regionData 区域数据
     * @param unlockPrice 解锁价格
     */
    private void createDoorHologram(String gameName, String doorName, Map<String, Object> regionData, int unlockPrice) {
        try {
            // 获取区域中心点
            String worldName = (String) regionData.get("world");
            double minX = (double) regionData.get("minX");
            double minY = (double) regionData.get("minY");
            double minZ = (double) regionData.get("minZ");
            double maxX = (double) regionData.get("maxX");
            double maxY = (double) regionData.get("maxY");
            double maxZ = (double) regionData.get("maxZ");

            // 计算中心点
            double centerX = (minX + maxX) / 2;
            double centerY = (minY + maxY) / 2;
            double centerZ = (minZ + maxZ) / 2;

            // 创建位置
            Location holoLocation = new Location(Bukkit.getWorld(worldName), centerX, centerY, centerZ);

            // 创建悬浮文字
            String holoId = hologramHelper.createDoorHologram(gameName, doorName, holoLocation, unlockPrice,
                    player -> handleDoorHologramClick(player, gameName, doorName, unlockPrice));

            // 存储悬浮文字引用
            if (holoId != null) {
                if (!doorHolograms.containsKey(gameName)) {
                    doorHolograms.put(gameName, new HashMap<>());
                }
                doorHolograms.get(gameName).put(doorName, holoId);

                // 不再创建单独的物品展示悬浮文字，避免重复
                plugin.getLogger().info("已为游戏 " + gameName + " 的门 " + doorName
                        + " 创建解锁悬浮文字，价格: " + unlockPrice + "，位置: " + holoLocation);
            }
        } catch (Exception e) {
            plugin.getLogger().severe("为门 " + doorName + " 创建悬浮文字时出错: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 处理门悬浮文字点击事件
     *
     * @param player 玩家
     * @param gameName 游戏名称
     * @param doorName 门名称
     * @param unlockPrice 解锁价格
     */
    private void handleDoorHologramClick(Player player, String gameName, String doorName, int unlockPrice) {
        // 检查玩家是否在游戏中
        if (!plugin.getGameSessionManager().isPlayerInGame(player.getUniqueId(), gameName)) {
            player.sendMessage(ChatColor.RED + "你不在游戏 " + gameName + " 中，无法解锁门！");
            return;
        }

        // 解锁门
        boolean success = doorManager.unlockDoor(player, gameName, doorName);
        if (success) {
            // 如果门已成功解锁，移除悬浮文字
            if (doorHolograms.containsKey(gameName) && doorHolograms.get(gameName).containsKey(doorName)) {
                String holoId = doorHolograms.get(gameName).get(doorName);
                hologramHelper.removeHologram(holoId);
                doorHolograms.get(gameName).remove(doorName);
                plugin.getLogger().info("已移除门 '" + doorName + "' 的悬浮文字");
            }

            // 播放解锁音效和特效
            player.playSound(player.getLocation(), Sound.BLOCK_IRON_DOOR_OPEN, 1.0f, 1.0f);

            // 发送成功消息
            player.sendMessage(ChatColor.GREEN + "你已成功解锁门 '" + doorName + "'，花费了 " + unlockPrice + " 金币！");

            // 广播消息给同一游戏中的其他玩家
            for (UUID playerId : plugin.getGameSessionManager().getGameParticipants(gameName)) {
                Player p = Bukkit.getPlayer(playerId);
                if (p != null && p.isOnline() && !p.equals(player)) {
                    p.sendMessage(ChatColor.GREEN + player.getName() + " 解锁了门 '" + doorName + "'！");
                }
            }
        }
    }

    /**
     * 清理指定游戏的所有门悬浮文字
     *
     * @param gameName 游戏名称
     */
    public void cleanupDoorHolograms(String gameName) {
        if (!doorHolograms.containsKey(gameName)) {
            return;
        }

        for (String holoId : doorHolograms.get(gameName).values()) {
            hologramHelper.removeHologram(holoId);
        }

        doorHolograms.get(gameName).clear();
        plugin.getLogger().info("已清理游戏 " + gameName + " 的所有门悬浮文字");
    }

    /**
     * 清理所有门悬浮文字
     */
    public void cleanupAllHolograms() {
        for (String gameName : doorHolograms.keySet()) {
            cleanupDoorHolograms(gameName);
        }
        doorHolograms.clear();
        plugin.getLogger().info("已清理所有门悬浮文字");
    }

    /**
     * 当游戏结束时清理悬浮文字
     *
     * @param gameName 游戏名称
     */
    public void onGameEnd(String gameName) {
        cleanupDoorHolograms(gameName);
    }

    /**
     * 移除指定游戏中指定门的悬浮文字
     *
     * @param gameName 游戏名称
     * @param doorName 门名称
     * @return 是否成功移除
     */
    public boolean removeDoorHologram(String gameName, String doorName) {
        if (!doorHolograms.containsKey(gameName) || !doorHolograms.get(gameName).containsKey(doorName)) {
            plugin.getLogger().warning("游戏 " + gameName + " 中的门 " + doorName + " 没有悬浮文字");
            return false;
        }

        String holoId = doorHolograms.get(gameName).get(doorName);
        boolean success = hologramHelper.removeHologram(holoId);

        if (success) {
            doorHolograms.get(gameName).remove(doorName);
            plugin.getLogger().info("已移除游戏 " + gameName + " 中门 " + doorName + " 的悬浮文字");
        }

        return success;
    }

    /**
     * 获取所有门悬浮文字的映射
     *
     * @return 门悬浮文字映射 <游戏名, <门名, 悬浮文字ID>>
     */
    public Map<String, Map<String, String>> getDoorHolograms() {
        return doorHolograms;
    }
}
