package org.Ver_zhzh.deathZombieV4.utils;

import java.lang.reflect.Constructor;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;

import org.Ver_zhzh.deathZombieV4.DeathZombieV4;
import org.Ver_zhzh.deathZombieV4.game.GameKitManager;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.entity.ArmorStand;
import org.bukkit.entity.EntityType;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.plugin.Plugin;

import net.citizensnpcs.api.CitizensAPI;
import net.citizensnpcs.api.npc.NPC;

/**
 * 与Shoot插件交互的辅助类
 */
public class ShootPluginHelper {

    private final DeathZombieV4 plugin;

    public ShootPluginHelper(DeathZombieV4 plugin) {
        this.plugin = plugin;
    }

    /**
     * 给玩家武器
     *
     * @param player 玩家
     * @param itemId 武器ID
     * @return 是否成功给予
     */
    public boolean giveGunToPlayer(Player player, String itemId) {
        try {
            Plugin shootPlugin = plugin.getShootPlugin();
            if (shootPlugin == null) {
                plugin.getLogger().warning("无法获取Shoot插件实例，无法给予武器");
                return false;
            }

            // 记录原始ID，用于日志
            String originalId = itemId;
            plugin.getLogger().info("【武器购买调试】原始武器ID: " + originalId + ", 调用堆栈: " + getCallerInfo());

            // 特殊处理id1（手枪）和id11（突击步枪）
            if (itemId.equals("id1") || itemId.equals("1")) {
                // 确保id1是手枪
                plugin.getLogger().info("【武器购买调试】特殊处理id1为手枪");
                itemId = "id1"; // 确保使用正确的手枪ID

                // 验证Shoot插件中id1是否正确映射为手枪
                try {
                    Method getItemNameMethod = shootPlugin.getClass().getMethod("getItemName", String.class, String.class);
                    String gunName = (String) getItemNameMethod.invoke(shootPlugin, "wp", "id1");
                    plugin.getLogger().info("【武器购买调试】Shoot插件中id1对应的武器名称: " + gunName);
                } catch (Exception e) {
                    plugin.getLogger().warning("【武器购买调试】获取Shoot插件中id1武器名称失败: " + e.getMessage());
                }
            } else if (itemId.equals("id11") || itemId.equals("11")) {
                // 确保id11是突击步枪
                plugin.getLogger().info("【武器购买调试】特殊处理id11为突击步枪");
                itemId = "id11"; // 确保使用正确的突击步枪ID

                // 验证Shoot插件中id11是否正确映射为突击步枪
                try {
                    Method getItemNameMethod = shootPlugin.getClass().getMethod("getItemName", String.class, String.class);
                    String gunName = (String) getItemNameMethod.invoke(shootPlugin, "wp", "id11");
                    plugin.getLogger().info("【武器购买调试】Shoot插件中id11对应的武器名称: " + gunName);
                } catch (Exception e) {
                    plugin.getLogger().warning("【武器购买调试】获取Shoot插件中id11武器名称失败: " + e.getMessage());
                }
            } else {
                // 从gameKit.yml获取正确的Shoot插件ID映射
                GameKitManager gameKitManager = plugin.getGameKitManager();
                if (gameKitManager != null) {
                    String shootId = gameKitManager.getShootId(itemId);
                    if (shootId != null && !shootId.isEmpty() && !shootId.equals(itemId)) {
                        plugin.getLogger().info("【武器购买调试】从gameKit.yml获取映射ID: " + shootId + " 替代 " + itemId);
                        itemId = shootId;
                    }
                }
            }

            // 确保ID格式正确，Shoot插件期望的格式是"id数字"
            if (!itemId.startsWith("id") && itemId.matches("\\d+")) {
                String oldId = itemId;
                itemId = "id" + itemId;
                plugin.getLogger().info("【武器购买调试】转换纯数字ID " + oldId + " 为Shoot格式: " + itemId);
            }

            // 检查是否是手枪或突击步枪，确保使用正确的ID
            if (itemId.equals("id1")) {
                plugin.getLogger().info("【武器购买调试】最终确认：给予玩家手枪(id1)");
            } else if (itemId.equals("id11")) {
                plugin.getLogger().info("【武器购买调试】最终确认：给予玩家突击步枪(id11)");
            } else {
                plugin.getLogger().info("【武器购买调试】最终确认：给予玩家武器: " + itemId);
            }

            // 在调用前检查玩家物品栏中是否已有武器
            plugin.getLogger().info("【武器购买调试】检查玩家 " + player.getName() + " 物品栏中的武器");
            ItemStack[] inventory = player.getInventory().getContents();
            for (ItemStack item : inventory) {
                if (item != null && item.hasItemMeta() && item.getItemMeta().hasDisplayName()) {
                    String displayName = ChatColor.stripColor(item.getItemMeta().getDisplayName());
                    plugin.getLogger().info("【武器购买调试】玩家物品栏中的物品: " + displayName);

                    // 检查是否已有手枪或突击步枪
                    if (itemId.equals("id1") && displayName.contains("手枪")) {
                        plugin.getLogger().info("【武器购买调试】玩家已有手枪，将补充弹药而不是给予新武器");
                        // 调用补充弹药方法
                        return replenishPlayerAmmo(player, itemId);
                    } else if (itemId.equals("id11") && displayName.contains("突击步枪")) {
                        plugin.getLogger().info("【武器购买调试】玩家已有突击步枪，将补充弹药而不是给予新武器");
                        // 调用补充弹药方法
                        return replenishPlayerAmmo(player, itemId);
                    }
                }
            }

            // 尝试使用Shoot插件的createGun方法创建枪支物品
            try {
                Method createGunMethod = shootPlugin.getClass().getMethod("createGun", String.class);
                Object result = createGunMethod.invoke(shootPlugin, itemId);

                if (result instanceof ItemStack) {
                    ItemStack gun = (ItemStack) result;
                    plugin.getLogger().info("【武器购买调试】成功使用Shoot插件的createGun方法创建枪支: " + itemId);

                    // 将枪支放入玩家手中
                    player.getInventory().setItemInMainHand(gun);
                    player.updateInventory();

                    plugin.getLogger().info("【武器购买调试】使用createGun方法成功给予玩家 " + player.getName() + " 武器: " + itemId);
                    return true;
                } else {
                    plugin.getLogger().warning("【武器购买调试】Shoot插件的createGun方法返回了非ItemStack对象，尝试使用备用方法");
                }
            } catch (NoSuchMethodException e) {
                plugin.getLogger().warning("【武器购买调试】Shoot插件没有createGun方法，尝试使用备用方法");
            } catch (Exception e) {
                plugin.getLogger().warning("【武器购买调试】调用Shoot插件的createGun方法失败: " + e.getMessage());
                plugin.getLogger().info("【武器购买调试】尝试使用备用方法giveGunToPlayer");
            }

            // 备用方法：使用giveGunToPlayer方法
            Method method = shootPlugin.getClass().getMethod("giveGunToPlayer", Player.class, String.class);

            // 调用Shoot插件的giveGunToPlayer方法
            plugin.getLogger().info("【武器购买调试】调用Shoot插件的giveGunToPlayer方法，参数: player=" + player.getName() + ", itemId=" + itemId);
            method.invoke(shootPlugin, player, itemId);

            // 验证玩家是否成功获得武器
            boolean foundWeapon = false;
            String weaponName = "";
            for (ItemStack item : player.getInventory().getContents()) {
                if (item != null && item.hasItemMeta() && item.getItemMeta().hasDisplayName()) {
                    String displayName = ChatColor.stripColor(item.getItemMeta().getDisplayName());
                    plugin.getLogger().info("【武器购买调试】验证物品栏中的物品: " + displayName);
                    if (itemId.equals("id1") && displayName.contains("手枪")) {
                        foundWeapon = true;
                        weaponName = displayName;
                        break;
                    } else if (itemId.equals("id11") && displayName.contains("突击步枪")) {
                        foundWeapon = true;
                        weaponName = displayName;
                        break;
                    }
                }
            }

            if (foundWeapon) {
                plugin.getLogger().info("【武器购买调试】成功验证玩家 " + player.getName() + " 获得武器: " + weaponName);
            } else {
                plugin.getLogger().warning("【武器购买调试】无法验证玩家 " + player.getName() + " 是否获得正确的武器");
            }

            plugin.getLogger().info("成功给予玩家 " + player.getName() + " 武器: " + itemId);
            return true;
        } catch (Exception e) {
            plugin.getLogger().warning("【武器购买调试】调用Shoot插件的giveGunToPlayer方法时出错: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 获取调用者信息，用于调试
     *
     * @return 调用堆栈信息
     */
    private String getCallerInfo() {
        StackTraceElement[] stackTrace = Thread.currentThread().getStackTrace();
        StringBuilder sb = new StringBuilder();
        // 从索引2开始，跳过getStackTrace和getCallerInfo方法
        for (int i = 2; i < Math.min(6, stackTrace.length); i++) {
            sb.append(stackTrace[i].getClassName())
                    .append(".")
                    .append(stackTrace[i].getMethodName())
                    .append("(")
                    .append(stackTrace[i].getFileName())
                    .append(":")
                    .append(stackTrace[i].getLineNumber())
                    .append(")")
                    .append(" -> ");
        }
        if (sb.length() > 4) {
            sb.setLength(sb.length() - 4); // 移除最后的 " -> "
        }
        return sb.toString();
    }

    /**
     * 给玩家非枪支武器
     *
     * @param player 玩家
     * @param itemId 物品ID
     * @return 是否成功给予
     */
    public boolean giveNonGunWeaponToPlayer(Player player, String itemId) {
        try {
            Plugin shootPlugin = plugin.getShootPlugin();
            if (shootPlugin == null) {
                plugin.getLogger().warning("无法获取Shoot插件实例，无法给予非枪支武器");
                return false;
            }

            // 记录原始ID，用于日志
            String originalId = itemId;

            // 从gameKit.yml获取正确的Shoot插件ID映射
            GameKitManager gameKitManager = plugin.getGameKitManager();
            if (gameKitManager != null) {
                String shootId = gameKitManager.getShootId(itemId);
                if (shootId != null && !shootId.isEmpty() && !shootId.equals(itemId)) {
                    itemId = shootId;
                    plugin.getLogger().info("giveNonGunWeaponToPlayer: 使用gameKit.yml中的映射ID: " + itemId + " 替代 " + originalId);
                }
            }

            plugin.getLogger().info("尝试给玩家 " + player.getName() + " 非枪支武器: " + itemId);
            Method method = shootPlugin.getClass().getMethod("giveNonGunWeaponToPlayer", Player.class, String.class);
            method.invoke(shootPlugin, player, itemId);
            plugin.getLogger().info("成功给予玩家 " + player.getName() + " 非枪支武器: " + itemId);
            return true;
        } catch (Exception e) {
            plugin.getLogger().warning("调用Shoot插件的giveNonGunWeaponToPlayer方法时出错: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 给玩家护甲
     *
     * @param player 玩家
     * @param itemId 护甲ID
     * @return 是否成功给予
     */
    public boolean giveArmorToPlayer(Player player, String itemId) {
        try {
            Plugin shootPlugin = plugin.getShootPlugin();
            if (shootPlugin == null) {
                plugin.getLogger().warning("无法获取Shoot插件实例，无法给予护甲");
                return false;
            }

            // 记录原始ID，用于日志
            String originalId = itemId;

            // 从gameKit.yml获取正确的Shoot插件ID映射
            GameKitManager gameKitManager = plugin.getGameKitManager();
            if (gameKitManager != null) {
                String shootId = gameKitManager.getShootId(itemId);
                if (shootId != null && !shootId.isEmpty() && !shootId.equals(itemId)) {
                    itemId = shootId;
                    plugin.getLogger().info("giveArmorToPlayer: 使用gameKit.yml中的映射ID: " + itemId + " 替代 " + originalId);
                }
            }

            plugin.getLogger().info("尝试给玩家 " + player.getName() + " 护甲: " + itemId);
            Method method = shootPlugin.getClass().getMethod("giveArmorToPlayer", Player.class, String.class);
            method.invoke(shootPlugin, player, itemId);
            plugin.getLogger().info("成功给予玩家 " + player.getName() + " 护甲: " + itemId);
            return true;
        } catch (Exception e) {
            plugin.getLogger().warning("调用Shoot插件的giveArmorToPlayer方法时出错: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 给玩家道具
     *
     * @param player 玩家
     * @param itemId 道具ID
     * @return 是否成功给予
     */
    public boolean giveItemToPlayer(Player player, String itemId) {
        try {
            Plugin shootPlugin = plugin.getShootPlugin();
            if (shootPlugin == null) {
                plugin.getLogger().warning("无法获取Shoot插件实例，无法给予道具");
                return false;
            }

            // 记录原始ID，用于日志
            String originalId = itemId;

            // 从gameKit.yml获取正确的Shoot插件ID映射
            GameKitManager gameKitManager = plugin.getGameKitManager();
            if (gameKitManager != null) {
                String shootId = gameKitManager.getShootId(itemId);
                if (shootId != null && !shootId.isEmpty() && !shootId.equals(itemId)) {
                    itemId = shootId;
                    plugin.getLogger().info("giveItemToPlayer: 使用gameKit.yml中的映射ID: " + itemId + " 替代 " + originalId);
                }
            }

            plugin.getLogger().info("尝试给玩家 " + player.getName() + " 道具: " + itemId);
            Method method = shootPlugin.getClass().getMethod("giveItemToPlayer", Player.class, String.class);
            method.invoke(shootPlugin, player, itemId);
            plugin.getLogger().info("成功给予玩家 " + player.getName() + " 道具: " + itemId);
            return true;
        } catch (Exception e) {
            plugin.getLogger().warning("调用Shoot插件的giveItemToPlayer方法时出错: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 激活特殊功能
     *
     * @param player 玩家
     * @param itemId 特殊功能ID
     * @return 是否成功激活
     */
    public boolean activateSpecialFunction(Player player, String itemId) {
        try {
            Plugin shootPlugin = plugin.getShootPlugin();
            if (shootPlugin == null) {
                plugin.getLogger().warning("无法获取Shoot插件实例，无法激活特殊功能");
                return false;
            }

            // 记录原始ID，用于日志
            String originalId = itemId;

            // 从gameKit.yml获取正确的Shoot插件ID映射
            GameKitManager gameKitManager = plugin.getGameKitManager();
            if (gameKitManager != null) {
                String shootId = gameKitManager.getShootId(itemId);
                if (shootId != null && !shootId.isEmpty() && !shootId.equals(itemId)) {
                    itemId = shootId;
                    plugin.getLogger().info("activateSpecialFunction: 使用gameKit.yml中的映射ID: " + itemId + " 替代 " + originalId);
                }
            }

            plugin.getLogger().info("尝试为玩家 " + player.getName() + " 激活特殊功能: " + itemId);
            Method method = shootPlugin.getClass().getMethod("activateSpecialFunction", String.class);
            method.invoke(shootPlugin, itemId);
            plugin.getLogger().info("成功为玩家 " + player.getName() + " 激活特殊功能: " + itemId);
            return true;
        } catch (Exception e) {
            plugin.getLogger().warning("调用Shoot插件的activateSpecialFunction方法时出错: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 检查物品是否存在于Shoot插件中
     *
     * @param type 物品类型 (ar|wp|it|sp)
     * @param itemId 物品ID
     * @return 如果物品存在则返回true，否则返回false
     */
    public boolean itemExists(String type, String itemId) {
        try {
            Plugin shootPlugin = plugin.getShootPlugin();
            Method method = shootPlugin.getClass().getMethod("itemExists", String.class, String.class);
            boolean result = (boolean) method.invoke(shootPlugin, type, itemId);

            // 如果反射方法返回false，记录详细日志并尝试备用检查
            if (!result) {
                plugin.getLogger().warning("Shoot插件未找到物品: " + type + "_" + itemId);
                // 尝试备用检查方法
                return checkItemExistsManually(type, itemId);
            }

            return result;
        } catch (Exception e) {
            plugin.getLogger().warning("调用Shoot插件的itemExists方法时出错: " + e.getMessage());
            e.printStackTrace();
            // 异常时尝试备用检查方法
            return checkItemExistsManually(type, itemId);
        }
    }

    /**
     * 手动检查物品是否存在（备用方法）
     */
    private boolean checkItemExistsManually(String type, String itemId) {
        try {
            Plugin shootPlugin = plugin.getShootPlugin();
            // 尝试获取Shoot插件中的buyConfig字段
            Field buyConfigField = shootPlugin.getClass().getDeclaredField("buyConfig");
            buyConfigField.setAccessible(true);
            Object buyConfig = buyConfigField.get(shootPlugin);

            if (buyConfig instanceof FileConfiguration) {
                FileConfiguration config = (FileConfiguration) buyConfig;
                boolean exists = config.contains(type + "." + itemId);
                plugin.getLogger().info("手动检查物品 " + type + "_" + itemId + " 结果: " + exists);
                return exists;
            }

            return false;
        } catch (Exception e) {
            plugin.getLogger().warning("手动检查物品时出错: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 获取物品名称
     *
     * @param type 物品类型 (ar|wp|it|sp)
     * @param itemId 物品ID
     * @return 物品名称，如果获取失败则返回null
     */
    public String getItemName(String type, String itemId) {
        try {
            Plugin shootPlugin = plugin.getShootPlugin();
            Method method = shootPlugin.getClass().getMethod("getItemName", String.class, String.class);
            return (String) method.invoke(shootPlugin, type, itemId);
        } catch (Exception e) {
            plugin.getLogger().warning("调用Shoot插件的getItemName方法时出错: " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 根据中文名称查找物品ID
     *
     * @param type 物品类型 (ar|wp|it|sp)
     * @param itemName 物品中文名称
     * @return 物品ID，如果未找到则返回null
     */
    public String getItemIdByName(String type, String itemName) {
        try {
            Plugin shootPlugin = plugin.getShootPlugin();

            // 尝试调用Shoot插件的getItemIdByName方法
            try {
                Method method = shootPlugin.getClass().getMethod("getItemIdByName", String.class, String.class);
                return (String) method.invoke(shootPlugin, type, itemName);
            } catch (NoSuchMethodException e) {
                // 如果Shoot插件没有这个方法，使用手动查找
                plugin.getLogger().info("Shoot插件没有getItemIdByName方法，使用手动查找");
                return findItemIdByNameManually(type, itemName);
            }
        } catch (Exception e) {
            plugin.getLogger().warning("调用Shoot插件的getItemIdByName方法时出错: " + e.getMessage());
            // 异常时使用手动查找
            return findItemIdByNameManually(type, itemName);
        }
    }

    /**
     * 手动查找物品ID（通过遍历Buy.yml配置）
     *
     * @param type 物品类型 (ar|wp|it|sp)
     * @param itemName 物品中文名称
     * @return 物品ID，如果未找到则返回null
     */
    private String findItemIdByNameManually(String type, String itemName) {
        try {
            Plugin shootPlugin = plugin.getShootPlugin();

            // 获取Buy.yml配置
            Field buyConfigField = shootPlugin.getClass().getDeclaredField("buyConfig");
            buyConfigField.setAccessible(true);
            FileConfiguration buyConfig = (FileConfiguration) buyConfigField.get(shootPlugin);

            if (buyConfig == null) {
                plugin.getLogger().warning("无法获取Shoot插件的Buy.yml配置");
                return null;
            }

            // 遍历指定类型的所有物品
            ConfigurationSection typeSection = buyConfig.getConfigurationSection(type);
            if (typeSection == null) {
                plugin.getLogger().warning("Buy.yml中没有找到类型: " + type);
                return null;
            }

            for (String itemId : typeSection.getKeys(false)) {
                String configItemName = buyConfig.getString(type + "." + itemId + ".name", "");

                // 移除颜色代码进行比较
                String cleanConfigName = ChatColor.stripColor(configItemName);
                String cleanInputName = ChatColor.stripColor(itemName);

                if (cleanConfigName.equals(cleanInputName)) {
                    plugin.getLogger().info("找到匹配的物品: " + itemName + " -> " + itemId);
                    return itemId;
                }
            }

            plugin.getLogger().warning("在类型 " + type + " 中未找到物品: " + itemName);
            return null;

        } catch (Exception e) {
            plugin.getLogger().warning("手动查找物品ID时出错: " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 获取指定类型的所有物品名称列表
     *
     * @param type 物品类型 (ar|wp|it|sp)
     * @return 物品名称列表
     */
    public List<String> getItemNamesByType(String type) {
        List<String> itemNames = new ArrayList<>();

        try {
            Plugin shootPlugin = plugin.getShootPlugin();

            // 获取Buy.yml配置
            Field buyConfigField = shootPlugin.getClass().getDeclaredField("buyConfig");
            buyConfigField.setAccessible(true);
            FileConfiguration buyConfig = (FileConfiguration) buyConfigField.get(shootPlugin);

            if (buyConfig == null) {
                plugin.getLogger().warning("无法获取Shoot插件的Buy.yml配置");
                return itemNames;
            }

            // 遍历指定类型的所有物品
            ConfigurationSection typeSection = buyConfig.getConfigurationSection(type);
            if (typeSection == null) {
                plugin.getLogger().warning("Buy.yml中没有找到类型: " + type);
                return itemNames;
            }

            for (String itemId : typeSection.getKeys(false)) {
                String itemName = buyConfig.getString(type + "." + itemId + ".name", "");
                if (!itemName.isEmpty()) {
                    // 移除颜色代码
                    String cleanName = ChatColor.stripColor(itemName);
                    itemNames.add(cleanName);
                }
            }

        } catch (Exception e) {
            plugin.getLogger().warning("获取物品名称列表时出错: " + e.getMessage());
            e.printStackTrace();
        }

        return itemNames;
    }

    /**
     * 清除玩家的购买记录，允许重复购买
     *
     * @param player 玩家
     * @param npcUUID NPC的UUID
     * @return 是否成功清除
     */
    public boolean clearPurchaseRecord(Player player, String npcUUID) {
        try {
            Plugin shootPlugin = plugin.getShootPlugin();

            // 获取playerDataConfig字段
            Field playerDataConfigField = shootPlugin.getClass().getDeclaredField("playerDataConfig");
            playerDataConfigField.setAccessible(true);
            Object playerDataConfig = playerDataConfigField.get(shootPlugin);

            if (playerDataConfig instanceof FileConfiguration) {
                FileConfiguration config = (FileConfiguration) playerDataConfig;
                String playerUUID = player.getUniqueId().toString();
                String purchaseKey = "purchased." + npcUUID + "." + playerUUID;

                // 清除购买记录
                config.set(purchaseKey, null);

                // 保存配置
                Method saveAllConfigsMethod = shootPlugin.getClass().getMethod("saveAllConfigs");
                saveAllConfigsMethod.invoke(shootPlugin);

                return true;
            }

            return false;
        } catch (Exception e) {
            plugin.getLogger().warning("清除玩家购买记录时出错: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 获取物品价格
     *
     * @param type 物品类型 (ar|wp|it|sp)
     * @param itemId 物品ID
     * @return 物品价格，如果获取失败则返回-1
     */
    public int getItemPrice(String type, String itemId) {
        try {
            Plugin shootPlugin = plugin.getShootPlugin();
            Method method = shootPlugin.getClass().getMethod("getItemPrice", String.class, String.class);
            return (int) method.invoke(shootPlugin, type, itemId);
        } catch (Exception e) {
            plugin.getLogger().warning("调用Shoot插件的getItemPrice方法时出错: " + e.getMessage());
            e.printStackTrace();
            return -1;
        }
    }

    /**
     * 创建购买点NPC
     *
     * @param location NPC的生成位置
     * @param npcName NPC的名称
     * @param type 物品类型 (ar|wp|it|sp)
     * @param itemId 物品ID
     * @return 创建的NPC对象，如果创建失败则返回null
     */
    public NPC createBuyPointNPC(Location location, String npcName, String type, String itemId) {
        try {
            Plugin shootPlugin = plugin.getShootPlugin();
            Method method = shootPlugin.getClass().getMethod("createBuyPointNPC", Location.class, String.class, String.class, String.class);
            return (NPC) method.invoke(shootPlugin, location, npcName, type, itemId);
        } catch (Exception e) {
            plugin.getLogger().warning("调用Shoot插件的createBuyPointNPC方法时出错: " + e.getMessage());
            e.printStackTrace();

            // 如果反射调用失败，则尝试手动创建NPC
            return createNPCManually(location, npcName, type, itemId);
        }
    }

    /**
     * 清理所有购买点NPC
     *
     * @return 是否成功清理
     */
    public boolean cleanupAllBuyPointNPCs() {
        try {
            Plugin shootPlugin = plugin.getShootPlugin();
            Method method = shootPlugin.getClass().getMethod("cleanupAllBuyPointNPCs");
            method.invoke(shootPlugin);
            return true;
        } catch (Exception e) {
            plugin.getLogger().warning("调用Shoot插件的cleanupAllBuyPointNPCs方法时出错: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 手动创建NPC（后备方案）
     */
    private NPC createNPCManually(Location location, String npcName, String type, String itemId) {
        try {
            // 创建NPC - 使用村民类型而非玩家类型，避免僵尸攻击商人
            NPC npc = CitizensAPI.getNPCRegistry().createNPC(EntityType.VILLAGER, npcName);
            npc.spawn(location);
            npc.setProtected(true); // 设置为受保护状态

            // 获取价格
            int price = getItemPrice(type, itemId);
            if (price <= 0) {
                price = 100; // 默认价格
            }

            // 添加价格悬浮文字
            ArmorStand priceStand = location.getWorld().spawn(location.clone().add(0, 1.5, 0), ArmorStand.class);
            priceStand.setVisible(false);
            priceStand.setCustomName(ChatColor.GOLD + "价格: " + price);
            priceStand.setCustomNameVisible(true);
            priceStand.setGravity(false);

            return npc;
        } catch (Exception e) {
            plugin.getLogger().severe("手动创建NPC失败: " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 获取玩家金钱
     *
     * @param player 玩家
     * @return 玩家当前金钱数量，如果获取失败则返回-1
     */
    public double getPlayerMoney(Player player) {
        try {
            Plugin shootPlugin = plugin.getShootPlugin();
            Method method = shootPlugin.getClass().getMethod("getPlayerMoney", Player.class);
            return (double) method.invoke(shootPlugin, player);
        } catch (Exception e) {
            plugin.getLogger().warning("调用Shoot插件的getPlayerMoney方法时出错: " + e.getMessage());
            e.printStackTrace();
            return -1;
        }
    }

    /**
     * 设置玩家金钱
     *
     * @param player 玩家
     * @param amount 金钱数量
     * @return 操作是否成功
     */
    public boolean setPlayerMoney(Player player, double amount) {
        try {
            Plugin shootPlugin = plugin.getShootPlugin();
            Method method = shootPlugin.getClass().getMethod("setPlayerMoney", Player.class, double.class);
            method.invoke(shootPlugin, player, amount);
            return true;
        } catch (Exception e) {
            plugin.getLogger().warning("调用Shoot插件的setPlayerMoney方法时出错: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 增加玩家金钱
     *
     * @param player 玩家
     * @param amount 增加的金钱数量
     * @return 操作是否成功
     */
    public boolean addPlayerMoney(Player player, double amount) {
        try {
            Plugin shootPlugin = plugin.getShootPlugin();
            if (shootPlugin == null) {
                plugin.getLogger().warning("无法获取Shoot插件实例，无法增加金钱");
                return false;
            }

            plugin.getLogger().info("尝试给玩家 " + player.getName() + " 增加 " + amount + " 金钱");

            // 先获取玩家当前金钱
            double beforeMoney = getPlayerMoney(player);
            plugin.getLogger().info("玩家 " + player.getName() + " 当前金钱: " + beforeMoney);

            // 调用Shoot插件的addPlayerMoney方法
            Method method = shootPlugin.getClass().getMethod("addPlayerMoney", Player.class, double.class);
            method.invoke(shootPlugin, player, amount);

            // 再次获取玩家金钱确认是否增加
            double afterMoney = getPlayerMoney(player);
            plugin.getLogger().info("玩家 " + player.getName() + " 新金钱: " + afterMoney + ", 增加了: " + (afterMoney - beforeMoney));

            return true;
        } catch (Exception e) {
            plugin.getLogger().warning("调用Shoot插件的addPlayerMoney方法时出错: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 减少玩家金钱
     *
     * @param player 玩家
     * @param amount 减少的金钱数量
     * @return 是否成功减少（余额是否足够）
     */
    public boolean subtractPlayerMoney(Player player, double amount) {
        try {
            Plugin shootPlugin = plugin.getShootPlugin();
            Method method = shootPlugin.getClass().getMethod("subtractPlayerMoney", Player.class, double.class);
            return (boolean) method.invoke(shootPlugin, player, amount);
        } catch (Exception e) {
            plugin.getLogger().warning("调用Shoot插件的subtractPlayerMoney方法时出错: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 获取玩家当前手持武器的ID
     *
     * @param player 玩家
     * @return 武器ID，如果未找到则返回 null
     */
    public String getPlayerCurrentGunId(Player player) {
        try {
            if (player == null) {
                plugin.getLogger().warning("getPlayerCurrentGunId: 玩家为null");
                return null;
            }

            Plugin shootPlugin = plugin.getShootPlugin();
            if (shootPlugin == null) {
                plugin.getLogger().warning("getPlayerCurrentGunId: Shoot插件未找到");
                return null;
            }

            // 尝试调用Shoot插件的getPlayerGunId方法
            try {
                Method method = shootPlugin.getClass().getMethod("getPlayerGunId", Player.class);
                Object result = method.invoke(shootPlugin, player);
                if (result instanceof String) {
                    String gunId = (String) result;
                    plugin.getLogger().info("getPlayerCurrentGunId: 通过Shoot插件方法获取到枪支ID: " + gunId);
                    return gunId;
                }
            } catch (NoSuchMethodException e) {
                // 如果没有getPlayerGunId方法，尝试手动解析
                plugin.getLogger().info("getPlayerCurrentGunId: Shoot插件没有getPlayerGunId方法，使用手动解析");
            } catch (Exception e) {
                plugin.getLogger().warning("getPlayerCurrentGunId: 调用Shoot插件方法失败: " + e.getMessage());
            }

            // 手动解析玩家手持物品
            ItemStack item = player.getInventory().getItemInMainHand();
            if (item == null || item.getType() == Material.AIR) {
                plugin.getLogger().info("getPlayerCurrentGunId: 玩家手中没有物品");
                return null;
            }

            ItemMeta meta = item.getItemMeta();
            if (meta == null || !meta.hasDisplayName()) {
                plugin.getLogger().info("getPlayerCurrentGunId: 物品没有显示名称");
                return null;
            }

            String displayName = ChatColor.stripColor(meta.getDisplayName());
            plugin.getLogger().info("getPlayerCurrentGunId: 玩家手持物品名称: " + displayName);

            // 检查是否是弹药耗尽的武器（钻石）
            if (item.getType() == Material.DIAMOND && displayName.contains("弹药耗尽的武器")) {
                plugin.getLogger().info("getPlayerCurrentGunId: 检测到弹药耗尽的武器");
                // 从lore中获取原始枪支ID
                if (meta.hasLore()) {
                    List<String> lore = meta.getLore();
                    if (lore != null) {
                        for (String line : lore) {
                            if (line.contains("gun_id:")) {
                                // 提取gun_id后面的内容
                                String gunId = ChatColor.stripColor(line).replace("gun_id:", "").trim();
                                plugin.getLogger().info("getPlayerCurrentGunId: 从lore获取到枪支ID: " + gunId);
                                return gunId;
                            }
                        }
                    }
                }
            }

            // 通过反射获取Shoot插件的配置，匹配枪支名称
            try {
                Field configField = shootPlugin.getClass().getDeclaredField("config");
                configField.setAccessible(true);
                FileConfiguration config = (FileConfiguration) configField.get(shootPlugin);

                if (config != null && config.contains("guns")) {
                    plugin.getLogger().info("getPlayerCurrentGunId: 开始匹配枪支名称");
                    // 检查是否是正常的枪支
                    for (String gunId : config.getConfigurationSection("guns").getKeys(false)) {
                        String gunName = config.getString("guns." + gunId + ".name", "未知武器");
                        if (displayName.equals(gunName)) {
                            plugin.getLogger().info("getPlayerCurrentGunId: 匹配到枪支ID: " + gunId + " (名称: " + gunName + ")");
                            return gunId;
                        }
                    }
                    plugin.getLogger().info("getPlayerCurrentGunId: 未找到匹配的枪支名称");
                } else {
                    plugin.getLogger().warning("getPlayerCurrentGunId: Shoot插件配置为空或没有guns节");
                }
            } catch (Exception e) {
                plugin.getLogger().warning("getPlayerCurrentGunId: 获取Shoot插件配置失败: " + e.getMessage());
                e.printStackTrace();
            }

            plugin.getLogger().info("getPlayerCurrentGunId: 未找到枪支ID，返回null");
            return null;
        } catch (Exception e) {
            plugin.getLogger().warning("getPlayerCurrentGunId: 获取玩家当前枪支ID时出错: " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 创建枪支物品
     *
     * @param gunId 枪支ID
     * @return 创建的枪支物品
     */
    public ItemStack createGun(int gunId) {
        try {
            // 尝试通过反射调用Shoot插件的方法
            Plugin shootPlugin = plugin.getShootPlugin();
            if (shootPlugin != null) {
                // 将数字ID转换为字符串ID格式
                String stringGunId = "id" + gunId;
                String originalGunId = stringGunId; // 保存原始ID，用于日志记录
                plugin.getLogger().info("【武器购买调试】createGun: 原始武器ID: " + originalGunId);

                // 特殊处理id1（手枪）和id11（突击步枪）
                if (gunId == 1) {
                    // 确保id1是手枪
                    plugin.getLogger().info("【武器购买调试】createGun: 特殊处理id1为手枪");
                    stringGunId = "id1"; // 确保使用正确的手枪ID
                } else if (gunId == 11) {
                    // 确保id11是突击步枪
                    plugin.getLogger().info("【武器购买调试】createGun: 特殊处理id11为突击步枪");
                    stringGunId = "id11"; // 确保使用正确的突击步枪ID
                } else {
                    // 从gameKit.yml获取正确的Shoot插件ID映射
                    GameKitManager gameKitManager = plugin.getGameKitManager();
                    if (gameKitManager != null) {
                        String shootId = gameKitManager.getShootId(stringGunId);
                        if (shootId != null && !shootId.isEmpty() && !shootId.equals(stringGunId)) {
                            stringGunId = shootId;
                            plugin.getLogger().info("【武器购买调试】createGun: 从gameKit.yml获取映射ID: " + shootId + " 替代 " + originalGunId);
                        }
                    }
                }

                // 检查是否是ID1-ID24范围内的枪支，这些应该完全由Shoot插件处理
                boolean isShootGun = gunId >= 1 && gunId <= 24;
                if (isShootGun) {
                    plugin.getLogger().info("【武器购买调试】createGun: 检测到ID1-ID24范围内的枪支，使用Shoot插件的逻辑处理: " + stringGunId);
                }

                // 使用Shoot插件的createGun方法直接创建枪支物品
                try {
                    // 尝试使用Shoot插件的createGun方法
                    Method createGunMethod = shootPlugin.getClass().getMethod("createGun", String.class);
                    Object result = createGunMethod.invoke(shootPlugin, stringGunId);

                    if (result instanceof ItemStack) {
                        ItemStack gun = (ItemStack) result;
                        plugin.getLogger().info("【武器购买调试】成功使用Shoot插件的createGun方法创建枪支: " + stringGunId);

                        // 特殊处理手枪(id1)，确保使用正确的材质和耐久度
                        if (stringGunId.equals("id1")) {
                            // 检查材质是否正确
                            if (gun.getType() != Material.WOODEN_SHOVEL) {
                                plugin.getLogger().info("【武器购买调试】修正手枪材质为WOODEN_SHOVEL");
                                // 创建新的物品而不是直接修改材质（避免使用已弃用的setType方法）
                                ItemStack newGun = new ItemStack(Material.WOODEN_SHOVEL, 1);
                                newGun.setItemMeta(gun.getItemMeta());
                                gun = newGun;
                            }

                            // 设置耐久度为最大值，以便在Shoot插件中正确显示换弹动画
                            ItemMeta meta = gun.getItemMeta();
                            if (meta != null) {
                                org.bukkit.inventory.meta.Damageable damageable = (org.bukkit.inventory.meta.Damageable) meta;
                                int maxDurability = Material.WOODEN_SHOVEL.getMaxDurability();
                                damageable.setDamage(0); // 设置为全新状态
                                gun.setItemMeta(meta);
                                plugin.getLogger().info("【武器购买调试】为手枪(id1)设置耐久度: " + maxDurability);
                            }
                        }

                        return gun;
                    } else {
                        plugin.getLogger().warning("【武器购买调试】Shoot插件的createGun方法返回了非ItemStack对象");
                    }
                } catch (NoSuchMethodException e) {
                    plugin.getLogger().warning("【武器购买调试】Shoot插件没有createGun方法，尝试使用备用方法");
                } catch (Exception e) {
                    plugin.getLogger().warning("【武器购买调试】调用Shoot插件的createGun方法失败: " + e.getMessage());
                    e.printStackTrace();
                }

                // 备用方法：使用临时玩家实例来创建枪支，避免影响在线玩家
                try {
                    // 创建一个临时的离线玩家实例
                    plugin.getLogger().info("【武器购买调试】createGun: 使用临时玩家实例创建枪支: " + stringGunId);

                    // 使用Shoot插件的createGun方法直接创建枪支物品
                    try {
                        Method createGunMethod = shootPlugin.getClass().getMethod("createGun", String.class);
                        Object result = createGunMethod.invoke(shootPlugin, stringGunId);

                        if (result instanceof ItemStack) {
                            ItemStack gun = (ItemStack) result;
                            plugin.getLogger().info("【武器购买调试】成功使用Shoot插件的createGun方法创建枪支: " + stringGunId);
                            return gun.clone(); // 返回克隆的物品，避免引用问题
                        }
                    } catch (Exception e) {
                        plugin.getLogger().warning("【武器购买调试】使用createGun方法创建枪支失败: " + e.getMessage());
                    }

                    // 如果上面的方法失败，尝试使用备用方法
                    // 查找一个不在游戏中的玩家作为临时玩家
                    Player tempPlayer = null;
                    for (Player p : Bukkit.getOnlinePlayers()) {
                        if (plugin.getGameSessionManager().getPlayerGame(p) == null) {
                            tempPlayer = p;
                            break;
                        }
                    }

                    // 如果找不到不在游戏中的玩家，使用第一个在线玩家
                    if (tempPlayer == null && !Bukkit.getOnlinePlayers().isEmpty()) {
                        tempPlayer = Bukkit.getOnlinePlayers().iterator().next();
                    }

                    if (tempPlayer != null) {
                        // 保存玩家当前物品栏内容
                        ItemStack[] savedInventory = tempPlayer.getInventory().getContents().clone();

                        // 保存玩家当前经验值和等级
                        float savedExp = tempPlayer.getExp();
                        int savedLevel = tempPlayer.getLevel();

                        try {
                            // 清空玩家物品栏的第一个槽位
                            tempPlayer.getInventory().setItem(0, null);

                            // 直接调用Shoot插件的giveGunToPlayer方法
                            plugin.getLogger().info("【武器购买调试】尝试使用Shoot插件的giveGunToPlayer方法创建枪支: " + stringGunId);

                            // 特殊处理id1和id11，确保使用正确的ID
                            if (stringGunId.equals("id1")) {
                                plugin.getLogger().info("【武器购买调试】createGun: 使用特殊处理为id1(手枪)");
                                Method method = shootPlugin.getClass().getMethod("giveGunToPlayer", Player.class, String.class);
                                method.invoke(shootPlugin, tempPlayer, "id1");
                            } else if (stringGunId.equals("id11")) {
                                plugin.getLogger().info("【武器购买调试】createGun: 使用特殊处理为id11(突击步枪)");
                                Method method = shootPlugin.getClass().getMethod("giveGunToPlayer", Player.class, String.class);
                                method.invoke(shootPlugin, tempPlayer, "id11");
                            } else {
                                Method method = shootPlugin.getClass().getMethod("giveGunToPlayer", Player.class, String.class);
                                method.invoke(shootPlugin, tempPlayer, stringGunId);
                            }

                            // 获取枪支物品
                            ItemStack gun = tempPlayer.getInventory().getItem(0);

                            if (gun != null) {
                                plugin.getLogger().info("【武器购买调试】成功创建枪支，ID: " + stringGunId);
                                return gun.clone(); // 返回克隆的物品，避免引用问题
                            } else {
                                plugin.getLogger().warning("【武器购买调试】无法通过giveGunToPlayer创建枪支，物品为null");
                            }
                        } catch (Exception e) {
                            plugin.getLogger().warning("【武器购买调试】通过giveGunToPlayer方法创建枪支失败: " + e.getMessage());
                            e.printStackTrace();
                        } finally {
                            // 恢复玩家物品栏
                            tempPlayer.getInventory().setContents(savedInventory);

                            // 恢复玩家经验值和等级
                            tempPlayer.setExp(savedExp);
                            tempPlayer.setLevel(savedLevel);

                            // 更新玩家物品栏
                            tempPlayer.updateInventory();
                        }
                    } else {
                        plugin.getLogger().warning("【武器购买调试】无法找到在线玩家来创建枪支");
                    }
                } catch (Exception e) {
                    plugin.getLogger().warning("【武器购买调试】创建临时玩家实例失败: " + e.getMessage());
                    e.printStackTrace();
                }
            } else {
                plugin.getLogger().warning("无法获取Shoot插件实例");
            }
        } catch (Exception e) {
            plugin.getLogger().warning("通过反射创建枪支失败: " + e.getMessage());
            e.printStackTrace();
        }

        // 备用方法：手动创建一个简单的枪支物品
        plugin.getLogger().warning("使用备用方法创建枪支: id" + gunId);
        return createFallbackGun(gunId);
    }

    /**
     * 创建备用枪支物品（当无法通过Shoot插件创建时使用）
     *
     * @param gunId 枪支ID
     * @return 创建的备用枪支物品
     */
    private ItemStack createFallbackGun(int gunId) {
        Material material;
        String name;
        int ammo = 10; // 默认弹药数量

        // 根据ID选择不同的材质和名称
        switch (gunId) {
            case 1: // 手枪 - 使用与Shoot插件相同的材质
                material = Material.WOODEN_SHOVEL;
                name = "手枪";
                plugin.getLogger().info("【武器购买调试】createFallbackGun: 为手枪(id1)设置正确的材质: WOODEN_SHOVEL");
                break;
            case 2: // 霰弹枪
                material = Material.IRON_PICKAXE;
                name = "霰弹枪";
                break;
            case 3: // 步枪
                material = Material.IRON_AXE;
                name = "步枪";
                break;
            case 4: // 狙击枪
                material = Material.IRON_SHOVEL;
                name = "狙击枪";
                break;
            case 11: // 突击步枪
                material = Material.WOODEN_HOE;
                name = "突击步枪";
                plugin.getLogger().info("【武器购买调试】createFallbackGun: 为突击步枪(id11)设置正确的材质: WOODEN_HOE");
                break;
            default:
                material = Material.WOODEN_HOE;
                name = "未知枪支";
        }

        // 获取Shoot插件的配置信息
        String gunName = name;
        int clipSize = 5; // 默认弹夹容量
        int maxAmmo = ammo; // 默认总弹药量

        try {
            Plugin shootPlugin = plugin.getShootPlugin();
            if (shootPlugin != null) {
                // 尝试从Shoot插件的配置中获取枪支信息
                try {
                    // 获取配置文件
                    Field configField = shootPlugin.getClass().getDeclaredField("config");
                    configField.setAccessible(true);
                    FileConfiguration config = (FileConfiguration) configField.get(shootPlugin);

                    if (config != null) {
                        String gunIdStr = "id" + gunId;
                        gunName = config.getString("guns." + gunIdStr + ".name", name);
                        clipSize = config.getInt("guns." + gunIdStr + ".clip_size", 5);
                        maxAmmo = config.getInt("guns." + gunIdStr + ".ammo", ammo);
                        plugin.getLogger().info("【武器购买调试】createFallbackGun: 从Shoot配置获取枪支信息: " + gunName + ", 弹夹容量: " + clipSize + ", 总弹药: " + maxAmmo);
                    }
                } catch (Exception e) {
                    plugin.getLogger().warning("【武器购买调试】createFallbackGun: 获取Shoot配置失败: " + e.getMessage());
                }
            }
        } catch (Exception e) {
            plugin.getLogger().warning("【武器购买调试】createFallbackGun: 获取Shoot插件实例失败: " + e.getMessage());
        }

        // 创建物品
        ItemStack gun = new ItemStack(material);
        ItemMeta meta = gun.getItemMeta();
        meta.setDisplayName(ChatColor.GOLD + gunName);

        // 添加描述 - 使用与Shoot插件相同的格式
        List<String> lore = new ArrayList<>();
        lore.add(ChatColor.GRAY + "武器类型: " + ChatColor.GREEN + "远程武器");
        lore.add(ChatColor.GRAY + "当前弹药: " + ChatColor.YELLOW + clipSize);
        lore.add(ChatColor.GRAY + "弹夹容量: " + ChatColor.YELLOW + clipSize);
        lore.add(ChatColor.GRAY + "总弹药: " + ChatColor.YELLOW + maxAmmo);

        // 添加特定枪支的描述
        switch (gunId) {
            case 1: // 手枪
                lore.add(ChatColor.GRAY + "伤害: " + ChatColor.RED + "15");
                lore.add(ChatColor.GRAY + "射速: " + ChatColor.YELLOW + "中等");
                lore.add(ChatColor.GRAY + "换弹时间: " + ChatColor.YELLOW + "2秒");
                break;
            case 11: // 突击步枪
                lore.add(ChatColor.GRAY + "伤害: " + ChatColor.RED + "45");
                lore.add(ChatColor.GRAY + "射速: " + ChatColor.YELLOW + "高");
                lore.add(ChatColor.GRAY + "换弹时间: " + ChatColor.YELLOW + "2秒");
                break;
            default:
                lore.add(ChatColor.GRAY + "伤害: " + ChatColor.RED + "中等");
                lore.add(ChatColor.GRAY + "射速: " + ChatColor.YELLOW + "中等");
        }

        // 添加隐藏的gun_id标记，使Shoot插件能识别此枪支
        lore.add(ChatColor.BLACK + "gun_id:id" + gunId);

        lore.add(ChatColor.YELLOW + "右键点击射击，左键双击换弹");
        meta.setLore(lore);

        // 设置不可破坏
        meta.setUnbreakable(true);

        // 为手枪(id1)设置耐久度，以支持换弹动画
        if (gunId == 1) {
            // 设置耐久度为最大值，以便在Shoot插件中正确显示换弹动画
            try {
                // 获取物品的最大耐久度
                int maxDurability = material.getMaxDurability();
                if (maxDurability > 0 && meta instanceof org.bukkit.inventory.meta.Damageable) {
                    // 设置耐久度为最大值
                    org.bukkit.inventory.meta.Damageable damageable = (org.bukkit.inventory.meta.Damageable) meta;
                    damageable.setDamage(0); // 设置为全新状态
                    plugin.getLogger().info("【武器购买调试】createFallbackGun: 为手枪(id1)设置耐久度: " + maxDurability);
                }
            } catch (Exception e) {
                plugin.getLogger().warning("【武器购买调试】createFallbackGun: 设置手枪耐久度失败: " + e.getMessage());
            }
        }

        gun.setItemMeta(meta);

        // 设置物品数量为弹夹容量，而不是总弹药量
        gun.setAmount(clipSize);

        return gun;
    }

    /**
     * 创建物品
     *
     * @param type 物品类型 (it)
     * @param itemId 物品ID
     * @return 创建的物品
     */
    public ItemStack createItem(String type, String itemId) {
        try {
            // 尝试通过反射调用Shoot插件的方法
            Plugin shootPlugin = plugin.getShootPlugin();
            if (shootPlugin != null) {
                // 尝试使用giveItemToPlayer方法
                try {
                    // 查找一个不在游戏中的玩家作为临时玩家
                    Player tempPlayer = null;
                    for (Player p : Bukkit.getOnlinePlayers()) {
                        if (plugin.getGameSessionManager().getPlayerGame(p) == null) {
                            tempPlayer = p;
                            break;
                        }
                    }

                    // 如果找不到不在游戏中的玩家，使用第一个在线玩家
                    if (tempPlayer == null && !Bukkit.getOnlinePlayers().isEmpty()) {
                        tempPlayer = Bukkit.getOnlinePlayers().iterator().next();
                    }

                    if (tempPlayer != null) {
                        // 保存玩家当前物品栏内容
                        ItemStack[] savedInventory = tempPlayer.getInventory().getContents().clone();

                        // 清空玩家物品栏的第一个槽位
                        tempPlayer.getInventory().setItem(0, null);

                        // 调用giveItemToPlayer方法
                        Method method = shootPlugin.getClass().getMethod("giveItemToPlayer", Player.class, String.class);
                        method.invoke(shootPlugin, tempPlayer, itemId);

                        // 获取物品
                        ItemStack item = tempPlayer.getInventory().getItem(0);

                        // 恢复玩家物品栏
                        tempPlayer.getInventory().setContents(savedInventory);

                        // 更新玩家物品栏
                        tempPlayer.updateInventory();

                        if (item != null) {
                            return item.clone(); // 返回克隆的物品，避免引用问题
                        }
                    }
                } catch (Exception e) {
                    plugin.getLogger().warning("通过giveItemToPlayer方法创建物品失败: " + e.getMessage());
                }
            }
        } catch (Exception e) {
            plugin.getLogger().warning("通过反射创建物品失败: " + e.getMessage());
        }

        // 备用方法：手动创建一个简单的物品
        return createFallbackItem(type, itemId);
    }

    /**
     * 创建备用物品（当无法通过Shoot插件创建时使用）
     *
     * @param type 物品类型
     * @param itemId 物品ID
     * @return 创建的备用物品
     */
    private ItemStack createFallbackItem(String type, String itemId) {
        Material material;
        String name;

        // 尝试从itemId中提取数字ID
        int id = 1;
        try {
            if (itemId.startsWith("id")) {
                id = Integer.parseInt(itemId.substring(2));
            }
        } catch (NumberFormatException e) {
            // 忽略解析错误
        }

        // 根据类型和ID选择不同的材质和名称
        if ("it".equals(type)) {
            switch (id) {
                case 1: // 药水
                    material = Material.POTION;
                    name = "治疗药水";
                    break;
                case 2: // 喷溅药水
                    material = Material.SPLASH_POTION;
                    name = "喷溅治疗药水";
                    break;
                case 3: // 金苹果
                    material = Material.GOLDEN_APPLE;
                    name = "金苹果";
                    break;
                case 4: // 牛排
                    material = Material.COOKED_BEEF;
                    name = "牛排";
                    break;
                case 5: // 面包
                    material = Material.BREAD;
                    name = "面包";
                    break;
                default:
                    material = Material.APPLE;
                    name = "未知物品";
            }
        } else {
            material = Material.APPLE;
            name = "未知物品";
        }

        // 创建物品
        ItemStack item = new ItemStack(material);
        ItemMeta meta = item.getItemMeta();
        meta.setDisplayName(ChatColor.GREEN + name);

        // 添加描述
        List<String> lore = new ArrayList<>();
        lore.add(ChatColor.GRAY + "物品ID: " + itemId);
        lore.add(ChatColor.GREEN + "右键点击使用");
        meta.setLore(lore);

        item.setItemMeta(meta);

        return item;
    }

    /**
     * 创建护甲
     *
     * @param armorId 护甲ID
     * @return 创建的护甲
     */
    public ItemStack createArmor(int armorId) {
        try {
            // 尝试通过反射调用Shoot插件的方法
            Plugin shootPlugin = plugin.getShootPlugin();
            if (shootPlugin != null) {
                // 将数字ID转换为字符串ID格式
                String stringArmorId = "id" + armorId;
                String originalArmorId = stringArmorId; // 保存原始ID，用于日志记录

                // 从gameKit.yml获取正确的Shoot插件ID映射
                GameKitManager gameKitManager = plugin.getGameKitManager();
                if (gameKitManager != null) {
                    String shootId = gameKitManager.getShootId(stringArmorId);
                    if (shootId != null && !shootId.isEmpty() && !shootId.equals(stringArmorId)) {
                        stringArmorId = shootId;
                        plugin.getLogger().info("使用gameKit.yml中的映射ID: " + stringArmorId + " 替代 " + originalArmorId);
                    }
                }

                // 尝试使用giveArmorToPlayer方法
                try {
                    // 查找一个不在游戏中的玩家作为临时玩家
                    Player tempPlayer = null;
                    for (Player p : Bukkit.getOnlinePlayers()) {
                        if (plugin.getGameSessionManager().getPlayerGame(p) == null) {
                            tempPlayer = p;
                            break;
                        }
                    }

                    // 如果找不到不在游戏中的玩家，使用第一个在线玩家
                    if (tempPlayer == null && !Bukkit.getOnlinePlayers().isEmpty()) {
                        tempPlayer = Bukkit.getOnlinePlayers().iterator().next();
                    }

                    if (tempPlayer != null) {
                        // 保存玩家当前物品栏内容
                        ItemStack[] savedInventory = tempPlayer.getInventory().getContents().clone();

                        // 清空玩家物品栏的第一个槽位
                        tempPlayer.getInventory().setItem(0, null);

                        // 调用giveArmorToPlayer方法
                        Method method = shootPlugin.getClass().getMethod("giveArmorToPlayer", Player.class, String.class);
                        method.invoke(shootPlugin, tempPlayer, stringArmorId);

                        // 获取护甲物品
                        ItemStack armor = tempPlayer.getInventory().getItem(0);

                        // 恢复玩家物品栏
                        tempPlayer.getInventory().setContents(savedInventory);

                        // 更新玩家物品栏
                        tempPlayer.updateInventory();

                        if (armor != null) {
                            return armor.clone(); // 返回克隆的物品，避免引用问题
                        }
                    }
                } catch (Exception e) {
                    plugin.getLogger().warning("通过giveArmorToPlayer方法创建护甲失败: " + e.getMessage());
                }
            }
        } catch (Exception e) {
            plugin.getLogger().warning("通过反射创建护甲失败: " + e.getMessage());
        }

        // 备用方法：手动创建一个简单的护甲物品
        return createFallbackArmor(armorId);
    }

    /**
     * 创建备用护甲（当无法通过Shoot插件创建时使用）
     *
     * @param armorId 护甲ID
     * @return 创建的备用护甲
     */
    private ItemStack createFallbackArmor(int armorId) {
        Material material;
        String name;

        // 根据ID选择不同的材质和名称
        switch (armorId) {
            case 1: // 皮革护甲
                material = Material.LEATHER_CHESTPLATE;
                name = "皮革护甲";
                break;
            case 2: // 锁链护甲
                material = Material.CHAINMAIL_CHESTPLATE;
                name = "锁链护甲";
                break;
            case 3: // 铁护甲
                material = Material.IRON_CHESTPLATE;
                name = "铁护甲";
                break;
            case 4: // 钻石护甲
                material = Material.DIAMOND_CHESTPLATE;
                name = "钻石护甲";
                break;
            default:
                material = Material.LEATHER_CHESTPLATE;
                name = "未知护甲";
        }

        // 创建物品
        ItemStack armor = new ItemStack(material);
        ItemMeta meta = armor.getItemMeta();
        meta.setDisplayName(ChatColor.BLUE + name);

        // 添加描述
        List<String> lore = new ArrayList<>();
        lore.add(ChatColor.GRAY + "护甲ID: " + armorId);
        lore.add(ChatColor.GREEN + "提供额外防护");
        meta.setLore(lore);

        // 设置不可破坏
        meta.setUnbreakable(true);

        armor.setItemMeta(meta);

        return armor;
    }

    /**
     * 创建特殊物品
     *
     * @param specialId 特殊物品ID
     * @return 创建的特殊物品
     */
    public ItemStack createSpecialItem(int specialId) {
        try {
            // 尝试通过反射调用Shoot插件的方法
            Plugin shootPlugin = plugin.getShootPlugin();
            if (shootPlugin != null) {
                // 将数字ID转换为字符串ID格式
                String stringSpecialId = "id" + specialId;
                String originalSpecialId = stringSpecialId; // 保存原始ID，用于日志记录

                // 从gameKit.yml获取正确的Shoot插件ID映射
                GameKitManager gameKitManager = plugin.getGameKitManager();
                if (gameKitManager != null) {
                    String shootId = gameKitManager.getShootId(stringSpecialId);
                    if (shootId != null && !shootId.isEmpty() && !shootId.equals(stringSpecialId)) {
                        stringSpecialId = shootId;
                        plugin.getLogger().info("使用gameKit.yml中的映射ID: " + stringSpecialId + " 替代 " + originalSpecialId);
                    }
                }

                // 尝试使用activateSpecialFunction方法
                try {
                    // 查找一个不在游戏中的玩家作为临时玩家
                    Player tempPlayer = null;
                    for (Player p : Bukkit.getOnlinePlayers()) {
                        if (plugin.getGameSessionManager().getPlayerGame(p) == null) {
                            tempPlayer = p;
                            break;
                        }
                    }

                    // 如果找不到不在游戏中的玩家，使用第一个在线玩家
                    if (tempPlayer == null && !Bukkit.getOnlinePlayers().isEmpty()) {
                        tempPlayer = Bukkit.getOnlinePlayers().iterator().next();
                    }

                    if (tempPlayer != null) {
                        // 保存玩家当前物品栏内容
                        ItemStack[] savedInventory = tempPlayer.getInventory().getContents().clone();

                        // 清空玩家物品栏的第一个槽位
                        tempPlayer.getInventory().setItem(0, null);

                        // 尝试使用giveItemToPlayer方法（假设特殊物品也是通过这个方法给予的）
                        Method method = shootPlugin.getClass().getMethod("giveItemToPlayer", Player.class, String.class);
                        method.invoke(shootPlugin, tempPlayer, stringSpecialId);

                        // 获取特殊物品
                        ItemStack specialItem = tempPlayer.getInventory().getItem(0);

                        // 恢复玩家物品栏
                        tempPlayer.getInventory().setContents(savedInventory);

                        // 更新玩家物品栏
                        tempPlayer.updateInventory();

                        if (specialItem != null) {
                            return specialItem.clone(); // 返回克隆的物品，避免引用问题
                        }
                    }
                } catch (Exception e) {
                    plugin.getLogger().warning("通过giveItemToPlayer方法创建特殊物品失败: " + e.getMessage());
                }
            }
        } catch (Exception e) {
            plugin.getLogger().warning("通过反射创建特殊物品失败: " + e.getMessage());
        }

        // 备用方法：手动创建一个简单的特殊物品
        return createFallbackSpecialItem(specialId);
    }

    /**
     * 创建备用特殊物品（当无法通过Shoot插件创建时使用）
     *
     * @param specialId 特殊物品ID
     * @return 创建的备用特殊物品
     */
    private ItemStack createFallbackSpecialItem(int specialId) {
        Material material;
        String name;

        // 根据ID选择不同的材质和名称
        switch (specialId) {
            case 1: // 闪光弹
                material = Material.SNOWBALL;
                name = "闪光弹";
                break;
            case 2: // 烟雾弹
                material = Material.FIREWORK_ROCKET;
                name = "烟雾弹";
                break;
            case 3: // 手榴弹
                material = Material.EGG;
                name = "手榴弹";
                break;
            case 4: // 医疗包
                material = Material.CAKE;
                name = "医疗包";
                break;
            default:
                material = Material.NETHER_STAR;
                name = "未知特殊物品";
        }

        // 创建物品
        ItemStack specialItem = new ItemStack(material);
        ItemMeta meta = specialItem.getItemMeta();
        meta.setDisplayName(ChatColor.LIGHT_PURPLE + name);

        // 添加描述
        List<String> lore = new ArrayList<>();
        lore.add(ChatColor.GRAY + "特殊物品ID: " + specialId);
        lore.add(ChatColor.GREEN + "右键点击使用");
        meta.setLore(lore);

        specialItem.setItemMeta(meta);

        return specialItem;
    }

    /**
     * 获取物品图标URL
     *
     * @param type 物品类型 (wp, it, ar, sp)
     * @param itemId 物品ID
     * @return 物品图标URL，如果没有则返回默认图标
     */
    public String getItemIconUrl(String type, String itemId) {
        // 从Shoot插件配置中获取物品名称
        String itemName = getItemName(type, itemId);
        if (itemName == null || itemName.isEmpty()) {
            return getDefaultIconUrl(type);
        }

        // 根据物品类型和名称生成图标URL
        String encodedName = itemName.replace(" ", "_").replace("§", "");

        // 根据物品类型返回不同的默认图标
        if (type.equals("wp")) {
            // 武器类型
            return "https://mc.netease.com/item/" + encodedName + "/icon";
        } else if (type.equals("it")) {
            // 物品类型
            return "https://mc.netease.com/item/" + encodedName + "/icon";
        } else if (type.equals("ar")) {
            // 护甲类型
            return "https://mc.netease.com/item/" + encodedName + "/icon";
        } else if (type.equals("sp")) {
            // 特殊物品类型
            return "https://mc.netease.com/item/" + encodedName + "/icon";
        }

        // 默认图标
        return getDefaultIconUrl(type);
    }

    /**
     * 获取默认图标URL
     *
     * @param type 物品类型
     * @return 默认图标URL
     */
    private String getDefaultIconUrl(String type) {
        switch (type) {
            case "wp":
                return "https://mc.netease.com/item/wooden_hoe/icon";
            case "it":
                return "https://mc.netease.com/item/gunpowder/icon";
            case "ar":
                return "https://mc.netease.com/item/leather_chestplate/icon";
            case "sp":
                return "https://mc.netease.com/item/nether_star/icon";
            default:
                return "https://mc.netease.com/item/barrier/icon";
        }
    }

    /**
     * 获取物品的材质名称
     *
     * @param type 物品类型 (wp, it, ar, sp)
     * @param itemId 物品ID
     * @return 物品的材质名称，如果获取失败则返回默认材质
     */
    public String getItemMaterial(String type, String itemId) {
        try {
            Plugin shootPlugin = plugin.getShootPlugin();
            if (shootPlugin == null) {
                return getDefaultMaterial(type);
            }

            // 获取配置文件
            FileConfiguration config = null;

            if (type.equals("wp")) {
                // 武器类型，从config.yml获取
                try {
                    Field configField = shootPlugin.getClass().getDeclaredField("config");
                    configField.setAccessible(true);
                    config = (FileConfiguration) configField.get(shootPlugin);

                    if (config != null && config.contains("guns." + itemId)) {
                        return config.getString("guns." + itemId + ".material", "WOODEN_HOE");
                    }
                } catch (Exception e) {
                    plugin.getLogger().warning("获取武器材质失败: " + e.getMessage());
                }
            } else {
                // 其他类型，从Buy.yml获取
                try {
                    Field buyConfigField = shootPlugin.getClass().getDeclaredField("buyConfig");
                    buyConfigField.setAccessible(true);
                    config = (FileConfiguration) buyConfigField.get(shootPlugin);

                    if (config != null && config.contains(type + "." + itemId)) {
                        return config.getString(type + "." + itemId + ".material", getDefaultMaterial(type));
                    }
                } catch (Exception e) {
                    plugin.getLogger().warning("获取物品材质失败: " + e.getMessage());
                }
            }

            return getDefaultMaterial(type);
        } catch (Exception e) {
            plugin.getLogger().warning("获取物品材质时出错: " + e.getMessage());
            return getDefaultMaterial(type);
        }
    }

    /**
     * 获取默认材质名称
     *
     * @param type 物品类型
     * @return 默认材质名称
     */
    private String getDefaultMaterial(String type) {
        switch (type) {
            case "wp":
                return "WOODEN_HOE";
            case "it":
                return "GUNPOWDER";
            case "ar":
                return "LEATHER_CHESTPLATE";
            case "sp":
                return "NETHER_STAR";
            default:
                return "BARRIER";
        }
    }

    /**
     * 初始化枪支弹药系统 确保玩家获得的枪支有正确的弹药设置
     *
     * @param player 玩家
     * @param itemId 枪支ID
     * @return 是否成功初始化
     */
    public boolean initializeGunAmmo(Player player, String itemId) {
        try {
            Plugin shootPlugin = plugin.getShootPlugin();
            if (shootPlugin == null) {
                plugin.getLogger().warning("无法获取Shoot插件实例，无法初始化弹药系统");
                return false;
            }

            // 记录原始ID，用于日志
            String originalId = itemId;
            plugin.getLogger().info("【弹药系统】初始化弹药系统，原始武器ID: " + originalId);

            // 特殊处理id1（手枪）和id11（突击步枪）
            if (itemId.equals("id1") || itemId.equals("1")) {
                // 确保id1是手枪
                plugin.getLogger().info("【弹药系统】特殊处理id1为手枪");
                itemId = "id1"; // 确保使用正确的手枪ID
            } else if (itemId.equals("id11") || itemId.equals("11")) {
                // 确保id11是突击步枪
                plugin.getLogger().info("【弹药系统】特殊处理id11为突击步枪");
                itemId = "id11"; // 确保使用正确的突击步枪ID
            } else {
                // 从gameKit.yml获取正确的Shoot插件ID映射
                GameKitManager gameKitManager = plugin.getGameKitManager();
                if (gameKitManager != null) {
                    String shootId = gameKitManager.getShootId(itemId);
                    if (shootId != null && !shootId.isEmpty() && !shootId.equals(itemId)) {
                        plugin.getLogger().info("【弹药系统】从gameKit.yml获取映射ID: " + shootId + " 替代 " + itemId);
                        itemId = shootId;
                    }
                }
            }

            // 确保ID格式正确，Shoot插件期望的格式是"id数字"
            if (!itemId.startsWith("id") && itemId.matches("\\d+")) {
                String oldId = itemId;
                itemId = "id" + itemId;
                plugin.getLogger().info("【弹药系统】转换纯数字ID " + oldId + " 为Shoot格式: " + itemId);
            }

            // 尝试获取枪支的弹药信息
            int clipSize = 0;
            int maxAmmo = 0;

            try {
                // 获取Shoot插件的配置
                Field configField = shootPlugin.getClass().getDeclaredField("config");
                configField.setAccessible(true);
                FileConfiguration config = (FileConfiguration) configField.get(shootPlugin);

                if (config != null) {
                    // 获取弹夹容量和最大弹药量
                    clipSize = config.getInt("guns." + itemId + ".clip_size", 5);
                    maxAmmo = config.getInt("guns." + itemId + ".ammo", 30);
                    plugin.getLogger().info("【弹药系统】从Shoot配置获取枪支信息: 弹夹容量: " + clipSize + ", 总弹药: " + maxAmmo);
                }
            } catch (Exception e) {
                plugin.getLogger().warning("【弹药系统】获取Shoot配置失败: " + e.getMessage());
                // 使用默认值
                clipSize = 5;
                maxAmmo = 30;
            }

            // 尝试使用Shoot插件的方法设置弹药
            try {
                // 尝试调用setPlayerAmmo方法
                Method setPlayerAmmoMethod = shootPlugin.getClass().getMethod("setPlayerAmmo", Player.class, String.class, int.class);
                setPlayerAmmoMethod.invoke(shootPlugin, player, itemId, maxAmmo);
                plugin.getLogger().info("【弹药系统】成功设置玩家 " + player.getName() + " 的武器 " + itemId + " 弹药为 " + maxAmmo);

                // 尝试调用setPlayerClip方法
                Method setPlayerClipMethod = shootPlugin.getClass().getMethod("setPlayerClip", Player.class, String.class, int.class);
                setPlayerClipMethod.invoke(shootPlugin, player, itemId, clipSize);
                plugin.getLogger().info("【弹药系统】成功设置玩家 " + player.getName() + " 的武器 " + itemId + " 弹夹为 " + clipSize);

                return true;
            } catch (NoSuchMethodException e) {
                plugin.getLogger().warning("【弹药系统】Shoot插件没有setPlayerAmmo或setPlayerClip方法，尝试使用备用方法");
            } catch (Exception e) {
                plugin.getLogger().warning("【弹药系统】调用Shoot插件的弹药设置方法失败: " + e.getMessage());
            }

            // 备用方法：直接设置玩家的经验值作为弹药量
            try {
                // 获取ammoMap字段
                Field ammoMapField = shootPlugin.getClass().getDeclaredField("ammoMap");
                ammoMapField.setAccessible(true);
                Object ammoMap = ammoMapField.get(shootPlugin);

                if (ammoMap instanceof java.util.Map) {
                    @SuppressWarnings("unchecked")
                    java.util.Map<String, Integer> map = (java.util.Map<String, Integer>) ammoMap;
                    String key = player.getUniqueId().toString() + "." + itemId;
                    map.put(key, maxAmmo);
                    plugin.getLogger().info("【弹药系统】成功使用备用方法设置玩家 " + player.getName() + " 的武器 " + itemId + " 弹药为 " + maxAmmo);

                    // 设置玩家经验等级为弹药量
                    player.setLevel(maxAmmo);

                    return true;
                }
            } catch (Exception e) {
                plugin.getLogger().warning("【弹药系统】使用备用方法设置弹药失败: " + e.getMessage());
            }

            // 如果所有方法都失败，至少设置玩家的经验等级
            player.setLevel(maxAmmo);
            plugin.getLogger().info("【弹药系统】使用最后的备用方法设置玩家 " + player.getName() + " 的经验等级为 " + maxAmmo);

            return false;
        } catch (Exception e) {
            plugin.getLogger().warning("【弹药系统】初始化弹药系统时出错: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 使用Shoot插件的原生弹药补充方法（就像奖励箱那样）
     *
     * @param player 玩家
     * @return 是否成功补充
     */
    public boolean replenishPlayerAmmoNative(Player player) {
        try {
            Plugin shootPlugin = plugin.getShootPlugin();
            if (shootPlugin == null) {
                plugin.getLogger().warning("无法获取Shoot插件实例，无法补充弹药");
                return false;
            }

            plugin.getLogger().info("【弹药补充】使用Shoot插件原生方法为玩家 " + player.getName() + " 补充弹药");

            // 直接调用Shoot插件的replenishPlayerAmmo方法
            try {
                Method method = shootPlugin.getClass().getMethod("replenishPlayerAmmo", Player.class);
                boolean result = (boolean) method.invoke(shootPlugin, player);

                if (result) {
                    plugin.getLogger().info("【弹药补充】成功使用Shoot插件的replenishPlayerAmmo方法");
                    return true;
                } else {
                    plugin.getLogger().warning("【弹药补充】Shoot插件的replenishPlayerAmmo方法返回false");
                    return false;
                }
            } catch (NoSuchMethodException e) {
                plugin.getLogger().warning("【弹药补充】Shoot插件没有replenishPlayerAmmo方法");
                return false;
            } catch (Exception e) {
                plugin.getLogger().warning("【弹药补充】调用Shoot插件的replenishPlayerAmmo方法失败: " + e.getMessage());
                return false;
            }
        } catch (Exception e) {
            plugin.getLogger().warning("【弹药补充】原生弹药补充方法异常: " + e.getMessage());
            return false;
        }
    }

    /**
     * 补充玩家武器弹药（遵循Shoot插件的验证规则）
     *
     * @param player 玩家
     * @param weaponId 武器ID
     * @return 是否成功补充
     */
    public boolean replenishPlayerAmmo(Player player, String weaponId) {
        try {
            Plugin shootPlugin = plugin.getShootPlugin();
            if (shootPlugin == null) {
                plugin.getLogger().warning("无法获取Shoot插件实例，无法补充弹药");
                return false;
            }

            plugin.getLogger().info("【弹药补充】尝试为玩家 " + player.getName() + " 补充武器 " + weaponId + " 的弹药");

            // 首先检查玩家当前手持的武器是否与要补充的武器匹配
            String currentGunId = getCurrentPlayerGunId(player);
            plugin.getLogger().info("【弹药补充】玩家当前手持武器: " + currentGunId + ", 要补充的武器: " + weaponId);

            if (currentGunId == null || !currentGunId.equals(weaponId)) {
                plugin.getLogger().warning("【弹药补充】玩家当前手持的武器(" + currentGunId + ")与要补充的武器(" + weaponId + ")不匹配");
                return false;
            }

            // 使用Shoot插件的原生方法补充弹药（只补充当前手持武器）
            try {
                Method method = shootPlugin.getClass().getMethod("replenishPlayerAmmo", Player.class);
                boolean result = (boolean) method.invoke(shootPlugin, player);

                if (result) {
                    plugin.getLogger().info("【弹药补充】成功使用Shoot插件的replenishPlayerAmmo方法");
                    return true;
                } else {
                    plugin.getLogger().warning("【弹药补充】Shoot插件的replenishPlayerAmmo方法返回false");
                }
            } catch (NoSuchMethodException e) {
                plugin.getLogger().warning("【弹药补充】Shoot插件没有replenishPlayerAmmo方法");
            } catch (Exception e) {
                plugin.getLogger().warning("【弹药补充】调用Shoot插件的replenishPlayerAmmo方法失败: " + e.getMessage());
            }

            // 如果上面的方法失败，使用初始化弹药系统作为备用方法
            plugin.getLogger().info("【弹药补充】使用备用方法: initializeGunAmmo");
            return initializeGunAmmo(player, weaponId);

        } catch (Exception e) {
            plugin.getLogger().warning("【弹药补充】补充玩家弹药时出错: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 获取玩家当前手持的武器ID
     *
     * @param player 玩家
     * @return 武器ID，如果没有手持武器则返回null
     */
    private String getCurrentPlayerGunId(Player player) {
        try {
            Plugin shootPlugin = plugin.getShootPlugin();
            if (shootPlugin == null) {
                return null;
            }

            // 尝试获取currentGunMap字段
            Field currentGunMapField = shootPlugin.getClass().getDeclaredField("currentGunMap");
            currentGunMapField.setAccessible(true);
            Object currentGunMap = currentGunMapField.get(shootPlugin);

            if (currentGunMap instanceof java.util.Map) {
                @SuppressWarnings("unchecked")
                java.util.Map<Player, String> map = (java.util.Map<Player, String>) currentGunMap;
                return map.get(player);
            }
        } catch (Exception e) {
            plugin.getLogger().warning("【弹药补充】获取玩家当前武器ID失败: " + e.getMessage());
        }
        return null;
    }

    /**
     * 设置玩家的显示设置
     *
     * @param player 玩家
     * @param type 显示类型 ("start" 或 "hit")
     * @param enable 是否启用
     * @return 是否成功设置
     */
    public boolean setPlayerDisplay(Player player, String type, boolean enable) {
        try {
            Plugin shootPlugin = plugin.getShootPlugin();
            if (shootPlugin == null) {
                plugin.getLogger().warning("无法获取Shoot插件实例，无法设置显示设置");
                return false;
            }

            // 调用Shoot插件的setPlayerDisplay方法
            Method method = shootPlugin.getClass().getMethod("setPlayerDisplay", Player.class, String.class, boolean.class);
            boolean result = (boolean) method.invoke(shootPlugin, player, type, enable);

            plugin.getLogger().info("成功设置玩家 " + player.getName() + " 的显示设置: " + type + " = " + enable);
            return result;
        } catch (Exception e) {
            plugin.getLogger().warning("调用Shoot插件的setPlayerDisplay方法时出错: " + e.getMessage());
            return false;
        }
    }

    /**
     * 静默设置玩家的显示设置（不发送消息给玩家）
     *
     * @param player 玩家
     * @param type 显示类型 ("start" 或 "hit")
     * @param enable 是否启用
     * @return 是否成功设置
     */
    public boolean setPlayerDisplaySilent(Player player, String type, boolean enable) {
        try {
            Plugin shootPlugin = plugin.getShootPlugin();
            if (shootPlugin == null) {
                plugin.getLogger().warning("无法获取Shoot插件实例，无法设置显示设置");
                return false;
            }

            // 直接操作Shoot插件的displaySettingsMap字段
            try {
                Field displaySettingsMapField = shootPlugin.getClass().getDeclaredField("displaySettingsMap");
                displaySettingsMapField.setAccessible(true);
                Object displaySettingsMap = displaySettingsMapField.get(shootPlugin);

                if (displaySettingsMap instanceof java.util.Map) {
                    @SuppressWarnings("unchecked")
                    java.util.Map<Player, Object> map = (java.util.Map<Player, Object>) displaySettingsMap;

                    // 获取或创建玩家的DisplaySettings对象
                    Object settings = map.get(player);
                    if (settings == null) {
                        // 创建新的DisplaySettings对象
                        Class<?> displaySettingsClass = Class.forName(shootPlugin.getClass().getName() + "$DisplaySettings");
                        Constructor<?> constructor = displaySettingsClass.getDeclaredConstructor(shootPlugin.getClass(), boolean.class, boolean.class);
                        constructor.setAccessible(true);
                        settings = constructor.newInstance(shootPlugin, true, true);
                    }

                    // 设置对应的属性
                    if (type.equals("start")) {
                        Method setStartTitleMethod = settings.getClass().getMethod("setStartTitle", boolean.class);
                        setStartTitleMethod.invoke(settings, enable);
                    } else if (type.equals("hit")) {
                        Method setHitTitleMethod = settings.getClass().getMethod("setHitTitle", boolean.class);
                        setHitTitleMethod.invoke(settings, enable);
                    }

                    // 更新map
                    map.put(player, settings);

                    plugin.getLogger().info("静默设置玩家 " + player.getName() + " 的显示设置: " + type + " = " + enable);
                    return true;
                }
            } catch (Exception e) {
                plugin.getLogger().warning("静默设置失败，尝试使用普通方法: " + e.getMessage());
                // 如果静默设置失败，使用普通方法
                return setPlayerDisplay(player, type, enable);
            }

            return false;
        } catch (Exception e) {
            plugin.getLogger().warning("静默设置玩家显示设置时出错: " + e.getMessage());
            return false;
        }
    }

    /**
     * 同步玩家的显示设置到Shoot插件
     * 从player-display-settings.yml读取设置并应用到Shoot插件
     *
     * @param player 玩家
     * @return 是否成功同步
     */
    public boolean syncPlayerDisplaySettings(Player player) {
        try {
            // 获取玩家的显示设置
            PlayerDisplaySettingsManager.PlayerDisplaySettings settings =
                plugin.getPlayerDisplaySettingsManager().getPlayerSettings(player);

            // 同步射击显示设置
            boolean shootResult = setPlayerDisplaySilent(player, "start", settings.isShootTitle());

            // 同步命中显示设置
            boolean hitResult = setPlayerDisplaySilent(player, "hit", settings.isHitTitle());

            if (shootResult && hitResult) {
                plugin.getLogger().info("成功同步玩家 " + player.getName() + " 的显示设置到Shoot插件: 射击=" +
                    settings.isShootTitle() + ", 命中=" + settings.isHitTitle());
                return true;
            } else {
                plugin.getLogger().warning("同步玩家 " + player.getName() + " 的显示设置时部分失败");
                return false;
            }
        } catch (Exception e) {
            plugin.getLogger().warning("同步玩家 " + player.getName() + " 的显示设置时出错: " + e.getMessage());
            return false;
        }
    }
}
