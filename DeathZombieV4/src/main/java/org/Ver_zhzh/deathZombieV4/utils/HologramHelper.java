package org.Ver_zhzh.deathZombieV4.utils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.function.Consumer;

import org.Ver_zhzh.deathZombieV4.DeathZombieV4;
import org.bukkit.ChatColor;
import org.bukkit.Location;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.player.PlayerInteractAtEntityEvent;
import org.bukkit.event.player.PlayerInteractEvent;
import org.bukkit.util.Vector;

import eu.decentsoftware.holograms.api.DHAPI;
import eu.decentsoftware.holograms.api.holograms.Hologram;

/**
 * 悬浮文字助手类，用于创建和管理DecentHolograms悬浮文字
 */
public class HologramHelper implements Listener {

    private final DeathZombieV4 plugin;

    // 存储悬浮文字ID和对应的点击处理器
    private final Map<String, Consumer<Player>> rightClickHandlers = new HashMap<>();

    // 存储悬浮文字ID和对应的游戏名称
    private final Map<String, String> hologramGameMap = new HashMap<>();

    // 存储悬浮文字ID和对应的门名称或购买点类型
    private final Map<String, String> hologramDataMap = new HashMap<>();

    // 存储每个游戏的悬浮文字列表
    private final Map<String, List<String>> gameHologramsMap = new HashMap<>();

    /**
     * 构造函数
     *
     * @param plugin 插件实例
     */
    public HologramHelper(DeathZombieV4 plugin) {
        this.plugin = plugin;
        plugin.getServer().getPluginManager().registerEvents(this, plugin);
    }

    /**
     * 创建门的悬浮文字
     *
     * @param gameName 游戏名称
     * @param doorName 门名称
     * @param location 位置
     * @param price 价格
     * @param clickHandler 点击处理器
     * @return 悬浮文字ID
     */
    public String createDoorHologram(String gameName, String doorName, Location location, int price, Consumer<Player> clickHandler) {
        // 生成唯一ID - 使用安全的ID格式，只包含字母、数字、下划线和短横线
        String safeGameName = gameName.replaceAll("[^a-zA-Z0-9_-]", "_");
        // 生成唯一ID，不使用门名称，避免中文字符问题
        String holoId = "door_" + safeGameName + "_door_" + UUID.randomUUID().toString().substring(0, 8);

        // 创建悬浮文字内容
        List<String> lines = new ArrayList<>();
        lines.add(ChatColor.GOLD + "== " + ChatColor.YELLOW + "门: " + doorName + ChatColor.GOLD + " ==");

        // 添加旋转的物品展示 - 铁门
        lines.add("#ICON: IRON_DOOR");

        lines.add(ChatColor.GREEN + "价格: " + ChatColor.YELLOW + price + " 金币");
        lines.add(ChatColor.AQUA + "右键点击解锁");

        // 创建悬浮文字
        try {
            // 调整位置到门的中心点
            Location holoLoc = location.clone().add(0, 1.5, 0);

            // 检查是否已存在相同ID的悬浮文字，如果有则先删除
            if (DHAPI.getHologram(holoId) != null) {
                plugin.getLogger().warning("发现已存在相同ID的门悬浮文字，先删除: " + holoId);
                DHAPI.removeHologram(holoId);
            }

            // 创建悬浮文字
            DHAPI.createHologram(holoId, holoLoc, lines);

            // 注册点击处理器
            if (clickHandler != null) {
                rightClickHandlers.put(holoId, clickHandler);
            }

            // 记录悬浮文字信息
            hologramGameMap.put(holoId, gameName);
            hologramDataMap.put(holoId, doorName);

            // 添加到游戏悬浮文字列表
            if (!gameHologramsMap.containsKey(gameName)) {
                gameHologramsMap.put(gameName, new ArrayList<>());
            }
            gameHologramsMap.get(gameName).add(holoId);

            plugin.getLogger().info("成功为游戏 " + gameName + " 的门 " + doorName + " 创建悬浮文字，ID: " + holoId);
            return holoId;
        } catch (Exception e) {
            plugin.getLogger().severe("创建门悬浮文字失败: " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 创建购买点悬浮文字
     *
     * @param gameName 游戏名称
     * @param location 位置
     * @param itemName 物品名称
     * @param type 物品类型
     * @param itemId 物品ID
     * @param price 价格
     * @param clickHandler 点击处理器
     * @return 悬浮文字ID
     */
    public String createShopHologram(String gameName, Location location, String itemName, String type, String itemId, int price, Consumer<Player> clickHandler) {
        // 生成唯一ID - 使用安全的ID格式，只包含字母、数字、下划线和短横线
        String safeGameName = gameName.replaceAll("[^a-zA-Z0-9_-]", "_");
        // 生成唯一ID，不使用物品名称，避免中文字符问题
        String holoId = "shop_" + safeGameName + "_" + type + "_" + itemId + "_" + UUID.randomUUID().toString().substring(0, 8);

        // 创建悬浮文字内容
        List<String> lines = new ArrayList<>();
        lines.add(ChatColor.AQUA + "== " + ChatColor.WHITE + "购买: " + itemName + ChatColor.AQUA + " ==");

        // 根据物品类型选择合适的物品材质
        String itemMaterial = "EMERALD"; // 默认物品

        if (type.equals("wp")) {
            // 武器类型
            if (itemId.equals("id1")) {
                itemMaterial = "IRON_SWORD"; // 铁剑
            } else if (itemId.startsWith("id")) {
                int gunId = 0;
                try {
                    gunId = Integer.parseInt(itemId.substring(2));
                } catch (NumberFormatException e) {
                    // 忽略错误
                }

                // 根据枪支ID选择合适的物品展示
                if (gunId >= 1 && gunId <= 6) {
                    itemMaterial = "WOODEN_HOE"; // 木锄头 - 手枪类
                } else if (gunId >= 7 && gunId <= 12) {
                    itemMaterial = "STONE_HOE"; // 石锄头 - 步枪类
                } else if (gunId >= 13 && gunId <= 18) {
                    itemMaterial = "IRON_HOE"; // 铁锄头 - 霰弹枪类
                } else {
                    itemMaterial = "DIAMOND_HOE"; // 钻石锄头 - 其他高级武器
                }
            }
        } else if (type.equals("ar")) {
            // 护甲类型
            if (itemId.equals("id1")) {
                itemMaterial = "LEATHER_CHESTPLATE"; // 皮革胸甲
            } else if (itemId.equals("id2")) {
                itemMaterial = "CHAINMAIL_CHESTPLATE"; // 锁链胸甲
            } else if (itemId.equals("id3")) {
                itemMaterial = "IRON_CHESTPLATE"; // 铁胸甲
            } else if (itemId.equals("id4")) {
                itemMaterial = "DIAMOND_CHESTPLATE"; // 钻石胸甲
            } else {
                itemMaterial = "GOLDEN_CHESTPLATE"; // 金胸甲
            }
        } else if (type.equals("it")) {
            // 道具类型
            if (itemId.equals("id1") || itemId.equals("id2")) {
                itemMaterial = "POTION"; // 药水
            } else if (itemId.matches("id(3|4|5|6|7|8|9|10|11|12)")) {
                itemMaterial = "ARROW"; // 弹药
            } else {
                itemMaterial = "CHEST"; // 其他物品
            }
        } else if (type.equals("sp")) {
            // 特殊功能
            itemMaterial = "REDSTONE"; // 红石代表特殊功能
        }

        // 添加旋转的物品展示
        lines.add("#ICON: " + itemMaterial);

        // 特殊处理手枪价格显示
        if (type.equals("wp") && (itemId.equals("id1") || itemId.equals("1"))) {
            lines.add(ChatColor.GREEN + "价格: " + ChatColor.YELLOW + "150 金币");
        } else {
            lines.add(ChatColor.GREEN + "价格: " + ChatColor.YELLOW + price + " 金币");
        }
        lines.add(ChatColor.GOLD + "右键点击购买");

        // 创建悬浮文字
        try {
            // 调整位置
            Location holoLoc = location.clone().add(0, 1.5, 0);

            // 检查是否已存在相同ID的悬浮文字，如果有则先删除
            if (DHAPI.getHologram(holoId) != null) {
                plugin.getLogger().warning("发现已存在相同ID的购买点悬浮文字，先删除: " + holoId);
                DHAPI.removeHologram(holoId);
            }

            // 创建悬浮文字
            DHAPI.createHologram(holoId, holoLoc, lines);

            // 注册点击处理器
            if (clickHandler != null) {
                rightClickHandlers.put(holoId, clickHandler);
            }

            // 记录悬浮文字信息
            hologramGameMap.put(holoId, gameName);
            hologramDataMap.put(holoId, type + "_" + itemId);

            // 添加到游戏悬浮文字列表
            if (!gameHologramsMap.containsKey(gameName)) {
                gameHologramsMap.put(gameName, new ArrayList<>());
            }
            gameHologramsMap.get(gameName).add(holoId);

            plugin.getLogger().info("成功为游戏 " + gameName + " 创建购买点悬浮文字，物品: " + itemName + "，ID: " + holoId);
            return holoId;
        } catch (Exception e) {
            plugin.getLogger().severe("创建购买点悬浮文字失败: " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 删除悬浮文字
     *
     * @param holoId 悬浮文字ID
     * @return 是否成功删除
     */
    public boolean removeHologram(String holoId) {
        try {
            // 删除悬浮文字
            DHAPI.removeHologram(holoId);

            // 移除点击处理器
            rightClickHandlers.remove(holoId);

            // 从游戏悬浮文字列表中移除
            String gameName = hologramGameMap.get(holoId);
            if (gameName != null && gameHologramsMap.containsKey(gameName)) {
                gameHologramsMap.get(gameName).remove(holoId);
            }

            // 移除记录
            hologramGameMap.remove(holoId);
            hologramDataMap.remove(holoId);

            plugin.getLogger().info("成功删除悬浮文字，ID: " + holoId);
            return true;
        } catch (Exception e) {
            plugin.getLogger().warning("删除悬浮文字失败: " + e.getMessage());
            return false;
        }
    }

    /**
     * 清理指定游戏的所有悬浮文字
     *
     * @param gameName 游戏名称
     */
    public void clearGameHolograms(String gameName) {
        if (!gameHologramsMap.containsKey(gameName)) {
            return;
        }

        List<String> holoIds = new ArrayList<>(gameHologramsMap.get(gameName));
        for (String holoId : holoIds) {
            removeHologram(holoId);
        }

        gameHologramsMap.remove(gameName);
        plugin.getLogger().info("已清理游戏 " + gameName + " 的所有悬浮文字");
    }

    /**
     * 清理所有悬浮文字
     */
    public void clearAllHolograms() {
        for (String gameName : new ArrayList<>(gameHologramsMap.keySet())) {
            clearGameHolograms(gameName);
        }

        // 以防万一，清空所有映射
        rightClickHandlers.clear();
        hologramGameMap.clear();
        hologramDataMap.clear();
        gameHologramsMap.clear();

        plugin.getLogger().info("已清理所有悬浮文字");
    }

    /**
     * 获取最近的悬浮文字
     *
     * @param location 位置
     * @param maxDistance 最大距离
     * @return 最近的悬浮文字ID，如果没有则返回null
     */
    public String getNearestHologram(Location location, double maxDistance) {
        String nearestHoloId = null;
        double minDistance = maxDistance;

        for (String holoId : hologramGameMap.keySet()) {
            try {
                Hologram hologram = DHAPI.getHologram(holoId);
                if (hologram != null && hologram.getLocation().getWorld().equals(location.getWorld())) {
                    double distance = hologram.getLocation().distance(location);
                    if (distance < minDistance) {
                        minDistance = distance;
                        nearestHoloId = holoId;
                    }
                }
            } catch (Exception e) {
                // 忽略错误，继续检查下一个
            }
        }

        return nearestHoloId;
    }

    // 防止重复触发的玩家记录
    private final Map<UUID, Long> lastClickTime = new HashMap<>();
    private static final long CLICK_COOLDOWN = 500; // 500ms冷却时间

    /**
     * 处理玩家交互事件
     *
     * @param event 玩家交互事件
     */
    @EventHandler
    public void onPlayerInteract(PlayerInteractEvent event) {
        Player player = event.getPlayer();

        // 只处理右键点击
        if (!event.getAction().toString().contains("RIGHT_CLICK")) {
            return;
        }

        // 防止重复触发
        UUID playerUUID = player.getUniqueId();
        long currentTime = System.currentTimeMillis();
        Long lastClick = lastClickTime.get(playerUUID);

        if (lastClick != null && (currentTime - lastClick) < CLICK_COOLDOWN) {
            // 在冷却时间内，忽略这次点击
            return;
        }

        // 获取玩家视线方向上的悬浮文字
        Location eyeLocation = player.getEyeLocation();
        Vector direction = eyeLocation.getDirection().normalize();

        // 检查玩家前方5格内是否有悬浮文字
        for (double d = 0; d <= 5; d += 0.5) {
            Location checkLoc = eyeLocation.clone().add(direction.clone().multiply(d));
            String holoId = getNearestHologram(checkLoc, 1.5);

            if (holoId != null && rightClickHandlers.containsKey(holoId)) {
                // 更新最后点击时间
                lastClickTime.put(playerUUID, currentTime);

                // 执行点击处理器
                rightClickHandlers.get(holoId).accept(player);
                event.setCancelled(true);
                return;
            }
        }
    }

    /**
     * 处理玩家与实体交互事件（备用方法）
     *
     * @param event 玩家与实体交互事件
     */
    @EventHandler
    public void onPlayerInteractAtEntity(PlayerInteractAtEntityEvent event) {
        Player player = event.getPlayer();

        // 获取点击位置附近的悬浮文字
        String holoId = getNearestHologram(event.getRightClicked().getLocation(), 2.0);

        if (holoId != null && rightClickHandlers.containsKey(holoId)) {
            // 执行点击处理器
            rightClickHandlers.get(holoId).accept(player);
            event.setCancelled(true);
        }
    }

    /**
     * 创建带有旋转动画的物品展示悬浮文字
     *
     * @param gameName 游戏名称
     * @param location 位置
     * @param title 标题
     * @param itemMaterial 物品材质
     * @param description 描述
     * @return 悬浮文字ID
     */
    public String createAnimatedItemHologram(String gameName, Location location, String title, String itemMaterial, String description) {
        // 生成唯一ID
        String safeGameName = gameName.replaceAll("[^a-zA-Z0-9_-]", "_");
        String holoId = "item_" + safeGameName + "_" + UUID.randomUUID().toString().substring(0, 8);

        // 创建悬浮文字内容
        List<String> lines = new ArrayList<>();

        // 添加标题
        if (title != null && !title.isEmpty()) {
            lines.add(ChatColor.GOLD + title);
        }

        // 添加旋转的物品展示
        if (itemMaterial != null && !itemMaterial.isEmpty()) {
            lines.add("#ICON: " + itemMaterial);
        } else {
            lines.add("#ICON: DIAMOND");
        }

        // 添加描述
        if (description != null && !description.isEmpty()) {
            lines.add(ChatColor.AQUA + description);
        }

        // 创建悬浮文字
        try {
            // 调整位置
            Location holoLoc = location.clone().add(0, 1.5, 0);

            // 检查是否已存在相同ID的悬浮文字，如果有则先删除
            if (DHAPI.getHologram(holoId) != null) {
                plugin.getLogger().warning("发现已存在相同ID的物品展示悬浮文字，先删除: " + holoId);
                DHAPI.removeHologram(holoId);
            }

            // 创建悬浮文字
            DHAPI.createHologram(holoId, holoLoc, lines);

            // 记录悬浮文字信息
            hologramGameMap.put(holoId, gameName);
            hologramDataMap.put(holoId, "animated_item");

            // 添加到游戏悬浮文字列表
            if (!gameHologramsMap.containsKey(gameName)) {
                gameHologramsMap.put(gameName, new ArrayList<>());
            }
            gameHologramsMap.get(gameName).add(holoId);

            plugin.getLogger().info("成功为游戏 " + gameName + " 创建物品展示悬浮文字，ID: " + holoId);
            return holoId;
        } catch (Exception e) {
            plugin.getLogger().severe("创建物品展示悬浮文字失败: " + e.getMessage());
            return null;
        }
    }
}
