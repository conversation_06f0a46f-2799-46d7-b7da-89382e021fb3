package org.Ver_zhzh.deathZombieV4.utils;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

import org.Ver_zhzh.deathZombieV4.DeathZombieV4;
import org.Ver_zhzh.deathZombieV4.game.DoorManager;
import org.Ver_zhzh.deathZombieV4.game.GameManager;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.Location;
import org.bukkit.entity.Entity;
import org.bukkit.entity.EntityType;
import org.bukkit.entity.Player;
import org.bukkit.entity.Villager;
import org.bukkit.entity.Villager.Profession;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.player.PlayerInteractEntityEvent;
import org.bukkit.metadata.FixedMetadataValue;

/**
 * 管理门解锁NPC的类
 */
public class DoorNPCManager implements Listener {

    private final DeathZombieV4 plugin;
    private final GameManager gameManager;
    private final DoorManager doorManager;
    private final ZombieHelper zombieHelper;

    // 存储游戏中的门NPC: <游戏名, <门名, NPC UUID>>
    private final Map<String, Map<String, UUID>> doorNPCs = new HashMap<>();

    /**
     * 构造函数
     *
     * @param plugin 插件实例
     */
    public DoorNPCManager(DeathZombieV4 plugin) {
        this.plugin = plugin;
        this.gameManager = plugin.getGameManager();
        this.doorManager = plugin.getDoorManager();
        this.zombieHelper = plugin.getZombieHelper();

        // 注册事件监听器
        plugin.getServer().getPluginManager().registerEvents(this, plugin);
    }

    /**
     * 为游戏中的门创建解锁NPC
     *
     * @param gameName 游戏名称
     */
    public void createDoorNPCsForGame(String gameName) {
        plugin.getLogger().info("为游戏 " + gameName + " 创建门解锁NPC");

        // 获取游戏中的所有门
        Map<String, Map<String, Object>> doors = gameManager.getDoors(gameName);
        if (doors == null || doors.isEmpty()) {
            plugin.getLogger().info("游戏 " + gameName + " 没有设置任何门");
            return;
        }

        // 清理之前的NPC（如果有）
        cleanupDoorNPCs(gameName);

        // 为每个锁住的门创建NPC
        for (Map.Entry<String, Map<String, Object>> entry : doors.entrySet()) {
            String doorName = entry.getKey();
            Map<String, Object> doorInfo = entry.getValue();

            // 检查门是否锁住
            if (doorInfo.containsKey("locked") && (boolean) doorInfo.get("locked")) {
                // 获取门的区域
                if (doorInfo.containsKey("region")) {
                    @SuppressWarnings("unchecked")
                    Map<String, Object> regionData = (Map<String, Object>) doorInfo.get("region");

                    // 获取解锁价格
                    int unlockPrice = doorInfo.containsKey("unlockPrice") ? (int) doorInfo.get("unlockPrice") : 0;

                    // 创建NPC在门附近
                    createDoorNPC(gameName, doorName, regionData, unlockPrice);
                }
            }
        }
    }

    /**
     * 创建门解锁NPC
     *
     * @param gameName 游戏名称
     * @param doorName 门名称
     * @param regionData 门区域数据
     * @param unlockPrice 解锁价格
     */
    private void createDoorNPC(String gameName, String doorName, Map<String, Object> regionData, int unlockPrice) {
        try {
            // 从区域数据中获取世界和坐标
            String worldName = (String) regionData.get("world");

            // 尝试从门配置中获取NPC位置
            Map<String, Map<String, Object>> doors = gameManager.getDoors(gameName);
            Map<String, Object> doorInfo = doors.get(doorName);

            plugin.getLogger().info("尝试为游戏 " + gameName + " 的门 " + doorName + " 创建NPC");

            double x, y, z;

            // 检查是否有预定义的NPC位置
            if (doorInfo != null && doorInfo.containsKey("npcLocation")) {
                @SuppressWarnings("unchecked")
                Map<String, Object> npcLocation = (Map<String, Object>) doorInfo.get("npcLocation");

                // 输出调试信息
                plugin.getLogger().info("找到预定义的NPC位置: " + npcLocation);

                // 使用预定义的NPC位置
                worldName = (String) npcLocation.get("world");
                x = (double) npcLocation.get("x");
                y = (double) npcLocation.get("y");
                z = (double) npcLocation.get("z");

                plugin.getLogger().info("使用预定义的NPC位置: x=" + x + ", y=" + y + ", z=" + z);
            } else {
                // 如果没有预定义位置，则计算位置
                plugin.getLogger().info("没有找到预定义的NPC位置，将计算位置");

                double minX = (double) regionData.get("minX");
                double minY = (double) regionData.get("minY");
                double minZ = (double) regionData.get("minZ");
                double maxX = (double) regionData.get("maxX");
                double maxY = (double) regionData.get("maxY");
                double maxZ = (double) regionData.get("maxZ");

                // 计算NPC位置（在门附近，避免阻挡通道）
                x = (minX + maxX) / 2;
                y = minY + 1; // 向上偏移一格，避免嵌入地面
                z = (minZ + maxZ) / 2;

                // 向门外偏移一格，避免阻挡通道
                if (minX == maxX) { // 东西向门
                    z += (z > 0) ? 1.5 : -1.5;
                } else if (minZ == maxZ) { // 南北向门
                    x += (x > 0) ? 1.5 : -1.5;
                }

                plugin.getLogger().info("使用计算的NPC位置: x=" + x + ", y=" + y + ", z=" + z);
            }

            // 创建NPC
            Location npcLocation = new Location(Bukkit.getWorld(worldName), x, y, z);
            Villager npc = (Villager) npcLocation.getWorld().spawnEntity(npcLocation, EntityType.VILLAGER);

            // 设置NPC属性
            npc.setCustomName(ChatColor.GOLD + "门卫 - " + doorName + ChatColor.GREEN + " [" + unlockPrice + "金币]");
            npc.setCustomNameVisible(true);
            npc.setProfession(Profession.LIBRARIAN);
            npc.setAdult();
            npc.setAI(false);
            npc.setInvulnerable(true);
            npc.setGravity(false);

            // 设置元数据，标识这是一个门NPC
            npc.setMetadata("doorNPC", new FixedMetadataValue(plugin, true));
            npc.setMetadata("gameName", new FixedMetadataValue(plugin, gameName));
            npc.setMetadata("doorName", new FixedMetadataValue(plugin, doorName));
            npc.setMetadata("unlockPrice", new FixedMetadataValue(plugin, unlockPrice));

            // 存储NPC引用
            if (!doorNPCs.containsKey(gameName)) {
                doorNPCs.put(gameName, new HashMap<>());
            }
            doorNPCs.get(gameName).put(doorName, npc.getUniqueId());

            plugin.getLogger().info("已为游戏 " + gameName + " 的门 " + doorName
                    + " 创建解锁NPC，价格: " + unlockPrice + "，位置: " + npcLocation);

        } catch (Exception e) {
            plugin.getLogger().severe("为门 " + doorName + " 创建NPC时出错: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 处理玩家与NPC交互事件
     *
     * @param event 玩家交互实体事件
     */
    @EventHandler
    public void onPlayerInteractEntity(PlayerInteractEntityEvent event) {
        Entity entity = event.getRightClicked();

        // 检查是否是门NPC
        if (entity.hasMetadata("doorNPC")) {
            event.setCancelled(true);

            Player player = event.getPlayer();
            String gameName = entity.getMetadata("gameName").get(0).asString();
            String doorName = entity.getMetadata("doorName").get(0).asString();
            int unlockPrice = entity.getMetadata("unlockPrice").get(0).asInt();

            // 检查玩家是否在游戏中
            if (!plugin.getGameSessionManager().isPlayerInGame(player, gameName)) {
                player.sendMessage(ChatColor.RED + "你不在游戏 " + gameName + " 中，无法解锁这个门！");
                return;
            }

            // 检查门是否已解锁
            if (doorManager.isDoorUnlocked(gameName, doorName)) {
                player.sendMessage(ChatColor.YELLOW + "这个门已经解锁了！");
                return;
            }

            // 获取玩家当前金钱
            double playerMoney = plugin.getShootPluginHelper().getPlayerMoney(player);

            // 检查玩家是否有足够的金钱
            if (playerMoney < unlockPrice) {
                player.sendMessage(ChatColor.RED + "你没有足够的金钱解锁这个门！需要 " + unlockPrice + " 金币，你只有 " + (int) playerMoney + " 金币。");
                return;
            }

            // 显示解锁菜单
            player.sendMessage(ChatColor.GREEN + "要解锁这个门需要 " + ChatColor.GOLD + unlockPrice
                    + ChatColor.GREEN + " 金币。正在解锁...");

            // 解锁门
            boolean success = doorManager.unlockDoor(player, gameName, doorName);
            if (success) {
                // 如果门已成功解锁，只移除当前交互的NPC，而不是所有门NPC
                // 保存当前实体的UUID，以便只移除这个特定的NPC
                UUID npcUUID = entity.getUniqueId();
                entity.remove();

                // 播放解锁音效和特效
                player.playSound(entity.getLocation(), "minecraft:block.iron_door.open", 1.0f, 1.0f);

                // 发送成功消息
                player.sendMessage(ChatColor.GREEN + "你已成功解锁门 '" + doorName + "'，花费了 " + unlockPrice + " 金币！");

                // 从记录中移除当前门的NPC
                if (doorNPCs.containsKey(gameName) && doorNPCs.get(gameName).containsKey(doorName)) {
                    // 确保只移除当前门的NPC记录
                    UUID storedUUID = doorNPCs.get(gameName).get(doorName);
                    if (storedUUID.equals(npcUUID)) {
                        doorNPCs.get(gameName).remove(doorName);
                        plugin.getLogger().info("已从记录中移除门 '" + doorName + "' 的NPC");
                    }
                }
            } else {
                player.sendMessage(ChatColor.RED + "解锁门失败，请稍后再试！");
            }
        }
    }

    /**
     * 清理指定游戏的所有门NPC
     *
     * @param gameName 游戏名称
     */
    public void cleanupDoorNPCs(String gameName) {
        if (!doorNPCs.containsKey(gameName)) {
            return;
        }

        for (UUID npcUUID : doorNPCs.get(gameName).values()) {
            Entity npc = Bukkit.getEntity(npcUUID);
            if (npc != null) {
                npc.remove();
            }
        }

        doorNPCs.get(gameName).clear();
        plugin.getLogger().info("已清理游戏 " + gameName + " 的所有门NPC");
    }

    /**
     * 清理所有门NPC
     */
    public void cleanupAllNPCs() {
        for (String gameName : doorNPCs.keySet()) {
            cleanupDoorNPCs(gameName);
        }
        doorNPCs.clear();
        plugin.getLogger().info("已清理所有门NPC");
    }

    /**
     * 当游戏结束时清理NPC
     *
     * @param gameName 游戏名称
     */
    public void onGameEnd(String gameName) {
        cleanupDoorNPCs(gameName);
    }
}
