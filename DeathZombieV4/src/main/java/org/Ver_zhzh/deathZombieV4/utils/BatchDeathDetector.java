package org.Ver_zhzh.deathZombieV4.utils;

import org.Ver_zhzh.deathZombieV4.DeathZombieV4;
import org.bukkit.entity.LivingEntity;
import org.bukkit.scheduler.BukkitRunnable;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.Map;

/**
 * 批量死亡检测器 - 检测和处理大量实体同时死亡的情况
 */
public class BatchDeathDetector {
    
    private final DeathZombieV4 plugin;
    private final Map<String, AtomicInteger> gameDeathCounts = new ConcurrentHashMap<>();
    private final Map<String, Long> lastResetTime = new ConcurrentHashMap<>();
    
    // 配置参数
    private int batchThreshold;
    private String handlingMode;
    private final long RESET_INTERVAL = 5000; // 5秒重置计数
    
    public BatchDeathDetector(DeathZombieV4 plugin) {
        this.plugin = plugin;
        loadConfig();
        startResetTask();
    }
    
    /**
     * 加载配置
     */
    private void loadConfig() {
        this.batchThreshold = plugin.getConfig().getInt("logging.entity_death_logging.batch_death_threshold", 10);
        this.handlingMode = plugin.getConfig().getString("logging.entity_death_logging.batch_death_handling", "summary");
    }
    
    /**
     * 记录实体死亡并检查是否为批量死亡
     * 
     * @param entity 死亡的实体
     * @param gameName 游戏名称
     * @return 是否应该记录这次死亡（false表示应该抑制）
     */
    public boolean recordDeath(LivingEntity entity, String gameName) {
        if (gameName == null) {
            return true; // 无法确定游戏，正常记录
        }
        
        // 获取当前游戏的死亡计数
        AtomicInteger count = gameDeathCounts.computeIfAbsent(gameName, k -> new AtomicInteger(0));
        int currentCount = count.incrementAndGet();
        
        // 更新最后重置时间
        lastResetTime.put(gameName, System.currentTimeMillis());
        
        // 检查是否达到批量死亡阈值
        if (currentCount >= batchThreshold) {
            return handleBatchDeath(gameName, currentCount, entity);
        }
        
        return true; // 正常记录
    }
    
    /**
     * 处理批量死亡情况
     */
    private boolean handleBatchDeath(String gameName, int deathCount, LivingEntity entity) {
        switch (handlingMode.toLowerCase()) {
            case "suppress":
                // 抑制模式：完全不记录
                return false;
                
            case "summary":
                // 汇总模式：只在达到阈值时记录一次汇总信息
                if (deathCount == batchThreshold) {
                    plugin.getLogger().warning(String.format(
                        "检测到批量死亡事件 - 游戏: %s, 已有 %d 个实体死亡，后续死亡将被汇总记录",
                        gameName, deathCount
                    ));
                    return true;
                } else if (deathCount % (batchThreshold * 2) == 0) {
                    // 每达到阈值的2倍时记录一次汇总
                    plugin.getLogger().warning(String.format(
                        "批量死亡持续中 - 游戏: %s, 累计死亡: %d 个实体",
                        gameName, deathCount
                    ));
                    return true;
                }
                return false;
                
            case "normal":
            default:
                // 正常模式：继续记录但添加批量死亡标记
                if (deathCount == batchThreshold) {
                    plugin.getLogger().warning(String.format(
                        "检测到批量死亡事件 - 游戏: %s, 可能存在性能问题",
                        gameName
                    ));
                }
                return true;
        }
    }
    
    /**
     * 获取游戏当前的死亡计数
     */
    public int getDeathCount(String gameName) {
        AtomicInteger count = gameDeathCounts.get(gameName);
        return count != null ? count.get() : 0;
    }
    
    /**
     * 手动重置游戏的死亡计数
     */
    public void resetDeathCount(String gameName) {
        gameDeathCounts.remove(gameName);
        lastResetTime.remove(gameName);
    }
    
    /**
     * 启动定期重置任务
     */
    private void startResetTask() {
        new BukkitRunnable() {
            @Override
            public void run() {
                long currentTime = System.currentTimeMillis();
                
                // 清理超过重置间隔的计数
                lastResetTime.entrySet().removeIf(entry -> {
                    String gameName = entry.getKey();
                    long lastTime = entry.getValue();
                    
                    if (currentTime - lastTime > RESET_INTERVAL) {
                        gameDeathCounts.remove(gameName);
                        return true;
                    }
                    return false;
                });
            }
        }.runTaskTimer(plugin, 100L, 100L); // 每5秒检查一次
    }
    
    /**
     * 检查是否为批量死亡状态
     */
    public boolean isBatchDeathActive(String gameName) {
        AtomicInteger count = gameDeathCounts.get(gameName);
        return count != null && count.get() >= batchThreshold;
    }
    
    /**
     * 获取批量死亡统计信息
     */
    public String getBatchDeathSummary() {
        if (gameDeathCounts.isEmpty()) {
            return "当前无批量死亡事件";
        }
        
        StringBuilder summary = new StringBuilder("批量死亡统计:\n");
        gameDeathCounts.forEach((gameName, count) -> {
            if (count.get() >= batchThreshold) {
                summary.append(String.format("- 游戏 %s: %d 个实体死亡\n", gameName, count.get()));
            }
        });
        
        return summary.toString();
    }
    
    /**
     * 重新加载配置
     */
    public void reloadConfig() {
        loadConfig();
    }
}
