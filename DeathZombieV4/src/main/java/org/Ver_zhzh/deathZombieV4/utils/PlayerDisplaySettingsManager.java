package org.Ver_zhzh.deathZombieV4.utils;

import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.configuration.file.YamlConfiguration;
import org.bukkit.entity.Player;
import org.Ver_zhzh.deathZombieV4.DeathZombieV4;

import java.io.File;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * 玩家显示设置管理器
 * 管理玩家的各种title显示设置
 * 现在使用统一的playerdata.yml文件
 */
public class PlayerDisplaySettingsManager {

    private final DeathZombieV4 plugin;

    // 存储玩家显示设置: <玩家UUID, 显示设置>
    private final Map<UUID, PlayerDisplaySettings> playerSettings;

    // 玩家数据配置文件
    private File playerDataFile;
    private FileConfiguration playerDataConfig;

    public PlayerDisplaySettingsManager(DeathZombieV4 plugin) {
        this.plugin = plugin;
        this.playerSettings = new HashMap<>();

        // 初始化玩家数据文件
        initPlayerDataFile();

        // 迁移旧的显示设置文件
        migrateOldSettings();

        // 加载保存的设置
        loadPlayerSettings();
    }

    /**
     * 初始化玩家数据文件
     */
    private void initPlayerDataFile() {
        playerDataFile = new File(plugin.getDataFolder(), "playerdata.yml");
        if (!playerDataFile.exists()) {
            try {
                playerDataFile.getParentFile().mkdirs();
                playerDataFile.createNewFile();
            } catch (IOException e) {
                plugin.getLogger().severe("无法创建玩家数据文件: " + e.getMessage());
            }
        }
        playerDataConfig = YamlConfiguration.loadConfiguration(playerDataFile);
    }

    /**
     * 迁移旧的显示设置文件
     */
    private void migrateOldSettings() {
        File oldSettingsFile = new File(plugin.getDataFolder(), "player-display-settings.yml");
        if (!oldSettingsFile.exists()) {
            return;
        }

        try {
            FileConfiguration oldConfig = YamlConfiguration.loadConfiguration(oldSettingsFile);
            if (!oldConfig.contains("players")) {
                return;
            }

            ConfigurationSection playersSection = oldConfig.getConfigurationSection("players");
            if (playersSection == null) {
                return;
            }

            int migratedCount = 0;
            for (String uuidString : playersSection.getKeys(false)) {
                try {
                    UUID uuid = UUID.fromString(uuidString);
                    boolean killTitle = oldConfig.getBoolean("players." + uuidString + ".kill_title", true);
                    boolean shootTitle = oldConfig.getBoolean("players." + uuidString + ".shoot_title", true);
                    boolean hitTitle = oldConfig.getBoolean("players." + uuidString + ".hit_title", true);

                    // 迁移到新的playerdata.yml结构
                    playerDataConfig.set("players." + uuidString + ".display_settings.kill_title", killTitle);
                    playerDataConfig.set("players." + uuidString + ".display_settings.shoot_title", shootTitle);
                    playerDataConfig.set("players." + uuidString + ".display_settings.hit_title", hitTitle);

                    PlayerDisplaySettings settings = new PlayerDisplaySettings(killTitle, shootTitle, hitTitle);
                    playerSettings.put(uuid, settings);
                    migratedCount++;
                } catch (IllegalArgumentException e) {
                    plugin.getLogger().warning("迁移时发现无效的UUID格式: " + uuidString);
                }
            }

            if (migratedCount > 0) {
                savePlayerSettings();
                plugin.getLogger().info("已迁移 " + migratedCount + " 个玩家的显示设置到新的playerdata.yml文件");

                // 重命名旧文件作为备份
                File backupFile = new File(plugin.getDataFolder(), "player-display-settings.yml.backup");
                if (oldSettingsFile.renameTo(backupFile)) {
                    plugin.getLogger().info("旧的显示设置文件已备份为: player-display-settings.yml.backup");
                }
            }
        } catch (Exception e) {
            plugin.getLogger().warning("迁移旧显示设置文件时出错: " + e.getMessage());
        }
    }

    /**
     * 加载玩家设置
     */
    private void loadPlayerSettings() {
        if (!playerDataConfig.contains("players")) {
            playerDataConfig.createSection("players");
            return;
        }

        ConfigurationSection playersSection = playerDataConfig.getConfigurationSection("players");
        if (playersSection == null) {
            return;
        }

        for (String uuidString : playersSection.getKeys(false)) {
            try {
                UUID uuid = UUID.fromString(uuidString);
                boolean killTitle = playerDataConfig.getBoolean("players." + uuidString + ".display_settings.kill_title", true);
                boolean shootTitle = playerDataConfig.getBoolean("players." + uuidString + ".display_settings.shoot_title", true);
                boolean hitTitle = playerDataConfig.getBoolean("players." + uuidString + ".display_settings.hit_title", true);

                PlayerDisplaySettings settings = new PlayerDisplaySettings(killTitle, shootTitle, hitTitle);
                playerSettings.put(uuid, settings);
            } catch (IllegalArgumentException e) {
                plugin.getLogger().warning("无效的UUID格式: " + uuidString);
            }
        }

        plugin.getLogger().info("已加载 " + playerSettings.size() + " 个玩家的显示设置");
    }

    /**
     * 保存玩家设置
     */
    public void savePlayerSettings() {
        for (Map.Entry<UUID, PlayerDisplaySettings> entry : playerSettings.entrySet()) {
            String uuidString = entry.getKey().toString();
            PlayerDisplaySettings settings = entry.getValue();

            playerDataConfig.set("players." + uuidString + ".display_settings.kill_title", settings.isKillTitle());
            playerDataConfig.set("players." + uuidString + ".display_settings.shoot_title", settings.isShootTitle());
            playerDataConfig.set("players." + uuidString + ".display_settings.hit_title", settings.isHitTitle());
        }

        try {
            playerDataConfig.save(playerDataFile);
            plugin.getLogger().info("已保存 " + playerSettings.size() + " 个玩家的显示设置");
        } catch (IOException e) {
            plugin.getLogger().severe("保存玩家显示设置时出错: " + e.getMessage());
        }
    }

    /**
     * 获取玩家的显示设置
     *
     * @param player 玩家
     * @return 玩家的显示设置
     */
    public PlayerDisplaySettings getPlayerSettings(Player player) {
        return playerSettings.getOrDefault(player.getUniqueId(), new PlayerDisplaySettings());
    }

    /**
     * 设置玩家的击杀title显示
     *
     * @param player 玩家
     * @param enabled 是否启用
     */
    public void setKillTitleEnabled(Player player, boolean enabled) {
        PlayerDisplaySettings settings = getPlayerSettings(player);
        settings.setKillTitle(enabled);
        playerSettings.put(player.getUniqueId(), settings);
        savePlayerSetting(player, "kill_title", enabled);
    }

    /**
     * 设置玩家的射击title显示
     *
     * @param player 玩家
     * @param enabled 是否启用
     */
    public void setShootTitleEnabled(Player player, boolean enabled) {
        PlayerDisplaySettings settings = getPlayerSettings(player);
        settings.setShootTitle(enabled);
        playerSettings.put(player.getUniqueId(), settings);
        savePlayerSetting(player, "shoot_title", enabled);

        // 同时设置Shoot插件的显示设置（静默模式，不发送消息）
        if (plugin.getShootPluginHelper() != null) {
            plugin.getShootPluginHelper().setPlayerDisplaySilent(player, "start", enabled);
        }
    }

    /**
     * 设置玩家的命中title显示
     *
     * @param player 玩家
     * @param enabled 是否启用
     */
    public void setHitTitleEnabled(Player player, boolean enabled) {
        PlayerDisplaySettings settings = getPlayerSettings(player);
        settings.setHitTitle(enabled);
        playerSettings.put(player.getUniqueId(), settings);
        savePlayerSetting(player, "hit_title", enabled);
        
        // 同时设置Shoot插件的显示设置（静默模式，不发送消息）
        if (plugin.getShootPluginHelper() != null) {
            plugin.getShootPluginHelper().setPlayerDisplaySilent(player, "hit", enabled);
        }
    }

    /**
     * 保存单个玩家的设置
     *
     * @param player 玩家
     * @param key 设置键
     * @param value 设置值
     */
    private void savePlayerSetting(Player player, String key, boolean value) {
        String uuidString = player.getUniqueId().toString();
        playerDataConfig.set("players." + uuidString + ".display_settings." + key, value);

        try {
            playerDataConfig.save(playerDataFile);
        } catch (IOException e) {
            plugin.getLogger().warning("保存玩家设置时出错: " + e.getMessage());
        }
    }

    /**
     * 玩家显示设置类
     */
    public static class PlayerDisplaySettings {
        private boolean killTitle;   // 是否显示击杀金钱title
        private boolean shootTitle;  // 是否显示射击title
        private boolean hitTitle;    // 是否显示命中title

        public PlayerDisplaySettings() {
            this(true, true, true); // 默认全部开启
        }

        public PlayerDisplaySettings(boolean killTitle, boolean shootTitle, boolean hitTitle) {
            this.killTitle = killTitle;
            this.shootTitle = shootTitle;
            this.hitTitle = hitTitle;
        }

        public boolean isKillTitle() {
            return killTitle;
        }

        public void setKillTitle(boolean killTitle) {
            this.killTitle = killTitle;
        }

        public boolean isShootTitle() {
            return shootTitle;
        }

        public void setShootTitle(boolean shootTitle) {
            this.shootTitle = shootTitle;
        }

        public boolean isHitTitle() {
            return hitTitle;
        }

        public void setHitTitle(boolean hitTitle) {
            this.hitTitle = hitTitle;
        }
    }

    /**
     * 同步所有在线玩家的显示设置到Shoot插件
     * 用于插件启动时或重新加载配置后的批量同步
     */
    public void syncAllPlayersToShoot() {
        if (plugin.getShootPluginHelper() == null) {
            plugin.getLogger().warning("ShootPluginHelper未初始化，无法同步显示设置");
            return;
        }

        int syncCount = 0;
        for (org.bukkit.entity.Player player : org.bukkit.Bukkit.getOnlinePlayers()) {
            if (plugin.getShootPluginHelper().syncPlayerDisplaySettings(player)) {
                syncCount++;
            }
        }

        plugin.getLogger().info("已同步 " + syncCount + " 个在线玩家的显示设置到Shoot插件");
    }

    /**
     * 同步单个玩家的显示设置到Shoot插件
     *
     * @param player 玩家
     */
    public void syncPlayerToShoot(org.bukkit.entity.Player player) {
        if (plugin.getShootPluginHelper() != null) {
            plugin.getShootPluginHelper().syncPlayerDisplaySettings(player);
        }
    }
}
