package org.Ver_zhzh.deathZombieV4.utils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;

import org.Ver_zhzh.deathZombieV4.DeathZombieV4;
import org.Ver_zhzh.deathZombieV4.game.GameManager;
import org.Ver_zhzh.deathZombieV4.game.GameSessionManager;
import org.Ver_zhzh.deathZombieV4.game.GameSessionManager.GameState;
import org.Ver_zhzh.deathZombieV4.enums.PlayerStatus;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.entity.Entity;
import org.bukkit.entity.EntityType;
import org.bukkit.entity.LivingEntity;
import org.bukkit.entity.Player;
import org.bukkit.entity.Zombie;
import org.bukkit.metadata.FixedMetadataValue;
import org.bukkit.metadata.MetadataValue;
import org.bukkit.scheduler.BukkitRunnable;
import org.bukkit.scheduler.BukkitTask;
import org.bukkit.scoreboard.DisplaySlot;
import org.bukkit.scoreboard.Objective;
import org.bukkit.scoreboard.Scoreboard;

/**
 * 管理玩家计分板的工具类
 */
public class ScoreboardManager {

    private final DeathZombieV4 plugin;
    private final GameSessionManager gameSessionManager;
    private final PlayerInteractionManager playerManager;
    private final GameManager gameManager;
    private final Map<UUID, Scoreboard> playerScoreboards;
    private BukkitTask updateTask;

    // 游戏倒计时和回合计时器
    private final Map<String, Integer> gameCountdowns = new HashMap<>();
    private final Map<String, Integer> roundTimers = new HashMap<>();
    private final Map<String, Integer> currentRounds = new HashMap<>();
    private final Map<String, Integer> totalRounds = new HashMap<>();

    // 存储每个游戏的僵尸总数和剩余数量
    private final Map<String, Integer> totalZombiesInRound = new HashMap<>();
    private final Map<String, Integer> remainingZombiesInRound = new HashMap<>();

    /**
     * 构造函数
     *
     * @param plugin 插件实例
     */
    public ScoreboardManager(DeathZombieV4 plugin) {
        this.plugin = plugin;
        this.gameSessionManager = plugin.getGameSessionManager();
        this.playerManager = plugin.getPlayerInteractionManager();
        this.gameManager = plugin.getGameManager();
        this.playerScoreboards = new HashMap<>();

        // 启动计分板更新任务
        startUpdateTask();
    }

    /**
     * 启动计分板更新任务
     */
    private void startUpdateTask() {
        // 保持计分板频繁更新，确保游戏体验流畅
        int updateInterval = 5; // 每5tick更新一次，保持实时性

        // 调试日志
        if (plugin.getConfig().getBoolean("debug", false)) {
            plugin.getLogger().info("计分板更新间隔设置为: " + updateInterval + " ticks");
        }

        // 取消现有任务
        if (updateTask != null && !updateTask.isCancelled()) {
            updateTask.cancel();
        }

        // 创建秒级计时器，用于更新游戏时间
        final BukkitTask timeTask = new BukkitRunnable() {
            private int tickCounter = 0;

            @Override
            public void run() {
                tickCounter++;

                // 每秒(20 ticks)更新一次时间相关逻辑
                if (tickCounter >= 20) {
                    tickCounter = 0;
                    updateTimers();
                }
            }
        }.runTaskTimer(plugin, 1L, 1L); // 每tick运行一次，精确控制秒级更新

        // 创建新的计分板更新任务（更频繁的更新间隔）
        updateTask = new BukkitRunnable() {
            @Override
            public void run() {
                if (!plugin.getConfig().getBoolean("game.scoreboard.enabled", true)) {
                    return;
                }

                try {
                    // 为每个在线玩家更新计分板显示
                    for (Player player : Bukkit.getOnlinePlayers()) {
                        updatePlayerScoreboard(player);
                    }
                } catch (Exception e) {
                    // 捕获并记录任何异常，防止任务崩溃
                    plugin.getLogger().warning("更新计分板时发生错误: " + e.getMessage());
                    if (plugin.getConfig().getBoolean("debug", false)) {
                        e.printStackTrace();
                    }
                }
            }
        }.runTaskTimer(plugin, 10L, updateInterval); // 初始延迟减少为10tick，更频繁的更新间隔
    }

    // 日志计数器，用于控制日志输出频率
    private int logCounter = 0;
    private static final int LOG_INTERVAL = 15; // 每15秒输出一次日志

    /**
     * 更新所有游戏计时器
     */
    private void updateTimers() {
        // 更新日志计数器
        logCounter++;

        // 获取所有正在运行的游戏
        Map<String, GameState> runningGames = new HashMap<>();
        for (String gameName : gameManager.getAllGames()) {
            GameState state = gameSessionManager.getGameState(gameName);
            if (state != null) {
                runningGames.put(gameName, state);
            }
        }

        for (Map.Entry<String, GameState> entry : runningGames.entrySet()) {
            String gameName = entry.getKey();
            GameState state = entry.getValue();

            // 检查游戏是否已标记为结束，如果是则跳过处理
            if (isGameEnded(gameName)) {
                if (plugin.getConfig().getBoolean("debug", false)) {
                    plugin.getLogger().info("游戏 " + gameName + " 已标记为结束，跳过计时器处理");
                }
                continue;
            }

            // 更新等待状态的游戏倒计时
            if (state == GameState.WAITING) {
                // 检查是否有足够的玩家
                int currentPlayerCount = gameSessionManager.getPlayerCount(gameName);
                int minPlayers = gameManager.getMinPlayers(gameName);

                if (currentPlayerCount < minPlayers) {
                    // 玩家数量不足，不启动倒计时
                    if (gameCountdowns.containsKey(gameName)) {
                        // 如果已经有倒计时，重置它
                        gameCountdowns.remove(gameName);

                        // 通知玩家等待更多的玩家
                        for (UUID playerId : gameSessionManager.getGameParticipants(gameName)) {
                            Player player = Bukkit.getPlayer(playerId);
                            if (player != null && player.isOnline()) {
                                player.sendMessage(ChatColor.YELLOW + "玩家数量不足，需要" + minPlayers + "名玩家才能开始，当前:" + currentPlayerCount);
                            }
                        }
                    }
                } else {
                    // 玩家数量足够，可以启动或继续倒计时
                    if (!gameCountdowns.containsKey(gameName)) {
                        // 从游戏配置文件中获取等待时间，如果未设置则使用全局默认值
                        int waitTime = gameManager.getWaitTime(gameName);
                        gameCountdowns.put(gameName, waitTime);

                        // 通知玩家倒计时开始
                        for (UUID playerId : gameSessionManager.getGameParticipants(gameName)) {
                            Player player = Bukkit.getPlayer(playerId);
                            if (player != null && player.isOnline()) {
                                player.sendMessage(ChatColor.GREEN + "玩家数量已满足要求，游戏将在 " + waitTime + " 秒后开始！");
                            }
                        }

                        if (plugin.getConfig().getBoolean("debug", false)) {
                            plugin.getLogger().info("游戏 " + gameName + " 的等待时间设置为: " + waitTime + " 秒");
                        }
                    } else {
                        int countdown = gameCountdowns.get(gameName);
                        if (countdown > 0) {
                            gameCountdowns.put(gameName, countdown - 1);
                        } else {
                            // 倒计时结束，启动游戏
                            gameSessionManager.startGame(gameName);

                            // 游戏开始时间现在在GameSessionManager.startGame()中设置
                            // 这里不再重复设置，避免时间不同步问题

                            // 设置回合信息
                            int totalRound = getGameTotalRounds(gameName);
                            totalRounds.put(gameName, totalRound);
                            currentRounds.put(gameName, 1);

                            // 设置回合计时器
                            int roundDuration = plugin.getConfig().getInt("game.rounds.duration", 300);
                            roundTimers.put(gameName, roundDuration);

                            // 广播游戏开始消息
                            for (UUID playerId : gameSessionManager.getGameParticipants(gameName)) {
                                Player player = Bukkit.getPlayer(playerId);
                                if (player != null && player.isOnline()) {
                                    player.sendMessage(ChatColor.GREEN + "游戏开始！当前玩家数:" + currentPlayerCount);
                                }
                            }
                        }
                    }
                }
            }

            // 更新游戏中的回合计时器
            if (state == GameState.RUNNING) {
                if (!currentRounds.containsKey(gameName)) {
                    currentRounds.put(gameName, 1);
                }

                if (!totalRounds.containsKey(gameName)) {
                    totalRounds.put(gameName, getGameTotalRounds(gameName));
                }

                if (!roundTimers.containsKey(gameName)) {
                    roundTimers.put(gameName, plugin.getConfig().getInt("game.rounds.duration", 300));
                } else {
                    int timeLeft = roundTimers.get(gameName);
                    if (timeLeft > 0) {
                        roundTimers.put(gameName, timeLeft - 1);
                    } else {
                        // 当前回合时间用尽
                        int currentRound = currentRounds.get(gameName);
                        int totalRound = totalRounds.get(gameName);

                        plugin.getLogger().info("游戏 " + gameName + " 第 " + currentRound + " 回合时间用尽");

                        if (currentRound < totalRound) {
                            // 进入下一回合（由于时间用尽）
                            int nextRound = currentRound + 1;
                            currentRounds.put(gameName, nextRound);
                            roundTimers.put(gameName, plugin.getConfig().getInt("game.rounds.duration", 300));

                            plugin.getLogger().info("游戏 " + gameName + " 由于时间用尽进入下一回合: " + nextRound);

                            // 清除上一回合的僵尸
                            try {
                                // 尝试获取ZombieSpawnManager实例
                                Object zombieSpawnManager = plugin.getZombieSpawnManager();
                                if (zombieSpawnManager != null) {
                                    // 先清除所有僵尸
                                    java.lang.reflect.Method clearAllZombiesMethod = zombieSpawnManager.getClass().getDeclaredMethod("clearAllZombies", String.class);
                                    clearAllZombiesMethod.setAccessible(true);
                                    clearAllZombiesMethod.invoke(zombieSpawnManager, gameName);
                                    plugin.getLogger().info("清除了游戏 " + gameName + " 第 " + currentRound + " 回合的僵尸");

                                    // 然后更新游戏回合
                                    java.lang.reflect.Method updateGameRoundMethod = zombieSpawnManager.getClass().getDeclaredMethod("updateGameRound", String.class, int.class);
                                    updateGameRoundMethod.setAccessible(true);
                                    updateGameRoundMethod.invoke(zombieSpawnManager, gameName, nextRound);
                                    plugin.getLogger().info("已通知ZombieSpawnManager更新游戏 " + gameName + " 回合为 " + nextRound);
                                }
                            } catch (Exception e) {
                                plugin.getLogger().warning("无法清除僵尸或更新回合: " + e.getMessage());
                                e.printStackTrace();
                            }

                            // 通知玩家进入下一回合（由于时间用尽）
                            for (UUID playerId : gameSessionManager.getGameParticipants(gameName)) {
                                Player player = Bukkit.getPlayer(playerId);
                                if (player != null && player.isOnline()) {
                                    player.sendMessage(ChatColor.YELLOW + "第 " + currentRound + " 回合时间用尽! 第 " + nextRound + " 回合开始!");

                                    // 使用MessageManager发送Title提示
                                    try {
                                        plugin.getMessageManager().sendRoundTitleMessage(player, nextRound, gameName);
                                    } catch (Exception titleEx) {
                                        // 如果MessageManager发送失败，使用备用的硬编码Title
                                        try {
                                            player.sendTitle(
                                                    ChatColor.GOLD + "第 " + nextRound + " 回合",
                                                    ChatColor.YELLOW + "准备迎战新的挑战！",
                                                    10, 70, 20
                                            );
                                        } catch (Exception backupTitleEx) {
                                            plugin.getLogger().warning("向玩家 " + player.getName() + " 发送Title失败: " + backupTitleEx.getMessage());
                                        }
                                    }
                                }
                            }
                        } else {
                            // 游戏由于时间用尽而结束（区别于僵尸击杀完成）
                            plugin.getLogger().info("游戏 " + gameName + " 由于时间用尽而结束");

                            // 通知玩家游戏由于时间用尽而结束
                            for (UUID playerId : gameSessionManager.getGameParticipants(gameName)) {
                                Player player = Bukkit.getPlayer(playerId);
                                if (player != null && player.isOnline()) {
                                    player.sendMessage(ChatColor.RED + "时间用尽！游戏结束！");

                                    // 获取游戏耗时和坚持回合数
                                    String gameDuration = getGameDuration(gameName);
                                    int survivedRounds = currentRounds.getOrDefault(gameName, 1);

                                    // 使用MessageManager发送时间用尽Title
                                    plugin.getMessageManager().sendGameEndTitleMessage(player, "timeout", gameName, gameDuration, survivedRounds);
                                }
                            }

                            // 结束游戏并执行完整清理
                            plugin.getZombieSpawnManager().endGameAndCleanup(gameName);

                            // 清除游戏状态
                            gameCountdowns.remove(gameName);
                            roundTimers.remove(gameName);
                            currentRounds.remove(gameName);
                            totalRounds.remove(gameName);
                        }
                    }
                }
            }
        }
    }

    /**
     * 获取游戏的总回合数
     *
     * @param gameName 游戏名称
     * @return 总回合数
     */
    private int getGameTotalRounds(String gameName) {
        // 从配置文件获取游戏的总回合数
        if (gameManager.gameExists(gameName)) {
            try {
                return gameManager.getGameConfig(gameName).getInt("rounds", 5);
            } catch (Exception e) {
                plugin.getLogger().warning("无法获取游戏 " + gameName + " 的回合数: " + e.getMessage());
            }
        }
        return 5; // 默认5回合
    }

    /**
     * 更新游戏回合信息
     *
     * @param gameName 游戏名称
     * @param round 新的回合数
     */
    public void updateRoundInfo(String gameName, int round) {
        // 更新当前回合数
        currentRounds.put(gameName, round);

        // 确保总回合数已设置
        if (!totalRounds.containsKey(gameName)) {
            totalRounds.put(gameName, getGameTotalRounds(gameName));
        }

        // 重置回合计时器
        int roundTime = plugin.getConfig().getInt("game.round_time", 300); // 默认300秒
        roundTimers.put(gameName, roundTime);

        // 清除上一回合的僵尸数量缓存，确保重新计算
        totalZombiesInRound.remove(gameName);
        remainingZombiesInRound.remove(gameName);

        if (plugin.getConfig().getBoolean("debug", false)) {
            plugin.getLogger().info("计分板管理器: 游戏 " + gameName + " 回合已更新为 " + round + "/" + totalRounds.get(gameName));
        }

        // 检查是否正在显示游戏结束计分板，如果是则不更新
        if (isShowingGameEndScoreboard(gameName)) {
            return;
        }

        // 为所有游戏玩家更新计分板
        for (UUID uuid : gameSessionManager.getGameParticipants(gameName)) {
            Player player = Bukkit.getPlayer(uuid);
            if (player != null && player.isOnline()) {
                updatePlayerScoreboard(player);
            }
        }
    }

    /**
     * 更新玩家的计分板
     *
     * @param player 玩家
     */
    public void updatePlayerScoreboard(Player player) {
        if (!plugin.getConfig().getBoolean("game.scoreboard.enabled", true)) {
            return;
        }

        PlayerStatus status = playerManager.getPlayerStatus(player);
        String gameName = gameSessionManager.getPlayerGame(player);

        // 检查是否正在显示游戏结束计分板，如果是则不更新
        if (gameName != null && isShowingGameEndScoreboard(gameName)) {
            // 游戏已结束并正在显示结束计分板，不覆盖
            return;
        }

        // 为未加入游戏的玩家显示服务器信息
        if (status == PlayerStatus.NOT_JOINED) {
            setServerInfoScoreboard(player);
            return;
        }

        // 为已加入但游戏尚未开始的玩家显示等待计分板
        if (status == PlayerStatus.WAITING && gameName != null) {
            setWaitingScoreboard(player, gameName);
            return;
        }

        // 为游戏中的玩家显示游戏计分板
        if (status == PlayerStatus.IN_GAME && gameName != null) {
            GameState gameState = gameSessionManager.getGameState(gameName);
            if (gameState == GameState.RUNNING) {
                setInGameScoreboard(player, gameName);
                return;
            }
        }

        // 其他情况移除计分板
        removePlayerScoreboard(player);
    }

    /**
     * 设置服务器信息计分板
     *
     * @param player 玩家
     */
    private void setServerInfoScoreboard(Player player) {
        org.bukkit.scoreboard.ScoreboardManager bukkitManager = Bukkit.getScoreboardManager();
        Scoreboard board = bukkitManager.getNewScoreboard();

        // 获取计分板标题
        String title = ChatColor.translateAlternateColorCodes('&',
                plugin.getConfig().getString("game.scoreboard.title", "&e&l僵尸生存"));

        Objective objective = board.registerNewObjective("server_info", "dummy", title);
        objective.setDisplaySlot(DisplaySlot.SIDEBAR);

        // 设置计分板内容
        int line = 15;

        // 添加顶部分隔线
        String header = ChatColor.translateAlternateColorCodes('&', "&c&l===== &6服务器信息 &c&l=====");
        objective.getScore(header).setScore(line--);

        // 空行
        objective.getScore(" ").setScore(line--);

        // 显示可用游戏
        objective.getScore(ChatColor.YELLOW + "可用游戏:").setScore(line--);

        List<String> enabledGames = plugin.getGameManager().getEnabledGames();
        if (enabledGames.isEmpty()) {
            objective.getScore(ChatColor.RED + "  没有可用的游戏").setScore(line--);
        } else {
            int count = 0;
            for (String game : enabledGames) {
                if (count >= 5) {
                    break; // 最多显示5个游戏
                }
                GameSessionManager.GameState state = gameSessionManager.getGameState(game);
                String stateText = (state == GameSessionManager.GameState.WAITING)
                        ? ChatColor.GREEN + "等待中"
                        : (state == GameSessionManager.GameState.RUNNING
                                ? ChatColor.RED + "进行中" : ChatColor.GRAY + "未开始");

                objective.getScore(ChatColor.AQUA + "  " + game + ": " + stateText).setScore(line--);
                count++;
            }
        }

        // 空行
        objective.getScore("  ").setScore(line--);

        // 玩家加入提示
        objective.getScore(ChatColor.YELLOW + "输入 " + ChatColor.WHITE + "/dzs join <游戏名>"
                + ChatColor.YELLOW + " 加入游戏").setScore(line--);

        // 空行
        objective.getScore("   ").setScore(line--);

        // 服务器名称
        String serverName = ChatColor.translateAlternateColorCodes('&',
                plugin.getConfig().getString("game.server_name", "&6僵尸生存服务器"));
        objective.getScore(serverName).setScore(line);

        // 设置玩家的计分板
        player.setScoreboard(board);
        playerScoreboards.put(player.getUniqueId(), board);
    }

    /**
     * 设置等待游戏时的计分板
     *
     * @param player 玩家
     * @param gameName 游戏名称
     */
    private void setWaitingScoreboard(Player player, String gameName) {
        org.bukkit.scoreboard.ScoreboardManager bukkitManager = Bukkit.getScoreboardManager();
        Scoreboard board = bukkitManager.getNewScoreboard();

        // 获取计分板标题
        String title = ChatColor.translateAlternateColorCodes('&',
                plugin.getConfig().getString("game.scoreboard.title", "&e&l僵尸生存"));

        Objective objective = board.registerNewObjective("waiting", "dummy", title);
        objective.setDisplaySlot(DisplaySlot.SIDEBAR);

        // 获取计分板样式
        ConfigurationSection styles = plugin.getConfig().getConfigurationSection("game.scoreboard.styles.waiting");

        // 设置计分板内容
        int line = 15;

        // 添加顶部分隔线
        String header = ChatColor.translateAlternateColorCodes('&',
                styles.getString("header", "&c&l===== &6等待游戏 &c&l====="));
        objective.getScore(header).setScore(line--);

        // 空行
        objective.getScore(" ").setScore(line--);

        // 地图名称
        String mapNameFormat = ChatColor.translateAlternateColorCodes('&',
                styles.getString("map_name", "&a地图: &f{map_name}"));
        String mapName = mapNameFormat.replace("{map_name}", gameName);
        objective.getScore(mapName).setScore(line--);

        // 玩家数量
        int currentPlayers = gameSessionManager.getPlayerCount(gameName);
        int maxPlayers = getGameMaxPlayers(gameName);
        String playersFormat = ChatColor.translateAlternateColorCodes('&',
                styles.getString("players_count", "&a玩家: &f{current}/{max}"));
        String players = playersFormat.replace("{current}", String.valueOf(currentPlayers))
                .replace("{max}", String.valueOf(maxPlayers));
        objective.getScore(players).setScore(line--);

        // 开始倒计时
        int countdown = gameCountdowns.getOrDefault(gameName, gameManager.getWaitTime(gameName));
        String countdownFormat = ChatColor.translateAlternateColorCodes('&',
                styles.getString("countdown", "&a开始倒计时: &f{countdown}秒"));
        String countdownText = countdownFormat.replace("{countdown}", String.valueOf(countdown));
        objective.getScore(countdownText).setScore(line--);

        // 空行
        objective.getScore("  ").setScore(line--);

        // 服务器名称
        String serverName = ChatColor.translateAlternateColorCodes('&',
                plugin.getConfig().getString("game.server_name", "&6僵尸生存服务器"));
        objective.getScore(serverName).setScore(line--);

        // 添加底部分隔线
        String footer = ChatColor.translateAlternateColorCodes('&',
                styles.getString("footer", "&c&l====================="));
        objective.getScore(footer).setScore(line);

        // 设置玩家的计分板
        player.setScoreboard(board);
        playerScoreboards.put(player.getUniqueId(), board);
    }

    /**
     * 获取游戏的最大玩家数
     *
     * @param gameName 游戏名称
     * @return 最大玩家数
     */
    private int getGameMaxPlayers(String gameName) {
        // 从配置文件获取游戏的最大玩家数
        if (gameManager.gameExists(gameName)) {
            try {
                return gameManager.getGameConfig(gameName).getInt("maxPlayers", 10);
            } catch (Exception e) {
                plugin.getLogger().warning("无法获取游戏 " + gameName + " 的最大玩家数: " + e.getMessage());
            }
        }
        return 10; // 默认10名玩家
    }

    /**
     * 设置游戏中的计分板
     *
     * @param player 玩家
     * @param gameName 游戏名称
     */
    private void setInGameScoreboard(Player player, String gameName) {
        org.bukkit.scoreboard.ScoreboardManager bukkitManager = Bukkit.getScoreboardManager();
        Scoreboard board = bukkitManager.getNewScoreboard();

        // 获取计分板标题
        String title = ChatColor.translateAlternateColorCodes('&',
                plugin.getConfig().getString("game.scoreboard.title", "&e&l僵尸生存"));

        Objective objective = board.registerNewObjective("ingame", "dummy", title);
        objective.setDisplaySlot(DisplaySlot.SIDEBAR);

        // 获取计分板样式
        ConfigurationSection styles = plugin.getConfig().getConfigurationSection("game.scoreboard.styles.in_game");

        // 设置计分板内容
        int line = 15;

        // 添加顶部分隔线
        String header = ChatColor.translateAlternateColorCodes('&',
                styles.getString("header", "&c&l===== &6游戏进行中 &c&l====="));
        objective.getScore(header).setScore(line--);

        // 空行
        objective.getScore(" ").setScore(line--);

        // 地图名称
        String mapNameFormat = ChatColor.translateAlternateColorCodes('&',
                styles.getString("map_name", "&a地图: &f{map_name}"));
        String mapName = mapNameFormat.replace("{map_name}", gameName);
        objective.getScore(mapName).setScore(line--);

        // 回合信息
        int currentRound = currentRounds.getOrDefault(gameName, 1);
        int totalRound = totalRounds.getOrDefault(gameName, getGameTotalRounds(gameName));
        String roundFormat = ChatColor.translateAlternateColorCodes('&',
                styles.getString("round", "&a回合: &f{current}/{total}"));
        String round = roundFormat.replace("{current}", String.valueOf(currentRound))
                .replace("{total}", String.valueOf(totalRound));
        objective.getScore(round).setScore(line--);

        // 剩余时间
        int timeLeft = roundTimers.getOrDefault(gameName, 0);
        int minutes = timeLeft / 60;
        int seconds = timeLeft % 60;
        String timeFormat = ChatColor.translateAlternateColorCodes('&',
                styles.getString("time_left", "&a剩余时间: &f{minutes}:{seconds}"));
        String time = timeFormat.replace("{minutes}", String.valueOf(minutes))
                .replace("{seconds}", String.format("%02d", seconds));
        objective.getScore(time).setScore(line--);

        // 僵尸数量
        int remainingZombies = getRemainingZombiesInRound(gameName);
        int totalZombies = getTotalZombiesForRound(gameName, currentRound);
        String zombiesFormat = ChatColor.translateAlternateColorCodes('&',
                styles.getString("zombies_left", "&a剩余僵尸: &f{count}/{total}"));
        String zombies = zombiesFormat.replace("{count}", String.valueOf(remainingZombies))
                .replace("{total}", String.valueOf(totalZombies));
        objective.getScore(zombies).setScore(line--);

        // 输出调试日志，但控制频率
        if (logCounter % LOG_INTERVAL == 0) {
            plugin.getLogger().info("计分板显示: 游戏 " + gameName + " 剩余僵尸数量: " + remainingZombies + "/" + totalZombies);
        }

        // 游戏耗时
        String gameDuration = getGameDuration(gameName);
        plugin.getLogger().info("计分板显示: 游戏 " + gameName + " 的耗时为: " + gameDuration);
        String durationFormat = ChatColor.translateAlternateColorCodes('&',
                styles.getString("game_duration", "&a游戏耗时: &f{duration}"));
        String durationText = durationFormat.replace("{duration}", gameDuration);
        objective.getScore(durationText).setScore(line--);

        // 其他玩家信息
        objective.getScore("  ").setScore(line--);

        Set<UUID> participants = gameSessionManager.getGameParticipants(gameName);
        // 限制显示的玩家数量，避免超出计分板行数限制
        int count = 0;
        for (UUID playerId : participants) {
            if (count >= 5) {
                break; // 最多显示5个玩家
            }
            Player gamePlayer = Bukkit.getPlayer(playerId);
            if (gamePlayer != null && gamePlayer.isOnline()) {
                double playerMoney = plugin.getShootPluginHelper().getPlayerMoney(gamePlayer);

                // 检查玩家状态，显示倒下或死亡状态
                String status = "";
                String playerStatus = playerManager.getPlayerData(gamePlayer, "status");
                if (playerStatus != null) {
                    if (playerStatus.equals("downed")) {
                        status = ChatColor.RED + "[已倒下] ";
                    } else if (playerStatus.equals("dead")) {
                        status = ChatColor.DARK_RED + "[已死亡] ";
                    }
                }

                String playerInfo = status + ChatColor.YELLOW + gamePlayer.getName() + ": "
                        + ChatColor.GREEN + (int) playerMoney + " 金钱";
                objective.getScore(playerInfo).setScore(line--);
                count++;
            }
        }

        while (count < 5) {
            // 填充空行以保持计分板稳定
            objective.getScore("   " + count).setScore(line--);
            count++;
        }

        // 服务器名称
        String serverName = ChatColor.translateAlternateColorCodes('&',
                plugin.getConfig().getString("game.server_name", "&6僵尸生存服务器"));
        objective.getScore(serverName).setScore(line--);

        // 添加底部分隔线
        String footer = ChatColor.translateAlternateColorCodes('&',
                styles.getString("footer", "&c&l====================="));
        objective.getScore(footer).setScore(line);

        // 设置玩家的计分板
        player.setScoreboard(board);
        playerScoreboards.put(player.getUniqueId(), board);
    }

    /**
     * 计算游戏中的僵尸数量
     *
     * @param gameName 游戏名称
     * @return 僵尸数量
     */
    public int countZombies(String gameName) {
        // 如果有存储的剩余僵尸数量，直接返回
        if (remainingZombiesInRound.containsKey(gameName)) {
            int storedCount = remainingZombiesInRound.get(gameName);
            // 只在调试模式下输出详细日志
            if (plugin.getConfig().getBoolean("debug", false)) {
                plugin.getLogger().info("从缓存中获取游戏 " + gameName + " 的剩余僵尸数量: " + storedCount);
            }
            return storedCount;
        }

        // 否则计算实际存在的僵尸数量
        int count = 0;

        // 考虑所有世界的实体
        for (org.bukkit.World world : Bukkit.getWorlds()) {
            for (Entity entity : world.getEntities()) {
                // 只计算游戏实体（有特定元数据标记的实体）
                if (isGameEntity(entity)) {
                    count++;
                }
            }
        }

        // 只在调试模式下输出详细日志
        if (plugin.getConfig().getBoolean("debug", false)) {
            plugin.getLogger().info("实际计算游戏 " + gameName + " 当前僵尸数量: " + count);
        }

        // 如果还没有初始化剩余僵尸数量，将当前计算结果存储
        if (!remainingZombiesInRound.containsKey(gameName)) {
            remainingZombiesInRound.put(gameName, count);
            if (plugin.getConfig().getBoolean("debug", false)) {
                plugin.getLogger().info("初始化游戏 " + gameName + " 的剩余僵尸数量为: " + count);
            }
        }

        return count;
    }

    /**
     * 获取当前回合数
     *
     * @param gameName 游戏名称
     * @return 当前回合数
     */
    public int getCurrentRound(String gameName) {
        return currentRounds.getOrDefault(gameName, 1);
    }

    /**
     * 获取剩余僵尸数量
     *
     * @param gameName 游戏名称
     * @return 剩余僵尸数量
     */
    public int getRemainingZombiesInRound(String gameName) {
        return remainingZombiesInRound.getOrDefault(gameName, 0);
    }

    /**
     * 设置剩余僵尸数量
     *
     * @param gameName 游戏名称
     * @param count 剩余僵尸数量
     */
    public void setRemainingZombiesInRound(String gameName, int count) {
        // 记录旧值以便于调试
        int oldCount = remainingZombiesInRound.getOrDefault(gameName, -1);

        // 更新剩余僵尸数量
        remainingZombiesInRound.put(gameName, count);

        // 只在调试模式下输出详细日志
        if (plugin.getConfig().getBoolean("debug", false)) {
            plugin.getLogger().info("计分板更新: 游戏 " + gameName + " 剩余僵尸数量从 " + oldCount + " 更新为: " + count);
        }

        // 立即更新所有玩家的计分板，确保僵尸数量变化能够实时反映
        try {
            updateZombieCountDisplay(gameName);
            if (plugin.getConfig().getBoolean("debug", false)) {
                plugin.getLogger().info("已强制更新游戏 " + gameName + " 的计分板显示");
            }
        } catch (Exception e) {
            plugin.getLogger().warning("更新计分板显示时发生错误: " + e.getMessage());
            if (plugin.getConfig().getBoolean("debug", false)) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 获取指定回合的总僵尸数量
     *
     * @param gameName 游戏名称
     * @param round 回合数
     * @return 总僵尸数量
     */
    public int getTotalZombiesForRound(String gameName, int round) {
        // 每次都重新计算，避免使用缓存导致的问题
        // 清除之前的缓存
        totalZombiesInRound.remove(gameName);

        // 从游戏配置中获取指定回合的僵尸数量
        int zombieCount = 0;
        if (gameManager.gameExists(gameName)) {
            try {
                // 尝试从回合配置中获取 - 首先尝试直接从zombieCount节点获取
                String roundKey = "round" + round;
                zombieCount = gameManager.getGameConfig(gameName).getInt("zombieCount." + roundKey, 0);

                // 如果上面的路径没有找到，尝试使用旧的路径格式
                if (zombieCount <= 0) {
                    zombieCount = gameManager.getGameConfig(gameName).getInt("rounds." + roundKey + ".zombieCount", 0);
                }

                // 如果没有特定配置，使用默认公式计算
                if (zombieCount <= 0) {
                    // 默认公式: 基础数量 + (回合数 * 每回合增加数)
                    int baseCount = gameManager.getGameConfig(gameName).getInt("rounds.baseZombieCount", 10);
                    int incrementPerRound = gameManager.getGameConfig(gameName).getInt("rounds.incrementPerRound", 5);
                    zombieCount = baseCount + ((round - 1) * incrementPerRound);
                }

                // 只在调试模式下输出详细日志
                if (plugin.getConfig().getBoolean("debug", false)) {
                    plugin.getLogger().info("游戏 " + gameName + " 第 " + round + " 回合总僵尸数量: " + zombieCount);
                }
            } catch (Exception e) {
                plugin.getLogger().warning("无法获取游戏 " + gameName + " 第 " + round + " 回合的僵尸数量: " + e.getMessage());
            }
        }

        // 如果从配置中获取失败，使用默认值
        if (zombieCount <= 0) {
            // 默认值: 10 + (回合数 * 5)
            zombieCount = 10 + ((round - 1) * 5);
            // 只在调试模式下输出详细日志
            if (plugin.getConfig().getBoolean("debug", false)) {
                plugin.getLogger().info("使用默认值作为游戏 " + gameName + " 第 " + round + " 回合总僵尸数量: " + zombieCount);
            }
        }

        // 存储计算结果
        totalZombiesInRound.put(gameName, zombieCount);

        // 只有在剩余僵尸数量未初始化时才设置它
        if (!remainingZombiesInRound.containsKey(gameName)) {
            remainingZombiesInRound.put(gameName, zombieCount);
            if (plugin.getConfig().getBoolean("debug", false)) {
                plugin.getLogger().info("初始化游戏 " + gameName + " 的剩余僵尸数量为: " + zombieCount);
            }
        }

        return zombieCount;
    }

    /**
     * 检查实体是否是游戏实体
     *
     * @param entity 实体
     * @return 是否是游戏实体
     */
    private boolean isGameEntity(Entity entity) {
        // 跳过玩家
        if (entity instanceof Player) {
            return false;
        }

        // 检查是否是生物实体
        if (!(entity instanceof LivingEntity)) {
            return false;
        }

        // 检查是否有idcZombieEntity标记，如果有则视为游戏实体
        if (entity.hasMetadata("idcZombieEntity")) {
            return true;
        }

        // 检查是否有not_round_zombie标记，如果有则不计入回合生成的僵尸
        if (entity.hasMetadata("not_round_zombie")) {
            return false;
        }

        // 检查是否有游戏标记元数据
        if (entity.hasMetadata("gameEntity")) {
            return true;
        }

        // 检查是否是自定义NPC
        if (entity.hasMetadata("NPC")) {
            return true;
        }

        // 检查是否是自定义僵尸类型
        if (entity.hasMetadata("customZombieType")) {
            for (MetadataValue meta : entity.getMetadata("customZombieType")) {
                if (meta.getOwningPlugin().equals(plugin)) {
                    String value = meta.asString();
                    // 检查是否是idc类型的实体
                    if (value.contains("idc")) {
                        // 添加idcZombieEntity标记，确保后续处理中被识别为僵尸
                        entity.setMetadata("idcZombieEntity", new FixedMetadataValue(plugin, true));
                        return true;
                    }
                }
            }
            return true;
        }

        // 额外检查：实体名称是否有特定前缀或包含特定字符串
        if (entity.getCustomName() != null) {
            String customName = entity.getCustomName();
            // 检查是否是游戏实体的命名格式
            if (customName.startsWith("游戏_")) {
                return true;
            }

            // 检查是否包含id或idc标识
            if (customName.contains("id") || customName.contains("idc")) {
                // 如果是idc类型，添加idcZombieEntity标记
                if (customName.contains("idc")) {
                    entity.setMetadata("idcZombieEntity", new FixedMetadataValue(plugin, true));
                }
                return true;
            }

            // 检查是否包含zombie或僵尸标识
            if (customName.contains("zombie") || customName.contains("僵尸")) {
                return true;
            }
        }

        return false;
    }

    /**
     * 移除玩家的计分板
     *
     * @param player 玩家
     */
    public void removePlayerScoreboard(Player player) {
        if (playerScoreboards.containsKey(player.getUniqueId())) {
            player.setScoreboard(Bukkit.getScoreboardManager().getNewScoreboard());
            playerScoreboards.remove(player.getUniqueId());
        }
    }

    /**
     * 启动游戏倒计时
     *
     * @param gameName 游戏名称
     * @param seconds 倒计时秒数
     */
    public void startGameCountdown(String gameName, int seconds) {
        gameCountdowns.put(gameName, seconds);
    }

    /**
     * 重置游戏倒计时 当玩家离开游戏导致人数不足时调用此方法
     *
     * @param gameName 游戏名称
     */
    public void resetGameCountdown(String gameName) {
        // 移除倒计时，这样在updateTimers方法中会重新检查玩家数量
        gameCountdowns.remove(gameName);
        plugin.getLogger().info("已重置游戏 '" + gameName + "' 的倒计时，等待玩家数量满足要求后重新开始");
    }

    /**
     * 重置游戏计时器和僵尸数量（不包括游戏开始时间）
     *
     * @param gameName 游戏名称
     */
    public void resetGameTimers(String gameName) {
        // 重置计时器
        gameCountdowns.remove(gameName);
        roundTimers.remove(gameName);
        currentRounds.remove(gameName);
        totalRounds.remove(gameName);

        // 重置僵尸数量相关的Map
        totalZombiesInRound.remove(gameName);
        remainingZombiesInRound.remove(gameName);

        // 注意：不再在这里清理游戏开始时间，使用clearGameStartTime()方法单独清理
        // 这样可以确保在统计数据保存完成后再清理游戏开始时间

        // 清理结束标记，允许游戏重新开始
        endedGames.remove(gameName);

        // 清理游戏结束计分板标记
        gameEndScoreboardGames.remove(gameName);

        plugin.getLogger().info("已重置游戏 " + gameName + " 的计时器和僵尸数量数据");
    }

    /**
     * 清理游戏开始时间（在统计数据保存完成后调用）
     *
     * @param gameName 游戏名称
     */
    public void clearGameStartTime(String gameName) {
        gameStartTimes.remove(gameName);
        if (plugin.getConfig().getBoolean("debug", false)) {
            plugin.getLogger().info("已清理游戏 " + gameName + " 的开始时间记录");
        }
    }

    /**
     * 停止游戏计时器（不清理僵尸数量数据）
     * 用于在游戏结束时立即停止计时器，防止计时器干扰游戏结束逻辑
     *
     * @param gameName 游戏名称
     */
    public void stopGameTimers(String gameName) {
        // 停止计时器
        gameCountdowns.remove(gameName);
        roundTimers.remove(gameName);

        // 标记游戏已结束，防止继续处理回合逻辑
        markGameAsEnded(gameName);

        plugin.getLogger().info("已停止游戏 " + gameName + " 的计时器");
    }

    // 存储已结束的游戏，防止继续处理回合逻辑
    private final Map<String, Boolean> endedGames = new HashMap<>();

    // 存储显示游戏结束计分板的游戏，防止定时更新覆盖
    private final Map<String, Boolean> gameEndScoreboardGames = new HashMap<>();

    // 存储游戏开始时间，用于计算游戏耗时
    private final Map<String, Long> gameStartTimes = new HashMap<>();

    /**
     * 标记游戏已结束
     *
     * @param gameName 游戏名称
     */
    private void markGameAsEnded(String gameName) {
        endedGames.put(gameName, true);
        plugin.getLogger().info("已标记游戏 " + gameName + " 为结束状态");
    }

    /**
     * 检查游戏是否已结束
     *
     * @param gameName 游戏名称
     * @return 是否已结束
     */
    public boolean isGameEnded(String gameName) {
        return endedGames.getOrDefault(gameName, false);
    }

    /**
     * 标记游戏正在显示结束计分板
     *
     * @param gameName 游戏名称
     */
    public void markGameShowingEndScoreboard(String gameName) {
        gameEndScoreboardGames.put(gameName, true);
        plugin.getLogger().info("已标记游戏 " + gameName + " 正在显示结束计分板");
    }

    /**
     * 检查游戏是否正在显示结束计分板
     *
     * @param gameName 游戏名称
     * @return 是否正在显示结束计分板
     */
    public boolean isShowingGameEndScoreboard(String gameName) {
        return gameEndScoreboardGames.getOrDefault(gameName, false);
    }

    /**
     * 清除游戏结束计分板标记
     *
     * @param gameName 游戏名称
     */
    public void clearGameEndScoreboardMark(String gameName) {
        gameEndScoreboardGames.remove(gameName);
        plugin.getLogger().info("已清除游戏 " + gameName + " 的结束计分板标记");
    }

    /**
     * 更新指定游戏的僵尸数量显示
     *
     * @param gameName 游戏名称
     */
    public void updateZombieCountDisplay(String gameName) {
        // 检查是否正在显示游戏结束计分板，如果是则不更新
        if (isShowingGameEndScoreboard(gameName)) {
            return;
        }

        // 获取该游戏的所有参与者
        Set<UUID> participants = gameSessionManager.getGameParticipants(gameName);
        if (participants == null || participants.isEmpty()) {
            return;
        }

        // 为所有游戏玩家更新计分板
        for (UUID uuid : participants) {
            Player player = Bukkit.getPlayer(uuid);
            if (player != null && player.isOnline()) {
                // 只有当玩家状态为IN_GAME且游戏状态为RUNNING时才更新
                PlayerStatus status = playerManager.getPlayerStatus(player);
                GameState gameState = gameSessionManager.getGameState(gameName);

                if (status == PlayerStatus.IN_GAME && gameState == GameState.RUNNING) {
                    updatePlayerScoreboard(player);
                }
            }
        }
    }

    /**
     * 停止所有计分板任务并清理资源
     */
    public void shutdown() {
        if (updateTask != null && !updateTask.isCancelled()) {
            updateTask.cancel();
        }

        // 移除所有玩家的计分板
        for (Player player : Bukkit.getOnlinePlayers()) {
            removePlayerScoreboard(player);
        }

        playerScoreboards.clear();
        gameCountdowns.clear();
        roundTimers.clear();
        currentRounds.clear();
        totalRounds.clear();
        totalZombiesInRound.clear();
        remainingZombiesInRound.clear();
        endedGames.clear();
        gameEndScoreboardGames.clear();
        gameStartTimes.clear();
    }

    /**
     * 设置游戏开始时间
     *
     * @param gameName 游戏名称
     * @param startTime 开始时间（毫秒时间戳）
     */
    public void setGameStartTime(String gameName, long startTime) {
        gameStartTimes.put(gameName, startTime);
        // 始终输出调试信息，不受debug配置限制，便于追踪问题
        plugin.getLogger().info("已设置游戏 " + gameName + " 的开始时间: " + startTime + ", 当前gameStartTimes大小: " + gameStartTimes.size());
        plugin.getLogger().info("当前gameStartTimes包含的游戏: " + gameStartTimes.keySet());
    }

    /**
     * 获取游戏耗时的格式化字符串
     *
     * @param gameName 游戏名称
     * @return 格式化的游戏耗时字符串
     */
    public String getGameDuration(String gameName) {
        // 始终输出调试信息，不受debug配置限制，便于追踪问题
        plugin.getLogger().info("获取游戏 " + gameName + " 的耗时，当前gameStartTimes包含的游戏: " + gameStartTimes.keySet());

        if (!gameStartTimes.containsKey(gameName)) {
            plugin.getLogger().warning("游戏 " + gameName + " 没有开始时间记录，返回默认值 00:00");
            return "00:00";
        }

        long startTime = gameStartTimes.get(gameName);
        long currentTime = System.currentTimeMillis();
        long durationMillis = currentTime - startTime;

        // 转换为秒
        long durationSeconds = durationMillis / 1000;

        // 计算分钟和秒
        long minutes = durationSeconds / 60;
        long seconds = durationSeconds % 60;

        // 格式化为 MM:SS
        String formattedDuration = String.format("%02d:%02d", minutes, seconds);

        plugin.getLogger().info("游戏 " + gameName + " 耗时: " + formattedDuration + " (开始时间: " + startTime + ", 当前时间: " + currentTime + ", 耗时毫秒: " + durationMillis + ")");

        return formattedDuration;
    }
}
