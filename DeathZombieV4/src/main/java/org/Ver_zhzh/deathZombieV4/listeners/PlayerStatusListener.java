package org.Ver_zhzh.deathZombieV4.listeners;

import java.util.Set;
import java.util.UUID;

import org.Ver_zhzh.deathZombieV4.DeathZombieV4;
import org.Ver_zhzh.deathZombieV4.game.GameSessionManager.GameState;
import org.Ver_zhzh.deathZombieV4.utils.PlayerInteractionManager;
import org.Ver_zhzh.deathZombieV4.enums.PlayerStatus;
import org.bukkit.ChatColor;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.player.PlayerJoinEvent;
import org.bukkit.event.player.PlayerQuitEvent;

/**
 * 处理玩家状态变化的监听器
 */
public class PlayerStatusListener implements Listener {

    private final DeathZombieV4 plugin;
    private final PlayerInteractionManager playerManager;

    public PlayerStatusListener(DeathZombieV4 plugin) {
        this.plugin = plugin;
        this.playerManager = plugin.getPlayerInteractionManager();
    }

    /**
     * 处理玩家加入服务器事件
     *
     * @param event 玩家加入事件
     */
    @EventHandler
    public void onPlayerJoin(PlayerJoinEvent event) {
        Player player = event.getPlayer();

        // 检查玩家是否有保存的状态
        PlayerStatus status = playerManager.getPlayerStatus(player);

        // 如果玩家状态是等待、游戏中或暂时离开，尝试恢复游戏会话
        if (status == PlayerStatus.WAITING || status == PlayerStatus.IN_GAME || status == PlayerStatus.TEMPORARILY_LEFT) {
            // 检查玩家之前的游戏是否存在且已启用
            String gameName = plugin.getGameSessionManager().getPlayerGame(player);

            if (gameName != null && plugin.getGameManager().isGameEnabled(gameName)) {
                // 恢复玩家的游戏会话
                plugin.getLogger().info("正在尝试恢复玩家 " + player.getName() + " 的游戏会话: " + gameName);

                // 根据状态决定是加入等待队列还是重新连接到游戏
                if (status == PlayerStatus.WAITING) {
                    boolean success = plugin.getGameSessionManager().addPlayerToGame(player, gameName);
                    if (success) {
                        player.sendMessage("§a已恢复你在游戏 " + gameName + " 中的等待状态！");
                    } else {
                        // 如果无法恢复，重置为未加入状态
                        playerManager.setPlayerStatus(player, PlayerStatus.NOT_JOINED, true);
                        player.sendMessage("§c无法恢复你的游戏会话，请重新加入游戏。");
                    }
                } else if (status == PlayerStatus.TEMPORARILY_LEFT) {
                    // 如果是暂时离开状态，尝试重新连接到游戏
                    boolean success = plugin.getGameSessionManager().rejoinPlayerToGame(player, gameName);
                    if (success) {
                        player.sendMessage("§a已自动重新连接到游戏 " + gameName + "！");
                        player.sendMessage("§e你可以使用 /dzs rejoin 命令手动重新连接。");
                    } else {
                        // 如果无法重新连接，尝试加入等待队列
                        boolean fallbackSuccess = plugin.getGameSessionManager().addPlayerToGame(player, gameName);
                        if (fallbackSuccess) {
                            player.sendMessage("§e无法直接重新连接到游戏，已将你加入等待队列。");
                        } else {
                            // 如果都无法恢复，重置为未加入状态
                            playerManager.setPlayerStatus(player, PlayerStatus.NOT_JOINED, true);
                            player.sendMessage("§c无法恢复你的游戏会话，请重新加入游戏。");
                        }
                    }
                } else {
                    // 如果是IN_GAME状态，这里应该处理重新连接到进行中的游戏
                    // 暂时简单处理为加入等待队列
                    boolean success = plugin.getGameSessionManager().addPlayerToGame(player, gameName);
                    if (success) {
                        player.sendMessage("§a已恢复你在游戏 " + gameName + " 中的状态！");

                        // 添加发光效果
                        player.setGlowing(true);
                    } else {
                        // 如果无法恢复，重置为未加入状态
                        playerManager.setPlayerStatus(player, PlayerStatus.NOT_JOINED, true);
                        player.sendMessage("§c无法恢复你的游戏会话，请重新加入游戏。");
                    }
                }
            } else {
                // 游戏不存在或未启用，重置玩家状态
                playerManager.setPlayerStatus(player, PlayerStatus.NOT_JOINED, true);
                player.sendMessage("§c你之前参与的游戏已不可用，已重置你的状态。");
            }
        } else {
            // 确保新玩家状态为未加入
            playerManager.setPlayerStatus(player, PlayerStatus.NOT_JOINED, true);
        }
    }

    /**
     * 处理玩家离开服务器事件
     *
     * @param event 玩家离开事件
     */
    @EventHandler
    public void onPlayerQuit(PlayerQuitEvent event) {
        Player player = event.getPlayer();

        // 保存玩家当前状态，不清除状态，以便下次登录时恢复
        if (playerManager.isPlayerInStatus(player, PlayerStatus.WAITING)
                || playerManager.isPlayerInStatus(player, PlayerStatus.IN_GAME)) {

            // 如果玩家在游戏中，将状态设置为暂时离开
            if (playerManager.isPlayerInStatus(player, PlayerStatus.IN_GAME)) {
                playerManager.setPlayerStatus(player, PlayerStatus.TEMPORARILY_LEFT, true);
                plugin.getLogger().info("玩家 " + player.getName() + " 在游戏中离开服务器，状态设置为暂时离开");
            } else {
                // 如果玩家在等待状态，保持原状态
                playerManager.setPlayerStatus(player, playerManager.getPlayerStatus(player), true);
                plugin.getLogger().info("已保存玩家 " + player.getName() + " 的状态: " + playerManager.getPlayerStatus(player));
            }

            // 清理玩家的幸运箱资源
            if (plugin.getLuckyBoxManager() != null) {
                plugin.getLuckyBoxManager().onPlayerLeaveGame(player);
            }

            // 获取玩家当前所在的游戏
            String gameName = plugin.getGameSessionManager().getPlayerGame(player);
            if (gameName != null) {
                // 获取游戏参与者
                Set<UUID> participants = plugin.getGameSessionManager().getGameParticipants(gameName);

                // 获取游戏最小玩家数
                int minPlayers = plugin.getGameManager().getMinPlayers(gameName);

                // 检查配置项是否允许在人数不足时结束游戏
                boolean endGameWhenBelowMin = plugin.getConfig().getBoolean("game.end_game_when_below_min_players", true);

                // 玩家即将离开，所以检查剩余人数-1是否低于最小玩家数
                if (endGameWhenBelowMin && participants.size() - 1 < minPlayers
                        && plugin.getGameSessionManager().getGameState(gameName) == GameState.RUNNING) {
                    // 如果人数不足，结束游戏
                    plugin.getLogger().info("玩家 " + player.getName() + " 离开后，游戏 " + gameName
                            + " 人数不足最小启动人数 " + minPlayers + "，将结束游戏");

                    // 结束游戏并清理资源
                    plugin.getZombieSpawnManager().endGameAndCleanup(gameName);

                    // 向所有在线玩家广播游戏结束消息
                    for (Player p : plugin.getServer().getOnlinePlayers()) {
                        p.sendMessage(ChatColor.RED + "游戏 " + gameName + " 因玩家离开导致人数不足而结束");
                    }
                }
            }
        } else {
            // 如果玩家未加入任何游戏，可以清除状态
            playerManager.clearPlayerStatus(player, true);
        }

        // 注意：不再从游戏会话中移除玩家，以便下次登录时恢复
        // 如果确实需要从游戏中移除玩家（例如游戏已满），应该在玩家尝试恢复会话时处理
    }
}
