package org.Ver_zhzh.deathZombieV4.listeners;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

import org.Ver_zhzh.deathZombieV4.DeathZombieV4;
import org.Ver_zhzh.deathZombieV4.game.LuckyBoxManager;
import org.Ver_zhzh.deathZombieV4.utils.PlayerInteractionManager;
import org.bukkit.ChatColor;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.Sound;
import org.bukkit.block.Block;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.block.Action;
import org.bukkit.event.block.BlockBreakEvent;
import org.bukkit.event.player.PlayerInteractEvent;

/**
 * 幸运箱交互监听器 处理玩家与幸运箱的交互和设置
 */
public class LuckyBoxInteractionListener implements Listener {

    private final DeathZombieV4 plugin;
    private final LuckyBoxManager luckyBoxManager;
    private final PlayerInteractionManager playerInteractionManager;

    // 存储玩家第一次选择的箱子位置
    private final Map<UUID, Location> firstSelectedChest = new HashMap<>();

    public LuckyBoxInteractionListener(DeathZombieV4 plugin) {
        this.plugin = plugin;
        this.luckyBoxManager = plugin.getLuckyBoxManager();
        this.playerInteractionManager = plugin.getPlayerInteractionManager();
    }

    /**
     * 处理玩家右键点击事件
     */
    @EventHandler(priority = EventPriority.HIGHEST)
    public void onPlayerInteract(PlayerInteractEvent event) {
        Player player = event.getPlayer();

        // 检查是否正在设置幸运箱
        String settingLuckyBox = playerInteractionManager.getPlayerData(player, "setting_lucky_box");

        // 如果玩家正在设置幸运箱
        if (settingLuckyBox != null && settingLuckyBox.equals("true")) {
            // 只处理箱子方块的交互
            if (event.getClickedBlock() != null
                    && (event.getClickedBlock().getType() == Material.CHEST
                    || event.getClickedBlock().getType() == Material.TRAPPED_CHEST)) {

                // 右键点击，选择第一个箱子
                if (event.getAction() == Action.RIGHT_CLICK_BLOCK) {
                    event.setCancelled(true); // 取消原始交互

                    // 记录第一个位置
                    firstSelectedChest.put(player.getUniqueId(), event.getClickedBlock().getLocation());

                    player.sendMessage(ChatColor.GREEN + "已选择第一个箱子，请左键点击第二个箱子来创建大箱子，或再次左键同一个箱子来创建小箱子。");
                    return;
                }
            }
        }

        // 以下是普通游戏中的交互处理
        if (event.getAction() != Action.RIGHT_CLICK_BLOCK || event.getClickedBlock() == null) {
            return;
        }

        Block clickedBlock = event.getClickedBlock();

        // 检查是否点击的是箱子（普通箱子或陷阱箱）
        if (clickedBlock.getType() != Material.CHEST && clickedBlock.getType() != Material.TRAPPED_CHEST) {
            return;
        }

        // 获取玩家所在的游戏
        String gameName = plugin.getGameSessionManager().getPlayerGame(player);
        if (gameName == null) {
            return;
        }

        // 检查玩家是否在游戏中
        if (!plugin.getGameSessionManager().isPlayerInGame(player, gameName)) {
            return;
        }

        // 尝试处理幸运箱交互
        if (luckyBoxManager.handleLuckyBoxInteraction(player, clickedBlock.getLocation(), gameName)) {
            // 取消事件，防止打开箱子界面
            event.setCancelled(true);

            // 播放箱子打开声音，但不显示GUI
            player.playSound(clickedBlock.getLocation(), Sound.BLOCK_CHEST_OPEN, 0.8f, 1.0f);

            // 播放箱子打开动画
            playChestOpenAnimation(clickedBlock);
        }
    }

    /**
     * 播放箱子打开动画但不显示GUI
     *
     * @param block 箱子方块
     */
    private void playChestOpenAnimation(Block block) {
        if (block.getType() != Material.CHEST && block.getType() != Material.TRAPPED_CHEST) {
            return;
        }

        // 获取箱子数据
        org.bukkit.block.data.BlockData blockData = block.getBlockData();
        if (!(blockData instanceof org.bukkit.block.data.type.Chest)) {
            return;
        }

        org.bukkit.block.data.type.Chest chestData = (org.bukkit.block.data.type.Chest) blockData;

        try {
            // 获取箱子状态
            org.bukkit.block.Chest chest = (org.bukkit.block.Chest) block.getState();

            // 打开箱子动画（使用BlockState的isOpen方法）
            if (chest.getBlockData() instanceof org.bukkit.block.data.Openable) {
                org.bukkit.block.data.Openable openable = (org.bukkit.block.data.Openable) chest.getBlockData();
                openable.setOpen(true);
                chest.setBlockData(openable);
                chest.update(true, false);

                // 关闭箱子动画（延迟5秒）
                plugin.getServer().getScheduler().runTaskLater(plugin, () -> {
                    if (block.getState() instanceof org.bukkit.block.Chest) {
                        org.bukkit.block.Chest updatedChest = (org.bukkit.block.Chest) block.getState();
                        if (updatedChest.getBlockData() instanceof org.bukkit.block.data.Openable) {
                            org.bukkit.block.data.Openable updatedOpenable = (org.bukkit.block.data.Openable) updatedChest.getBlockData();
                            updatedOpenable.setOpen(false);
                            updatedChest.setBlockData(updatedOpenable);
                            updatedChest.update(true, false);

                            // 播放箱子关闭声音
                            block.getWorld().playSound(block.getLocation(), Sound.BLOCK_CHEST_CLOSE, 0.5f, 1.0f);
                        }
                    }
                }, 100L); // 5秒后关闭
            }
        } catch (Exception e) {
            plugin.getLogger().warning("播放箱子动画失败: " + e.getMessage());
        }
    }

    /**
     * 处理方块破坏事件，用于设置幸运箱位置
     */
    @EventHandler
    public void onBlockBreak(BlockBreakEvent event) {
        Player player = event.getPlayer();
        Block brokenBlock = event.getBlock();

        // 只处理箱子方块的破坏
        if (brokenBlock.getType() != Material.CHEST && brokenBlock.getType() != Material.TRAPPED_CHEST) {
            // 如果不是箱子方块，检查玩家数据以确定是否取消事件
            if (playerInteractionManager.getPlayerData(player, "setting_lucky_box") != null
                    && playerInteractionManager.getPlayerData(player, "setting_lucky_box").equals("true")) {
                player.sendMessage(ChatColor.RED + "请破坏一个箱子方块来设置幸运箱!");
                event.setCancelled(true);
            }
            return;
        }

        // 检查玩家是否正在设置幸运箱
        String settingLuckyBox = playerInteractionManager.getPlayerData(player, "setting_lucky_box");
        String settingLuckyBoxPosition = playerInteractionManager.getPlayerData(player, "setting_lucky_box_position");

        // 如果玩家正在设置幸运箱（新的大箱子设置逻辑）
        if (settingLuckyBox != null && settingLuckyBox.equals("true")) {
            // 取消事件，防止箱子被破坏
            event.setCancelled(true);

            // 检查是否有第一个选择的箱子
            Location firstLocation = firstSelectedChest.get(player.getUniqueId());
            Location currentLocation = brokenBlock.getLocation();

            if (firstLocation != null) {
                // 检查两个位置是否在同一世界
                if (!firstLocation.getWorld().equals(currentLocation.getWorld())) {
                    player.sendMessage(ChatColor.RED + "两个箱子必须在同一个世界！");
                    return;
                }

                // 检查两个箱子是否相邻（用于创建大箱子）
                boolean areAdjacent = areLocationsAdjacent(firstLocation, currentLocation);

                // 如果位置相同，创建小箱子
                if (areLocationsEqual(firstLocation, currentLocation)) {
                    player.sendMessage(ChatColor.GREEN + "选择了同一个箱子，将创建单个幸运箱。");
                    handleLuckyBoxSetting(event, player, firstLocation); // 使用第一个位置创建单个箱子
                } // 如果位置相邻，创建大箱子
                else if (areAdjacent) {
                    player.sendMessage(ChatColor.GREEN + "选择了相邻的箱子，将创建大幸运箱。");
                    // 计算大箱子的中心位置
                    Location centerLocation = new Location(
                            firstLocation.getWorld(),
                            (firstLocation.getX() + currentLocation.getX()) / 2,
                            (firstLocation.getY() + currentLocation.getY()) / 2,
                            (firstLocation.getZ() + currentLocation.getZ()) / 2
                    );
                    handleLuckyBoxSetting(event, player, centerLocation); // 使用中心位置创建大箱子
                } else {
                    player.sendMessage(ChatColor.RED + "两个箱子必须相邻才能创建大箱子！");
                }

                // 清除第一个选择
                firstSelectedChest.remove(player.getUniqueId());
            } else {
                // 没有第一个选择，记录当前位置作为第一个选择
                firstSelectedChest.put(player.getUniqueId(), currentLocation);
                player.sendMessage(ChatColor.YELLOW + "已选择第一个箱子位置，请再次左键点击另一个箱子来创建大箱子，或再次左键同一个箱子来创建小箱子。");
            }
        } else if (settingLuckyBoxPosition != null && settingLuckyBoxPosition.equals("true")) {
            // 玩家正在设置现有幸运箱的位置
            handleLuckyBoxPositionSetting(event, player);
        }
    }

    /**
     * 检查两个位置是否相邻
     */
    private boolean areLocationsAdjacent(Location loc1, Location loc2) {
        // 检查是否在同一高度
        if (loc1.getBlockY() != loc2.getBlockY()) {
            return false;
        }

        // 计算x和z的距离
        int xDiff = Math.abs(loc1.getBlockX() - loc2.getBlockX());
        int zDiff = Math.abs(loc1.getBlockZ() - loc2.getBlockZ());

        // 如果x相同，则z相差1；如果z相同，则x相差1
        return (xDiff == 0 && zDiff == 1) || (zDiff == 0 && xDiff == 1);
    }

    /**
     * 检查两个位置是否相同
     */
    private boolean areLocationsEqual(Location loc1, Location loc2) {
        return loc1.getBlockX() == loc2.getBlockX()
                && loc1.getBlockY() == loc2.getBlockY()
                && loc1.getBlockZ() == loc2.getBlockZ();
    }

    /**
     * 处理幸运箱设置
     */
    private void handleLuckyBoxSetting(BlockBreakEvent event, Player player) {
        handleLuckyBoxSetting(event, player, event.getBlock().getLocation());
    }

    /**
     * 处理幸运箱设置（使用指定位置）
     */
    private void handleLuckyBoxSetting(BlockBreakEvent event, Player player, Location location) {
        // 取消破坏事件
        event.setCancelled(true);

        // 获取设置信息
        String gameName = playerInteractionManager.getEditingGame(player);
        String boxName = playerInteractionManager.getPlayerData(player, "lucky_box_name");
        String boxType = playerInteractionManager.getPlayerData(player, "lucky_box_type");
        String roundStr = playerInteractionManager.getPlayerData(player, "lucky_box_round");
        String costStr = playerInteractionManager.getPlayerData(player, "lucky_box_cost");

        if (gameName == null || boxName == null || boxType == null || roundStr == null || costStr == null) {
            player.sendMessage(ChatColor.RED + "设置信息不完整，请重新执行命令！");
            clearLuckyBoxSetting(player);
            return;
        }

        try {
            int openRound = Integer.parseInt(roundStr);
            int openCost = Integer.parseInt(costStr);

            // 添加幸运箱 - 使用新的方法传递箱子名称
            if (luckyBoxManager.setLuckyBoxByInteraction(gameName, boxName, location, boxType, openRound, openCost)) {
                player.sendMessage(ChatColor.GREEN + "成功设置幸运箱 '" + boxName + "'！");
                player.sendMessage(ChatColor.YELLOW + "位置: " + formatLocation(location));
                player.sendMessage(ChatColor.YELLOW + "类型: " + boxType + ", 开启回合: " + openRound + ", 消耗金钱: " + openCost);
            } else {
                player.sendMessage(ChatColor.RED + "设置幸运箱失败！");
            }

        } catch (NumberFormatException e) {
            player.sendMessage(ChatColor.RED + "设置信息格式错误！");
        }

        // 清除设置状态
        clearLuckyBoxSetting(player);
    }

    /**
     * 处理幸运箱位置设置
     */
    private void handleLuckyBoxPositionSetting(BlockBreakEvent event, Player player) {
        // 取消破坏事件
        event.setCancelled(true);

        // 获取设置信息
        String gameName = playerInteractionManager.getEditingGame(player);
        String boxId = playerInteractionManager.getPlayerData(player, "lucky_box_id");

        if (gameName == null || boxId == null) {
            player.sendMessage(ChatColor.RED + "设置信息不完整，请重新执行命令！");
            clearLuckyBoxPositionSetting(player);
            return;
        }

        Location location = event.getBlock().getLocation();

        // 设置幸运箱位置
        if (luckyBoxManager.setLuckyBoxLocation(gameName, boxId, location)) {
            player.sendMessage(ChatColor.GREEN + "成功设置幸运箱 " + boxId + " 的位置！");
            player.sendMessage(ChatColor.YELLOW + "位置: " + formatLocation(location));
        } else {
            player.sendMessage(ChatColor.RED + "设置幸运箱位置失败！");
        }

        // 清除设置状态
        clearLuckyBoxPositionSetting(player);
    }

    /**
     * 清除幸运箱设置状态
     */
    private void clearLuckyBoxSetting(Player player) {
        playerInteractionManager.clearPlayerData(player, "setting_lucky_box");
        playerInteractionManager.clearPlayerData(player, "lucky_box_name");
        playerInteractionManager.clearPlayerData(player, "lucky_box_type");
        playerInteractionManager.clearPlayerData(player, "lucky_box_round");
        playerInteractionManager.clearPlayerData(player, "lucky_box_cost");
        playerInteractionManager.clearEditingGame(player);
        firstSelectedChest.remove(player.getUniqueId());
    }

    /**
     * 清除幸运箱位置设置状态
     */
    private void clearLuckyBoxPositionSetting(Player player) {
        playerInteractionManager.clearPlayerData(player, "setting_lucky_box_position");
        playerInteractionManager.clearPlayerData(player, "lucky_box_id");
        playerInteractionManager.clearEditingGame(player);
    }

    /**
     * 格式化位置信息
     */
    private String formatLocation(Location location) {
        return String.format("%s [%.1f, %.1f, %.1f]",
                location.getWorld().getName(),
                location.getX(),
                location.getY(),
                location.getZ());
    }
}
