package org.Ver_zhzh.deathZombieV4.listeners;

import java.util.UUID;

import org.Ver_zhzh.deathZombieV4.DeathZombieV4;
import org.Ver_zhzh.deathZombieV4.game.DoorManager;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.block.Action;
import org.bukkit.event.player.PlayerInteractEvent;

/**
 * 处理玩家与门的交互 注意：现在门的解锁主要通过NPC进行，此类仅处理自动解锁的门
 */
public class DoorInteractionListener implements Listener {

    private final DeathZombieV4 plugin;
    private final DoorManager doorManager;

    public DoorInteractionListener(DeathZombieV4 plugin, DoorManager doorManager) {
        this.plugin = plugin;
        this.doorManager = doorManager;
    }

    @EventHandler
    public void onPlayerInteract(PlayerInteractEvent event) {
        Player player = event.getPlayer();

        // 只处理右键点击方块的事件
        if (event.getAction() != Action.RIGHT_CLICK_BLOCK || event.getClickedBlock() == null) {
            return;
        }

        // 添加调试日志，只在调试模式下输出
        if (plugin.getConfig().getBoolean("debug", false)) {
            plugin.getLogger().info("玩家 " + player.getName() + " 右键点击了方块: " + event.getClickedBlock().getType()
                    + " 在位置: " + event.getClickedBlock().getLocation());
        }

        // 当前没有正在运行的游戏，直接返回
        String currentGame = getCurrentGame(player);
        if (currentGame == null) {
            if (plugin.getConfig().getBoolean("debug", false)) {
                plugin.getLogger().info("玩家 " + player.getName() + " 当前不在任何游戏中");
            }
            return;
        }

        // 检查玩家是否在游戏中
        if (!plugin.getGameSessionManager().isPlayerInGame(player, currentGame)) {
            if (plugin.getConfig().getBoolean("debug", false)) {
                plugin.getLogger().info("玩家 " + player.getName() + " 不在游戏 " + currentGame + " 中");
            }
            return;
        }

        // 检查点击的方块是否在门区域内
        String doorName = doorManager.getPlayerDoorRegion(player, currentGame);
        if (doorName == null) {
            // 玩家不在门区域内
            if (plugin.getConfig().getBoolean("debug", false)) {
                plugin.getLogger().info("玩家 " + player.getName() + " 不在任何门区域内");
            }
            return;
        }

        if (plugin.getConfig().getBoolean("debug", false)) {
            plugin.getLogger().info("玩家 " + player.getName() + " 在门区域 " + doorName + " 内");
        }

        // 检查门是否已解锁
        if (doorManager.isDoorUnlocked(currentGame, doorName)) {
            // 门已经解锁，不需要处理
            if (plugin.getConfig().getBoolean("debug", false)) {
                plugin.getLogger().info("门 " + doorName + " 已经解锁，不需要处理");
            }
            return;
        }

        // 获取门的解锁价格
        int unlockPrice = doorManager.getDoorUnlockPrice(currentGame, doorName);
        if (unlockPrice < 0) {
            plugin.getLogger().warning("找不到门 '" + doorName + "' 的配置信息！");
            player.sendMessage(ChatColor.RED + "找不到门 '" + doorName + "' 的配置信息！");
            event.setCancelled(true);
            return;
        }

        if (unlockPrice == 0) {
            // 自动解锁的门
            doorManager.setDoorUnlocked(currentGame, doorName, true);
            player.sendMessage(ChatColor.GREEN + "门 '" + doorName + "' 已自动解锁！");
            if (plugin.getConfig().getBoolean("debug", false)) {
                plugin.getLogger().info("门 " + doorName + " 已自动解锁");
            }
        } else {
            // 检查玩家是否有足够的金钱
            double playerMoney = plugin.getShootPluginHelper().getPlayerMoney(player);

            if (playerMoney < unlockPrice) {
                player.sendMessage(ChatColor.RED + "你没有足够的金钱解锁这个门！需要 " + unlockPrice + " 金币，你只有 " + (int) playerMoney + " 金币。");
                if (plugin.getConfig().getBoolean("debug", false)) {
                    plugin.getLogger().info("玩家 " + player.getName() + " 尝试解锁门，但金钱不足");
                }
                return;
            }

            // 扣除金钱
            plugin.getShootPluginHelper().subtractPlayerMoney(player, unlockPrice);

            // 解锁门
            doorManager.setDoorUnlocked(currentGame, doorName, true);

            // 记录统计数据
            plugin.getPlayerInteractionManager().addPlayerDoorsOpened(player, currentGame, 1);
            plugin.getPlayerInteractionManager().addPlayerMoneySpent(player, currentGame, unlockPrice);

            // 发送消息
            player.sendMessage(ChatColor.GREEN + "你花费了 " + ChatColor.GOLD + unlockPrice + ChatColor.GREEN + " 金币解锁了门 '" + doorName + "'！");
            if (plugin.getConfig().getBoolean("debug", false)) {
                plugin.getLogger().info("玩家 " + player.getName() + " 花费 " + unlockPrice + " 金币解锁了门 " + doorName);
            }

            // 广播消息给同一游戏中的其他玩家
            for (UUID playerId : plugin.getGameSessionManager().getGameParticipants(currentGame)) {
                Player p = Bukkit.getPlayer(playerId);
                if (p != null && p.isOnline() && !p.equals(player)) {
                    p.sendMessage(ChatColor.GREEN + player.getName() + " 解锁了门 '" + doorName + "'！");
                }
            }
        }

        // 取消原有的方块交互事件
        event.setCancelled(true);
    }

    /**
     * 获取玩家当前所在的游戏名称
     *
     * @param player 玩家
     * @return 游戏名称，如果不在游戏中则返回null
     */
    private String getCurrentGame(Player player) {
        // 这里需要根据实际的游戏管理系统来实现
        // 暂时假设有一个GameSession类或类似的系统来管理游戏会话
        return plugin.getGameSessionManager().getPlayerGame(player);
    }
}
