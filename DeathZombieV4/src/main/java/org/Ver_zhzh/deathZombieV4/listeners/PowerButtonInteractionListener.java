package org.Ver_zhzh.deathZombieV4.listeners;

import java.io.File;
import java.io.IOException;

import org.Ver_zhzh.deathZombieV4.DeathZombieV4;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.Location;
import org.bukkit.World;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.configuration.file.YamlConfiguration;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.block.Action;
import org.bukkit.event.player.PlayerInteractEvent;

/**
 * 处理玩家与全局电源按钮的交互
 */
public class PowerButtonInteractionListener implements Listener {

    private final DeathZombieV4 plugin;

    public PowerButtonInteractionListener(DeathZombieV4 plugin) {
        this.plugin = plugin;
    }

    @EventHandler
    public void onPlayerInteract(PlayerInteractEvent event) {
        Player player = event.getPlayer();

        // 只处理右键点击方块的事件
        if (event.getAction() != Action.RIGHT_CLICK_BLOCK || event.getClickedBlock() == null) {
            return;
        }

        // 当前没有正在运行的游戏，直接返回
        String currentGame = plugin.getGameSessionManager().getPlayerGame(player);
        if (currentGame == null) {
            return;
        }

        // 检查玩家是否在游戏中
        if (!plugin.getGameSessionManager().isPlayerInGame(player, currentGame)) {
            return;
        }

        // 获取游戏配置文件 - 修正路径从"games/"到"game/"
        File gameFile = new File(plugin.getDataFolder(), "game/" + currentGame + ".yml");
        if (!gameFile.exists()) {
            plugin.getLogger().warning("游戏配置文件不存在: " + gameFile.getAbsolutePath());
            return;
        }

        FileConfiguration gameConfig = YamlConfiguration.loadConfiguration(gameFile);

        // 检查是否有电源按钮配置
        if (!gameConfig.isConfigurationSection("power_button")) {
            return;
        }

        // 检查电源按钮是否已解锁
        boolean isUnlocked = gameConfig.getBoolean("power_button.unlocked", false);
        if (isUnlocked) {
            // 电源按钮已解锁，不需要处理
            return;
        }

        // 获取电源按钮位置
        String worldName = gameConfig.getString("power_button.world");
        double x = gameConfig.getDouble("power_button.x");
        double y = gameConfig.getDouble("power_button.y");
        double z = gameConfig.getDouble("power_button.z");

        if (worldName == null) {
            return;
        }

        World world = Bukkit.getWorld(worldName);
        if (world == null) {
            return;
        }

        Location buttonLocation = new Location(world, x, y, z);

        // 检查点击的方块是否是电源按钮
        if (!event.getClickedBlock().getLocation().equals(buttonLocation)) {
            return;
        }

        // 获取电源按钮解锁价格
        int unlockPrice = gameConfig.getInt("power_button.price", 1000);

        // 检查玩家是否有足够的金钱
        double playerMoney = plugin.getShootPluginHelper().getPlayerMoney(player);

        if (playerMoney < unlockPrice) {
            player.sendMessage(ChatColor.RED + "你没有足够的金钱解锁全局电源按钮！需要 " + unlockPrice + " 金币，你只有 " + (int) playerMoney + " 金币。");
            return;
        }

        // 扣除金钱
        plugin.getShootPluginHelper().subtractPlayerMoney(player, unlockPrice);

        // 解锁电源按钮
        gameConfig.set("power_button.unlocked", true);

        try {
            gameConfig.save(gameFile);

            // 发送消息
            player.sendMessage(ChatColor.GREEN + "你花费了 " + ChatColor.GOLD + unlockPrice + ChatColor.GREEN + " 金币解锁了全局电源按钮！");
            player.sendMessage(ChatColor.YELLOW + "现在所有玩家都可以购买增益效果了！");

            // 广播消息给同一游戏中的所有玩家
            for (Player p : Bukkit.getOnlinePlayers()) {
                if (p != player && plugin.getGameSessionManager().isPlayerInGame(p, currentGame)) {
                    p.sendMessage(ChatColor.GREEN + player.getName() + " 解锁了全局电源按钮！");
                    p.sendMessage(ChatColor.YELLOW + "现在你可以购买增益效果了！");
                }
            }

            // 更新全局电源按钮效果
            if (plugin.getPowerButtonEffectManager() != null) {
                plugin.getPowerButtonEffectManager().updatePowerButtonEffects(currentGame);
            }
        } catch (IOException e) {
            player.sendMessage(ChatColor.RED + "解锁电源按钮失败：" + e.getMessage());
            plugin.getLogger().severe("保存游戏配置文件失败: " + e.getMessage());
        }

        // 取消原有的方块交互事件
        event.setCancelled(true);
    }
}
