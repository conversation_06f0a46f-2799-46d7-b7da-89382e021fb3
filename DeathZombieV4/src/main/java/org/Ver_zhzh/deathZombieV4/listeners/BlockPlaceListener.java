package org.Ver_zhzh.deathZombieV4.listeners;

import org.Ver_zhzh.deathZombieV4.DeathZombieV4;
import org.Ver_zhzh.deathZombieV4.game.EquipmentConstants;
import org.Ver_zhzh.deathZombieV4.game.GameSessionManager;
import org.Ver_zhzh.deathZombieV4.enums.PlayerStatus;
import org.bukkit.ChatColor;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.block.BlockPlaceEvent;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;

/**
 * 监听玩家放置方块事件，阻止玩家放置特定物品
 */
public class BlockPlaceListener implements Listener {

    private final DeathZombieV4 plugin;
    private final GameSessionManager gameSessionManager;

    /**
     * 构造函数
     *
     * @param plugin 插件实例
     */
    public BlockPlaceListener(DeathZombieV4 plugin) {
        this.plugin = plugin;
        this.gameSessionManager = plugin.getGameSessionManager();
    }

    /**
     * 处理玩家放置方块事件
     *
     * @param event 玩家放置方块事件
     */
    @EventHandler(priority = EventPriority.HIGH)
    public void onBlockPlace(BlockPlaceEvent event) {
        Player player = event.getPlayer();

        // 检查玩家是否在游戏中
        if (!plugin.getPlayerInteractionManager().isPlayerInStatus(player, PlayerStatus.IN_GAME)) {
            return;
        }

        // 获取玩家所在的游戏
        String gameName = gameSessionManager.getPlayerGame(player);
        if (gameName == null) {
            return;
        }

        // 获取玩家手中的物品
        ItemStack itemInHand = event.getItemInHand();
        if (itemInHand == null || itemInHand.getType() == Material.AIR) {
            return;
        }

        // 检查是否是玻璃板（锁定槽位）
        if (itemInHand.getType() == Material.GLASS_PANE) {
            // 检查物品是否有名称
            if (itemInHand.hasItemMeta() && itemInHand.getItemMeta().hasDisplayName()) {
                ItemMeta meta = itemInHand.getItemMeta();
                String displayName = ChatColor.stripColor(meta.getDisplayName());

                // 如果是锁定槽位，阻止放置
                if (displayName.equals("锁定槽位") || displayName.equals(EquipmentConstants.SLOT_TYPE_LOCKED)) {
                    event.setCancelled(true);
                    player.sendMessage(ChatColor.RED + "你不能放置锁定槽位物品！");
                    return;
                }
            }
        }

        // 检查是否是白色染料（用于锁定槽位）
        if (itemInHand.getType() == Material.WHITE_DYE) {
            // 检查物品是否有名称
            if (itemInHand.hasItemMeta() && itemInHand.getItemMeta().hasDisplayName()) {
                ItemMeta meta = itemInHand.getItemMeta();
                String displayName = ChatColor.stripColor(meta.getDisplayName());

                // 如果是锁定槽位，阻止放置
                if (displayName.equals("锁定槽位") || displayName.equals("白色染料")) {
                    event.setCancelled(true);
                    player.sendMessage(ChatColor.RED + "你不能放置锁定槽位物品！");
                    return;
                }
            }
        }
    }
}
