package org.Ver_zhzh.deathZombieV4.listeners;

import org.Ver_zhzh.deathZombieV4.DeathZombieV4;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.player.PlayerJoinEvent;

/**
 * 玩家显示设置同步监听器
 * 负责在玩家加入游戏时同步显示设置到Shoot插件
 */
public class PlayerDisplaySyncListener implements Listener {

    private final DeathZombieV4 plugin;

    public PlayerDisplaySyncListener(DeathZombieV4 plugin) {
        this.plugin = plugin;
    }

    /**
     * 玩家加入事件处理
     * 延迟同步玩家的显示设置到Shoot插件
     *
     * @param event 玩家加入事件
     */
    @EventHandler(priority = EventPriority.MONITOR)
    public void onPlayerJoin(PlayerJoinEvent event) {
        Player player = event.getPlayer();
        
        // 延迟同步显示设置，确保Shoot插件已完全处理玩家加入
        plugin.getServer().getScheduler().runTaskLater(plugin, () -> {
            if (plugin.getPlayerDisplaySettingsManager() != null) {
                plugin.getPlayerDisplaySettingsManager().syncPlayerToShoot(player);
                plugin.getLogger().info("已为新加入的玩家 " + player.getName() + " 同步显示设置到Shoot插件");
            }
        }, 40L); // 延迟2秒，确保Shoot插件已处理完玩家加入逻辑
    }
}
