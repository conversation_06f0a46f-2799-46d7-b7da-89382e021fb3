package org.Ver_zhzh.deathZombieV4.listeners;

import org.Ver_zhzh.deathZombieV4.DeathZombieV4;
import org.Ver_zhzh.deathZombieV4.game.GameSessionManager;
import org.Ver_zhzh.deathZombieV4.game.WindowManager;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.player.PlayerToggleSneakEvent;
import org.bukkit.scheduler.BukkitRunnable;

/**
 * 监听玩家修复窗户的交互
 */
public class WindowRepairListener implements Listener {

    private final DeathZombieV4 plugin;
    private final GameSessionManager gameSessionManager;
    private final WindowManager windowManager;

    /**
     * 构造函数
     *
     * @param plugin 插件实例
     */
    public WindowRepairListener(DeathZombieV4 plugin) {
        this.plugin = plugin;
        this.gameSessionManager = plugin.getGameSessionManager();
        this.windowManager = plugin.getWindowManager();
    }

    /**
     * 监听玩家蹲下事件
     *
     * @param event 玩家蹲下事件
     */
    @EventHandler
    public void onPlayerSneak(PlayerToggleSneakEvent event) {
        Player player = event.getPlayer();

        // 只处理玩家开始蹲下的事件
        if (!event.isSneaking()) {
            return;
        }

        // 检查玩家是否在游戏中
        String gameName = gameSessionManager.getPlayerGame(player);
        if (gameName == null || gameSessionManager.getGameState(gameName) != GameSessionManager.GameState.RUNNING) {
            return;
        }

        // 创建一个持续检测玩家蹲下状态的任务
        new BukkitRunnable() {
            int ticks = 0;

            @Override
            public void run() {
                // 如果玩家不再蹲下或离线，取消任务
                if (!player.isOnline() || !player.isSneaking()) {
                    this.cancel();
                    return;
                }

                // 每10ticks (0.5秒) 尝试修复一次窗户
                if (ticks % 10 == 0) {
                    windowManager.handlePlayerRepairWindow(player, gameName);
                }

                ticks++;

                // 最多持续100ticks (5秒)
                if (ticks >= 100) {
                    this.cancel();
                }
            }
        }.runTaskTimer(plugin, 0L, 1L);
    }
}
