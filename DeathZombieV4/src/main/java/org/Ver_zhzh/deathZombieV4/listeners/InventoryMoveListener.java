package org.Ver_zhzh.deathZombieV4.listeners;

import org.Ver_zhzh.deathZombieV4.DeathZombieV4;
import org.Ver_zhzh.deathZombieV4.game.GameSessionManager;
import org.Ver_zhzh.deathZombieV4.utils.PlayerInteractionManager;
import org.Ver_zhzh.deathZombieV4.enums.PlayerStatus;
import org.bukkit.ChatColor;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.event.inventory.InventoryDragEvent;
import org.bukkit.event.player.PlayerDropItemEvent;
import org.bukkit.event.player.PlayerSwapHandItemsEvent;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;

import java.util.List;

/**
 * 处理玩家物品栏移动限制的监听器
 */
public class InventoryMoveListener implements Listener {

    private final DeathZombieV4 plugin;
    private final PlayerInteractionManager playerManager;
    private final GameSessionManager gameSessionManager;

    public InventoryMoveListener(DeathZombieV4 plugin) {
        this.plugin = plugin;
        this.playerManager = plugin.getPlayerInteractionManager();
        this.gameSessionManager = plugin.getGameSessionManager();
    }

    /**
     * 检查是否启用错误提示消息
     *
     * @return 如果启用错误提示则返回true，否则返回false
     */
    private boolean isErrorMessageEnabled() {
        return plugin.getConfig().getBoolean("error_messages.enabled", true);
    }

    /**
     * 处理物品栏点击事件
     *
     * @param event 物品栏点击事件
     */
    @EventHandler(priority = EventPriority.HIGH)
    public void onInventoryClick(InventoryClickEvent event) {
        if (!(event.getWhoClicked() instanceof Player)) {
            return;
        }

        Player player = (Player) event.getWhoClicked();

        // 检查玩家是否在游戏中
        if (!playerManager.isPlayerInStatus(player, PlayerStatus.IN_GAME)) {
            return;
        }

        // 获取玩家所在的游戏
        String gameName = gameSessionManager.getPlayerGame(player);
        if (gameName == null) {
            return;
        }

        // 检查点击的物品是否是锁定槽位或不可移动物品
        if (isLockedSlot(event.getCurrentItem()) || isImmovableItem(event.getCurrentItem())) {
            event.setCancelled(true);

            // 只有在启用错误提示时才发送消息
            if (isErrorMessageEnabled()) {
                player.sendMessage(ChatColor.RED + "此物品不能移动！");
            }
            return;
        }

        // 检查目标槽位是否是锁定槽位
        if (event.getClickedInventory() != null && event.getClickedInventory().equals(player.getInventory())) {
            ItemStack targetSlotItem = player.getInventory().getItem(event.getSlot());
            if (isLockedSlot(targetSlotItem)) {
                event.setCancelled(true);

                // 只有在启用错误提示时才发送消息
                if (isErrorMessageEnabled()) {
                    player.sendMessage(ChatColor.RED + "此槽位已锁定，不能放置物品！");
                }
                return;
            }
        }
    }

    /**
     * 处理物品拖动事件
     *
     * @param event 物品拖动事件
     */
    @EventHandler(priority = EventPriority.HIGH)
    public void onInventoryDrag(InventoryDragEvent event) {
        if (!(event.getWhoClicked() instanceof Player)) {
            return;
        }

        Player player = (Player) event.getWhoClicked();

        // 检查玩家是否在游戏中
        if (!playerManager.isPlayerInStatus(player, PlayerStatus.IN_GAME)) {
            return;
        }

        // 获取玩家所在的游戏
        String gameName = gameSessionManager.getPlayerGame(player);
        if (gameName == null) {
            return;
        }

        // 检查拖动的物品是否是不可移动物品
        if (isImmovableItem(event.getOldCursor())) {
            event.setCancelled(true);

            // 只有在启用错误提示时才发送消息
            if (isErrorMessageEnabled()) {
                player.sendMessage(ChatColor.RED + "此物品不能移动！");
            }
            return;
        }

        // 检查目标槽位是否包含锁定槽位
        for (Integer slot : event.getRawSlots()) {
            if (slot < player.getInventory().getSize()) {
                ItemStack targetSlotItem = player.getInventory().getItem(slot);
                if (isLockedSlot(targetSlotItem)) {
                    event.setCancelled(true);

                    // 只有在启用错误提示时才发送消息
                    if (isErrorMessageEnabled()) {
                        player.sendMessage(ChatColor.RED + "此槽位已锁定，不能放置物品！");
                    }
                    return;
                }
            }
        }
    }

    /**
     * 处理物品丢弃事件
     *
     * @param event 物品丢弃事件
     */
    @EventHandler(priority = EventPriority.HIGH)
    public void onItemDrop(PlayerDropItemEvent event) {
        Player player = event.getPlayer();

        // 检查玩家是否在游戏中
        if (!playerManager.isPlayerInStatus(player, PlayerStatus.IN_GAME)) {
            return;
        }

        // 获取玩家所在的游戏
        String gameName = gameSessionManager.getPlayerGame(player);
        if (gameName == null) {
            return;
        }

        // 检查丢弃的物品是否是不可移动物品
        if (isImmovableItem(event.getItemDrop().getItemStack())) {
            event.setCancelled(true);

            // 只有在启用错误提示时才发送消息
            if (isErrorMessageEnabled()) {
                player.sendMessage(ChatColor.RED + "此物品不能丢弃！");
            }
            return;
        }
    }

    /**
     * 处理物品交换事件（主副手交换）
     *
     * @param event 物品交换事件
     */
    @EventHandler(priority = EventPriority.HIGH)
    public void onHandItemSwap(PlayerSwapHandItemsEvent event) {
        Player player = event.getPlayer();

        // 检查玩家是否在游戏中
        if (!playerManager.isPlayerInStatus(player, PlayerStatus.IN_GAME)) {
            return;
        }

        // 获取玩家所在的游戏
        String gameName = gameSessionManager.getPlayerGame(player);
        if (gameName == null) {
            return;
        }

        // 检查交换的物品是否是不可移动物品
        if (isImmovableItem(event.getMainHandItem()) || isImmovableItem(event.getOffHandItem())) {
            event.setCancelled(true);

            // 只有在启用错误提示时才发送消息
            if (isErrorMessageEnabled()) {
                player.sendMessage(ChatColor.RED + "此物品不能交换到副手！");
            }
            return;
        }
    }

    /**
     * 检查物品是否是锁定槽位（白色染料）
     *
     * @param item 物品
     * @return 是否是锁定槽位
     */
    private boolean isLockedSlot(ItemStack item) {
        if (item == null) {
            return false;
        }

        return item.getType() == Material.WHITE_DYE && item.hasItemMeta() && 
               item.getItemMeta().hasDisplayName() && 
               item.getItemMeta().getDisplayName().contains("锁定槽位");
    }

    /**
     * 检查物品是否是不可移动物品（通过物品描述判断）
     *
     * @param item 物品
     * @return 是否是不可移动物品
     */
    private boolean isImmovableItem(ItemStack item) {
        if (item == null) {
            return false;
        }

        if (!item.hasItemMeta()) {
            return false;
        }

        ItemMeta meta = item.getItemMeta();
        if (!meta.hasLore()) {
            return false;
        }

        List<String> lore = meta.getLore();
        for (String line : lore) {
            if (line.contains("此物品不可移动")) {
                return true;
            }
        }

        return false;
    }
}
