package org.Ver_zhzh.deathZombieV4.game;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;

import org.Ver_zhzh.deathZombieV4.DeathZombieV4;
import org.Ver_zhzh.deathZombieV4.utils.PlayerInteractionManager;
import org.Ver_zhzh.deathZombieV4.enums.PlayerStatus;
import org.Ver_zhzh.deathZombieV4.utils.Region;
import org.Ver_zhzh.deathZombieV4.utils.ScoreboardManager;
import org.Ver_zhzh.deathZombieV4.utils.ShootPluginHelper;
import org.Ver_zhzh.deathZombieV4.utils.ZombieSpawnManager;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.GameMode;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.World;
import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.configuration.file.YamlConfiguration;
import org.bukkit.enchantments.Enchantment;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.potion.PotionEffect;
import org.bukkit.potion.PotionEffectType;

/**
 * 管理游戏会话和玩家参与的游戏
 */
public class GameSessionManager {

    private final DeathZombieV4 plugin;
    private final GameManager gameManager;

    // 存储正在运行的游戏: <游戏名, 游戏状态>
    private final Map<String, GameState> runningGames;

    // 存储玩家参与的游戏: <玩家UUID, 游戏名>
    private final Map<UUID, String> playerGames;

    // 存储每个游戏的玩家: <游戏名, 玩家集合>
    private final Map<String, Set<UUID>> gameParticipants;

    // 游戏会话数据文件
    private File sessionDataFile;
    private FileConfiguration sessionDataConfig;

    /**
     * 游戏状态枚举
     */
    public enum GameState {
        WAITING, // 等待开始
        RUNNING, // 游戏中
        ENDED    // 已结束
    }

    /**
     * 构造函数 - 兼容旧版调用方式
     *
     * @param plugin 插件实例
     */
    public GameSessionManager(DeathZombieV4 plugin) {
        this(plugin, plugin.getGameManager());
    }

    /**
     * 构造函数 - 接受插件实例和游戏管理器
     *
     * @param plugin 插件实例
     * @param gameManager 游戏管理器
     */
    public GameSessionManager(DeathZombieV4 plugin, GameManager gameManager) {
        this.plugin = plugin;
        this.gameManager = gameManager;
        this.runningGames = new HashMap<>();
        this.playerGames = new HashMap<>();
        this.gameParticipants = new HashMap<>();

        // 初始化会话数据文件
        initSessionDataFile();

        // 加载会话数据
        loadSessionData();
    }

    /**
     * 初始化会话数据文件
     */
    private void initSessionDataFile() {
        sessionDataFile = new File(plugin.getDataFolder(), "gamesessions.yml");
        if (!sessionDataFile.exists()) {
            try {
                sessionDataFile.getParentFile().mkdirs();
                sessionDataFile.createNewFile();
            } catch (IOException e) {
                plugin.getLogger().severe("无法创建游戏会话数据文件: " + e.getMessage());
            }
        }
        sessionDataConfig = YamlConfiguration.loadConfiguration(sessionDataFile);
    }

    /**
     * 加载会话数据
     */
    private void loadSessionData() {
        // 加载游戏状态
        if (sessionDataConfig.contains("runningGames")) {
            ConfigurationSection gamesSection = sessionDataConfig.getConfigurationSection("runningGames");
            for (String gameName : gamesSection.getKeys(false)) {
                String stateStr = gamesSection.getString(gameName);
                try {
                    GameState state = GameState.valueOf(stateStr);
                    runningGames.put(gameName, state);
                    // 为游戏创建参与者集合
                    gameParticipants.put(gameName, new HashSet<>());
                } catch (IllegalArgumentException e) {
                    plugin.getLogger().warning("加载游戏状态时出错: " + e.getMessage());
                }
            }
        }

        // 加载玩家游戏关联
        if (sessionDataConfig.contains("playerGames")) {
            ConfigurationSection playersSection = sessionDataConfig.getConfigurationSection("playerGames");
            for (String uuidStr : playersSection.getKeys(false)) {
                try {
                    UUID uuid = UUID.fromString(uuidStr);
                    String gameName = playersSection.getString(uuidStr);

                    // 检查游戏是否存在且已启用
                    if (gameName != null && gameManager.gameExists(gameName) && gameManager.isGameEnabled(gameName)) {
                        playerGames.put(uuid, gameName);

                        // 将玩家添加到游戏参与者列表
                        if (gameParticipants.containsKey(gameName)) {
                            gameParticipants.get(gameName).add(uuid);
                        } else {
                            Set<UUID> participants = new HashSet<>();
                            participants.add(uuid);
                            gameParticipants.put(gameName, participants);
                        }
                    }
                } catch (IllegalArgumentException e) {
                    plugin.getLogger().warning("加载玩家游戏关联时出错: " + e.getMessage());
                }
            }
        }

        plugin.getLogger().info("已加载 " + runningGames.size() + " 个游戏会话和 " + playerGames.size() + " 个玩家游戏关联");
    }

    /**
     * 保存会话数据
     */
    public void saveSessionData() {
        // 保存游戏状态
        ConfigurationSection gamesSection = sessionDataConfig.createSection("runningGames");
        for (Map.Entry<String, GameState> entry : runningGames.entrySet()) {
            gamesSection.set(entry.getKey(), entry.getValue().name());
        }

        // 保存玩家游戏关联
        ConfigurationSection playersSection = sessionDataConfig.createSection("playerGames");
        for (Map.Entry<UUID, String> entry : playerGames.entrySet()) {
            playersSection.set(entry.getKey().toString(), entry.getValue());
        }

        try {
            sessionDataConfig.save(sessionDataFile);
            plugin.getLogger().info("已保存 " + runningGames.size() + " 个游戏会话和 " + playerGames.size() + " 个玩家游戏关联");
        } catch (IOException e) {
            plugin.getLogger().severe("保存游戏会话数据时出错: " + e.getMessage());
        }
    }

    /**
     * 停止所有游戏会话 在插件禁用时调用此方法清理资源
     */
    public void stopAllSessions() {
        // 创建一个副本以避免并发修改异常
        Set<String> games = new HashSet<>(runningGames.keySet());

        // 结束所有正在运行的游戏
        for (String gameName : games) {
            endGame(gameName);
            plugin.getLogger().info("已停止游戏会话: " + gameName);
        }

        // 保存会话数据
        saveSessionData();

        // 清空所有集合
        runningGames.clear();
        playerGames.clear();
        gameParticipants.clear();

        plugin.getLogger().info("已停止所有游戏会话");
    }

    /**
     * 创建一个新的游戏会话
     *
     * @param gameName 游戏名称
     * @return 是否成功创建
     */
    public boolean createGame(String gameName) {
        if (runningGames.containsKey(gameName)) {
            return false;
        }

        runningGames.put(gameName, GameState.WAITING);
        gameParticipants.put(gameName, new HashSet<>());

        // 保存会话状态
        saveSessionData();

        return true;
    }

    /**
     * 添加玩家到游戏
     *
     * @param player 玩家
     * @param gameName 游戏名称
     * @return 是否成功添加
     */
    public boolean addPlayerToGame(Player player, String gameName) {
        // 记录调试信息
        plugin.getLogger().info("尝试将玩家 " + player.getName() + " 添加到游戏 " + gameName);

        // 检查游戏是否存在且已启用
        if (!gameManager.gameExists(gameName)) {
            plugin.getLogger().warning("游戏 " + gameName + " 不存在，无法添加玩家");
            return false;
        }

        if (!gameManager.isGameEnabled(gameName)) {
            plugin.getLogger().warning("游戏 " + gameName + " 未启用，无法添加玩家");
            return false;
        }

        // 检查游戏状态
        if (!runningGames.containsKey(gameName)) {
            // 如果游戏会话不存在但游戏已启用，创建一个新会话
            plugin.getLogger().info("游戏会话不存在，正在创建新会话: " + gameName);
            createGame(gameName);
        } else if (runningGames.get(gameName) != GameState.WAITING) {
            // 如果游戏不在等待状态，不允许加入
            plugin.getLogger().warning("游戏 " + gameName + " 不在等待状态，当前状态: " + runningGames.get(gameName) + "，无法添加玩家");
            return false;
        }

        // 检查游戏人数是否已满
        FileConfiguration gameConfig = gameManager.getGameConfig(gameName);
        if (gameConfig != null && gameConfig.contains("maxPlayers")) {
            int maxPlayers = gameConfig.getInt("maxPlayers");
            if (gameParticipants.get(gameName).size() >= maxPlayers) {
                plugin.getLogger().warning("游戏 " + gameName + " 人数已满，当前人数: " + gameParticipants.get(gameName).size() + ", 最大人数: " + maxPlayers);
                return false;
            }
        }

        // 如果玩家已经在其他游戏中，先移除
        if (playerGames.containsKey(player.getUniqueId())) {
            String currentGame = playerGames.get(player.getUniqueId());
            plugin.getLogger().info("玩家 " + player.getName() + " 已经在游戏 " + currentGame + " 中，先移除");
            removePlayerFromCurrentGame(player);
        }

        // 添加玩家到游戏
        playerGames.put(player.getUniqueId(), gameName);
        gameParticipants.get(gameName).add(player.getUniqueId());

        // 更新玩家状态为等待
        PlayerInteractionManager playerManager = plugin.getPlayerInteractionManager();
        playerManager.setPlayerStatus(player, PlayerStatus.WAITING);

        // 确保玩家统计数据被初始化（这会触发数据加载和保存）
        try {
            plugin.getPlayerStatisticsManager().getPlayerStatistics(player);
            plugin.getLogger().info("已确保玩家 " + player.getName() + " 的统计数据初始化");
        } catch (Exception e) {
            plugin.getLogger().warning("初始化玩家 " + player.getName() + " 统计数据时出错: " + e.getMessage());
        }

        // 保存会话状态
        saveSessionData();

        // 发送加入游戏的Title消息
        try {
            int currentPlayerCount = gameParticipants.get(gameName).size();
            int maxPlayers = gameConfig != null ? gameConfig.getInt("maxPlayers", 4) : 4;
            plugin.getMessageManager().sendJoinGameTitleMessage(player, gameName, currentPlayerCount, maxPlayers);
        } catch (Exception e) {
            // 如果MessageManager发送失败，发送简单的欢迎消息
            player.sendMessage(ChatColor.GREEN + "欢迎加入游戏 " + gameName + "!");
            plugin.getLogger().warning("发送加入游戏Title失败: " + e.getMessage());
        }

        plugin.getLogger().info("成功将玩家 " + player.getName() + " 添加到游戏 " + gameName);
        return true;
    }

    /**
     * 从当前游戏中移除玩家
     *
     * @param player 玩家
     */
    public void removePlayerFromCurrentGame(Player player) {
        UUID playerId = player.getUniqueId();

        if (playerGames.containsKey(playerId)) {
            String currentGame = playerGames.get(playerId);
            gameParticipants.get(currentGame).remove(playerId);
            playerGames.remove(playerId);

            // 重置玩家状态为未加入
            PlayerInteractionManager playerManager = plugin.getPlayerInteractionManager();
            playerManager.setPlayerStatus(player, PlayerStatus.NOT_JOINED);

            // 保存会话状态
            saveSessionData();
        }
    }

    /**
     * 从指定游戏中移除玩家
     *
     * @param player 玩家
     * @param gameName 游戏名称
     * @return 是否成功移除
     */
    public boolean removePlayerFromGame(Player player, String gameName) {
        UUID playerId = player.getUniqueId();

        // 检查玩家是否在指定游戏中
        if (playerGames.containsKey(playerId) && playerGames.get(playerId).equals(gameName)) {
            // 获取移除前的玩家数量
            int playerCountBeforeRemoval = gameParticipants.get(gameName).size();

            // 从游戏参与者列表中移除玩家
            gameParticipants.get(gameName).remove(playerId);
            playerGames.remove(playerId);

            // 重置玩家状态为未加入
            PlayerInteractionManager playerManager = plugin.getPlayerInteractionManager();
            playerManager.setPlayerStatus(player, PlayerStatus.NOT_JOINED);

            // 清理玩家的幸运箱资源
            if (plugin.getLuckyBoxManager() != null) {
                plugin.getLuckyBoxManager().onPlayerLeaveGame(player);
            }

            // 检查是否需要结束游戏（仅在游戏进行中时）
            GameState currentState = getGameState(gameName);
            if (currentState == GameState.RUNNING) {
                // 检查配置项
                boolean endGameWhenBelowMin = plugin.getConfig().getBoolean("game.end_game_when_below_min_players", true);

                if (endGameWhenBelowMin) {
                    // 获取游戏的最小玩家数
                    int minPlayers = gameManager.getMinPlayers(gameName);
                    int remainingPlayers = gameParticipants.get(gameName).size();

                    if (remainingPlayers < minPlayers) {
                        plugin.getLogger().info("玩家 " + player.getName() + " 离开后，游戏 " + gameName
                                + " 人数不足最小启动人数 " + minPlayers + "，将结束游戏");

                        // 结束游戏并清理资源
                        plugin.getZombieSpawnManager().endGameAndCleanup(gameName);

                        // 向所有在线玩家广播游戏结束消息
                        for (Player p : plugin.getServer().getOnlinePlayers()) {
                            p.sendMessage(ChatColor.RED + "游戏 " + gameName + " 因玩家离开导致人数不足而结束");
                        }
                    }
                }
            }

            // 保存会话状态
            saveSessionData();

            plugin.getLogger().info("玩家 " + player.getName() + " 已从游戏 " + gameName + " 中移除");
            return true;
        } else {
            plugin.getLogger().warning("玩家 " + player.getName() + " 不在游戏 " + gameName + " 中，无法移除");
            return false;
        }
    }

    /**
     * 启动游戏
     *
     * @param gameName 游戏名称
     * @return 是否成功开始游戏
     */
    public boolean startGame(String gameName) {
        plugin.getLogger().info("尝试启动游戏: " + gameName);

        // 检查游戏是否存在
        if (!gameManager.gameExists(gameName)) {
            plugin.getLogger().warning("游戏 " + gameName + " 不存在，无法启动");
            return false;
        }

        // 检查游戏是否启用
        if (!gameManager.isGameEnabled(gameName)) {
            plugin.getLogger().warning("游戏 " + gameName + " 未启用，无法启动");
            return false;
        }

        // 检查游戏状态
        if (!runningGames.containsKey(gameName)) {
            plugin.getLogger().warning("游戏会话不存在，尝试创建新会话");
            createGame(gameName);
        } else if (runningGames.get(gameName) != GameState.WAITING) {
            plugin.getLogger().warning("游戏 " + gameName + " 不在等待状态，当前状态: " + runningGames.get(gameName) + "，无法启动");
            return false;
        }

        // 检查游戏中的玩家数量
        Set<UUID> players = gameParticipants.get(gameName);
        if (players.isEmpty()) {
            plugin.getLogger().warning("游戏 " + gameName + " 没有玩家，无法启动");
            return false;
        }

        plugin.getLogger().info("游戏 " + gameName + " 当前有 " + players.size() + " 个玩家");

        // 更新游戏状态为运行中
        runningGames.put(gameName, GameState.RUNNING);
        plugin.getLogger().info("游戏 " + gameName + " 状态已更新为 RUNNING");

        // 记录游戏开始时间（确保在游戏状态更新后立即设置）
        if (plugin.getScoreboardManager() != null) {
            plugin.getScoreboardManager().setGameStartTime(gameName, System.currentTimeMillis());
            plugin.getLogger().info("已为游戏 " + gameName + " 设置开始时间");
        } else {
            plugin.getLogger().warning("ScoreboardManager未初始化，无法设置游戏开始时间");
        }

        // 更新所有参与玩家的状态为游戏中
        PlayerInteractionManager playerManager = plugin.getPlayerInteractionManager();

        // 获取游戏出生点位置
        Location spawnLocation = getGameSpawnLocation(gameName);

        if (spawnLocation == null) {
            plugin.getLogger().warning("游戏 " + gameName + " 没有设置出生点，无法传送玩家！");
            return false;
        }

        // 处理游戏中的门
        setupGameDoors(gameName);

        // 处理游戏中的窗户
        setupGameWindows(gameName);

        // 设置购买点
        setupBuyPoints(gameName);

        // 创建门悬浮文字或NPC
        if (plugin.isDecentHologramsAvailable()) {
            // 使用悬浮文字
            plugin.getDoorHologramManager().setupDoorHolograms(gameName);
            plugin.getLogger().info("使用DecentHolograms创建门悬浮文字");

            // 创建全局电源按钮效果
            if (plugin.getPowerButtonEffectManager() != null) {
                plugin.getPowerButtonEffectManager().createPowerButtonEffects(gameName);
                plugin.getLogger().info("已创建游戏 " + gameName + " 的全局电源按钮效果");
            }
        } else {
            // 使用传统NPC
            plugin.getDoorNPCManager().createDoorNPCsForGame(gameName);
            plugin.getLogger().info("使用传统NPC创建门交互点");
        }

        // 记录在线玩家数量
        int onlinePlayerCount = 0;

        // 获取在线玩家列表，用于装备复制
        List<Player> onlinePlayers = new ArrayList<>();
        Player firstPlayer = null;

        // 第一步：收集所有在线玩家，并找到第一个玩家
        for (UUID playerId : players) {
            Player player = plugin.getServer().getPlayer(playerId);
            if (player != null && player.isOnline()) {
                onlinePlayerCount++;
                onlinePlayers.add(player);

                if (firstPlayer == null) {
                    firstPlayer = player;
                }

                plugin.getLogger().info("找到在线玩家 " + player.getName());

                // 更新玩家状态
                playerManager.setPlayerStatus(player, PlayerStatus.IN_GAME);

                // 添加发光效果
                player.setGlowing(true);

                // 设置玩家为冒险模式
                player.setGameMode(GameMode.ADVENTURE);
                plugin.getLogger().info("已将玩家 " + player.getName() + " 设置为冒险模式");

                // 应用游戏buff效果
                applyGameBuffs(player, gameName);

                // 传送玩家到游戏出生点
                try {
                    player.teleport(spawnLocation);
                    plugin.getLogger().info("已将玩家 " + player.getName() + " 传送到游戏出生点");
                } catch (Exception e) {
                    plugin.getLogger().warning("传送玩家 " + player.getName() + " 失败: " + e.getMessage());
                }

                // 初始化玩家金钱为0
                try {
                    initializePlayerMoney(player);
                    plugin.getLogger().info("已初始化玩家 " + player.getName() + " 的金钱");
                } catch (Exception e) {
                    plugin.getLogger().warning("初始化玩家 " + player.getName() + " 的金钱失败: " + e.getMessage());
                }

                // 重置玩家游戏统计数据
                try {
                    plugin.getPlayerInteractionManager().resetPlayerGameStatistics(player, gameName);
                    plugin.getLogger().info("已重置玩家 " + player.getName() + " 的游戏统计数据");
                } catch (Exception e) {
                    plugin.getLogger().warning("重置玩家 " + player.getName() + " 的游戏统计数据失败: " + e.getMessage());
                }
            }
        }

        // 第二步：如果有多个玩家，先给第一个玩家分发装备，然后复制给其他玩家
        if (onlinePlayers.size() > 0) {
            if (firstPlayer != null) {
                // 给第一个玩家分发装备
                try {
                    plugin.getLogger().info("正在给第一个玩家 " + firstPlayer.getName() + " 分发初始装备");
                    givePlayerStartItems(firstPlayer);
                    plugin.getLogger().info("已给予第一个玩家 " + firstPlayer.getName() + " 起始物品");

                    // 如果有多个玩家，复制第一个玩家的装备给其他玩家
                    if (onlinePlayers.size() > 1) {
                        for (Player otherPlayer : onlinePlayers) {
                            if (otherPlayer != firstPlayer) {
                                plugin.getLogger().info("正在将第一个玩家的装备复制给玩家 " + otherPlayer.getName());
                                copyPlayerEquipment(firstPlayer, otherPlayer);
                                plugin.getLogger().info("已成功将装备复制给玩家 " + otherPlayer.getName());
                            }
                        }
                    }
                } catch (Exception e) {
                    plugin.getLogger().warning("给予玩家装备过程中出错: " + e.getMessage());
                    e.printStackTrace();

                    // 如果复制失败，尝试为每个玩家单独分发装备
                    plugin.getLogger().info("复制装备失败，尝试为每个玩家单独分发装备");
                    for (Player player : onlinePlayers) {
                        try {
                            givePlayerStartItems(player);
                            plugin.getLogger().info("已给予玩家 " + player.getName() + " 起始物品（备用方法）");
                        } catch (Exception ex) {
                            plugin.getLogger().warning("给予玩家 " + player.getName() + " 起始物品失败: " + ex.getMessage());
                        }
                    }
                }
            } else {
                plugin.getLogger().warning("无法找到第一个在线玩家，尝试为每个玩家单独分发装备");
                // 如果没有找到第一个玩家，为每个玩家单独分发装备
                for (Player player : onlinePlayers) {
                    try {
                        givePlayerStartItems(player);
                        plugin.getLogger().info("已给予玩家 " + player.getName() + " 起始物品（备用方法）");
                    } catch (Exception e) {
                        plugin.getLogger().warning("给予玩家 " + player.getName() + " 起始物品失败: " + e.getMessage());
                    }
                }
            }
        } else {
            plugin.getLogger().warning("没有找到在线玩家，无法分发装备");
        }

        // 向所有玩家发送游戏开始消息和提示
        for (Player gamePlayer : onlinePlayers) {
            // 发送游戏开始消息
            gamePlayer.sendMessage(ChatColor.GREEN + "游戏开始！");

            // 添加重要游戏说明
            gamePlayer.sendMessage(ChatColor.GOLD + "========================");
            gamePlayer.sendMessage(ChatColor.YELLOW + "游戏提示: ");
            gamePlayer.sendMessage(ChatColor.WHITE + "1. 击杀僵尸获得金钱，生存更多回合");
            gamePlayer.sendMessage(ChatColor.WHITE + "2. 使用金钱购买武器、护甲和道具");
            gamePlayer.sendMessage(ChatColor.WHITE + "3. 注意: " + ChatColor.RED + "购买弹药需要先手持对应的武器");
            gamePlayer.sendMessage(ChatColor.WHITE + "4. 蹲下救援队友，修复破损窗户");
            gamePlayer.sendMessage(ChatColor.GOLD + "========================");

            // 发送Title通知
            try {
                gamePlayer.sendTitle(
                        ChatColor.RED + "游戏开始！",
                        ChatColor.GOLD + "击杀僵尸，生存下去！",
                        10, 70, 20
                );
                plugin.getLogger().info("向玩家 " + gamePlayer.getName() + " 发送了游戏开始 Title通知");

                // 播放游戏开始音效
                try {
                    String soundName = plugin.getConfig().getString("sounds.game_start", "ENTITY_WITHER_SPAWN");
                    // 直接使用字符串形式播放音效，避免使用valueOf方法
                    gamePlayer.playSound(gamePlayer.getLocation(), soundName, 1.0f, 1.0f);
                    plugin.getLogger().info("向玩家 " + gamePlayer.getName() + " 播放了游戏开始音效: " + soundName);
                } catch (Exception soundEx) {
                    plugin.getLogger().warning("播放游戏开始音效失败: " + soundEx.getMessage());
                    // 尝试使用默认音效
                    try {
                        gamePlayer.playSound(gamePlayer.getLocation(), "ENTITY_WITHER_SPAWN", 1.0f, 1.0f);
                    } catch (Exception ex) {
                        plugin.getLogger().warning("播放默认游戏开始音效也失败: " + ex.getMessage());
                    }
                }
            } catch (Exception e) {
                plugin.getLogger().warning("发送游戏开始Title失败: " + e.getMessage());
                // 发送普通消息代替
                gamePlayer.sendMessage(ChatColor.RED + "游戏开始！");
                gamePlayer.sendMessage(ChatColor.GOLD + "击杀僵尸，生存下去！");

                // 播放游戏开始音效
                try {
                    String soundName = plugin.getConfig().getString("sounds.game_start", "ENTITY_WITHER_SPAWN");
                    // 直接使用字符串形式播放音效，避免使用valueOf方法
                    gamePlayer.playSound(gamePlayer.getLocation(), soundName, 1.0f, 1.0f);
                    plugin.getLogger().info("向玩家 " + gamePlayer.getName() + " 播放了游戏开始音效: " + soundName);
                } catch (Exception soundEx) {
                    plugin.getLogger().warning("播放游戏开始音效失败: " + soundEx.getMessage());
                    // 尝试使用默认音效
                    try {
                        gamePlayer.playSound(gamePlayer.getLocation(), "ENTITY_WITHER_SPAWN", 1.0f, 1.0f);
                    } catch (Exception ex) {
                        plugin.getLogger().warning("播放默认游戏开始音效也失败: " + ex.getMessage());
                    }
                }
            }
        }

        // 记录游戏开始并启动僵尸生成
        recordGameStart(gameName, onlinePlayerCount);

        return true;
    }

    /**
     * 复制玩家装备给另一个玩家
     *
     * @param sourcePlayer 源玩家（拥有正确装备的玩家）
     * @param targetPlayer 目标玩家（需要接收装备的玩家）
     */
    private void copyPlayerEquipment(Player sourcePlayer, Player targetPlayer) {
        plugin.getLogger().info("开始将玩家 " + sourcePlayer.getName() + " 的装备复制给玩家 " + targetPlayer.getName());

        // 清空目标玩家的物品栏和经验值
        targetPlayer.getInventory().clear();
        targetPlayer.setExp(0);
        targetPlayer.setLevel(0);

        // 清空目标玩家的护甲槽
        targetPlayer.getInventory().setHelmet(null);
        targetPlayer.getInventory().setChestplate(null);
        targetPlayer.getInventory().setLeggings(null);
        targetPlayer.getInventory().setBoots(null);

        // 复制物品栏内容
        ItemStack[] contents = sourcePlayer.getInventory().getContents();
        for (int i = 0; i < contents.length; i++) {
            if (contents[i] != null) {
                targetPlayer.getInventory().setItem(i, contents[i].clone());
            }
        }

        // 复制护甲内容
        if (sourcePlayer.getInventory().getHelmet() != null) {
            targetPlayer.getInventory().setHelmet(sourcePlayer.getInventory().getHelmet().clone());
        }
        if (sourcePlayer.getInventory().getChestplate() != null) {
            targetPlayer.getInventory().setChestplate(sourcePlayer.getInventory().getChestplate().clone());
        }
        if (sourcePlayer.getInventory().getLeggings() != null) {
            targetPlayer.getInventory().setLeggings(sourcePlayer.getInventory().getLeggings().clone());
        }
        if (sourcePlayer.getInventory().getBoots() != null) {
            targetPlayer.getInventory().setBoots(sourcePlayer.getInventory().getBoots().clone());
        }

        // 复制经验值和等级（用于弹药显示）
        targetPlayer.setExp(sourcePlayer.getExp());
        targetPlayer.setLevel(sourcePlayer.getLevel());

        // 初始化弹药系统
        // 查找所有枪支并初始化弹药
        for (int i = 0; i < contents.length; i++) {
            ItemStack item = contents[i];
            if (item != null) {
                // 检查物品的lore，查找枪支ID
                ItemMeta meta = item.getItemMeta();
                if (meta != null && meta.hasLore()) {
                    List<String> lore = meta.getLore();
                    for (String line : lore) {
                        if (line.contains("ID: id")) {
                            // 提取ID
                            String idPart = line.substring(line.indexOf("ID: ") + 4).trim();
                            if (idPart.startsWith("id") && idPart.length() > 2 && Character.isDigit(idPart.charAt(2))) {
                                try {
                                    int gunId = Integer.parseInt(idPart.substring(2));
                                    if (gunId >= 1 && gunId <= 24) {
                                        // 这是一个枪支，初始化弹药系统
                                        ShootPluginHelper shootHelper = plugin.getShootPluginHelper();
                                        if (shootHelper != null) {
                                            shootHelper.initializeGunAmmo(targetPlayer, idPart);
                                            plugin.getLogger().info("为玩家 " + targetPlayer.getName() + " 的枪支 " + idPart + " 初始化弹药系统");
                                        }
                                    }
                                } catch (NumberFormatException e) {
                                    // 忽略解析错误
                                }
                            }
                        }
                    }
                }
            }
        }

        // 更新物品栏
        targetPlayer.updateInventory();

        plugin.getLogger().info("成功将玩家 " + sourcePlayer.getName() + " 的装备复制给玩家 " + targetPlayer.getName());
    }

    // 记录在线玩家数量
    private void recordGameStart(String gameName, int onlinePlayerCount) {
        plugin.getLogger().info("游戏 " + gameName + " 开始，共有 " + onlinePlayerCount + " 个在线玩家参与");

        // 记录所有参与玩家的游戏次数
        Set<UUID> participants = gameParticipants.get(gameName);
        if (participants != null) {
            for (UUID playerId : participants) {
                Player player = plugin.getServer().getPlayer(playerId);
                if (player != null && player.isOnline()) {
                    plugin.getPlayerStatisticsManager().incrementGamesPlayed(player);
                    plugin.getLogger().info("已为玩家 " + player.getName() + " 记录游戏次数");
                }
            }
        }

        // 保存会话状态
        saveSessionData();

        // 开始生成僵尸
        try {
            plugin.getLogger().info("尝试生成第1回合的僵尸");
            // 使用正确的方法更新回合数
            plugin.getZombieSpawnManager().updateGameRound(gameName, 1);
            // 生成任务会在ZombieSpawnManager的监控任务中自动启动
            plugin.getLogger().info("已更新游戏 " + gameName + " 的回合数为1，僵尸生成任务将自动启动");
        } catch (Exception e) {
            plugin.getLogger().severe("生成僵尸失败: " + e.getMessage());
            e.printStackTrace();
        }
    }



    /**
     * 设置游戏中的门方块
     *
     * @param gameName 游戏名称
     */
    private void setupGameDoors(String gameName) {
        // 获取门管理器
        DoorManager doorManager = plugin.getDoorManager();

        // 重置游戏中的所有门状态
        doorManager.resetDoors(gameName);

        // 获取游戏中的所有门信息
        Map<String, Map<String, Object>> doors = plugin.getGameManager().getDoors(gameName);
        if (doors == null || doors.isEmpty()) {
            plugin.getLogger().info("游戏 " + gameName + " 没有设置任何门");
            return;
        }

        // 处理每个门
        for (Map.Entry<String, Map<String, Object>> entry : doors.entrySet()) {
            String doorName = entry.getKey();
            Map<String, Object> doorInfo = entry.getValue();

            boolean isLocked = (boolean) doorInfo.getOrDefault("locked", true);
            int unlockPrice = (int) doorInfo.getOrDefault("unlockPrice", 0);

            // 先移除所有门区域的方块，无论门是否锁定
            if (doorInfo.containsKey("region")) {
                @SuppressWarnings("unchecked")
                Map<String, Object> regionData = (Map<String, Object>) doorInfo.get("region");
                if (regionData != null) {
                    // 获取区域信息
                    String worldName = (String) regionData.get("world");
                    double minX = (double) regionData.get("minX");
                    double minY = (double) regionData.get("minY");
                    double minZ = (double) regionData.get("minZ");
                    double maxX = (double) regionData.get("maxX");
                    double maxY = (double) regionData.get("maxY");
                    double maxZ = (double) regionData.get("maxZ");

                    // 创建区域对象
                    World world = Bukkit.getWorld(worldName);
                    if (world != null) {
                        Region region = new Region(world, minX, minY, minZ, maxX, maxY, maxZ);
                        doorManager.registerDoorRegion(gameName, doorName, region);

                        // 只有自动解锁的门才在游戏开始时移除方块
                        // 需要解锁的门保持方块不变，直到玩家解锁
                        if (!isLocked || unlockPrice == 0) {
                            doorManager.removeDoorBlocks(gameName, doorName);
                            plugin.getLogger().info("游戏开始时移除自动解锁门 '" + doorName + "' 的方块");
                        } else {
                            plugin.getLogger().info("游戏开始时保留锁定门 '" + doorName + "' 的方块，等待玩家解锁");
                        }
                    }
                }
            }

            // 设置门的解锁状态
            if (!isLocked || unlockPrice == 0) {
                // 自动解锁的门
                doorManager.setDoorUnlocked(gameName, doorName, true);
                plugin.getLogger().info("游戏 " + gameName + " 的门 '" + doorName + "' 已自动解锁");
            } else {
                // 需要解锁的门，但不填充方块
                doorManager.setDoorUnlocked(gameName, doorName, false);
                plugin.getLogger().info("游戏 " + gameName + " 的门 '" + doorName + "' 已设置为锁定状态，解锁价格: " + unlockPrice);
            }
        }
    }

    /**
     * 获取游戏出生点位置
     *
     * @param gameName 游戏名称
     * @return 出生点位置，如果未设置则返回null
     */
    private Location getGameSpawnLocation(String gameName) {
        if (!gameManager.gameExists(gameName)) {
            return null;
        }

        FileConfiguration config = gameManager.getGameConfig(gameName);
        if (config == null || !config.contains("spawn")) {
            return null;
        }

        ConfigurationSection spawnSection = config.getConfigurationSection("spawn");
        if (spawnSection == null) {
            return null;
        }

        String worldName = spawnSection.getString("world");
        if (worldName == null || plugin.getServer().getWorld(worldName) == null) {
            return null;
        }

        double x = spawnSection.getDouble("x");
        double y = spawnSection.getDouble("y");
        double z = spawnSection.getDouble("z");
        float yaw = (float) spawnSection.getDouble("yaw");
        float pitch = (float) spawnSection.getDouble("pitch");

        return new Location(plugin.getServer().getWorld(worldName), x, y, z, yaw, pitch);
    }

    /**
     * 给予玩家初始物品
     *
     * @param player 玩家
     */
    private void givePlayerStartItems(Player player) {
        // 获取玩家当前游戏
        String gameName = getPlayerGame(player);
        if (gameName == null) {
            plugin.getLogger().warning("无法给予玩家 " + player.getName() + " 初始物品：玩家不在任何游戏中");
            return;
        }

        // 清空玩家物品栏和经验值（确保完全重置状态）
        player.getInventory().clear();
        player.setExp(0);
        player.setLevel(0);

        // 清空玩家的护甲槽
        player.getInventory().setHelmet(null);
        player.getInventory().setChestplate(null);
        player.getInventory().setLeggings(null);
        player.getInventory().setBoots(null);

        // 创建白色染料作为占位符（锁定槽位）
        ItemStack placeholder = new ItemStack(Material.WHITE_DYE);
        ItemMeta placeholderMeta = placeholder.getItemMeta();
        placeholderMeta.setDisplayName(ChatColor.GRAY + "锁定槽位");
        List<String> lore = new ArrayList<>();
        lore.add(ChatColor.RED + "此槽位已锁定");
        placeholderMeta.setLore(lore);
        placeholder.setItemMeta(placeholderMeta);

        // 创建武器槽位占位符
        ItemStack weaponSlot = new ItemStack(Material.IRON_SWORD);
        ItemMeta weaponMeta = weaponSlot.getItemMeta();
        weaponMeta.setDisplayName(ChatColor.GOLD + "武器槽位");
        List<String> weaponLore = new ArrayList<>();
        weaponLore.add(ChatColor.GRAY + "此槽位用于放置武器");
        weaponLore.add(ChatColor.RED + "此槽位已锁定");
        weaponMeta.setLore(weaponLore);
        weaponSlot.setItemMeta(weaponMeta);

        // 创建道具槽位占位符
        ItemStack itemSlot = new ItemStack(Material.CHEST);
        ItemMeta itemMeta = itemSlot.getItemMeta();
        itemMeta.setDisplayName(ChatColor.GOLD + "道具槽位");
        List<String> itemLore = new ArrayList<>();
        itemLore.add(ChatColor.GRAY + "此槽位用于放置道具");
        itemLore.add(ChatColor.RED + "此槽位已锁定");
        itemMeta.setLore(itemLore);
        itemSlot.setItemMeta(itemMeta);

        // 获取游戏的初始装备配置
        Map<Integer, String> initialEquipment = getGameInitialEquipment(gameName);

        // 获取游戏配置
        FileConfiguration config = gameManager.getGameConfig(gameName);

        // 跟踪已使用的槽位
        Set<Integer> usedSlots = new HashSet<>();

        // 如果有配置的初始装备，使用配置的装备
        if (initialEquipment != null && !initialEquipment.isEmpty()) {
            plugin.getLogger().info("使用配置的初始装备，共 " + initialEquipment.size() + " 个槽位");
            for (Map.Entry<Integer, String> entry : initialEquipment.entrySet()) {
                int slot = entry.getKey();
                String itemId = entry.getValue();

                // 处理槽位0（旧格式）或确保槽位编号在1-36范围内（新格式）
                if (slot < 0 || slot > 36) {
                    plugin.getLogger().warning("跳过无效的槽位编号: " + slot + "，有效范围为0-36");
                    continue;
                }

                // 如果是槽位0（旧格式），转换为槽位1（新格式）
                if (slot == 0) {
                    plugin.getLogger().info("检测到旧格式槽位0，转换为新格式槽位1");
                    slot = 1;
                }

                int inventorySlot = slot - 1; // Minecraft物品栏从0开始，我们的配置从1开始
                plugin.getLogger().info("处理槽位 " + slot + " (物品栏索引: " + inventorySlot + ")，物品ID: " + itemId);

                if (itemId != null && !itemId.isEmpty()) {
                    if (itemId.equals("white_dye")) {
                        // 检查是否有槽位类型配置
                        String slotTypeKey = "initialEquipment.slot_type_" + slot;
                        String slotType = config != null ? config.getString(slotTypeKey) : null;

                        if (slotType != null) {
                            if (slotType.equals("weapon_slot")) {
                                // 武器槽位
                                player.getInventory().setItem(inventorySlot, weaponSlot.clone());
                                plugin.getLogger().info("设置槽位 " + slot + " 为武器槽位");
                            } else if (slotType.equals("item_slot")) {
                                // 道具槽位
                                player.getInventory().setItem(inventorySlot, itemSlot.clone());
                                plugin.getLogger().info("设置槽位 " + slot + " 为道具槽位");
                            } else {
                                // 默认锁定槽位
                                player.getInventory().setItem(inventorySlot, placeholder.clone());
                                plugin.getLogger().info("设置槽位 " + slot + " 为默认锁定槽位");
                            }
                        } else {
                            // 默认锁定槽位
                            player.getInventory().setItem(inventorySlot, placeholder.clone());
                            plugin.getLogger().info("设置槽位 " + slot + " 为默认锁定槽位（无类型配置）");
                        }
                    } else if (itemId.equals("1") || itemId.equals("iron_sword")) {
                        // 特殊处理铁剑
                        plugin.getLogger().info("设置槽位 " + slot + " 为铁剑");
                        ItemStack ironSword = createItemById(EquipmentConstants.IRON_SWORD);
                        if (ironSword != null) {
                            // 设置物品的特殊NBT标签，标记为不可移动
                            ItemMeta meta = ironSword.getItemMeta();
                            if (meta != null) {
                                List<String> metaLore = meta.hasLore() ? meta.getLore() : new ArrayList<>();
                                metaLore.add(ChatColor.RED + "此物品不可移动");
                                meta.setLore(metaLore);
                                ironSword.setItemMeta(meta);
                            }
                            player.getInventory().setItem(inventorySlot, ironSword);
                        } else {
                            plugin.getLogger().warning("无法创建铁剑物品，使用默认铁剑");
                            player.getInventory().setItem(inventorySlot, new ItemStack(Material.IRON_SWORD));
                        }
                    } else {
                        // 根据ID创建物品并放入对应槽位
                        ItemStack item = createItemById(itemId);
                        if (item != null) {
                            // 设置物品的特殊NBT标签，标记为不可移动
                            ItemMeta meta = item.getItemMeta();
                            if (meta != null) {
                                List<String> metaLore = meta.hasLore() ? meta.getLore() : new ArrayList<>();
                                metaLore.add(ChatColor.RED + "此物品不可移动");
                                meta.setLore(metaLore);
                                item.setItemMeta(meta);
                            }

                            player.getInventory().setItem(inventorySlot, item);

                            // 如果是枪支类型，确保Shoot插件的弹药系统正确初始化
                            if (itemId.startsWith("id") && Character.isDigit(itemId.charAt(2))) {
                                try {
                                    int gunId = Integer.parseInt(itemId.substring(2));
                                    if (gunId >= 1 && gunId <= 24) {
                                        // 这是一个枪支，确保Shoot插件的弹药系统正确初始化
                                        ShootPluginHelper shootHelper = plugin.getShootPluginHelper();
                                        if (shootHelper != null) {
                                            // 初始化弹药系统
                                            shootHelper.initializeGunAmmo(player, itemId);
                                            plugin.getLogger().info("为玩家 " + player.getName() + " 的枪支 " + itemId + " 初始化弹药系统");
                                        }
                                    }
                                } catch (NumberFormatException e) {
                                    // 不是数字ID，忽略
                                }
                            }
                        }
                    }

                    usedSlots.add(inventorySlot);
                }
            }
        } else {
            // 使用默认初始装备
            // 创建一把铁剑并放在物品栏第一个位置
            ItemStack ironSword = createItemById("id1");
            if (ironSword != null) {
                ItemMeta swordMeta = ironSword.getItemMeta();
                if (swordMeta != null) {
                    List<String> swordLore = swordMeta.hasLore() ? swordMeta.getLore() : new ArrayList<>();
                    swordLore.add(ChatColor.RED + "此物品不可移动");
                    swordMeta.setLore(swordLore);
                    ironSword.setItemMeta(swordMeta);
                }
                player.getInventory().setItem(0, ironSword);
                usedSlots.add(0);
            }

            // 创建一把手枪并放在物品栏第二个位置
            ItemStack handgun = createItemById("id2");
            if (handgun != null) {
                ItemMeta gunMeta = handgun.getItemMeta();
                if (gunMeta != null) {
                    List<String> gunLore = gunMeta.hasLore() ? gunMeta.getLore() : new ArrayList<>();
                    gunLore.add(ChatColor.RED + "此物品不可移动");
                    gunMeta.setLore(gunLore);
                    handgun.setItemMeta(gunMeta);
                }
                player.getInventory().setItem(1, handgun);
                usedSlots.add(1);

                // 初始化手枪弹药系统
                ShootPluginHelper shootHelper = plugin.getShootPluginHelper();
                if (shootHelper != null) {
                    shootHelper.initializeGunAmmo(player, "id2");
                    plugin.getLogger().info("为玩家 " + player.getName() + " 的手枪初始化弹药系统");
                }
            }

            // 设置默认的槽位类型
            // 槽位0-1为武器槽位
            for (int i = 0; i <= 1; i++) {
                if (!usedSlots.contains(i)) {
                    player.getInventory().setItem(i, weaponSlot.clone());
                    usedSlots.add(i);
                }
            }

            // 槽位6-8为道具槽位
            for (int i = 6; i <= 8; i++) {
                if (!usedSlots.contains(i)) {
                    player.getInventory().setItem(i, itemSlot.clone());
                    usedSlots.add(i);
                }
            }

            // 其他槽位为锁定槽位
            for (int i = 2; i <= 5; i++) {
                if (!usedSlots.contains(i)) {
                    player.getInventory().setItem(i, placeholder.clone());
                    usedSlots.add(i);
                }
            }
            for (int i = 9; i < 36; i++) {
                if (!usedSlots.contains(i)) {
                    player.getInventory().setItem(i, placeholder.clone());
                    usedSlots.add(i);
                }
            }
        }

        // 应用初始护甲设置
        applyInitialArmor(player, gameName);

        // 更新物品栏
        player.updateInventory();
    }

    /**
     * 应用初始护甲设置
     *
     * @param player 玩家
     * @param gameName 游戏名称
     */
    private void applyInitialArmor(Player player, String gameName) {
        // 获取游戏配置
        FileConfiguration config = gameManager.getGameConfig(gameName);
        if (config == null) {
            plugin.getLogger().warning("无法获取游戏 " + gameName + " 的配置，无法应用初始护甲");
            return;
        }

        // 获取护甲设置
        String upperArmorId = config.getString("armorEquipment.upper");
        String lowerArmorId = config.getString("armorEquipment.lower");

        plugin.getLogger().info("游戏 " + gameName + " 的初始护甲设置: 上装=" + upperArmorId + ", 下装=" + lowerArmorId);

        // 应用上装护甲
        if (upperArmorId != null && !upperArmorId.isEmpty()) {
            // 确保ID格式正确（添加"id"前缀如果需要）
            if (!upperArmorId.startsWith("id") && isNumeric(upperArmorId)) {
                upperArmorId = "id" + upperArmorId;
            }

            // 使用ShootPluginHelper给玩家穿戴护甲
            ShootPluginHelper shootHelper = plugin.getShootPluginHelper();
            if (shootHelper != null) {
                boolean success = shootHelper.giveArmorToPlayer(player, upperArmorId);
                if (success) {
                    plugin.getLogger().info("成功给玩家 " + player.getName() + " 穿戴上装护甲: " + upperArmorId);
                } else {
                    plugin.getLogger().warning("给玩家 " + player.getName() + " 穿戴上装护甲失败: " + upperArmorId);
                }
            }
        }

        // 应用下装护甲
        if (lowerArmorId != null && !lowerArmorId.isEmpty()) {
            // 确保ID格式正确（添加"id"前缀如果需要）
            if (!lowerArmorId.startsWith("id") && isNumeric(lowerArmorId)) {
                lowerArmorId = "id" + lowerArmorId;
            }

            // 使用ShootPluginHelper给玩家穿戴护甲
            ShootPluginHelper shootHelper = plugin.getShootPluginHelper();
            if (shootHelper != null) {
                boolean success = shootHelper.giveArmorToPlayer(player, lowerArmorId);
                if (success) {
                    plugin.getLogger().info("成功给玩家 " + player.getName() + " 穿戴下装护甲: " + lowerArmorId);
                } else {
                    plugin.getLogger().warning("给玩家 " + player.getName() + " 穿戴下装护甲失败: " + lowerArmorId);
                }
            }
        }
    }

    /**
     * 判断字符串是否为数字
     *
     * @param str 要检查的字符串
     * @return 如果字符串是数字则返回true，否则返回false
     */
    private boolean isNumeric(String str) {
        if (str == null || str.isEmpty()) {
            return false;
        }
        for (char c : str.toCharArray()) {
            if (!Character.isDigit(c)) {
                return false;
            }
        }
        return true;
    }

    /**
     * 添加槽位信息物品
     *
     * @param player 玩家
     */
    private void addSlotInfoItems(Player player) {
        // 槽位1：铁剑槽位
        if (player.getInventory().getItem(0) == null) {
            ItemStack slotInfo = new ItemStack(Material.IRON_SWORD);
            ItemMeta meta = slotInfo.getItemMeta();
            meta.setDisplayName(ChatColor.GOLD + "铁剑槽位");
            List<String> lore = new ArrayList<>();
            lore.add(ChatColor.GRAY + "此槽位用于放置铁剑");
            meta.setLore(lore);
            slotInfo.setItemMeta(meta);
            player.getInventory().setItem(0, slotInfo);
        }

        // 槽位2：枪支槽位
        if (player.getInventory().getItem(1) == null) {
            ItemStack slotInfo = new ItemStack(Material.IRON_HOE);
            ItemMeta meta = slotInfo.getItemMeta();
            meta.setDisplayName(ChatColor.GOLD + "枪支槽位");
            List<String> lore = new ArrayList<>();
            lore.add(ChatColor.GRAY + "此槽位用于放置枪支");
            meta.setLore(lore);
            slotInfo.setItemMeta(meta);
            player.getInventory().setItem(1, slotInfo);
        }

        // 槽位3：空白槽位
        ItemStack emptySlot = new ItemStack(Material.LIGHT_GRAY_STAINED_GLASS_PANE);
        ItemMeta emptyMeta = emptySlot.getItemMeta();
        emptyMeta.setDisplayName(ChatColor.GRAY + "空白槽位");
        emptySlot.setItemMeta(emptyMeta);

        // 填充槽位3-6为空白槽位
        for (int i = 2; i <= 5; i++) {
            if (player.getInventory().getItem(i) == null) {
                player.getInventory().setItem(i, emptySlot.clone());
            }
        }

        // 槽位7-9：物品槽位
        ItemStack itemSlot = new ItemStack(Material.CHEST);
        ItemMeta itemMeta = itemSlot.getItemMeta();
        itemMeta.setDisplayName(ChatColor.GOLD + "物品槽位");
        List<String> itemLore = new ArrayList<>();
        itemLore.add(ChatColor.GRAY + "此槽位用于放置物品");
        itemMeta.setLore(itemLore);
        itemSlot.setItemMeta(itemMeta);

        for (int i = 6; i <= 8; i++) {
            if (player.getInventory().getItem(i) == null) {
                player.getInventory().setItem(i, itemSlot.clone());
            }
        }
    }

    /**
     * 根据ID创建物品
     *
     * @param itemId 物品ID
     * @return 创建的物品
     */
    private ItemStack createItemById(String itemId) {
        if (itemId == null || itemId.isEmpty()) {
            return null;
        }

        // 记录日志，帮助调试
        plugin.getLogger().info("正在创建物品，ID: " + itemId);

        // 处理特殊物品
        if (itemId.equals("white_dye")) { // 白色染料（用于锁定槽位）
            ItemStack whiteDye = new ItemStack(Material.WHITE_DYE);
            ItemMeta dyeMeta = whiteDye.getItemMeta();
            dyeMeta.setDisplayName(ChatColor.GRAY + "锁定槽位");
            List<String> dyeLore = new ArrayList<>();
            dyeLore.add(ChatColor.RED + "此槽位已锁定");
            dyeMeta.setLore(dyeLore);
            whiteDye.setItemMeta(dyeMeta);
            return whiteDye;
        } else if (itemId.equals("LIGHT_GRAY_DYE")) { // 淡灰色染料（用于未设置的槽位）
            ItemStack grayDye = new ItemStack(Material.LIGHT_GRAY_DYE);
            ItemMeta dyeMeta = grayDye.getItemMeta();
            dyeMeta.setDisplayName(ChatColor.GRAY + "未设置槽位");
            List<String> dyeLore = new ArrayList<>();
            dyeLore.add(ChatColor.YELLOW + "点击设置物品");
            dyeMeta.setLore(dyeLore);
            grayDye.setItemMeta(dyeMeta);
            return grayDye;
        } else if (itemId.equals("GUNPOWDER")) { // 火药（用于未设置的武器槽位）
            ItemStack gunpowder = new ItemStack(Material.GUNPOWDER);
            ItemMeta meta = gunpowder.getItemMeta();
            meta.setDisplayName(ChatColor.GRAY + "未设置武器");
            List<String> lore = new ArrayList<>();
            lore.add(ChatColor.YELLOW + "点击设置武器");
            meta.setLore(lore);
            gunpowder.setItemMeta(meta);
            return gunpowder;
        } else if (itemId.equals("id67") || itemId.equals("67") || itemId.equals(EquipmentConstants.WEAPON_SLOT)) { // 特殊ID: id67/67 - 空武器槽位
            ItemStack weaponSlot = new ItemStack(Material.LIGHT_GRAY_DYE);
            ItemMeta meta = weaponSlot.getItemMeta();
            meta.setDisplayName(ChatColor.GOLD + "武器槽位");
            List<String> lore = new ArrayList<>();
            lore.add(ChatColor.GRAY + "此槽位用于放置武器");
            lore.add(ChatColor.YELLOW + "此槽位未设置武器");
            meta.setLore(lore);
            weaponSlot.setItemMeta(meta);
            plugin.getLogger().info("创建特殊物品: 空武器槽位 (ID: " + itemId + ")");
            return weaponSlot;
        } else if (itemId.equals("id68") || itemId.equals("68") || itemId.equals(EquipmentConstants.EMPTY_ITEM_SLOT)) { // 特殊ID: id68/68 - 空物品槽
            ItemStack emptySlot = new ItemStack(Material.GUNPOWDER);
            ItemMeta meta = emptySlot.getItemMeta();
            meta.setDisplayName(ChatColor.GRAY + "空物品槽");
            List<String> lore = new ArrayList<>();
            lore.add(ChatColor.YELLOW + "此槽位未设置物品");
            meta.setLore(lore);
            emptySlot.setItemMeta(meta);
            plugin.getLogger().info("创建特殊物品: 空物品槽 (ID: " + itemId + ")");
            return emptySlot;
        } else if (itemId.equals("id69") || itemId.equals("69") || itemId.equals(EquipmentConstants.LOCKED_SLOT)) { // 特殊ID: id69/69 - 锁定槽
            ItemStack lockedSlot = new ItemStack(Material.GLASS_PANE);
            ItemMeta meta = lockedSlot.getItemMeta();
            meta.setDisplayName(ChatColor.RED + "锁定槽");
            List<String> lore = new ArrayList<>();
            lore.add(ChatColor.RED + "此槽位已锁定，无法使用");
            meta.setLore(lore);
            lockedSlot.setItemMeta(meta);
            plugin.getLogger().info("创建特殊物品: 锁定槽 (ID: " + itemId + ")");
            return lockedSlot;
        } else if (itemId.equals("id70") || itemId.equals("70")) { // 特殊ID: id70/70 - 僵尸猎人之剑
            plugin.getLogger().info("创建特殊物品: 僵尸猎人之剑 (ID: " + itemId + ")");
            ItemStack ironSword = new ItemStack(Material.IRON_SWORD);
            ItemMeta meta = ironSword.getItemMeta();
            meta.setDisplayName(ChatColor.GOLD + "僵尸猎人之剑");

            List<String> lore = new ArrayList<>();
            lore.add(ChatColor.GRAY + "用于对抗僵尸的基础武器");
            lore.add("");
            lore.add(ChatColor.GREEN + "击杀僵尸可获得金钱奖励");
            meta.setLore(lore);

            // 设置武器属性（如不磨损）
            meta.setUnbreakable(true);

            // 添加附魔效果 - 使用通用名称，确保兼容性
            meta.addEnchant(Enchantment.getByName("DAMAGE_UNDEAD"), 3, true); // 不死生物特效
            meta.addEnchant(Enchantment.getByName("DAMAGE_ALL"), 1, true); // 锋利I

            ironSword.setItemMeta(meta);
            return ironSword;
        }

        // 处理数字ID映射 (1-70)
        try {
            int numId = Integer.parseInt(itemId);
            // 特殊处理ID 70 (僵尸猎人之剑)
            if (numId == 70) {
                plugin.getLogger().info("数字ID " + itemId + " 映射到僵尸猎人之剑");
                return createItemById("id70"); // 使用上面添加的特殊处理逻辑
            } else if (numId >= 1 && numId <= 24) {
                // 1-24 映射到 wp 类型的枪支 (id1~id24)
                String wpId = "id" + numId;
                plugin.getLogger().info("数字ID " + itemId + " 映射到 wp 类型的枪支: " + wpId);

                // 对于枪支类型，使用ShootPluginHelper的giveGunToPlayer方法
                ShootPluginHelper shootHelperForGun = plugin.getShootPluginHelper();
                if (shootHelperForGun != null) {
                    // 创建一个临时玩家物品栏来获取枪支
                    Player tempPlayer = Bukkit.getOnlinePlayers().stream().findFirst().orElse(null);
                    if (tempPlayer != null) {
                        // 保存玩家当前物品栏内容
                        ItemStack[] savedInventory = tempPlayer.getInventory().getContents().clone();

                        // 保存玩家当前经验值和等级
                        float savedExp = tempPlayer.getExp();
                        int savedLevel = tempPlayer.getLevel();

                        try {
                            // 清空玩家物品栏的第一个槽位
                            tempPlayer.getInventory().setItem(0, null);

                            // 直接调用ShootPluginHelper的giveGunToPlayer方法
                            plugin.getLogger().info("尝试使用ShootPluginHelper的giveGunToPlayer方法创建枪支: " + wpId);
                            boolean success = shootHelperForGun.giveGunToPlayer(tempPlayer, wpId);

                            // 获取枪支物品
                            ItemStack gun = tempPlayer.getInventory().getItem(0);

                            if (gun != null) {
                                plugin.getLogger().info("成功创建枪支，ID: " + wpId);

                                // 确保物品有正确的Lore，标记为不可移动
                                ItemMeta meta = gun.getItemMeta();
                                if (meta != null) {
                                    List<String> metaLore = meta.hasLore() ? meta.getLore() : new ArrayList<>();
                                    // 检查是否已经有"此物品不可移动"标记
                                    boolean hasImmovableTag = false;
                                    for (String line : metaLore) {
                                        if (line.contains("此物品不可移动")) {
                                            hasImmovableTag = true;
                                            break;
                                        }
                                    }
                                    // 如果没有，添加标记
                                    if (!hasImmovableTag) {
                                        metaLore.add(ChatColor.RED + "此物品不可移动");
                                        meta.setLore(metaLore);
                                        gun.setItemMeta(meta);
                                    }
                                }

                                return gun.clone(); // 返回克隆的物品，避免引用问题
                            } else {
                                plugin.getLogger().warning("无法通过giveGunToPlayer创建枪支，物品为null，ID: " + wpId);
                            }
                        } catch (Exception e) {
                            plugin.getLogger().warning("通过giveGunToPlayer方法创建枪支失败: " + e.getMessage());
                            e.printStackTrace();
                        } finally {
                            // 恢复玩家物品栏
                            tempPlayer.getInventory().setContents(savedInventory);

                            // 恢复玩家经验值和等级
                            tempPlayer.setExp(savedExp);
                            tempPlayer.setLevel(savedLevel);
                        }
                    }
                }

                // 如果上面的方法失败，使用备用方法
                return createShootPluginItem("wp", wpId);
            } else if (numId >= 25 && numId <= 37) {
                // 25-37 映射到 ar 类型的装备 (id1-id12)
                int arId = numId - 24;
                String arItemId = "id" + arId;
                plugin.getLogger().info("数字ID " + itemId + " 映射到 ar 类型的装备: " + arItemId + " (原始ID: " + arId + ")");
                return createShootPluginItem("ar", arItemId);
            } else if (numId >= 38 && numId <= 67) {
                // 38-67 映射到 it 类型的物品 (id1-id29)
                int itId = numId - 37;
                String itItemId = "id" + itId;
                plugin.getLogger().info("数字ID " + itemId + " 映射到 it 类型的物品: " + itItemId + " (原始ID: " + itId + ")");
                return createShootPluginItem("it", itItemId);
            } else {
                plugin.getLogger().warning("数字ID " + itemId + " 超出有效范围 (1-67)");
                return null;
            }
        } catch (NumberFormatException e) {
            // 不是数字ID，继续处理其他格式
            plugin.getLogger().info("物品ID " + itemId + " 不是数字ID，继续处理其他格式");
        }

        // 处理DeathZombie自定义ID映射
        if (itemId.startsWith("DZ_WP_")) {
            try {
                int wpId = Integer.parseInt(itemId.substring(6));
                if (wpId >= 1 && wpId <= 24) {
                    String shootId = "id" + wpId;

                    // 对于枪支类型，使用ShootPluginHelper的giveGunToPlayer方法
                    ShootPluginHelper shootHelperForGun = plugin.getShootPluginHelper();
                    if (shootHelperForGun != null) {
                        // 创建一个临时玩家物品栏来获取枪支
                        Player tempPlayer = Bukkit.getOnlinePlayers().stream().findFirst().orElse(null);
                        if (tempPlayer != null) {
                            // 保存玩家当前物品栏内容
                            ItemStack[] savedInventory = tempPlayer.getInventory().getContents().clone();

                            // 保存玩家当前经验值和等级
                            float savedExp = tempPlayer.getExp();
                            int savedLevel = tempPlayer.getLevel();

                            try {
                                // 清空玩家物品栏的第一个槽位
                                tempPlayer.getInventory().setItem(0, null);

                                // 直接调用ShootPluginHelper的giveGunToPlayer方法
                                plugin.getLogger().info("尝试使用ShootPluginHelper的giveGunToPlayer方法创建枪支: " + shootId);
                                boolean success = shootHelperForGun.giveGunToPlayer(tempPlayer, shootId);

                                // 获取枪支物品
                                ItemStack gun = tempPlayer.getInventory().getItem(0);

                                if (gun != null) {
                                    plugin.getLogger().info("成功创建枪支，ID: " + shootId);
                                    return gun.clone(); // 返回克隆的物品，避免引用问题
                                } else {
                                    plugin.getLogger().warning("无法通过giveGunToPlayer创建枪支，物品为null，ID: " + shootId);
                                }
                            } catch (Exception e) {
                                plugin.getLogger().warning("通过giveGunToPlayer方法创建枪支失败: " + e.getMessage());
                                e.printStackTrace();
                            } finally {
                                // 恢复玩家物品栏
                                tempPlayer.getInventory().setContents(savedInventory);

                                // 恢复玩家经验值和等级
                                tempPlayer.setExp(savedExp);
                                tempPlayer.setLevel(savedLevel);
                            }
                        }
                    }

                    // 如果上面的方法失败，使用备用方法
                    return createShootPluginItem("wp", shootId);
                }
            } catch (NumberFormatException e) {
                plugin.getLogger().warning("无法解析DZ_WP_ID: " + itemId);
            }
        } else if (itemId.startsWith("DZ_AR_")) {
            try {
                int arId = Integer.parseInt(itemId.substring(6));
                if (arId >= 1 && arId <= 12) {
                    return createShootPluginItem("ar", "id" + arId);
                }
            } catch (NumberFormatException e) {
                plugin.getLogger().warning("无法解析DZ_AR_ID: " + itemId);
            }
        } else if (itemId.startsWith("DZ_IT_")) {
            try {
                int itId = Integer.parseInt(itemId.substring(6));
                if (itId >= 1 && itId <= 29) {
                    return createShootPluginItem("it", "id" + itId);
                }
            } catch (NumberFormatException e) {
                plugin.getLogger().warning("无法解析DZ_IT_ID: " + itemId);
            }
        } else if (itemId.startsWith("DZ_SP_")) {
            try {
                int spId = Integer.parseInt(itemId.substring(6));
                return createShootPluginItem("sp", "id" + spId);
            } catch (NumberFormatException e) {
                plugin.getLogger().warning("无法解析DZ_SP_ID: " + itemId);
            }
        }

        // 处理Shoot插件原始格式
        if (itemId.startsWith("id") && itemId.length() > 2) {
            // 处理Shoot插件的枪支ID (id1, id2, id3, ...)
            try {
                int gunId = Integer.parseInt(itemId.substring(2));
                ShootPluginHelper shootHelperForGun = plugin.getShootPluginHelper();
                if (shootHelperForGun != null) {
                    // 从gameKit.yml获取正确的Shoot插件ID映射
                    GameKitManager gameKitManager = plugin.getGameKitManager();
                    String mappedShootId = itemId; // 默认使用原始ID

                    if (gameKitManager != null) {
                        String shootId = gameKitManager.getShootId(itemId);
                        if (shootId != null && !shootId.isEmpty() && !shootId.equals(itemId)) {
                            mappedShootId = shootId;
                            plugin.getLogger().info("使用gameKit.yml中的映射ID: " + mappedShootId + " 替代 " + itemId);
                        }
                    }

                    // 创建一个临时玩家物品栏来获取枪支
                    Player tempPlayer = Bukkit.getOnlinePlayers().stream().findFirst().orElse(null);
                    if (tempPlayer != null) {
                        // 保存玩家当前物品栏内容
                        ItemStack[] savedInventory = tempPlayer.getInventory().getContents().clone();

                        // 保存玩家当前经验值和等级
                        float savedExp = tempPlayer.getExp();
                        int savedLevel = tempPlayer.getLevel();

                        try {
                            // 清空玩家物品栏的第一个槽位
                            tempPlayer.getInventory().setItem(0, null);

                            // 直接调用ShootPluginHelper的giveGunToPlayer方法，使用映射后的ID
                            plugin.getLogger().info("尝试使用ShootPluginHelper的giveGunToPlayer方法创建枪支: " + mappedShootId);
                            boolean success = shootHelperForGun.giveGunToPlayer(tempPlayer, mappedShootId);

                            // 获取枪支物品
                            ItemStack gun = tempPlayer.getInventory().getItem(0);

                            if (gun != null) {
                                plugin.getLogger().info("成功创建枪支，原始ID: " + itemId + ", 映射ID: " + mappedShootId);
                                return gun.clone(); // 返回克隆的物品，避免引用问题
                            } else {
                                plugin.getLogger().warning("无法通过giveGunToPlayer创建枪支，物品为null，原始ID: " + itemId + ", 映射ID: " + mappedShootId);
                            }
                        } catch (Exception e) {
                            plugin.getLogger().warning("通过giveGunToPlayer方法创建枪支失败: " + e.getMessage());
                            e.printStackTrace();
                        } finally {
                            // 恢复玩家物品栏
                            tempPlayer.getInventory().setContents(savedInventory);

                            // 恢复玩家经验值和等级
                            tempPlayer.setExp(savedExp);
                            tempPlayer.setLevel(savedLevel);
                        }
                    } else {
                        plugin.getLogger().warning("无法获取在线玩家来创建枪支");
                    }
                } else {
                    plugin.getLogger().warning("ShootPluginHelper为空，无法创建枪支");
                }
            } catch (NumberFormatException e) {
                plugin.getLogger().warning("无法解析枪支ID: " + itemId + ", 错误: " + e.getMessage());
            }
        } else if (itemId.startsWith("wp_") && itemId.length() > 3) {
            // 处理武器类型 (wp_id1, wp_id2, ...)
            String gunIdStr = itemId.substring(3);

            // 从gameKit.yml获取正确的Shoot插件ID映射
            GameKitManager gameKitManager = plugin.getGameKitManager();
            String mappedShootId = gunIdStr; // 默认使用原始ID

            if (gameKitManager != null) {
                String shootId = gameKitManager.getShootId(gunIdStr);
                if (shootId != null && !shootId.isEmpty() && !shootId.equals(gunIdStr)) {
                    mappedShootId = shootId;
                    plugin.getLogger().info("使用gameKit.yml中的映射ID: " + mappedShootId + " 替代 " + gunIdStr);
                }
            }

            // 对于枪支类型，使用ShootPluginHelper的giveGunToPlayer方法
            ShootPluginHelper shootHelperForGun = plugin.getShootPluginHelper();
            if (shootHelperForGun != null) {
                // 创建一个临时玩家物品栏来获取枪支
                Player tempPlayer = Bukkit.getOnlinePlayers().stream().findFirst().orElse(null);
                if (tempPlayer != null) {
                    // 保存玩家当前物品栏内容
                    ItemStack[] savedInventory = tempPlayer.getInventory().getContents().clone();

                    // 清空玩家物品栏的第一个槽位
                    tempPlayer.getInventory().setItem(0, null);

                    // 直接调用ShootPluginHelper的giveGunToPlayer方法，使用映射后的ID
                    plugin.getLogger().info("尝试使用映射ID创建枪支: " + mappedShootId);
                    boolean success = shootHelperForGun.giveGunToPlayer(tempPlayer, mappedShootId);

                    // 获取枪支物品
                    ItemStack gun = tempPlayer.getInventory().getItem(0);

                    // 恢复玩家物品栏
                    tempPlayer.getInventory().setContents(savedInventory);

                    if (gun != null) {
                        plugin.getLogger().info("成功创建枪支，原始ID: " + gunIdStr + ", 映射ID: " + mappedShootId);
                        return gun;
                    } else {
                        plugin.getLogger().warning("无法通过giveGunToPlayer创建枪支，原始ID: " + gunIdStr + ", 映射ID: " + mappedShootId + "，尝试使用备用方法");
                    }
                }
            }

            // 如果上面的方法失败，使用备用方法
            return createShootPluginItem("wp", gunIdStr);
        } else if (itemId.startsWith("it_") && itemId.length() > 3) {
            // 处理物品类型 (it_id1, it_id2, ...)
            String itemIdStr = itemId.substring(3);
            return createShootPluginItem("it", itemIdStr);
        } else if (itemId.startsWith("ar_") && itemId.length() > 3) {
            // 处理护甲类型 (ar_id1, ar_id2, ...)
            String armorIdStr = itemId.substring(3);
            return createShootPluginItem("ar", armorIdStr);
        } else if (itemId.startsWith("sp_") && itemId.length() > 3) {
            // 处理特殊物品类型 (sp_id1, sp_id2, ...)
            String specialIdStr = itemId.substring(3);
            return createShootPluginItem("sp", specialIdStr);
        }

        // 处理默认物品
        if (itemId.equals("id1") || itemId.equals("wp_id1") || itemId.equals("iron_sword") || itemId.equals("1")) { // 铁剑
            plugin.getLogger().info("创建铁剑物品，ID: " + itemId);
            ItemStack ironSword = new ItemStack(Material.IRON_SWORD);
            ItemMeta meta = ironSword.getItemMeta();
            meta.setDisplayName(ChatColor.GOLD + "僵尸猎人之剑");

            List<String> lore = new ArrayList<>();
            lore.add(ChatColor.GRAY + "用于对抗僵尸的基础武器");
            lore.add("");
            lore.add(ChatColor.GREEN + "击杀僵尸可获得金钱奖励");
            meta.setLore(lore);

            // 设置武器属性（如不磨损）
            meta.setUnbreakable(true);

            // 添加附魔效果 - 使用通用名称，确保兼容性
            meta.addEnchant(Enchantment.getByName("DAMAGE_UNDEAD"), 3, true); // 不死生物特效
            meta.addEnchant(Enchantment.getByName("DAMAGE_ALL"), 1, true); // 锋利I

            ironSword.setItemMeta(meta);
            return ironSword;
        }

        // 如果以上都不匹配，尝试使用旧的ID格式
        if (itemId.equals("id2")) { // 手枪
            ShootPluginHelper shootHelper = plugin.getShootPluginHelper();
            if (shootHelper != null) {
                return shootHelper.createGun(1); // 假设1是手枪的ID
            }
        }

        // 如果是Minecraft原版物品ID，尝试直接创建
        try {
            // 先尝试直接使用原始ID
            try {
                Material material = Material.valueOf(itemId);
                return new ItemStack(material);
            } catch (IllegalArgumentException e) {
                // 如果失败，尝试转换为大写再验证
                Material material = Material.valueOf(itemId.toUpperCase());
                return new ItemStack(material);
            }
        } catch (IllegalArgumentException e) {
            // 不是有效的Material枚举，检查常见的Minecraft物品
            if (itemId.equals("iron_sword")) {
                return new ItemStack(Material.IRON_SWORD);
            } else if (itemId.equals("diamond_sword")) {
                return new ItemStack(Material.DIAMOND_SWORD);
            } else if (itemId.equals("stone_sword")) {
                return new ItemStack(Material.STONE_SWORD);
            } else if (itemId.equals("wooden_sword")) {
                return new ItemStack(Material.WOODEN_SWORD);
            } else if (itemId.equals("golden_sword")) {
                return new ItemStack(Material.GOLDEN_SWORD);
            } else if (itemId.equals("iron_axe")) {
                return new ItemStack(Material.IRON_AXE);
            } else if (itemId.equals("diamond_axe")) {
                return new ItemStack(Material.DIAMOND_AXE);
            } else if (itemId.equals("stone_axe")) {
                return new ItemStack(Material.STONE_AXE);
            } else if (itemId.equals("wooden_axe")) {
                return new ItemStack(Material.WOODEN_AXE);
            } else if (itemId.equals("golden_axe")) {
                return new ItemStack(Material.GOLDEN_AXE);
            }
            // 不是有效的Material枚举，忽略
        }

        plugin.getLogger().warning("未知的物品ID: " + itemId);
        return null;
    }

    /**
     * 创建Shoot插件物品的辅助方法
     *
     * @param type 物品类型 (wp, it, ar, sp)
     * @param id 物品ID (id1, id2, ...)
     * @return 创建的物品
     */
    private ItemStack createShootPluginItem(String type, String id) {
        // 记录原始ID，用于日志
        String originalId = id;

        // 尝试使用GameKitManager获取Shoot插件ID
        GameKitManager gameKitManager = plugin.getGameKitManager();
        if (gameKitManager != null) {
            // 确保ID格式正确
            String itemId = id;
            if (!id.startsWith("id") && !id.contains("_")) {
                // 如果ID不是以"id"开头且不包含下划线，则添加"id"前缀
                itemId = "id" + id;
                plugin.getLogger().info(String.format("转换物品ID格式: %s -> %s", id, itemId));
            }

            // 获取Shoot插件ID
            String shootId = gameKitManager.getShootId(itemId);
            if (shootId != null && !shootId.isEmpty() && !shootId.equals(itemId)) {
                plugin.getLogger().info(String.format("通过GameKitManager映射物品ID: %s -> %s (原始ID: %s)", itemId, shootId, originalId));
                itemId = shootId;
            }

            // 获取物品材质
            String material = gameKitManager.getItemMaterial(itemId);
            if (material != null) {
                try {
                    Material mat = Material.valueOf(material);
                    ItemStack item = new ItemStack(mat);
                    ItemMeta meta = item.getItemMeta();
                    if (meta != null) {
                        meta.setDisplayName(ChatColor.YELLOW + gameKitManager.getItemName(itemId));
                        List<String> lore = new ArrayList<>();
                        lore.add(ChatColor.GRAY + gameKitManager.getItemDescription(itemId));
                        meta.setLore(lore);
                        item.setItemMeta(meta);
                    }
                    plugin.getLogger().info(String.format("通过GameKitManager创建物品: %s, 材质: %s", itemId, material));
                    return item;
                } catch (IllegalArgumentException e) {
                    plugin.getLogger().warning(String.format("无效的材质: %s, 错误: %s", material, e.getMessage()));
                }
            }
        }

        // 如果GameKitManager不可用或无法创建物品，使用旧的方法
        ShootPluginHelper shootHelper = plugin.getShootPluginHelper();
        if (shootHelper == null) {
            plugin.getLogger().warning(String.format("ShootPluginHelper为空，无法创建物品类型: %s, ID: %s", type, id));
            return createFallbackItem(type, id);
        }

        ItemStack item = null;

        // 确保ID格式正确
        String itemId = id;
        if (!id.startsWith("id") && !id.contains("_")) {
            // 如果ID不是以"id"开头且不包含下划线，则添加"id"前缀
            itemId = "id" + id;
            plugin.getLogger().info(String.format("转换物品ID格式: %s -> %s", id, itemId));
        }

        switch (type) {
            case "wp":
                // 武器类型
                if (itemId.startsWith("id") && itemId.length() > 2) {
                    try {
                        // 获取映射后的Shoot插件ID
                        String mappedShootId = itemId;
                        // 使用已经获取的gameKitManager
                        String shootId = gameKitManager != null ? gameKitManager.getShootId(itemId) : null;
                        if (shootId != null && !shootId.isEmpty() && !shootId.equals(itemId)) {
                            mappedShootId = shootId;
                            plugin.getLogger().info("使用gameKit.yml中的映射ID: " + mappedShootId + " 替代 " + itemId);
                        }

                        // 使用临时玩家直接调用Shoot插件的giveGunToPlayer方法
                        Player tempPlayer = Bukkit.getOnlinePlayers().stream().findFirst().orElse(null);
                        if (tempPlayer != null) {
                            // 保存玩家当前物品栏内容
                            ItemStack[] savedInventory = tempPlayer.getInventory().getContents().clone();

                            // 保存玩家当前经验值和等级
                            float savedExp = tempPlayer.getExp();
                            int savedLevel = tempPlayer.getLevel();

                            try {
                                // 清空玩家物品栏的第一个槽位
                                tempPlayer.getInventory().setItem(0, null);

                                // 直接调用ShootPluginHelper的giveGunToPlayer方法，使用映射后的ID
                                plugin.getLogger().info("尝试使用ShootPluginHelper的giveGunToPlayer方法创建枪支: " + mappedShootId);
                                shootHelper.giveGunToPlayer(tempPlayer, mappedShootId);

                                // 获取枪支物品
                                item = tempPlayer.getInventory().getItem(0);

                                if (item != null) {
                                    plugin.getLogger().info("成功通过Shoot插件创建枪支: " + mappedShootId);
                                    item = item.clone(); // 克隆物品，避免引用问题
                                } else {
                                    plugin.getLogger().warning("通过Shoot插件创建枪支失败，物品为null，尝试备用方法");
                                }
                            } catch (Exception e) {
                                plugin.getLogger().warning("通过giveGunToPlayer方法创建枪支失败: " + e.getMessage());
                                e.printStackTrace();
                            } finally {
                                // 恢复玩家物品栏
                                tempPlayer.getInventory().setContents(savedInventory);

                                // 恢复玩家经验值和等级
                                tempPlayer.setExp(savedExp);
                                tempPlayer.setLevel(savedLevel);
                            }

                            // 如果上面的方法失败，尝试使用createGun方法
                            if (item == null) {
                                try {
                                    int gunId = Integer.parseInt(itemId.substring(2));
                                    item = shootHelper.createGun(gunId);
                                    if (item != null) {
                                        plugin.getLogger().info("使用备用方法createGun成功创建枪支，ID: " + gunId);
                                    }
                                } catch (NumberFormatException e) {
                                    plugin.getLogger().warning("无法解析枪支ID: " + itemId + ", 错误: " + e.getMessage());
                                }
                            }
                        } else {
                            plugin.getLogger().warning("无法找到在线玩家来创建枪支，尝试备用方法");
                            // 如果没有在线玩家，尝试使用createGun方法
                            try {
                                int gunId = Integer.parseInt(itemId.substring(2));
                                item = shootHelper.createGun(gunId);
                                if (item != null) {
                                    plugin.getLogger().info("使用备用方法createGun成功创建枪支，ID: " + gunId);
                                }
                            } catch (NumberFormatException e) {
                                plugin.getLogger().warning("无法解析枪支ID: " + itemId + ", 错误: " + e.getMessage());
                            }
                        }

                        if (item == null) {
                            plugin.getLogger().warning(String.format("无法通过ID创建枪支: %s", itemId));
                        }
                    } catch (NumberFormatException e) {
                        plugin.getLogger().warning(String.format("无法解析枪支ID: %s, 错误: %s", itemId, e.getMessage()));
                    }
                } else {
                    plugin.getLogger().warning(String.format("无效的枪支ID格式: %s", itemId));
                }
                break;
            case "it":
                // 物品类型
                item = shootHelper.createItem(type, itemId);
                if (item == null) {
                    plugin.getLogger().warning(String.format("无法创建物品类型: %s, ID: %s", type, itemId));
                }
                break;
            case "ar":
                // 护甲类型
                if (itemId.startsWith("id") && itemId.length() > 2) {
                    try {
                        int armorId = Integer.parseInt(itemId.substring(2));
                        item = shootHelper.createArmor(armorId);
                        if (item == null) {
                            plugin.getLogger().warning(String.format("无法通过ID创建护甲: %s (ID: %d)", itemId, armorId));
                        }
                    } catch (NumberFormatException e) {
                        plugin.getLogger().warning(String.format("无法解析护甲ID: %s, 错误: %s", itemId, e.getMessage()));
                    }
                } else {
                    plugin.getLogger().warning(String.format("无效的护甲ID格式: %s", itemId));
                }
                break;
            case "sp":
                // 特殊物品类型
                if (itemId.startsWith("id") && itemId.length() > 2) {
                    try {
                        int specialId = Integer.parseInt(itemId.substring(2));
                        item = shootHelper.createSpecialItem(specialId);
                        if (item == null) {
                            plugin.getLogger().warning(String.format("无法通过ID创建特殊物品: %s (ID: %d)", itemId, specialId));
                        }
                    } catch (NumberFormatException e) {
                        plugin.getLogger().warning(String.format("无法解析特殊物品ID: %s, 错误: %s", itemId, e.getMessage()));
                    }
                } else {
                    plugin.getLogger().warning(String.format("无效的特殊物品ID格式: %s", itemId));
                }
                break;
            default:
                plugin.getLogger().warning(String.format("未知的物品类型: %s", type));
                break;
        }

        if (item != null) {
            plugin.getLogger().info(String.format("成功创建Shoot插件物品，类型: %s, ID: %s", type, itemId));
            return item;
        } else {
            plugin.getLogger().warning(String.format("无法创建Shoot插件物品，类型: %s, ID: %s，使用备用物品", type, itemId));
            return createFallbackItem(type, itemId);
        }
    }

    /**
     * 创建备用物品（当无法从Shoot插件获取物品时使用）
     *
     * @param type 物品类型
     * @param id 物品ID
     * @return 创建的备用物品
     */
    private ItemStack createFallbackItem(String type, String id) {
        // 提取数字ID
        int numId = 1;
        if (id.startsWith("id") && id.length() > 2) {
            try {
                numId = Integer.parseInt(id.substring(2));
            } catch (NumberFormatException e) {
                // 忽略解析错误
            }
        }

        // 创建通用图标
        ItemStack item;
        String displayName;
        String idType;

        // 根据类型创建不同的备用物品
        switch (type) {
            case "wp":
                // 武器类型
                item = new ItemStack(Material.WOODEN_HOE);
                displayName = String.format("§6武器 #%d", numId); // §6 是金色
                idType = "武器";
                break;
            case "it":
                // 物品类型
                item = new ItemStack(Material.GUNPOWDER);
                displayName = String.format("§a物品 #%d", numId); // §a 是绿色
                idType = "物品";
                break;
            case "ar":
                // 护甲类型
                item = new ItemStack(Material.LEATHER_CHESTPLATE);
                displayName = String.format("§9护甲 #%d", numId); // §9 是蓝色
                idType = "护甲";
                break;
            case "sp":
                // 特殊物品类型
                item = new ItemStack(Material.NETHER_STAR);
                displayName = String.format("§d特殊物品 #%d", numId); // §d 是淡紫色
                idType = "特殊物品";
                break;
            default:
                // 默认返回一个苹果
                item = new ItemStack(Material.APPLE);
                displayName = "§c未知物品"; // §c 是红色
                idType = "未知";
                break;
        }

        // 设置物品属性
        ItemMeta meta = item.getItemMeta();
        if (meta != null) {
            meta.setDisplayName(displayName);

            List<String> lore = new ArrayList<>();
            lore.add(String.format("§7%s ID: %s", idType, id)); // §7 是灰色
            lore.add("§c无法从Shoot插件获取，使用备用物品"); // §c 是红色

            // 添加通用图标标识
            lore.add("§8这是一个通用图标"); // §8 是深灰色

            meta.setLore(lore);
            item.setItemMeta(meta);
        }

        return item;
    }

    /**
     * 获取游戏的初始装备配置
     *
     * @param gameName 游戏名称
     * @return 初始装备配置 <槽位, 物品ID>
     */
    public Map<Integer, String> getGameInitialEquipment(String gameName) {
        if (!gameManager.gameExists(gameName)) {
            return null;
        }

        FileConfiguration config = gameManager.getGameConfig(gameName);
        if (config == null) {
            return null;
        }

        Map<Integer, String> equipment = new HashMap<>();

        // 检查游戏是否有自定义初始装备配置
        if (config.contains("initialEquipment")) {
            ConfigurationSection equipSection = config.getConfigurationSection("initialEquipment");
            if (equipSection != null) {
                for (String key : equipSection.getKeys(false)) {
                    try {
                        // 尝试将键解析为整数（新格式）
                        int slot = Integer.parseInt(key);
                        String itemId = equipSection.getString(key);

                        // 处理旧格式的槽位0（转换为新格式的槽位1）
                        if (slot == 0) {
                            plugin.getLogger().info("检测到旧格式槽位0，转换为新格式槽位1，物品ID: " + itemId);
                            slot = 1;
                        }

                        // 转换纯数字ID为idx格式
                        if (itemId != null && !itemId.isEmpty()) {
                            try {
                                int numId = Integer.parseInt(itemId);
                                // 只处理1-69范围内的数字ID
                                if (numId >= 1 && numId <= 69) {
                                    String newItemId = "id" + numId;
                                    plugin.getLogger().info("将数字ID " + itemId + " 转换为idx格式: " + newItemId);
                                    itemId = newItemId;
                                }
                            } catch (NumberFormatException e) {
                                // 不是数字ID，不需要转换
                            }
                        }

                        // 特殊处理特定ID
                        if (itemId != null) {
                            if (itemId.equals("67")) {
                                itemId = "id67";
                            } else if (itemId.equals("68")) {
                                itemId = "id68";
                            } else if (itemId.equals("69")) {
                                itemId = "id69";
                            }
                        }

                        equipment.put(slot, itemId);
                    } catch (NumberFormatException e) {
                        // 如果解析失败，检查是否是旧格式（slotX）
                        if (key.startsWith("slot")) {
                            try {
                                int slot = Integer.parseInt(key.substring(4));
                                String itemId = equipSection.getString(key);

                                // 处理旧格式的槽位0（转换为新格式的槽位1）
                                if (slot == 0) {
                                    plugin.getLogger().info("检测到旧格式槽位0（slotX格式），转换为新格式槽位1，物品ID: " + itemId);
                                    slot = 1;
                                }

                                // 转换纯数字ID为idx格式
                                if (itemId != null && !itemId.isEmpty()) {
                                    try {
                                        int numId = Integer.parseInt(itemId);
                                        // 只处理1-69范围内的数字ID
                                        if (numId >= 1 && numId <= 69) {
                                            String newItemId = "id" + numId;
                                            plugin.getLogger().info("将数字ID " + itemId + " 转换为idx格式: " + newItemId);
                                            itemId = newItemId;
                                        }
                                    } catch (NumberFormatException ex) {
                                        // 不是数字ID，不需要转换
                                    }
                                }

                                // 特殊处理特定ID
                                if (itemId != null) {
                                    if (itemId.equals("67")) {
                                        itemId = "id67";
                                    } else if (itemId.equals("68")) {
                                        itemId = "id68";
                                    } else if (itemId.equals("69")) {
                                        itemId = "id69";
                                    }
                                }

                                equipment.put(slot, itemId);
                            } catch (NumberFormatException ex) {
                                plugin.getLogger().warning("无效的槽位配置: " + key);
                            }
                        }
                    }
                }
            }
        }

        // 如果没有自定义配置，使用默认配置
        if (equipment.isEmpty()) {
            // 默认配置：槽位1为铁剑，槽位2为手枪
            equipment.put(1, "id1"); // 铁剑
            equipment.put(2, "id2"); // 手枪

            // 槽位3为空武器槽位
            equipment.put(3, "id67"); // 空武器槽位，使用idx格式

            // 槽位6-8为物品槽
            equipment.put(6, "id68"); // 空物品槽，使用idx格式
            equipment.put(7, "id68"); // 空物品槽，使用idx格式
            equipment.put(8, "id68"); // 空物品槽，使用idx格式

            // 其他槽位设置为锁定槽位
            for (int i = 4; i <= 5; i++) {
                equipment.put(i, "id69"); // 锁定槽位，使用idx格式
            }
            for (int i = 9; i <= 35; i++) {
                equipment.put(i, "id69"); // 锁定槽位，使用idx格式
            }

            // 最后一个槽位设置为白色染料
            equipment.put(36, "white_dye");
        }

        return equipment;
    }

    /**
     * 设置游戏的初始装备
     *
     * @param gameName 游戏名称
     * @param slot 槽位
     * @param itemId 物品ID
     * @return 是否成功设置
     */
    public boolean setGameInitialEquipment(String gameName, int slot, String itemId) {
        plugin.getLogger().info("开始设置游戏 " + gameName + " 的初始装备，槽位: " + slot + ", 物品ID: " + itemId);

        if (!gameManager.gameExists(gameName)) {
            plugin.getLogger().warning("尝试设置不存在的游戏 " + gameName + " 的初始装备");
            return false;
        }

        // 验证槽位是否有效 - 只支持1-36（新格式）
        if (slot < 1 || slot > 36) {
            plugin.getLogger().warning("尝试设置无效的槽位 " + slot + "，有效范围为1-36");
            return false;
        }

        // 转换纯数字ID为idx格式
        if (itemId != null && !itemId.isEmpty()) {
            try {
                int numId = Integer.parseInt(itemId);
                // 只处理1-69范围内的数字ID
                if (numId >= 1 && numId <= 69) {
                    String newItemId = "id" + numId;
                    plugin.getLogger().info("将数字ID " + itemId + " 转换为idx格式: " + newItemId);
                    itemId = newItemId;
                }
            } catch (NumberFormatException e) {
                // 不是数字ID，不需要转换
            }
        }

        // 特殊处理槽位1的铁剑
        if (slot == 1 && itemId != null && (itemId.equals("id1"))) {
            plugin.getLogger().info("检测到槽位1设置为" + itemId + "，自动转换为iron_sword");
            itemId = EquipmentConstants.IRON_SWORD;
        }

        // 特殊处理槽位2的手枪
        if (slot == 2 && itemId != null && (itemId.equals("id2"))) {
            plugin.getLogger().info("检测到槽位2设置为" + itemId + "，确保使用id2");
            itemId = "id2";
        }

        // 特殊处理武器槽位
        if (slot == 3 && (itemId == null || itemId.isEmpty())) {
            plugin.getLogger().info("检测到武器槽位" + slot + "为空，设置为空武器槽位");
            itemId = "id67"; // 使用idx格式替代纯数字"67"
        }

        // 特殊处理物品槽位
        if ((slot == 6 || slot == 7 || slot == 8) && (itemId == null || itemId.isEmpty())) {
            plugin.getLogger().info("检测到物品槽位" + slot + "为空，设置为空物品槽");
            itemId = "id68"; // 使用idx格式替代纯数字"68"
        }

        // 特殊处理锁定槽位
        if ((slot >= 4 && slot <= 5) || (slot >= 9 && slot <= 35)) {
            if (itemId == null || itemId.isEmpty() || itemId.equals("white_dye")) {
                plugin.getLogger().info("检测到锁定槽位" + slot + "为空或白色染料，设置为锁定槽位");
                itemId = "id69"; // 使用idx格式替代纯数字"69"
            }
        }

        // 特殊处理最后一个槽位
        if (slot == 36 && (itemId == null || itemId.isEmpty())) {
            plugin.getLogger().info("检测到最后一个槽位为空，设置为白色染料");
            itemId = EquipmentConstants.WHITE_DYE; // "white_dye"
        }

        // 验证物品ID是否有效
        if (itemId != null && !itemId.isEmpty()
                && !isValidItemId(itemId)
                && !itemId.equals("white_dye")
                && !itemId.equals("id67")
                && !itemId.equals("id68")
                && !itemId.equals("id69")) {
            plugin.getLogger().warning("尝试设置无效的物品ID: " + itemId);
            return false;
        }

        // 获取游戏配置
        FileConfiguration config = gameManager.getGameConfig(gameName);
        if (config == null) {
            plugin.getLogger().warning("无法获取游戏 " + gameName + " 的配置");
            return false;
        }

        // 设置初始装备 - 使用新格式（1-36）
        String path = "initialEquipment." + slot;
        plugin.getLogger().info("设置配置路径: " + path + " = " + itemId);
        config.set(path, itemId);

        // 检查配置是否已设置
        if (!config.contains(path)) {
            plugin.getLogger().warning("配置设置失败，路径 " + path + " 不存在");
        } else {
            plugin.getLogger().info("配置已设置，当前值: " + config.getString(path));
        }

        // 保存配置
        try {
            boolean saved = gameManager.saveGameConfig(gameName);
            if (saved) {
                plugin.getLogger().info("成功保存游戏 " + gameName + " 的配置文件");

                // 重新加载配置以验证保存是否成功
                FileConfiguration reloadedConfig = gameManager.getGameConfig(gameName);
                if (reloadedConfig != null && reloadedConfig.contains(path)) {
                    String savedValue = reloadedConfig.getString(path);
                    plugin.getLogger().info("验证保存结果: 路径 " + path + " = " + savedValue);
                    if (savedValue != null && savedValue.equals(itemId)) {
                        plugin.getLogger().info("成功设置游戏 " + gameName + " 的槽位 " + slot + " 的物品为 " + itemId);
                        return true;
                    } else {
                        plugin.getLogger().warning("保存验证失败: 期望值 " + itemId + "，实际值 " + savedValue);
                    }
                } else {
                    plugin.getLogger().warning("保存验证失败: 重新加载后路径 " + path + " 不存在");
                }
            } else {
                plugin.getLogger().warning("游戏配置保存方法返回失败");
            }

            return saved;
        } catch (Exception e) {
            plugin.getLogger().warning("保存游戏 " + gameName + " 的初始装备配置失败: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 移除指定游戏的指定槽位的初始装备
     *
     * @param gameName 游戏名称
     * @param slot 槽位
     * @return 是否成功移除
     */
    public boolean removeGameInitialEquipment(String gameName, int slot) {
        if (!gameManager.gameExists(gameName)) {
            plugin.getLogger().warning("尝试移除不存在的游戏 " + gameName + " 的初始装备");
            return false;
        }

        // 验证槽位是否有效 - 只支持1-36（新格式）
        if (slot < 1 || slot > 36) {
            plugin.getLogger().warning("尝试移除无效的槽位 " + slot + "，有效范围为1-36");
            return false;
        }

        // 获取游戏配置
        FileConfiguration config = gameManager.getGameConfig(gameName);
        if (config == null) {
            plugin.getLogger().warning("无法获取游戏 " + gameName + " 的配置");
            return false;
        }

        // 移除初始装备
        String path = "initialEquipment." + slot;
        if (!config.contains(path)) {
            plugin.getLogger().info("游戏 " + gameName + " 的槽位 " + slot + " 没有设置初始装备");
            return true; // 已经不存在，视为成功
        }

        config.set(path, null);

        // 保存配置
        try {
            gameManager.saveGameConfig(gameName);
            plugin.getLogger().info("成功移除游戏 " + gameName + " 的槽位 " + slot + " 的初始装备");
            return true;
        } catch (Exception e) {
            plugin.getLogger().severe("移除游戏 " + gameName + " 的初始装备时出错: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 清除指定游戏的所有初始装备
     *
     * @param gameName 游戏名称
     * @return 是否成功清除
     */
    public boolean clearGameInitialEquipment(String gameName) {
        if (!gameManager.gameExists(gameName)) {
            plugin.getLogger().warning("尝试清除不存在的游戏 " + gameName + " 的初始装备");
            return false;
        }

        // 获取游戏配置
        FileConfiguration config = gameManager.getGameConfig(gameName);
        if (config == null) {
            plugin.getLogger().warning("无法获取游戏 " + gameName + " 的配置");
            return false;
        }

        // 清除初始装备
        config.set("initialEquipment", null);

        // 保存配置
        try {
            gameManager.saveGameConfig(gameName);
            plugin.getLogger().info("成功清除游戏 " + gameName + " 的所有初始装备");
            return true;
        } catch (Exception e) {
            plugin.getLogger().severe("清除游戏 " + gameName + " 的初始装备时出错: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 检查物品ID是否有效
     *
     * @param itemId 物品ID
     * @return 是否有效
     */
    private boolean isValidItemId(String itemId) {
        if (itemId == null || itemId.isEmpty()) {
            return false;
        }

        // 记录日志，帮助调试
        plugin.getLogger().info("正在验证物品ID: " + itemId);

        // 使用GameKitManager验证物品ID
        GameKitManager gameKitManager = plugin.getGameKitManager();
        if (gameKitManager != null) {
            boolean isValid = gameKitManager.isValidItemId(itemId);
            if (isValid) {
                plugin.getLogger().info("物品ID " + itemId + " 通过GameKitManager验证有效");
            } else {
                plugin.getLogger().warning("物品ID " + itemId + " 通过GameKitManager验证无效");
            }
            return isValid;
        }

        // 如果GameKitManager不可用，使用旧的验证逻辑
        plugin.getLogger().warning("GameKitManager不可用，使用旧的验证逻辑");

        // 检查是否是铁剑（特殊处理）
        if (itemId.equals(EquipmentConstants.IRON_SWORD) || itemId.equals("1")) {
            plugin.getLogger().info("物品ID " + itemId + " 是有效的铁剑ID");
            return true;
        }

        // 支持多种格式的物品ID
        // 1. Shoot插件格式: id1, id2, wp_id1, it_id1, ar_id1, sp_id1
        // 2. Minecraft原版物品: IRON_SWORD, DIAMOND_PICKAXE等
        // 3. 自定义前缀: SHOOT_PISTOL, SHOOT_RIFLE等
        // 4. 特殊物品: white_dye, LIGHT_GRAY_DYE等
        // 5. DeathZombie自定义ID映射: DZ_WP_1, DZ_AR_1, DZ_IT_1等
        // 检查Shoot插件格式
        if (itemId.startsWith("id") || itemId.startsWith("wp_") || itemId.startsWith("it_")
                || itemId.startsWith("ar_") || itemId.startsWith("sp_")) {
            plugin.getLogger().info("物品ID " + itemId + " 是有效的Shoot插件格式");
            return true;
        }

        // 检查DeathZombie自定义ID映射格式
        if (itemId.startsWith("DZ_WP_") || itemId.startsWith("DZ_AR_") || itemId.startsWith("DZ_IT_")
                || itemId.startsWith("DZ_SP_")) {
            plugin.getLogger().info("物品ID " + itemId + " 是有效的DeathZombie自定义ID映射格式");
            return true;
        }

        // 检查Minecraft原版物品和自定义前缀
        try {
            // 尝试解析为Material枚举，验证是否为有效的Minecraft物品
            // 先尝试直接使用原始ID
            try {
                Material.valueOf(itemId);
                plugin.getLogger().info("物品ID " + itemId + " 是有效的Minecraft原版物品");
                return true;
            } catch (IllegalArgumentException e) {
                // 如果失败，尝试转换为大写再验证
                Material.valueOf(itemId.toUpperCase());
                plugin.getLogger().info("物品ID " + itemId + " 是有效的Minecraft原版物品（转换为大写后）");
                return true;
            }
        } catch (IllegalArgumentException e) {
            // 不是有效的Material枚举，但仍可能是自定义前缀
            if (itemId.toUpperCase().startsWith("SHOOT_")
                    || itemId.toUpperCase().contains("PISTOL")
                    || itemId.toUpperCase().contains("RIFLE")
                    || itemId.toUpperCase().contains("GUN")
                    || itemId.toUpperCase().contains("ARMOR")
                    || itemId.toUpperCase().contains("ITEM")) {
                plugin.getLogger().info("物品ID " + itemId + " 是有效的自定义前缀物品");
                return true;
            }

            // 检查常见的Minecraft物品ID
            if (itemId.equals("iron_sword")
                    || itemId.equals("diamond_sword")
                    || itemId.equals("stone_sword")
                    || itemId.equals("wooden_sword")
                    || itemId.equals("golden_sword")
                    || itemId.equals("iron_axe")
                    || itemId.equals("diamond_axe")
                    || itemId.equals("stone_axe")
                    || itemId.equals("wooden_axe")
                    || itemId.equals("golden_axe")) {
                plugin.getLogger().info("物品ID " + itemId + " 是有效的Minecraft武器物品");
                return true;
            }
        }

        // 检查特殊物品
        if (itemId.equals("white_dye") || itemId.equals("LIGHT_GRAY_DYE") || itemId.equals("GUNPOWDER")
                || itemId.equals("67") || itemId.equals("68") || itemId.equals("69")
                || itemId.equals("id67") || itemId.equals("id68") || itemId.equals("id69")
                || itemId.equals(EquipmentConstants.WEAPON_SLOT) || itemId.equals(EquipmentConstants.EMPTY_ITEM_SLOT)
                || itemId.equals(EquipmentConstants.LOCKED_SLOT)) {
            plugin.getLogger().info("物品ID " + itemId + " 是有效的特殊物品");
            return true;
        }

        // 检查是否是数字ID (1-69)，用于直接映射到Shoot插件物品或特殊槽位
        try {
            int numId = Integer.parseInt(itemId);
            if (numId >= 1 && numId <= 67) { // 支持1-67的数字ID
                plugin.getLogger().info("物品ID " + itemId + " 是有效的数字ID映射 (1-67)");
                return true;
            } else if (numId == 68) { // 特殊ID: 68 - 空物品槽
                plugin.getLogger().info("物品ID " + itemId + " 是有效的特殊ID: 空物品槽");
                return true;
            } else if (numId == 69) { // 特殊ID: 69 - 锁定槽
                plugin.getLogger().info("物品ID " + itemId + " 是有效的特殊ID: 锁定槽");
                return true;
            }
        } catch (NumberFormatException e) {
            // 不是数字ID，忽略
        }

        // 如果都不匹配，返回false
        plugin.getLogger().warning("物品ID " + itemId + " 无效");
        return false;
    }

    /**
     * 初始化玩家金钱
     *
     * @param player 玩家
     */
    private void initializePlayerMoney(Player player) {
        // 使用ShootPluginHelper设置玩家初始金钱为0
        plugin.getShootPluginHelper().setPlayerMoney(player, 0.0);

        // 发送金钱初始化消息
        player.sendMessage(ChatColor.YELLOW + "你的游戏金钱已初始化为0");
    }

    /**
     * 结束游戏
     *
     * @param gameName 游戏名称
     */
    public void endGame(String gameName) {
        if (!runningGames.containsKey(gameName)) {
            return;
        }

        plugin.getLogger().info("正在结束游戏: " + gameName);
        runningGames.put(gameName, GameState.ENDED);

        // 从游戏中移除所有玩家并重置状态
        Set<UUID> participants = new HashSet<>(gameParticipants.get(gameName));
        PlayerInteractionManager playerManager = plugin.getPlayerInteractionManager();

        for (UUID playerId : participants) {
            playerGames.remove(playerId);
            Player player = plugin.getServer().getPlayer(playerId);
            if (player != null && player.isOnline()) {
                playerManager.setPlayerStatus(player, PlayerStatus.NOT_JOINED);
            }
        }

        gameParticipants.get(gameName).clear();

        // 恢复所有门区域的方块
        plugin.getLogger().info("游戏结束时恢复所有门区域的方块");
        DoorManager doorManager = plugin.getDoorManager();
        Map<String, Map<String, Object>> doors = plugin.getGameManager().getDoors(gameName);
        if (doors != null && !doors.isEmpty()) {
            for (Map.Entry<String, Map<String, Object>> entry : doors.entrySet()) {
                String doorName = entry.getKey();
                // 恢复门区域的方块
                doorManager.fillDoorBlocks(gameName, doorName);
                plugin.getLogger().info("已恢复门 '" + doorName + "' 的方块");
            }
        }

        // 清理门交互点（NPC或悬浮文字）
        if (plugin.isDecentHologramsAvailable()) {
            plugin.getLogger().info("清理游戏 " + gameName + " 的门悬浮文字");
            plugin.getDoorHologramManager().cleanupDoorHolograms(gameName);
        } else {
            plugin.getLogger().info("清理游戏 " + gameName + " 的门NPC");
            plugin.getDoorNPCManager().onGameEnd(gameName);
        }

        // 清理购买点悬浮文字
        if (plugin.isDecentHologramsAvailable()) {
            plugin.getLogger().info("清理游戏 " + gameName + " 的购买点悬浮文字");
            plugin.getShopHologramManager().cleanupShopHolograms(gameName);

            // 清理全局电源按钮效果
            if (plugin.getPowerButtonEffectManager() != null) {
                plugin.getPowerButtonEffectManager().removePowerButtonEffects(gameName);
                plugin.getLogger().info("已清理游戏 " + gameName + " 的全局电源按钮效果");
            }
        }

        // 清理所有悬浮文字（使用HologramHelper的通用清理方法）
        if (plugin.isDecentHologramsAvailable() && plugin.getHologramHelper() != null) {
            plugin.getLogger().info("清理游戏 " + gameName + " 的所有其他悬浮文字");
            plugin.getHologramHelper().clearGameHolograms(gameName);
        }

        // 清理灵魂NPC
        plugin.getLogger().info("清理游戏 " + gameName + " 的灵魂NPC");
        plugin.getPlayerDeathReviveListener().cleanupAllSoulNPCs();

        // 清理所有僵尸
        plugin.getLogger().info("清理游戏 " + gameName + " 的所有僵尸");
        plugin.getZombieSpawnManager().clearAllZombies(gameName);

        // 清理窗户资源
        plugin.getLogger().info("清理游戏 " + gameName + " 的窗户资源");
        plugin.getWindowManager().cleanupGame(gameName);

        // 清理实体名称显示数据
        if (plugin.getEntityNameDisplayListener() != null) {
            plugin.getLogger().info("清理游戏 " + gameName + " 的实体名称显示数据");
            plugin.getEntityNameDisplayListener().cleanupGame(gameName);
        }

        // 清理幸运箱和抽奖全息图
        plugin.getLogger().info("清理游戏 " + gameName + " 的幸运箱和抽奖全息图");
        plugin.getLuckyBoxManager().clearLuckyBoxes(gameName);

        // 重置计分板计时器
        plugin.getLogger().info("重置游戏 " + gameName + " 的计分板计时器");
        plugin.getScoreboardManager().resetGameTimers(gameName);

        // 重置门状态
        plugin.getLogger().info("重置游戏 " + gameName + " 的门状态");
        plugin.getDoorManager().resetDoors(gameName);

        // 保存会话状态
        saveSessionData();

        plugin.getLogger().info("游戏 " + gameName + " 已结束");
    }

    /**
     * 获取玩家当前所在的游戏
     *
     * @param player 玩家
     * @return 游戏名称，如果不在游戏中则返回null
     */
    public String getPlayerGame(Player player) {
        return playerGames.get(player.getUniqueId());
    }

    /**
     * 获取游戏当前状态
     *
     * @param gameName 游戏名称
     * @return 游戏状态，如果游戏不存在则返回null
     */
    public GameState getGameState(String gameName) {
        return runningGames.get(gameName);
    }

    /**
     * 获取游戏中的玩家数量
     *
     * @param gameName 游戏名称
     * @return 玩家数量，如果游戏不存在则返回0
     */
    public int getPlayerCount(String gameName) {
        if (!gameParticipants.containsKey(gameName)) {
            return 0;
        }

        return gameParticipants.get(gameName).size();
    }

    /**
     * 获取游戏中的所有玩家
     *
     * @param gameName 游戏名称
     * @return 玩家UUID集合，如果游戏不存在则返回空集合
     */
    public Set<UUID> getGameParticipants(String gameName) {
        if (!gameParticipants.containsKey(gameName)) {
            return new HashSet<>();
        }

        return new HashSet<>(gameParticipants.get(gameName));
    }

    /**
     * 检查玩家是否在游戏中
     *
     * @param player 玩家
     * @return 是否在游戏中
     */
    public boolean isPlayerInGame(Player player) {
        return playerGames.containsKey(player.getUniqueId());
    }

    /**
     * 检查玩家是否在指定游戏中
     *
     * @param player 玩家
     * @param gameName 游戏名称
     * @return 是否在指定游戏中
     */
    public boolean isPlayerInGame(Player player, String gameName) {
        return playerGames.containsKey(player.getUniqueId()) && playerGames.get(player.getUniqueId()).equals(gameName);
    }

    /**
     * 检查玩家是否在指定游戏中（使用UUID）
     *
     * @param playerId 玩家UUID
     * @param gameName 游戏名称
     * @return 是否在指定游戏中
     */
    public boolean isPlayerInGame(UUID playerId, String gameName) {
        return playerGames.containsKey(playerId) && playerGames.get(playerId).equals(gameName);
    }

    /**
     * 获取所有运行中游戏的状态
     *
     * @return 游戏状态映射 <游戏名, 状态>
     */
    public Map<String, GameState> getRunningGamesWithStates() {
        return new HashMap<>(runningGames);
    }

    /**
     * 获取所有运行中游戏的名称
     *
     * @return 游戏名称集合
     */
    public Set<String> getRunningGames() {
        return new HashSet<>(runningGames.keySet());
    }

    /**
     * 重置游戏会话 将游戏从ENDED状态重置为WAITING状态
     *
     * @param gameName 游戏名称
     * @return 是否成功重置
     */
    public boolean resetGame(String gameName) {
        // 检查游戏是否存在并且已启用
        if (!gameManager.gameExists(gameName) || !gameManager.isGameEnabled(gameName)) {
            return false;
        }

        // 获取当前游戏中的所有玩家，以便在重置前清理他们的状态
        Set<UUID> currentPlayers = new HashSet<>();
        if (gameParticipants.containsKey(gameName)) {
            currentPlayers.addAll(gameParticipants.get(gameName));
        }

        // 清理所有玩家的状态
        for (UUID playerId : currentPlayers) {
            Player player = plugin.getServer().getPlayer(playerId);
            if (player != null && player.isOnline()) {
                // 清除玩家背包和装备
                player.getInventory().clear();
                player.getEquipment().clear();

                // 重置玩家游戏模式
                player.setGameMode(GameMode.ADVENTURE);

                // 清除玩家游戏中金钱（通过ShootPlugin）
                plugin.getShootPluginHelper().setPlayerMoney(player, 0);

                // 重置玩家生命值和饥饿值
                player.setHealth(player.getMaxHealth());
                player.setFoodLevel(20);

                // 清除所有药水效果
                for (PotionEffect effect : player.getActivePotionEffects()) {
                    player.removePotionEffect(effect.getType());
                }

                // 清除玩家的临时数据，包括倒下和死亡状态
                plugin.getPlayerInteractionManager().clearPlayerData(player, null);

                // 清除玩家的经验值和等级（用于弹药显示）
                player.setExp(0);
                player.setLevel(0);

                // 从游戏中移除玩家
                playerGames.remove(playerId);

                // 设置玩家状态为未加入
                plugin.getPlayerInteractionManager().setPlayerStatus(player, PlayerStatus.NOT_JOINED);

                player.sendMessage(ChatColor.YELLOW + "游戏已重置，你的状态已清理");
            }
        }

        // 如果游戏会话存在，先移除它
        if (runningGames.containsKey(gameName)) {
            runningGames.remove(gameName);
        }

        // 清空参与者列表
        if (gameParticipants.containsKey(gameName)) {
            gameParticipants.get(gameName).clear();
        } else {
            gameParticipants.put(gameName, new HashSet<>());
        }

        // 重新创建游戏会话
        runningGames.put(gameName, GameState.WAITING);

        // 重置计分板中的僵尸数量和计时器（添加空值检查）
        if (plugin.getScoreboardManager() != null) {
            plugin.getScoreboardManager().resetGameTimers(gameName);
            plugin.getLogger().info("重置游戏时清理计分板中的僵尸数量数据");
        } else {
            plugin.getLogger().warning("无法重置游戏计分板：ScoreboardManager未初始化");
        }

        // 清理所有僵尸和实体
        plugin.getZombieSpawnManager().clearAllZombies(gameName);

        // 清理悬浮文字（重置时也需要清理）
        if (plugin.isDecentHologramsAvailable()) {
            plugin.getLogger().info("重置游戏时清理悬浮文字: " + gameName);

            // 清理门悬浮文字
            plugin.getDoorHologramManager().cleanupDoorHolograms(gameName);

            // 清理购买点悬浮文字
            plugin.getShopHologramManager().cleanupShopHolograms(gameName);

            // 清理全局电源按钮效果
            if (plugin.getPowerButtonEffectManager() != null) {
                plugin.getPowerButtonEffectManager().removePowerButtonEffects(gameName);
                plugin.getLogger().info("重置游戏时清理全局电源按钮效果: " + gameName);
            }

            // 清理所有其他悬浮文字
            if (plugin.getHologramHelper() != null) {
                plugin.getHologramHelper().clearGameHolograms(gameName);
            }
        }

        // 清理灵魂NPC
        plugin.getPlayerDeathReviveListener().cleanupAllSoulNPCs();

        // 清理幸运箱和抽奖全息图
        plugin.getLogger().info("重置游戏时清理幸运箱: " + gameName);
        plugin.getLuckyBoxManager().clearLuckyBoxes(gameName);

        // 重置门和窗户
        DoorManager doorManager = plugin.getDoorManager();
        Map<String, Map<String, Object>> doors = plugin.getGameManager().getDoors(gameName);
        if (doors != null && !doors.isEmpty()) {
            for (Map.Entry<String, Map<String, Object>> entry : doors.entrySet()) {
                String doorName = entry.getKey();
                // 恢复门区域的方块
                doorManager.fillDoorBlocks(gameName, doorName);
                plugin.getLogger().info("已恢复门 '" + doorName + "' 的方块");
            }
        }

        // 重置门状态
        doorManager.resetDoors(gameName);

        // 重置窗户
        plugin.getWindowManager().fillWindowBlocks(gameName);

        // 重置电源按钮状态
        resetPowerButtonStatus(gameName);

        // 保存会话状态
        saveSessionData();

        plugin.getLogger().info("游戏 '" + gameName + "' 已重置为等待状态");
        return true;
    }

    /**
     * 重置游戏的电源按钮状态
     *
     * @param gameName 游戏名称
     */
    private void resetPowerButtonStatus(String gameName) {
        try {
            // 获取游戏配置文件
            File gameFile = new File(plugin.getDataFolder(), "game/" + gameName + ".yml");
            if (!gameFile.exists()) {
                return; // 如果游戏配置文件不存在，直接返回
            }

            FileConfiguration gameConfig = YamlConfiguration.loadConfiguration(gameFile);

            // 检查是否有电源按钮配置
            if (gameConfig.isConfigurationSection("power_button")) {
                // 重置电源按钮解锁状态为false
                gameConfig.set("power_button.unlocked", false);
                gameConfig.save(gameFile);
                plugin.getLogger().info("已重置游戏 " + gameName + " 的电源按钮状态为未解锁");
            }
        } catch (Exception e) {
            plugin.getLogger().warning("重置游戏 " + gameName + " 的电源按钮状态时出错: " + e.getMessage());
        }
    }

    /**
     * 设置游戏中的窗户
     *
     * @param gameName 游戏名称
     */
    private void setupGameWindows(String gameName) {
        // 获取游戏配置
        FileConfiguration config = gameManager.getGameConfig(gameName);
        if (config == null || !config.isConfigurationSection("windows")) {
            plugin.getLogger().info("游戏 " + gameName + " 没有设置任何窗户");
            return;
        }

        // 初始化窗户
        plugin.getWindowManager().initializeWindows(gameName);

        // 填充窗户方块
        plugin.getWindowManager().fillWindowBlocks(gameName);

        // 启动窗户检查任务
        plugin.getWindowManager().startWindowCheckTask(gameName);

        plugin.getLogger().info("游戏 " + gameName + " 的窗户已设置完成");
    }

    /**
     * 设置游戏中的购买点
     *
     * @param gameName 游戏名称
     */
    private void setupBuyPoints(String gameName) {
        // 获取游戏配置
        FileConfiguration config = gameManager.getGameConfig(gameName);
        if (config == null || !config.isConfigurationSection("buyPoints")) {
            plugin.getLogger().info("游戏 " + gameName + " 没有设置任何购买点");
            return;
        }

        // 检查是否使用悬浮文字
        if (plugin.isDecentHologramsAvailable()) {
            // 使用悬浮文字创建购买点
            plugin.getShopHologramManager().setupShopHolograms(gameName);
            plugin.getLogger().info("使用DecentHolograms创建购买点悬浮文字");
        } else {
            // 使用传统NPC创建购买点
            setupBuyPointsWithNPC(gameName);
            plugin.getLogger().info("使用传统NPC创建购买点");
        }
    }

    /**
     * 使用传统NPC设置游戏中的购买点
     *
     * @param gameName 游戏名称
     */
    private void setupBuyPointsWithNPC(String gameName) {
        // 获取游戏配置
        FileConfiguration config = gameManager.getGameConfig(gameName);
        if (config == null || !config.isConfigurationSection("buyPoints")) {
            return;
        }

        ShootPluginHelper shootHelper = plugin.getShootPluginHelper();
        if (shootHelper == null) {
            plugin.getLogger().warning("无法初始化购买点：ShootPluginHelper未初始化");
            return;
        }

        // 获取购买点配置
        ConfigurationSection buyPointsSection = config.getConfigurationSection("buyPoints");
        if (buyPointsSection == null) {
            return;
        }

        for (String id : buyPointsSection.getKeys(false)) {
            ConfigurationSection pointConfig = buyPointsSection.getConfigurationSection(id);
            if (pointConfig == null) {
                continue;
            }

            String type = pointConfig.getString("type", "wp");
            String itemId = pointConfig.getString("itemId", "id1");
            String name = pointConfig.getString("name", "购买点");

            // 获取位置
            ConfigurationSection locSection = pointConfig.getConfigurationSection("location");
            if (locSection == null) {
                plugin.getLogger().warning("购买点 " + id + " 没有设置位置，跳过创建");
                continue;
            }

            String worldName = locSection.getString("world");
            if (worldName == null) {
                continue;
            }

            double x = locSection.getDouble("x");
            double y = locSection.getDouble("y");
            double z = locSection.getDouble("z");
            float yaw = (float) locSection.getDouble("yaw", 0.0);
            float pitch = (float) locSection.getDouble("pitch", 0.0);

            World world = Bukkit.getWorld(worldName);
            if (world == null) {
                plugin.getLogger().warning("购买点 " + id + " 的世界 " + worldName + " 不存在，跳过创建");
                continue;
            }

            Location location = new Location(world, x, y, z, yaw, pitch);

            // 创建购买点
            net.citizensnpcs.api.npc.NPC npc = shootHelper.createBuyPointNPC(location, name, type, itemId);
            if (npc != null) {
                plugin.getLogger().info("成功为游戏 " + gameName + " 创建购买点 " + id + ": " + name + " (类型:" + type + ", 物品:" + itemId + ")");
            } else {
                plugin.getLogger().warning("为游戏 " + gameName + " 创建购买点 " + id + " 失败");
            }
        }
    }

    /**
     * 应用游戏buff效果
     *
     * @param player 玩家
     * @param gameName 游戏名称
     */
    private void applyGameBuffs(Player player, String gameName) {
        // 获取游戏的buff列表
        List<String> buffs = gameManager.getBuffs(gameName);

        if (buffs.isEmpty()) {
            plugin.getLogger().info("游戏 " + gameName + " 没有设置buff效果");
            return;
        }

        plugin.getLogger().info("为玩家 " + player.getName() + " 应用游戏buff效果: " + String.join(", ", buffs));

        // 应用每个buff效果
        for (String buffId : buffs) {
            switch (buffId) {
                case "speed1":
                    // 速度1效果（无限时间）
                    player.addPotionEffect(new PotionEffect(PotionEffectType.SPEED, Integer.MAX_VALUE, 0, false, false, true));
                    player.sendMessage(ChatColor.GREEN + "你获得了速度I效果！");
                    plugin.getLogger().info("为玩家 " + player.getName() + " 应用速度I效果");
                    break;
                case "speed2":
                    // 速度2效果（无限时间）
                    player.addPotionEffect(new PotionEffect(PotionEffectType.SPEED, Integer.MAX_VALUE, 1, false, false, true));
                    player.sendMessage(ChatColor.GREEN + "你获得了速度II效果！");
                    plugin.getLogger().info("为玩家 " + player.getName() + " 应用速度II效果");
                    break;
                case "jump1":
                    // 跳跃1效果（无限时间）
                    player.addPotionEffect(new PotionEffect(PotionEffectType.JUMP_BOOST, Integer.MAX_VALUE, 0, false, false, true));
                    player.sendMessage(ChatColor.GREEN + "你获得了跳跃I效果！");
                    plugin.getLogger().info("为玩家 " + player.getName() + " 应用跳跃I效果");
                    break;
                case "KeepFood":
                    // 饱食度满级效果（无限时间）
                    player.addPotionEffect(new PotionEffect(PotionEffectType.SATURATION, Integer.MAX_VALUE, 0, false, false, true));
                    player.setFoodLevel(20); // 设置饱食度为满
                    player.sendMessage(ChatColor.GREEN + "你获得了饱食度保持效果！");
                    plugin.getLogger().info("为玩家 " + player.getName() + " 应用饱食度保持效果");
                    break;
                case "health1":
                    // 生命恢复1效果（无限时间）
                    player.addPotionEffect(new PotionEffect(PotionEffectType.REGENERATION, Integer.MAX_VALUE, 0, false, false, true));
                    player.sendMessage(ChatColor.GREEN + "你获得了生命恢复I效果！");
                    plugin.getLogger().info("为玩家 " + player.getName() + " 应用生命恢复I效果");
                    break;
                case "health2":
                    // 生命恢复2效果（无限时间）
                    player.addPotionEffect(new PotionEffect(PotionEffectType.REGENERATION, Integer.MAX_VALUE, 1, false, false, true));
                    player.sendMessage(ChatColor.GREEN + "你获得了生命恢复II效果！");
                    plugin.getLogger().info("为玩家 " + player.getName() + " 应用生命恢复II效果");
                    break;
                case "health3":
                    // 生命恢复3效果（无限时间）
                    player.addPotionEffect(new PotionEffect(PotionEffectType.REGENERATION, Integer.MAX_VALUE, 2, false, false, true));
                    player.sendMessage(ChatColor.GREEN + "你获得了生命恢复III效果！");
                    plugin.getLogger().info("为玩家 " + player.getName() + " 应用生命恢复III效果");
                    break;
                case "health+":
                    // 额外生命1效果（无限时间）
                    player.addPotionEffect(new PotionEffect(PotionEffectType.HEALTH_BOOST, Integer.MAX_VALUE, 0, false, false, true));
                    // 确保玩家生命值更新
                    Bukkit.getScheduler().runTaskLater(plugin, () -> {
                        double maxHealth = player.getMaxHealth();
                        player.setHealth(Math.min(player.getHealth() + 4, maxHealth));
                    }, 5L);
                    player.sendMessage(ChatColor.GREEN + "你获得了额外生命I效果！");
                    plugin.getLogger().info("为玩家 " + player.getName() + " 应用额外生命I效果");
                    break;
                case "health++":
                    // 额外生命2效果（无限时间）
                    player.addPotionEffect(new PotionEffect(PotionEffectType.HEALTH_BOOST, Integer.MAX_VALUE, 1, false, false, true));
                    // 确保玩家生命值更新
                    Bukkit.getScheduler().runTaskLater(plugin, () -> {
                        double maxHealth = player.getMaxHealth();
                        player.setHealth(Math.min(player.getHealth() + 8, maxHealth));
                    }, 5L);
                    player.sendMessage(ChatColor.GREEN + "你获得了额外生命II效果！");
                    plugin.getLogger().info("为玩家 " + player.getName() + " 应用额外生命II效果");
                    break;
                case "health+++":
                    // 额外生命3效果（无限时间）
                    player.addPotionEffect(new PotionEffect(PotionEffectType.HEALTH_BOOST, Integer.MAX_VALUE, 2, false, false, true));
                    // 确保玩家生命值更新
                    Bukkit.getScheduler().runTaskLater(plugin, () -> {
                        double maxHealth = player.getMaxHealth();
                        player.setHealth(Math.min(player.getHealth() + 12, maxHealth));
                    }, 5L);
                    player.sendMessage(ChatColor.GREEN + "你获得了额外生命III效果！");
                    plugin.getLogger().info("为玩家 " + player.getName() + " 应用额外生命III效果");
                    break;
                default:
                    plugin.getLogger().warning("未知的buff ID: " + buffId);
                    break;
            }
        }
    }

    /**
     * 获取游戏当前回合数
     *
     * @param gameName 游戏名称
     * @return 当前回合数，如果游戏不存在则返回1
     */
    public int getGameRound(String gameName) {
        // 尝试从ZombieSpawnManager获取当前回合信息
        ZombieSpawnManager zombieSpawnManager = plugin.getZombieSpawnManager();
        if (zombieSpawnManager != null) {
            try {
                // 通过反射获取gameRounds字段
                java.lang.reflect.Field gameRoundsField = zombieSpawnManager.getClass().getDeclaredField("gameRounds");
                gameRoundsField.setAccessible(true);
                @SuppressWarnings("unchecked")
                Map<String, Integer> gameRounds = (Map<String, Integer>) gameRoundsField.get(zombieSpawnManager);

                // 获取指定游戏的回合数
                Integer round = gameRounds.get(gameName);
                if (round != null) {
                    return round;
                }
            } catch (Exception e) {
                plugin.getLogger().warning("无法获取游戏回合信息: " + e.getMessage());
            }
        }

        // 如果获取失败，尝试从计分板管理器获取
        ScoreboardManager scoreboardManager = plugin.getScoreboardManager();
        if (scoreboardManager != null) {
            try {
                // 通过反射获取currentRounds字段
                java.lang.reflect.Field currentRoundsField = scoreboardManager.getClass().getDeclaredField("currentRounds");
                currentRoundsField.setAccessible(true);
                @SuppressWarnings("unchecked")
                Map<String, Integer> currentRounds = (Map<String, Integer>) currentRoundsField.get(scoreboardManager);

                // 获取指定游戏的回合数
                Integer round = currentRounds.get(gameName);
                if (round != null) {
                    return round;
                }
            } catch (Exception e) {
                plugin.getLogger().warning("无法获取计分板回合信息: " + e.getMessage());
            }
        }

        // 默认返回第1回合
        return 1;
    }

    /**
     * 玩家重新连接到游戏
     *
     * @param player 玩家
     * @param gameName 游戏名称
     * @return 是否成功重新连接
     */
    public boolean rejoinPlayerToGame(Player player, String gameName) {
        plugin.getLogger().info("玩家 " + player.getName() + " 尝试重新连接到游戏 " + gameName);

        // 检查游戏是否存在且已启用
        if (!gameManager.gameExists(gameName)) {
            plugin.getLogger().warning("游戏 " + gameName + " 不存在，无法重新连接");
            return false;
        }

        if (!gameManager.isGameEnabled(gameName)) {
            plugin.getLogger().warning("游戏 " + gameName + " 未启用，无法重新连接");
            return false;
        }

        // 获取游戏状态
        GameState gameState = runningGames.get(gameName);

        if (gameState == null) {
            // 游戏会话不存在，创建新会话并加入等待队列
            plugin.getLogger().info("游戏会话不存在，创建新会话并加入等待队列: " + gameName);
            createGame(gameName);
            return addPlayerToGame(player, gameName);
        }

        // 根据游戏状态决定重新连接方式
        switch (gameState) {
            case WAITING:
                // 游戏在等待状态，直接加入等待队列
                plugin.getLogger().info("游戏 " + gameName + " 在等待状态，将玩家加入等待队列");
                return addPlayerToGame(player, gameName);

            case RUNNING:
                // 游戏正在运行，尝试重新连接到进行中的游戏
                plugin.getLogger().info("游戏 " + gameName + " 正在运行，尝试重新连接玩家到进行中的游戏");
                return rejoinRunningGame(player, gameName);

            case ENDED:
                // 游戏已结束，无法重新连接
                plugin.getLogger().warning("游戏 " + gameName + " 已结束，无法重新连接");
                return false;

            default:
                plugin.getLogger().warning("游戏 " + gameName + " 状态未知: " + gameState);
                return false;
        }
    }

    /**
     * 重新连接到正在运行的游戏
     *
     * @param player 玩家
     * @param gameName 游戏名称
     * @return 是否成功重新连接
     */
    private boolean rejoinRunningGame(Player player, String gameName) {
        UUID playerId = player.getUniqueId();

        // 检查玩家是否在游戏参与者列表中
        if (!gameParticipants.containsKey(gameName) || !gameParticipants.get(gameName).contains(playerId)) {
            plugin.getLogger().warning("玩家 " + player.getName() + " 不在游戏 " + gameName + " 的参与者列表中");
            return false;
        }

        // 检查游戏人数是否已满
        FileConfiguration gameConfig = gameManager.getGameConfig(gameName);
        if (gameConfig != null && gameConfig.contains("maxPlayers")) {
            int maxPlayers = gameConfig.getInt("maxPlayers");
            int currentOnlinePlayers = 0;

            // 计算当前在线玩家数量
            for (UUID participantId : gameParticipants.get(gameName)) {
                Player participant = plugin.getServer().getPlayer(participantId);
                if (participant != null && participant.isOnline() && !participant.equals(player)) {
                    currentOnlinePlayers++;
                }
            }

            if (currentOnlinePlayers >= maxPlayers) {
                plugin.getLogger().warning("游戏 " + gameName + " 人数已满，当前在线人数: " + currentOnlinePlayers + ", 最大人数: " + maxPlayers);
                return false;
            }
        }

        // 更新玩家游戏关联
        playerGames.put(playerId, gameName);

        // 获取游戏出生点位置
        Location spawnLocation = getGameSpawnLocation(gameName);
        if (spawnLocation == null) {
            plugin.getLogger().warning("游戏 " + gameName + " 没有设置出生点，无法传送玩家！");
            return false;
        }

        // 更新玩家状态为游戏中
        PlayerInteractionManager playerManager = plugin.getPlayerInteractionManager();
        playerManager.setPlayerStatus(player, PlayerStatus.IN_GAME);

        // 添加发光效果
        player.setGlowing(true);

        // 设置玩家为冒险模式
        player.setGameMode(GameMode.ADVENTURE);
        plugin.getLogger().info("已将重新连接的玩家 " + player.getName() + " 设置为冒险模式");

        // 应用游戏buff效果
        applyGameBuffs(player, gameName);

        // 传送玩家到游戏出生点
        try {
            player.teleport(spawnLocation);
            plugin.getLogger().info("已将重新连接的玩家 " + player.getName() + " 传送到游戏出生点");
        } catch (Exception e) {
            plugin.getLogger().warning("传送重新连接的玩家 " + player.getName() + " 失败: " + e.getMessage());
        }

        // 给玩家装备初始装备
        givePlayerStartItems(player);

        // 更新计分板
        plugin.getScoreboardManager().updatePlayerScoreboard(player);

        // 保存会话状态
        saveSessionData();

        plugin.getLogger().info("成功将玩家 " + player.getName() + " 重新连接到正在运行的游戏 " + gameName);
        return true;
    }
}
