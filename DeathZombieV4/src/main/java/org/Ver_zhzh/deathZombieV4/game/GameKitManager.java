package org.Ver_zhzh.deathZombieV4.game;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;
import java.util.logging.Level;

import org.Ver_zhzh.deathZombieV4.DeathZombieV4;
import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.configuration.file.YamlConfiguration;

/**
 * 游戏装备管理器，负责加载和管理gameKit.yml文件
 */
public class GameKitManager {

    private final DeathZombieV4 plugin;
    private FileConfiguration kitConfig;
    private File kitFile;

    // 缓存装备信息
    private final Map<String, Map<String, Object>> weaponsMap = new HashMap<>();
    private final Map<String, Map<String, Object>> armorsMap = new HashMap<>();
    private final Map<String, Map<String, Object>> itemsMap = new HashMap<>();
    private final Map<String, Map<String, Object>> specialMap = new HashMap<>();
    private final Map<String, Map<String, Object>> otherMap = new HashMap<>();

    public GameKitManager(DeathZombieV4 plugin) {
        this.plugin = plugin;
        loadGameKit();
    }

    /**
     * 加载gameKit.yml文件
     */
    public void loadGameKit() {
        if (kitFile == null) {
            kitFile = new File(plugin.getDataFolder(), "gameKit.yml");
        }

        // 如果文件不存在，从资源中复制
        if (!kitFile.exists()) {
            plugin.saveResource("gameKit.yml", false);
            plugin.getLogger().info("已创建gameKit.yml文件");
        }

        // 加载配置
        kitConfig = YamlConfiguration.loadConfiguration(kitFile);

        // 确保使用UTF-8编码
        try (InputStream inputStream = plugin.getResource("gameKit.yml")) {
            if (inputStream != null) {
                YamlConfiguration defaultConfig = YamlConfiguration.loadConfiguration(
                        new InputStreamReader(inputStream, StandardCharsets.UTF_8));
                kitConfig.setDefaults(defaultConfig);
            }
        } catch (IOException e) {
            plugin.getLogger().log(Level.WARNING, "加载默认gameKit.yml时出错", e);
        }

        // 清除缓存
        weaponsMap.clear();
        armorsMap.clear();
        itemsMap.clear();
        specialMap.clear();
        otherMap.clear();

        // 加载武器
        loadSection("weapons", weaponsMap);

        // 加载护甲
        loadSection("armors", armorsMap);

        // 加载物品
        loadSection("items", itemsMap);

        // 加载特殊物品
        loadSection("special", specialMap);

        // 加载其他物品
        loadSection("other", otherMap);

        plugin.getLogger().info("成功加载gameKit.yml: "
                + weaponsMap.size() + "个武器, "
                + armorsMap.size() + "个护甲, "
                + itemsMap.size() + "个物品, "
                + specialMap.size() + "个特殊物品, "
                + otherMap.size() + "个其他物品");
    }

    /**
     * 加载配置节点到Map中
     */
    private void loadSection(String sectionName, Map<String, Map<String, Object>> targetMap) {
        ConfigurationSection section = kitConfig.getConfigurationSection(sectionName);
        if (section != null) {
            for (String key : section.getKeys(false)) {
                ConfigurationSection itemSection = section.getConfigurationSection(key);
                if (itemSection != null) {
                    Map<String, Object> itemData = new HashMap<>();
                    for (String itemKey : itemSection.getKeys(false)) {
                        itemData.put(itemKey, itemSection.get(itemKey));
                    }
                    targetMap.put(key, itemData);
                }
            }
        }
    }

    /**
     * 获取物品信息
     *
     * @param itemId 物品ID
     * @return 物品信息Map，如果不存在则返回null
     */
    public Map<String, Object> getItemInfo(String itemId) {
        if (itemId == null || itemId.isEmpty()) {
            return null;
        }

        // 检查武器
        if (weaponsMap.containsKey(itemId)) {
            return weaponsMap.get(itemId);
        }

        // 检查护甲
        if (armorsMap.containsKey(itemId)) {
            return armorsMap.get(itemId);
        }

        // 检查物品
        if (itemsMap.containsKey(itemId)) {
            return itemsMap.get(itemId);
        }

        // 检查特殊物品
        if (specialMap.containsKey(itemId)) {
            return specialMap.get(itemId);
        }

        // 检查其他物品
        if (otherMap.containsKey(itemId)) {
            return otherMap.get(itemId);
        }

        // 尝试处理数字ID
        try {
            int numId = Integer.parseInt(itemId);
            String idStr = "id" + numId;

            // 根据ID范围判断类型
            if (numId >= 1 && numId <= 24) {
                return weaponsMap.get(idStr);
            } else if (numId >= 25 && numId <= 37) {
                return armorsMap.get(idStr);
            } else if (numId >= 38 && numId <= 67) {
                return itemsMap.get(idStr);
            } else if (numId >= 67 && numId <= 70) {
                return specialMap.get(idStr);
            }
        } catch (NumberFormatException e) {
            // 不是数字ID，忽略
        }

        return null;
    }

    /**
     * 获取物品名称
     *
     * @param itemId 物品ID
     * @return 物品名称，如果不存在则返回原始ID
     */
    public String getItemName(String itemId) {
        Map<String, Object> itemInfo = getItemInfo(itemId);
        if (itemInfo != null && itemInfo.containsKey("name")) {
            return (String) itemInfo.get("name");
        }
        return itemId;
    }

    /**
     * 获取物品描述
     *
     * @param itemId 物品ID
     * @return 物品描述，如果不存在则返回空字符串
     */
    public String getItemDescription(String itemId) {
        Map<String, Object> itemInfo = getItemInfo(itemId);
        if (itemInfo != null && itemInfo.containsKey("description")) {
            return (String) itemInfo.get("description");
        }
        return "";
    }

    /**
     * 获取物品材质
     *
     * @param itemId 物品ID
     * @return 物品材质，如果不存在则返回null
     */
    public String getItemMaterial(String itemId) {
        Map<String, Object> itemInfo = getItemInfo(itemId);
        if (itemInfo != null && itemInfo.containsKey("material")) {
            return (String) itemInfo.get("material");
        }
        return null;
    }

    /**
     * 获取Shoot插件中对应的ID
     *
     * @param itemId 物品ID
     * @return Shoot插件中对应的ID，如果不存在则返回原始ID
     */
    public String getShootId(String itemId) {
        if (itemId == null || itemId.isEmpty()) {
            return itemId;
        }

        // 记录原始ID，用于日志
        String originalId = itemId;

        // 尝试获取物品信息
        Map<String, Object> itemInfo = getItemInfo(itemId);
        if (itemInfo != null && itemInfo.containsKey("shootId")) {
            String shootId = (String) itemInfo.get("shootId");
            if (shootId != null && !shootId.isEmpty()) {
                plugin.getLogger().info("GameKitManager: 映射ID " + originalId + " 到 " + shootId);
                return shootId;
            }
        }

        // 如果没有找到映射，尝试处理数字ID
        if (itemId.startsWith("id") && itemId.length() > 2) {
            try {
                int numId = Integer.parseInt(itemId.substring(2));
                // 检查是否有对应的idX格式的映射
                String idFormat = "id" + numId;
                Map<String, Object> idFormatInfo = getItemInfo(idFormat);
                if (idFormatInfo != null && idFormatInfo.containsKey("shootId")) {
                    String shootId = (String) idFormatInfo.get("shootId");
                    if (shootId != null && !shootId.isEmpty()) {
                        plugin.getLogger().info("GameKitManager: 通过数字ID映射 " + originalId + " 到 " + shootId);
                        return shootId;
                    }
                }
            } catch (NumberFormatException e) {
                // 不是有效的数字ID，忽略
            }
        }

        return itemId;
    }

    /**
     * 检查物品ID是否有效
     *
     * @param itemId 物品ID
     * @return 是否有效
     */
    public boolean isValidItemId(String itemId) {
        if (itemId == null || itemId.isEmpty()) {
            return false;
        }

        // 直接检查是否存在于任何Map中
        if (weaponsMap.containsKey(itemId) || armorsMap.containsKey(itemId)
                || itemsMap.containsKey(itemId) || specialMap.containsKey(itemId)
                || otherMap.containsKey(itemId)) {
            return true;
        }

        // 检查特殊物品ID
        if ("white_dye".equals(itemId)
                || "id67".equals(itemId) || "id68".equals(itemId) || "id69".equals(itemId) || "id70".equals(itemId)
                || "67".equals(itemId) || "68".equals(itemId) || "69".equals(itemId) || "70".equals(itemId)) {
            return true;
        }

        // 检查数字ID
        try {
            int numId = Integer.parseInt(itemId);
            return numId >= 1 && numId <= 70; // 有效ID范围
        } catch (NumberFormatException e) {
            // 不是数字ID，继续检查
        }

        // 检查idx格式的ID
        if (itemId.startsWith("id") && itemId.length() > 2) {
            try {
                int numId = Integer.parseInt(itemId.substring(2));
                return numId >= 1 && numId <= 70; // 有效ID范围
            } catch (NumberFormatException e) {
                // 不是有效的idx格式ID
            }
        }

        return false;
    }

    /**
     * 获取物品类型
     *
     * @param itemId 物品ID
     * @return 物品类型（"weapon", "armor", "item", "special",
     * "other"），如果不存在则返回"unknown"
     */
    public String getItemType(String itemId) {
        if (itemId == null || itemId.isEmpty()) {
            return "unknown";
        }

        // 检查武器
        if (weaponsMap.containsKey(itemId)) {
            return "weapon";
        }

        // 检查护甲
        if (armorsMap.containsKey(itemId)) {
            return "armor";
        }

        // 检查物品
        if (itemsMap.containsKey(itemId)) {
            return "item";
        }

        // 检查特殊物品
        if (specialMap.containsKey(itemId)) {
            return "special";
        }

        // 检查其他物品
        if (otherMap.containsKey(itemId)) {
            return "other";
        }

        // 尝试处理数字ID
        try {
            int numId = Integer.parseInt(itemId);

            // 根据ID范围判断类型
            if (numId >= 1 && numId <= 24) {
                return "weapon";
            } else if (numId >= 25 && numId <= 37) {
                return "armor";
            } else if (numId >= 38 && numId <= 67) {
                return "item";
            } else if (numId >= 67 && numId <= 70) {
                return "special";
            }
        } catch (NumberFormatException e) {
            // 不是数字ID，忽略
        }

        return "unknown";
    }

    /**
     * 获取所有武器
     */
    public Map<String, Map<String, Object>> getAllWeapons() {
        return new HashMap<>(weaponsMap);
    }

    /**
     * 获取所有护甲
     */
    public Map<String, Map<String, Object>> getAllArmors() {
        return new HashMap<>(armorsMap);
    }

    /**
     * 获取所有物品
     */
    public Map<String, Map<String, Object>> getAllItems() {
        return new HashMap<>(itemsMap);
    }

    /**
     * 获取所有特殊物品
     */
    public Map<String, Map<String, Object>> getAllSpecialItems() {
        return new HashMap<>(specialMap);
    }

    /**
     * 获取所有其他物品
     */
    public Map<String, Map<String, Object>> getAllOtherItems() {
        return new HashMap<>(otherMap);
    }

    /**
     * 重新加载配置
     */
    public void reload() {
        loadGameKit();
    }
}
