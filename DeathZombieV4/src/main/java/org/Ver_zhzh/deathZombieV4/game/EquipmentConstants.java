package org.Ver_zhzh.deathZombieV4.game;

import org.Ver_zhzh.deathZombieV4.DeathZombieV4;

/**
 * 装备常量类，用于定义装备ID的可读名称
 */
public class EquipmentConstants {

    // 特殊物品ID
    public static final String IRON_SWORD = "iron_sword";  // 铁剑
    public static final String HANDGUN = "id2";            // 手枪

    // 槽位类型
    public static final String LOCKED_SLOT = "id69";       // 锁定槽位，使用idx格式
    public static final String EMPTY_ITEM_SLOT = "id68";   // 空物品槽，使用idx格式
    public static final String WEAPON_SLOT = "id67";       // 武器槽位，使用idx格式
    public static final String WHITE_DYE = "white_dye";    // 白色染料（用于锁定槽位）

    // 装备类型前缀
    public static final String WEAPON_PREFIX = "wp_";      // 武器前缀
    public static final String ARMOR_PREFIX = "ar_";       // 护甲前缀
    public static final String ITEM_PREFIX = "it_";        // 物品前缀
    public static final String SPECIAL_PREFIX = "sp_";     // 特殊物品前缀

    // 装备类型名称
    public static final String TYPE_WEAPON = "武器";
    public static final String TYPE_ARMOR = "护甲";
    public static final String TYPE_ITEM = "物品";
    public static final String TYPE_SPECIAL = "特殊物品";

    // 槽位类型名称
    public static final String SLOT_TYPE_LOCKED = "锁定槽位";
    public static final String SLOT_TYPE_EMPTY = "空物品槽";
    public static final String SLOT_TYPE_WEAPON = "武器槽位";

    // 插件实例，用于获取GameKitManager
    private static DeathZombieV4 plugin;

    /**
     * 设置插件实例
     *
     * @param plugin 插件实例
     */
    public static void setPlugin(DeathZombieV4 plugin) {
        EquipmentConstants.plugin = plugin;
    }

    /**
     * 获取装备ID的可读名称
     *
     * @param itemId 装备ID
     * @return 可读名称
     */
    public static String getReadableName(String itemId) {
        if (itemId == null || itemId.isEmpty()) {
            return "未知物品";
        }

        // 尝试使用GameKitManager获取物品名称
        if (plugin != null) {
            GameKitManager gameKitManager = plugin.getGameKitManager();
            if (gameKitManager != null) {
                String name = gameKitManager.getItemName(itemId);
                if (name != null && !name.equals(itemId)) {
                    return name;
                }
            }
        }

        // 如果GameKitManager不可用或未找到物品，使用旧的逻辑
        // 特殊物品
        if (IRON_SWORD.equals(itemId)) {
            return "僵尸猎人之剑";
        } else if (HANDGUN.equals(itemId)) {
            return "手枪";
        } else if (LOCKED_SLOT.equals(itemId)) {
            return SLOT_TYPE_LOCKED;
        } else if (EMPTY_ITEM_SLOT.equals(itemId)) {
            return SLOT_TYPE_EMPTY;
        } else if (WEAPON_SLOT.equals(itemId)) {
            return SLOT_TYPE_WEAPON;
        } else if (WHITE_DYE.equals(itemId)) {
            return "白色染料";
        }

        // 前缀物品
        if (itemId.startsWith(WEAPON_PREFIX)) {
            return TYPE_WEAPON + "-" + itemId.substring(WEAPON_PREFIX.length());
        } else if (itemId.startsWith(ARMOR_PREFIX)) {
            return TYPE_ARMOR + "-" + itemId.substring(ARMOR_PREFIX.length());
        } else if (itemId.startsWith(ITEM_PREFIX)) {
            return TYPE_ITEM + "-" + itemId.substring(ITEM_PREFIX.length());
        } else if (itemId.startsWith(SPECIAL_PREFIX)) {
            return TYPE_SPECIAL + "-" + itemId.substring(SPECIAL_PREFIX.length());
        }

        // 数字ID
        try {
            int numId = Integer.parseInt(itemId);
            if (numId >= 1 && numId <= 24) {
                return TYPE_WEAPON + "-" + numId;
            } else if (numId >= 25 && numId <= 37) {
                return TYPE_ARMOR + "-" + (numId - 24);
            } else if (numId >= 38 && numId <= 67) {
                return TYPE_ITEM + "-" + (numId - 37);
            }
        } catch (NumberFormatException e) {
            // 不是数字ID，继续处理
        }

        // 如果以"id"开头
        if (itemId.startsWith("id") && itemId.length() > 2) {
            try {
                int gunId = Integer.parseInt(itemId.substring(2));
                return "枪支-" + gunId;
            } catch (NumberFormatException e) {
                // 不是有效的枪支ID格式
            }
        }

        // 默认返回原始ID
        return itemId;
    }

    /**
     * 获取装备ID的描述
     *
     * @param itemId 装备ID
     * @return 描述
     */
    public static String getDescription(String itemId) {
        if (itemId == null || itemId.isEmpty()) {
            return "";
        }

        // 尝试使用GameKitManager获取物品描述
        if (plugin != null) {
            GameKitManager gameKitManager = plugin.getGameKitManager();
            if (gameKitManager != null) {
                String description = gameKitManager.getItemDescription(itemId);
                if (description != null && !description.isEmpty()) {
                    return description;
                }
            }
        }

        // 如果GameKitManager不可用或未找到物品描述，返回空字符串
        return "";
    }

    /**
     * 获取装备ID的材质
     *
     * @param itemId 装备ID
     * @return 材质
     */
    public static String getMaterial(String itemId) {
        if (itemId == null || itemId.isEmpty()) {
            return null;
        }

        // 尝试使用GameKitManager获取物品材质
        if (plugin != null) {
            GameKitManager gameKitManager = plugin.getGameKitManager();
            if (gameKitManager != null) {
                String material = gameKitManager.getItemMaterial(itemId);
                if (material != null && !material.isEmpty()) {
                    return material;
                }
            }
        }

        // 如果GameKitManager不可用或未找到物品材质，返回null
        return null;
    }

    /**
     * 获取装备ID对应的Shoot插件ID
     *
     * @param itemId 装备ID
     * @return Shoot插件ID
     */
    public static String getShootId(String itemId) {
        if (itemId == null || itemId.isEmpty()) {
            return itemId;
        }

        // 尝试使用GameKitManager获取Shoot插件ID
        if (plugin != null) {
            GameKitManager gameKitManager = plugin.getGameKitManager();
            if (gameKitManager != null) {
                String shootId = gameKitManager.getShootId(itemId);
                if (shootId != null && !shootId.isEmpty()) {
                    return shootId;
                }
            }
        }

        // 如果GameKitManager不可用或未找到Shoot插件ID，返回原始ID
        return itemId;
    }
}
