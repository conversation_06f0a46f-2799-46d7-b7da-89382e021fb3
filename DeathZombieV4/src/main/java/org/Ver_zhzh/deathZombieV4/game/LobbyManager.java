package org.Ver_zhzh.deathZombieV4.game;

import java.util.UUID;
import java.util.logging.Level;

import org.Ver_zhzh.deathZombieV4.DeathZombieV4;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.Location;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.entity.Player;

/**
 * 管理游戏大厅位置的类 负责设置、获取全局大厅位置以及传送玩家到大厅
 */
public class LobbyManager {

    private final DeathZombieV4 plugin;
    private Location lobbyLocation;
    private static final String CONFIG_PATH = "lobby";

    /**
     * 构造函数
     *
     * @param plugin 插件实例
     */
    public LobbyManager(DeathZombieV4 plugin) {
        this.plugin = plugin;

        // 加载大厅位置配置
        loadLobbyLocation();
    }

    /**
     * 从config.yml加载大厅位置
     */
    private void loadLobbyLocation() {
        // 确保重新加载配置文件，避免使用缓存的配置
        plugin.reloadConfig();
        FileConfiguration config = plugin.getConfig();

        plugin.getLogger().info("正在尝试从config.yml加载大厅位置...");

        if (config.contains(CONFIG_PATH + ".world")) {
            String worldName = config.getString(CONFIG_PATH + ".world");
            double x = config.getDouble(CONFIG_PATH + ".x");
            double y = config.getDouble(CONFIG_PATH + ".y");
            double z = config.getDouble(CONFIG_PATH + ".z");
            float yaw = (float) config.getDouble(CONFIG_PATH + ".yaw");
            float pitch = (float) config.getDouble(CONFIG_PATH + ".pitch");

            plugin.getLogger().info("找到大厅位置配置: 世界=" + worldName
                    + ", x=" + x + ", y=" + y + ", z=" + z);

            if (Bukkit.getWorld(worldName) != null) {
                lobbyLocation = new Location(Bukkit.getWorld(worldName), x, y, z, yaw, pitch);
                plugin.getLogger().info("已成功加载全局大厅位置");
            } else {
                plugin.getLogger().warning("无法加载大厅位置：世界 " + worldName + " 不存在");
            }
        } else {
            plugin.getLogger().info("未在config.yml中找到大厅位置配置");
        }
    }

    /**
     * 重新加载大厅位置 在需要刷新大厅位置时调用，例如在配置文件被修改后
     */
    public void reloadLobbyLocation() {
        plugin.getLogger().info("正在重新加载大厅位置...");
        loadLobbyLocation();
    }

    /**
     * 检查大厅位置是否已设置
     *
     * @return 如果大厅位置已设置则返回true，否则返回false
     */
    public boolean isLobbySet() {
        return lobbyLocation != null;
    }

    /**
     * 保存大厅位置到config.yml
     */
    private void saveLobbyLocation() {
        if (lobbyLocation == null) {
            return;
        }

        FileConfiguration config = plugin.getConfig();

        config.set(CONFIG_PATH + ".world", lobbyLocation.getWorld().getName());
        config.set(CONFIG_PATH + ".x", lobbyLocation.getX());
        config.set(CONFIG_PATH + ".y", lobbyLocation.getY());
        config.set(CONFIG_PATH + ".z", lobbyLocation.getZ());
        config.set(CONFIG_PATH + ".yaw", lobbyLocation.getYaw());
        config.set(CONFIG_PATH + ".pitch", lobbyLocation.getPitch());

        try {
            plugin.saveConfig();
            plugin.getLogger().info("已保存全局大厅位置到config.yml");
        } catch (Exception e) {
            plugin.getLogger().log(Level.SEVERE, "保存大厅位置配置失败", e);
        }
    }

    /**
     * 设置全局大厅位置
     *
     * @param location 大厅位置
     * @return 是否设置成功
     */
    public boolean setLobbyLocation(Location location) {
        if (location == null) {
            return false;
        }

        lobbyLocation = location.clone();
        saveLobbyLocation();
        return true;
    }

    /**
     * 获取全局大厅位置
     *
     * @return 大厅位置，如果未设置则返回null
     */
    public Location getLobbyLocation() {
        return lobbyLocation;
    }

    /**
     * 传送玩家到全局大厅
     *
     * @param player 玩家
     * @return 是否传送成功
     */
    public boolean teleportPlayerToLobby(Player player) {
        if (player == null || lobbyLocation == null) {
            return false;
        }

        try {
            player.teleport(lobbyLocation);
            player.sendMessage(ChatColor.GREEN + "你已被传送到大厅");
            return true;
        } catch (Exception e) {
            plugin.getLogger().log(Level.WARNING, "传送玩家 " + player.getName() + " 到大厅失败", e);
            return false;
        }
    }

    /**
     * 传送多个玩家到全局大厅
     *
     * @param players 玩家UUID集合
     * @return 成功传送的玩家数量
     */
    public int teleportPlayersToLobby(Iterable<UUID> players) {
        if (players == null || lobbyLocation == null) {
            return 0;
        }

        int count = 0;
        for (UUID playerId : players) {
            Player player = Bukkit.getPlayer(playerId);
            if (player != null && player.isOnline()) {
                try {
                    player.teleport(lobbyLocation);
                    player.sendMessage(ChatColor.GREEN + "游戏结束，你已被传送到大厅");
                    count++;
                } catch (Exception e) {
                    plugin.getLogger().log(Level.WARNING, "传送玩家 " + player.getName() + " 到大厅失败", e);
                }
            }
        }

        return count;
    }
}
