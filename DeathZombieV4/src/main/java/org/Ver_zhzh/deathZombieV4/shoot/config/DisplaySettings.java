package org.Ver_zhzh.deathZombieV4.shoot.config;

/**
 * 显示设置类，用于控制射击时的标题显示
 */
public class DisplaySettings {
    private boolean startTitle;
    private boolean hitTitle;

    /**
     * 构造函数
     */
    public DisplaySettings() {
        this.startTitle = true;
        this.hitTitle = true;
    }

    /**
     * 构造函数
     *
     * @param startTitle 是否显示开始射击标题
     * @param hitTitle 是否显示命中标题
     */
    public DisplaySettings(boolean startTitle, boolean hitTitle) {
        this.startTitle = startTitle;
        this.hitTitle = hitTitle;
    }

    /**
     * 获取是否显示开始射击标题
     *
     * @return 是否显示开始射击标题
     */
    public boolean isStartTitle() {
        return startTitle;
    }

    /**
     * 设置是否显示开始射击标题
     *
     * @param startTitle 是否显示开始射击标题
     */
    public void setStartTitle(boolean startTitle) {
        this.startTitle = startTitle;
    }

    /**
     * 获取是否显示命中标题
     *
     * @return 是否显示命中标题
     */
    public boolean isHitTitle() {
        return hitTitle;
    }

    /**
     * 设置是否显示命中标题
     *
     * @param hitTitle 是否显示命中标题
     */
    public void setHitTitle(boolean hitTitle) {
        this.hitTitle = hitTitle;
    }
}