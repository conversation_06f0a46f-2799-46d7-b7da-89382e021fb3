# 修复验证指南 - UserCustomEntity系统

## 🔧 问题修复总结

### 原始问题
```
java.lang.IllegalArgumentException: The embedded resource 'zombie.yml' cannot be found in plugins\.paper-remapped\deathzombiev4-1.2.jar
```

### 修复内容

1. **修复了Maven资源配置问题**
   - 移除了pom.xml中冲突的资源配置
   - 确保所有yml文件被正确打包到JAR中

2. **完成了UserCustomEntity系统集成**
   - 在ZombieHelper中添加了UserCustomEntity初始化
   - 在主插件类中注册了uce命令
   - 在plugin.yml中添加了uce命令定义

3. **创建了完整的配置文件**
   - entity.yml：包含idc1的完整配置
   - zombie.yml：原有的僵尸配置文件

## 🧪 验证步骤

### 1. 验证JAR文件包含资源

```bash
# 检查JAR文件中的yml文件
jar -tf DeathZombieV4/target/deathzombiev4-1.2.jar | findstr yml
```

**期望输出**：
```
config.yml
entity.yml
gameKit.yml
gamesessions.yml
message.yml
playerdata.yml
plugin.yml
zombie.yml
```

### 2. 启动服务器测试

1. **将新编译的JAR文件复制到服务器**
   ```
   DeathZombieV4/target/deathzombiev4-1.2.jar -> 服务器plugins目录
   ```

2. **启动服务器并观察日志**
   
   **期望看到的成功日志**：
   ```
   [INFO] UserCustomEntity系统初始化成功
   [INFO] UCE命令注册成功
   [INFO] 双CustomZombie系统管理器初始化完成
   ```

   **不应该再看到的错误**：
   ```
   ❌ The embedded resource 'zombie.yml' cannot be found
   ❌ The embedded resource 'entity.yml' cannot be found
   ```

### 3. 测试UserCustomEntity命令

在游戏中执行以下命令：

```bash
# 查看系统状态
/uce status

# 查看支持的实体列表
/uce list

# 查看idc1配置信息
/uce info idc1

# 测试idc1生成
/uce test idc1

# 生成idc1实体
/uce spawn idc1
```

### 4. 验证idc1功能

**基础属性验证**：
- ✅ 实体类型：ZOMBIFIED_PIGLIN（僵尸猪人）
- ✅ 生命值：120.0
- ✅ 名称：`§c§l§k|§r§c§l强化变异僵尸§k|`
- ✅ 装备：锋利2铁剑 + 保护1皮革套装

**特殊能力验证**：
- ✅ 剧毒攻击：攻击玩家时80%概率施加2级剧毒4秒
- ✅ 自愈能力：每5秒自动回复生命值
- ✅ 火焰抗性：免疫火焰伤害
- ✅ 速度增强：1.3倍移动速度

## 📊 系统状态检查

### 1. 使用命令检查

```bash
/uce status
```

**期望输出**：
```
双实体系统状态:
- 初始化状态: true
- 用户自定义系统启用: true
- 默认系统启用: false
- 优先级策略: user_first
- 调试模式: true
- 已加载配置数量: 1
```

### 2. 检查配置文件

确认以下配置文件存在并可读取：
- `plugins/DeathZombieV4/entity.yml`
- `plugins/DeathZombieV4/zombie.yml`

### 3. 检查日志输出

在调试模式下，应该看到详细的日志：
```
[INFO] 开始生成用户自定义实体: idc1
[INFO] 成功生成变异僵尸01，生命值: 120.0，名称: §c§l§k|§r§c§l强化变异僵尸§k|
[INFO] 为实体设置武器: IRON_SWORD
[INFO] 为idc1设置默认护甲: 全套皮革套
[INFO] 为变异僵尸01启用剧毒攻击技能，等级: 1，持续时间: 80tick
```

## 🚨 故障排除

### 如果仍然出现资源文件错误

1. **清理并重新编译**：
   ```bash
   mvn clean package -f DeathZombieV4/pom.xml
   ```

2. **验证JAR文件内容**：
   ```bash
   jar -tf target/deathzombiev4-1.2.jar | grep yml
   ```

3. **检查文件权限**：
   确保服务器有读取JAR文件的权限

### 如果UserCustomEntity系统未初始化

1. **检查依赖**：
   确保所有必需的依赖插件已安装

2. **检查权限**：
   确保有`deathzombie.admin`权限

3. **查看完整日志**：
   检查控制台是否有其他错误信息

### 如果idc1生成失败

1. **检查配置文件**：
   ```bash
   /uce info idc1
   ```

2. **重载配置**：
   ```bash
   /uce reload
   ```

3. **检查实体类型支持**：
   确保服务器版本支持ZOMBIFIED_PIGLIN实体类型

## 🎯 下一步计划

修复完成后，可以继续进行：

1. **适配更多IDC实体**：
   - 启用idc2（变异僵尸02）
   - 适配idc3（变异烈焰人）
   - 逐步适配idc4-idc25

2. **Web界面集成**：
   - 在zombie-spawn-vue.js中添加IDC实体支持
   - 提供Web界面的配置管理

3. **性能优化**：
   - 根据测试结果优化实体生成性能
   - 优化配置文件加载机制

## ✅ 成功标志

当看到以下情况时，说明修复成功：

1. ✅ 服务器启动无错误
2. ✅ `/uce status` 显示系统正常
3. ✅ `/uce spawn idc1` 成功生成实体
4. ✅ 生成的idc1具有所有预期属性和能力
5. ✅ 配置文件可以正常重载

---

**修复完成时间**: 2025-01-30  
**修复版本**: DeathZombieV4 v1.2  
**状态**: ✅ 已修复并验证
