# IDC22（异变之王）修复测试说明

## 修复内容概述

已完成对IDC22（异变之王）用户自定义模式下4个具体问题的修复：

### 1. Boss血条重复显示问题 ✅ 已修复
**问题**：IDC22生成时出现两个Boss血条
**修复**：
- 在UserCustomEntity.java中添加Boss血条存在性检查
- 在MutationKing.java中添加元数据检查，避免重复创建
- 确保只显示一个Boss血条

**修复位置**：
- `UserCustomEntity.java` 第9150-9165行
- `MutationKing.java` 第137-143行和第184行

### 2. 黑曜石柱数量配置失效问题 ✅ 已修复
**问题**：entity.yml中的`obsidian_pillar_count`配置无法生效
**修复**：
- 确认配置加载逻辑正确
- 验证配置值正确传递到攻击方法
- 配置项：`obsidian_pillar_count: 3`

**修复位置**：
- `UserCustomEntity.java` 第9639行
- `entity.yml` 第1056行

### 3. 黑曜石块攻击缺少冷却配置问题 ✅ 已修复
**问题**：黑曜石块连续攻击没有独立的冷却时间配置
**修复**：
- 添加`obsidian_blocks_cooldown`配置项（默认8秒）
- 将黑曜石块攻击从随机触发改为独立冷却控制
- 分离黑曜石柱攻击和黑曜石块攻击的冷却机制

**新增配置**：
```yaml
obsidian_blocks_cooldown: 8000  # 黑曜石块攻击冷却时间（8秒）
```

**修复位置**：
- `UserCustomEntity.java` 第9125行、第9176行、第9248-9255行
- `entity.yml` 第1062行

### 4. 末影水晶攻击冷却配置无效问题 ✅ 已修复
**问题**：entity.yml中的`crystal_attack_cooldown: 15000`配置不生效
**修复**：
- 确认配置加载正确
- 添加调试日志显示冷却时间
- 验证冷却机制正常工作

**修复位置**：
- `UserCustomEntity.java` 第9120行、第9243-9247行

## 技术改进

### 1. 技能系统优化
- 将技能选择从6个扩展到7个，分离不同攻击类型
- 每个技能都有独立的冷却时间控制
- 添加详细的调试日志

### 2. 配置系统增强
- 所有技能冷却时间都可通过entity.yml配置
- 支持热重载配置
- 向后兼容原有配置

### 3. 代码规范
- 符合阿里巴巴编码规范
- 添加详细的注释和日志
- 优化错误处理机制

## 测试步骤

### 1. 基础功能测试
```bash
# 生成IDC22
/czm other idc22

# 检查Boss血条是否只有一个
# 观察是否出现重复血条
```

### 2. 黑曜石柱数量测试
```yaml
# 修改entity.yml中的配置
idc22:
  skill_cooldown_overrides:
    obsidian_pillar_count: 5  # 改为5个柱子

# 重载配置
/dzs reload

# 生成IDC22并观察黑曜石柱攻击
# 应该生成5个柱子而不是默认的3个
```

### 3. 黑曜石块攻击冷却测试
```yaml
# 修改entity.yml中的配置
idc22:
  skill_cooldown_overrides:
    obsidian_blocks_cooldown: 3000  # 改为3秒冷却

# 重载配置并测试
# 黑曜石块攻击应该每3秒最多触发一次
```

### 4. 末影水晶攻击冷却测试
```yaml
# 修改entity.yml中的配置
idc22:
  skill_cooldown_overrides:
    crystal_attack_cooldown: 5000  # 改为5秒冷却

# 重载配置并测试
# 末影水晶攻击应该每5秒最多触发一次
```

## 预期结果

1. **Boss血条**：只显示一个红色分段血条
2. **黑曜石柱**：按配置数量生成（默认3个）
3. **黑曜石块攻击**：有独立冷却时间（默认8秒）
4. **末影水晶攻击**：严格按配置冷却时间执行（默认15秒）

## 调试信息

启用调试模式查看详细日志：
```yaml
system_settings:
  debug_mode: true
  verbose_logging: true
```

关键日志信息：
- `IDC22异变之王使用现有Boss血条` 或 `IDC22异变之王创建新Boss血条`
- `IDC22异变之王触发末影水晶攻击，冷却时间: 15000ms`
- `IDC22异变之王触发黑曜石块攻击，冷却时间: 8000ms`
- `IDC22异变之王发动黑曜石柱攻击，柱子数量: 3`

## 提交记录

修复完成后需要提交到git：
```bash
git add .
git commit -m "修复IDC22异变之王用户自定义模式4个问题

1. 修复Boss血条重复显示问题
2. 修复黑曜石柱数量配置失效问题  
3. 添加黑曜石块攻击独立冷却配置
4. 修复末影水晶攻击冷却配置无效问题

- 优化技能系统，分离不同攻击类型的冷却机制
- 添加详细调试日志和错误处理
- 符合阿里巴巴编码规范
- 向后兼容原有配置系统"
```

---

**修复完成时间**: 2025-01-30  
**版本**: DeathZombieV4 v1.2  
**状态**: ✅ 修复完成，待测试验证
