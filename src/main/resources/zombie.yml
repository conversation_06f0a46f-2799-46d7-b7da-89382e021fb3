# ========================================
# DeathZombieV4 双CustomZombie生成系统配置文件
# 作者: Ver_zhzh
# 版本: 1.0
# 符合阿里巴巴编码规范
# ========================================

# ========================================
# 系统总开关配置
# ========================================
system_settings:
  # 是否启用默认CustomZombie设置（原有硬编码系统）
  use_default_settings: false

  # 是否启用用户自定义设置（新的配置驱动系统）
  use_user_custom_settings: true

  # 调试模式开关
  debug_mode: true

# ========================================
# 游戏级别的开关配置
# ========================================
game_level_settings:
  # 按游戏名称配置不同的僵尸系统
  # 格式: 游戏名称: { use_default: true/false, use_user_custom: true/false }
  
  # 默认游戏配置（当游戏名称未在下方列表中时使用）
  default_game:
    use_default: true
    use_user_custom: false
    
  # 特定游戏配置示例
  # "测试游戏":
  #   use_default: false
  #   use_user_custom: true
  # "经典模式":
  #   use_default: true
  #   use_user_custom: false

# ========================================
# 用户自定义僵尸配置覆盖
# 这些配置将覆盖默认CustomZombie的硬编码设置
# ========================================
user_custom_overrides:
  # 全局默认覆盖设置
  global_defaults:
    # 生命值倍数（基于原始值）
    health_multiplier: 1.0
    
    # 伤害倍数（基于原始值）
    damage_multiplier: 1.0
    
    # 技能冷却时间倍数（基于原始值）
    skill_cooldown_multiplier: 1.0
    
    # 是否启用所有技能
    enable_all_skills: true
    
    # 默认移动速度倍数
    speed_multiplier: 1.0

  # 特定僵尸ID的覆盖配置（ID1-ID4先期实现）
  specific_overrides:
    # ID1 - 普通僵尸覆盖示例
    id1:
      enabled: true                    # 是否启用此覆盖配置
      health_override: 25.0           # 覆盖生命值（原值20.0）
      damage_override: 6.0            # 覆盖伤害值（原值默认）
      speed_multiplier: 1.2           # 速度倍数
      custom_name_override: "§a§l强化普通僵尸"  # 覆盖显示名称
      weapon_override: "STONE_AXE"    # 覆盖武器（原值WOODEN_AXE）
      skill_cooldown_overrides:
        # 技能冷却时间覆盖（如果有技能的话）
        basic_attack: 20              # 基础攻击冷却时间（tick）
      
    # ID2 - 小僵尸覆盖示例  
    id2:
      enabled: true
      health_override: 15.0           # 覆盖生命值（原值10.0）
      damage_override: 4.0            # 覆盖伤害值
      speed_multiplier: 1.5           # 更快的速度倍数
      custom_name_override: "§b§l强化小僵尸"
      weapon_override: "STONE_SWORD"  # 覆盖武器（原值STICK）
      potion_effects:
        # 药水效果覆盖
        speed:
          level: 2                    # 速度等级（原值2，0-based）
          duration: -1                # 持续时间（-1表示永久）
      
    # ID3 - 路障僵尸覆盖示例
    id3:
      enabled: true
      health_override: 40.0           # 覆盖生命值（原值30.0）
      damage_override: 8.0            # 覆盖伤害值
      custom_name_override: "§c§l强化路障僵尸"
      weapon_override: "STONE_AXE"    # 覆盖武器（原值WOODEN_AXE）
      armor_overrides:
        helmet: "DIAMOND_HELMET"      # 覆盖头盔（原值IRON_HELMET）
        helmet_enchantments:
          protection: 2               # 保护附魔等级（原值1）
          
    # ID4 - 钻斧僵尸覆盖示例
    id4:
      enabled: true
      health_override: 25.0           # 覆盖生命值（原值20.0）
      damage_override: 10.0           # 覆盖伤害值（钻石斧高伤害）
      custom_name_override: "§d§l强化钻斧僵尸"
      weapon_override: "NETHERITE_AXE"  # 覆盖武器（原值DIAMOND_AXE）
      armor_overrides:
        leggings: "DIAMOND_LEGGINGS"  # 升级护腿为钻石护腿（原值IRON_LEGGINGS）
        leggings_enchantments:
          protection: 3               # 升级保护附魔等级（原值1）
          unbreaking: 2               # 添加耐久附魔

    # ID5 - 剧毒僵尸覆盖示例
    id5:
      enabled: true
      health_override: 15.0           # 覆盖生命值（原值10.0）
      damage_override: 2.0            # 覆盖伤害值（原值1.0）
      speed_multiplier: 1.3           # 速度倍数（原有速度2效果）
      custom_name_override: "§5§l强化剧毒僵尸"
      weapon_override: "STONE_SWORD"  # 覆盖武器（原值STICK）
      armor_overrides:
        helmet: "LEATHER_HELMET"      # 保持皮革头盔
        chestplate: "CHAINMAIL_CHESTPLATE"  # 升级胸甲为锁链甲
        leggings: "LEATHER_LEGGINGS"  # 保持皮革护腿
        boots: "LEATHER_BOOTS"        # 保持皮革靴子
      potion_effects:
        speed:
          level: 2                    # 速度等级（保持原有效果）
          duration: -1                # 持续时间（永久）
      special_abilities:
        poison_attack_enabled: true   # 是否启用毒性攻击
        poison_duration: 80           # 中毒持续时间（tick，原值60）
        poison_level: 1               # 中毒等级（原值0，0-based）

    # ID6 - 双生僵尸覆盖示例
    id6:
      enabled: true
      health_override: 18.0           # 覆盖生命值（原值15.0）
      damage_override: 3.0            # 覆盖伤害值（原值2.0）
      custom_name_override: "§e§l强化双生僵尸"  # 修正颜色为黄色（原值§e）
      weapon_override: "STONE_HOE"    # 覆盖武器（原值WOODEN_HOE）
      skill_cooldown_overrides:
        twin_task_interval: 80        # 双生任务检查间隔（tick，原值100）
      special_abilities:
        twin_zombie_enabled: true     # 是否启用双生僵尸能力
        twin_spawn_on_death: true     # 死亡时是否生成普通僵尸
        twin_spawn_count: 2           # 死亡时生成的普通僵尸数量

    # ID7 - 骷髅僵尸覆盖示例
    id7:
      enabled: true
      health_override: 40.0           # 覆盖生命值（原值30.0）
      damage_override: 6.0            # 覆盖伤害值（原值4.0）
      custom_name_override: "§7§l强化骷髅僵尸"
      weapon_override: "NETHERITE_SWORD"  # 覆盖武器（原值DIAMOND_SWORD）
      armor_overrides:
        helmet: "DIAMOND_HELMET"      # 升级头盔为钻石头盔（原值IRON_HELMET）
        leggings: "DIAMOND_LEGGINGS"  # 升级护腿为钻石护腿（原值IRON_LEGGINGS）
        helmet_enchantments:
          protection: 3               # 保护附魔等级
          unbreaking: 2               # 耐久附魔等级
        leggings_enchantments:
          protection: 3               # 保护附魔等级
          unbreaking: 2               # 耐久附魔等级
      skill_cooldown_overrides:
        arrow_shooting: 40            # 射箭技能冷却时间（tick，原值60）
        arrow_burst_count: 5          # 每次射击的箭矢数量（原值3）
      special_abilities:
        shooting_enabled: true        # 是否启用射箭能力
        arrow_despawn_time: 300       # 箭矢消失时间（tick，原值200）
        target_range: 20.0            # 目标搜索范围（格，原值无限制）

    # ID8 - 武装僵尸覆盖示例
    id8:
      enabled: true
      health_override: 120.0          # 覆盖生命值（原值100.0）
      damage_override: 8.0            # 覆盖伤害值
      custom_name_override: "§8§l强化武装僵尸"
      weapon_override: "NETHERITE_SWORD"  # 覆盖武器（原值DIAMOND_SWORD）
      armor_overrides:
        helmet: "DIAMOND_HELMET"      # 升级头盔为钻石头盔（原值IRON_HELMET）
        chestplate: "DIAMOND_CHESTPLATE"  # 升级胸甲为钻石胸甲（原值IRON_CHESTPLATE）
        leggings: "DIAMOND_LEGGINGS"  # 升级护腿为钻石护腿（原值IRON_LEGGINGS）
        boots: "DIAMOND_BOOTS"        # 升级靴子为钻石靴子（原值IRON_BOOTS）
        helmet_enchantments:
          protection: 4               # 保护附魔等级
          unbreaking: 3               # 耐久附魔等级
        chestplate_enchantments:
          protection: 4               # 保护附魔等级
          unbreaking: 3               # 耐久附魔等级
      potion_effects:
        speed:
          level: 1                    # 速度等级（原值0，1-based）
          duration: -1                # 持续时间（永久）
        strength:
          level: 1                    # 力量等级（新增）
          duration: -1                # 持续时间（永久）

    # ID9 - 肥胖僵尸覆盖示例（修正版：使用真正的装备）
    id9:
      enabled: true
      health_override: 150.0          # 覆盖生命值（原值100.0）
      damage_override: 10.0           # 覆盖伤害值
      custom_name_override: "§9§l强化肥胖僵尸"
      # 肥胖僵尸装备配置（头盔可用方块，其他部位必须用真正装备）
      armor_overrides:
        # helmet: "COAL_BLOCK"          # 默认煤炭块头盔（方块头盔正常工作）
        # helmet: "OBSIDIAN"            # 例如：黑曜石头盔
        # helmet: "NETHERITE_BLOCK"     # 例如：下界合金块头盔
        chestplate: "IRON_CHESTPLATE"   # 默认皮革胸甲，可改为铁胸甲等真正装备
        leggings: "IRON_LEGGINGS"       # 可以添加护腿（真正装备）
        boots: "IRON_BOOTS"             # 默认皮革靴子，可改为铁靴子等真正装备
        # 防具附魔
        chestplate_enchantments:
          protection: 3                 # 保护附魔
          unbreaking: 4                 # 耐久附魔（肥胖僵尸需要耐用装备）
        boots_enchantments:
          protection: 2                 # 保护附魔
          fire_protection: 2            # 火焰保护
      potion_effects:
        slowness:
          level: 1                    # 缓慢等级（原值1，1-based）
          duration: -1                # 持续时间（永久）
        health_boost:
          level: 4                    # 生命提升等级（原值2，提升到4）
          duration: -1                # 持续时间（永久）
        resistance:
          level: 1                    # 抗性提升等级（新增）
          duration: -1                # 持续时间（永久）

    # ID10 - 法师僵尸覆盖示例（难点）
    id10:
      enabled: true
      health_override: 60.0           # 覆盖生命值（原值50.0）
      damage_override: 4.0            # 覆盖伤害值
      custom_name_override: "§6§l强化法师僵尸"
      weapon_override: "GOLDEN_AXE"   # 覆盖武器（原值WOODEN_AXE）
      armor_overrides:
        helmet: "GOLDEN_HELMET"       # 升级头盔为金头盔（原值CHAINMAIL_HELMET）
        chestplate: "GOLDEN_CHESTPLATE"  # 升级胸甲为金胸甲（原值CHAINMAIL_CHESTPLATE）
        helmet_enchantments:
          protection: 2               # 保护附魔等级
          unbreaking: 2               # 耐久附魔等级
      skill_cooldown_overrides:
        summon_interval: 150          # 召唤间隔时间（tick，原值200，7.5秒）
        summon_count: 3               # 每次召唤数量（原值2）
        max_nearby_zombies: 8         # 最大附近僵尸数量（原值5）
      special_abilities:
        summon_enabled: true          # 是否启用召唤能力
        summon_zombie_type: "id3"     # 召唤的僵尸类型（原值id4，改为路障僵尸）
      potion_effects:
        speed:
          level: 0                    # 速度等级（新增轻微速度提升）
          duration: -1                # 持续时间（永久）

    # ID11 - 自爆僵尸覆盖示例
    id11:
      enabled: true
      health_override: 40.0           # 覆盖生命值（原值30.0）
      damage_override: 5.0            # 覆盖伤害值
      custom_name_override: "§c§l强化自爆僵尸"
      # 自爆僵尸装备配置（可以自定义头部装备）
      armor_overrides:
        # helmet: "TNT"                 # 默认TNT头盔，可以改为其他物品
        # helmet: "CREEPER_HEAD"        # 例如：苦力怕头
        # helmet: "REDSTONE_BLOCK"      # 例如：红石块
        # helmet: "FIRE_CHARGE"         # 例如：火焰弹
        chestplate: "LEATHER_CHESTPLATE"  # 可以添加其他防具
        leggings: "LEATHER_LEGGINGS"
        boots: "LEATHER_BOOTS"
        # 防具附魔
        chestplate_enchantments:
          protection: 2               # 保护附魔
          fire_protection: 3          # 火焰保护（适合自爆僵尸）
      skill_cooldown_overrides:
        detection_range: 6            # 检测玩家范围（格，原值5）
        explosion_delay: 30           # 爆炸延迟时间（tick，原值40，1.5秒）
      special_abilities:
        explosion_enabled: true       # 是否启用自爆能力
        explosion_damage: 15.0        # 爆炸伤害（原值10.0）
        explosion_range: 6.0          # 爆炸范围（格，原值5.0）
      potion_effects:
        speed:
          level: 1                    # 速度等级（新增，让自爆僵尸更快接近目标）
          duration: -1                # 持续时间（永久）

    # ID12 - 毒箭僵尸覆盖示例
    id12:
      enabled: true
      health_override: 60.0           # 覆盖生命值（原值50.0）
      damage_override: 4.0            # 覆盖伤害值
      custom_name_override: "§2§l强化毒箭僵尸"
      weapon_override: "CROSSBOW"     # 覆盖武器（原值BOW，改为弩）
      armor_overrides:
        helmet: "DIAMOND_HELMET"      # 升级头盔为钻石头盔（原值IRON_HELMET）
        leggings: "DIAMOND_LEGGINGS"  # 升级护腿为钻石护腿（原值IRON_LEGGINGS）
        helmet_enchantments:
          protection: 3               # 保护附魔等级
          unbreaking: 2               # 耐久附魔等级
      skill_cooldown_overrides:
        shooting_interval: 80         # 射箭间隔时间（tick，原值100，4秒）
        arrow_count: 5                # 每次射箭数量（原值3）
        target_range: 20              # 目标搜索范围（格，原值16）
        arrow_despawn_time: 300       # 箭矢消失时间（tick，原值200，15秒）
      special_abilities:
        shooting_enabled: true        # 是否启用射箭能力
      potion_effects:
        speed:
          level: 0                    # 速度等级（轻微速度提升）
          duration: -1                # 持续时间（永久）

    # ID13 - 电击僵尸覆盖示例
    id13:
      enabled: true
      health_override: 60.0           # 覆盖生命值（原值50.0）
      damage_override: 6.0            # 覆盖伤害值
      custom_name_override: "§b§l强化电击僵尸"
      weapon_override: "BLAZE_ROD"    # 覆盖武器（原值STICK，改为烈焰棒）
      armor_overrides:
        helmet: "DIAMOND_HELMET"      # 升级头盔为钻石头盔（原值IRON_HELMET）
        chestplate: "DIAMOND_CHESTPLATE"  # 升级胸甲为钻石胸甲（原值IRON_CHESTPLATE）
        leggings: "DIAMOND_LEGGINGS"  # 升级护腿为钻石护腿（原值IRON_LEGGINGS）
        boots: "DIAMOND_BOOTS"        # 升级靴子为钻石靴子（原值IRON_BOOTS）
        helmet_enchantments:
          protection: 4               # 保护附魔等级
          unbreaking: 3               # 耐久附魔等级
      skill_cooldown_overrides:
        electric_interval: 30         # 电击间隔时间（tick，原值40，1.5秒）
        electric_range: 7             # 电击范围（格，原值5）
      special_abilities:
        electric_enabled: true        # 是否启用电击能力
        electric_damage: 2.0          # 电击伤害（原值1.0）
      potion_effects:
        speed:
          level: 0                    # 速度等级（轻微速度提升）
          duration: -1                # 持续时间（永久）
        resistance:
          level: 1                    # 抗性提升等级（新增，电击僵尸更耐打）
          duration: -1                # 持续时间（永久）

    # ID14 - 冰冻僵尸覆盖示例
    id14:
      enabled: true
      health_override: 80.0           # 覆盖生命值（原值70.0）
      damage_override: 6.0            # 覆盖伤害值
      custom_name_override: "§b§l强化冰冻僵尸"
      weapon_override: "DIAMOND"      # 覆盖武器（原值DIAMOND）
      armor_overrides:
        # helmet: "ICE"                 # 默认冰块头盔，可以改为其他物品
        # helmet: "PACKED_ICE"          # 例如：浮冰头盔
        # helmet: "BLUE_ICE"            # 例如：蓝冰头盔
        chestplate: "CHAINMAIL_CHESTPLATE"  # 升级胸甲
        leggings: "CHAINMAIL_LEGGINGS"      # 升级护腿
        boots: "CHAINMAIL_BOOTS"            # 升级靴子
        helmet_enchantments:
          protection: 2               # 保护附魔
          frost_walker: 2             # 冰霜行者附魔（主题匹配）
      skill_cooldown_overrides:
        freezing_interval: 30         # 冰冻间隔时间（tick，原值40，1.5秒）
        freezing_range: 8             # 冰冻范围（格，原值6）
        slowness_duration: 80         # 缓慢持续时间（tick，原值60，4秒）
      special_abilities:
        freezing_enabled: true        # 是否启用冰冻能力
        slowness_level: 2             # 缓慢等级（原值1，提升到2）
      potion_effects:
        speed:
          level: 0                    # 速度等级（轻微速度提升）
          duration: -1                # 持续时间（永久）

    # ID15 - 暗影僵尸覆盖示例
    id15:
      enabled: true
      health_override: 90.0           # 覆盖生命值（原值80.0）
      damage_override: 8.0            # 覆盖伤害值
      custom_name_override: "§8§l强化暗影僵尸"
      armor_overrides:
        # helmet: "OBSIDIAN"            # 默认黑曜石头盔，可以改为其他物品
        # helmet: "BLACKSTONE"          # 例如：黑石头盔
        # helmet: "COAL_BLOCK"          # 例如：煤炭块头盔
        chestplate: "NETHERITE_CHESTPLATE"  # 升级胸甲为下界合金胸甲
        leggings: "NETHERITE_LEGGINGS"      # 升级护腿为下界合金护腿
        boots: "NETHERITE_BOOTS"            # 升级靴子为下界合金靴子
        chestplate_enchantments:
          protection: 4               # 保护附魔
          unbreaking: 3               # 耐久附魔
      skill_cooldown_overrides:
        teleport_interval: 80         # 瞬移间隔时间（tick，原值100，4秒）
        target_range: 20              # 目标搜索范围（格，原值15）
      special_abilities:
        teleport_enabled: true        # 是否启用瞬移能力
        invisibility_enabled: true    # 是否启用隐身能力
      potion_effects:
        speed:
          level: 1                    # 速度等级（暗影僵尸应该更快）
          duration: -1                # 持续时间（永久）

    # ID16 - 毁灭僵尸覆盖示例
    id16:
      enabled: true
      health_override: 200.0          # 覆盖生命值（原值150.0）
      damage_override: 15.0           # 覆盖伤害值
      custom_name_override: "§4§l强化毁灭僵尸"
      weapon_override: "NETHERITE_SWORD"  # 覆盖武器（原值DIAMOND_SWORD，升级为下界合金剑）
      armor_overrides:
        helmet: "NETHERITE_HELMET"    # 升级头盔为下界合金头盔（原值IRON_HELMET）
        chestplate: "NETHERITE_CHESTPLATE"  # 升级胸甲为下界合金胸甲（原值IRON_CHESTPLATE）
        leggings: "NETHERITE_LEGGINGS"      # 升级护腿为下界合金护腿（原值IRON_LEGGINGS）
        boots: "NETHERITE_BOOTS"            # 升级靴子为下界合金靴子（原值IRON_BOOTS）
        # 全套下界合金装备附魔
        helmet_enchantments:
          protection: 4               # 保护附魔
          unbreaking: 3               # 耐久附魔
        chestplate_enchantments:
          protection: 4               # 保护附魔
          unbreaking: 3               # 耐久附魔
        weapon_enchantments:
          sharpness: 5                # 锋利附魔
          unbreaking: 3               # 耐久附魔
      potion_effects:
        strength:
          level: 2                    # 力量等级（3级力量）
          duration: -1                # 持续时间（永久）
        resistance:
          level: 1                    # 抗性提升等级
          duration: -1                # 持续时间（永久）
        speed:
          level: 0                    # 速度等级（轻微速度提升）
          duration: -1                # 持续时间（永久）

    # ID17 - 雷霆僵尸覆盖示例
    id17:
      enabled: true
      health_override: 100.0          # 覆盖生命值（原值80.0）
      damage_override: 8.0            # 覆盖伤害值
      custom_name_override: "§e§l强化雷霆僵尸"
      weapon_override: "BOW"          # 覆盖武器（原值BOW）
      armor_overrides:
        helmet: "GOLD_HELMET"         # 升级头盔为金头盔（原值IRON_HELMET）
        chestplate: "GOLD_CHESTPLATE" # 升级胸甲为金胸甲（原值IRON_CHESTPLATE）
        leggings: "GOLD_LEGGINGS"     # 升级护腿为金护腿（原值IRON_LEGGINGS）
        boots: "GOLD_BOOTS"           # 升级靴子为金靴子（原值IRON_BOOTS）
        helmet_enchantments:
          protection: 3               # 保护附魔
          unbreaking: 2               # 耐久附魔
      skill_cooldown_overrides:
        lightning_interval: 30        # 雷霆间隔时间（tick，原值40，1.5秒）
        target_range: 15              # 目标搜索范围（格，原值12）
      special_abilities:
        lightning_enabled: true       # 是否启用雷霆能力
        lightning_damage: 8.0         # 雷霆伤害（原值6.0）
        lightning_chance: 0.15        # 雷霆概率（原值0.1，15%）
      potion_effects:
        speed:
          level: 1                    # 速度等级（雷霆僵尸应该更快）
          duration: -1                # 持续时间（永久）

    # ID18 - 变异科学家覆盖示例（最复杂的配置）
    id18:
      enabled: true
      health_override: 600.0          # 覆盖生命值（原值600.0）
      damage_override: 10.0           # 覆盖伤害值
      custom_name_override: "§a§l强化变异科学家"
      weapon_override: "DIAMOND_SWORD" # 覆盖武器（原值DIAMOND_SWORD，火焰附加2）
      armor_overrides:
        # helmet: "PLAYER_HEAD"         # 默认玩家头颅，可以改为其他物品
        chestplate: "LEATHER_CHESTPLATE"  # 升级胸甲（原值LEATHER_CHESTPLATE）
        leggings: "LEATHER_LEGGINGS"      # 升级护腿（原值LEATHER_LEGGINGS）
        boots: "LEATHER_BOOTS"            # 升级靴子（原值LEATHER_BOOTS）
        weapon_enchantments:
          fire_aspect: 3              # 火焰附加附魔（原值2，提升到3）
          sharpness: 4                # 锋利附魔（新增）
        chestplate_enchantments:
          protection: 3               # 保护附魔
          unbreaking: 3               # 耐久附魔
      skill_cooldown_overrides:
        effect_interval: 15           # 效果间隔时间（tick，原值20，0.75秒）
        smoke_range: 12               # 烟雾范围（格，原值10）
        damage_range: 6               # 伤害范围（格，原值5）
        nausea_duration: 80           # 恶心持续时间（tick，原值60，4秒）
        slowness_duration: 80         # 缓慢持续时间（tick，原值60，4秒）
        global_damage_interval: 300   # 全局伤害间隔（tick，原值400，15秒）
        death_summon_count: 6         # 死亡召唤数量（原值4，提升到6）
      special_abilities:
        smoke_enabled: true           # 是否启用烟雾能力
        nausea_enabled: true          # 是否启用恶心效果
        slowness_enabled: true        # 是否启用缓慢效果
        smoke_damage: 3.0             # 烟雾伤害（原值2.0）
        # 新增的完整技能配置
        summon_enabled: true          # 是否启用受伤召唤能力
        summon_damage_threshold: 40.0 # 召唤伤害阈值（原值50.0，降低到40）
        global_damage_enabled: true  # 是否启用全局伤害能力
        global_damage: 12.0           # 全局伤害值（原值10.0，提升到12）
        death_summon_enabled: true    # 是否启用死亡召唤能力
        # 召唤版本控制（新增）
        use_user_custom_summon: true        # 受伤召唤使用用户配置版本（true=用户配置，false=默认版本）
        use_user_custom_death_summon: true  # 死亡召唤使用用户配置版本（true=用户配置，false=默认版本）
      potion_effects:
        speed:
          level: 2                    # 速度等级（原值1，提升到2）
          duration: -1                # 持续时间（永久）
        jump_boost:
          level: 1                    # 跳跃提升等级（原值1）
          duration: -1                # 持续时间（永久）
        strength:
          level: 0                    # 力量等级（原值0）
          duration: -1                # 持续时间（永久）

    # ID19 - 变异法师覆盖示例（复杂的三重技能系统）
    id19:
      enabled: true
      health_override: 400.0          # 覆盖生命值（原值400.0）
      damage_override: 8.0            # 覆盖伤害值
      custom_name_override: "§5§l强化变异法师"
      weapon_override: "STICK"        # 覆盖武器（原值STICK）
      armor_overrides:
        # helmet: "PLAYER_HEAD"         # 默认艾利克斯头颅，可以改为其他物品
        chestplate: "LEATHER_CHESTPLATE"  # 皮革胸甲（原值LEATHER_CHESTPLATE）
        leggings: "LEATHER_LEGGINGS"      # 皮革护腿（原值LEATHER_LEGGINGS）
        boots: "LEATHER_BOOTS"            # 皮革靴子（原值LEATHER_BOOTS）
        # 全套保护三附魔
        chestplate_enchantments:
          protection: 3               # 保护附魔
          unbreaking: 3               # 耐久附魔
        leggings_enchantments:
          protection: 3               # 保护附魔
          unbreaking: 3               # 耐久附魔
        boots_enchantments:
          protection: 3               # 保护附魔
          unbreaking: 3               # 耐久附魔
      skill_cooldown_overrides:
        lightning_interval: 80        # 闪电间隔时间（tick，原值100，4秒）
        lightning_range: 8            # 闪电范围（格，原值6，提升到8）
        effect_interval: 15           # 负面效果检查间隔（tick，原值20，0.75秒）
        effect_range: 12              # 负面效果范围（格，原值10，提升到12）
        blindness_duration: 80        # 失明持续时间（tick，原值60，4秒）
        slowness_duration: 80         # 缓慢持续时间（tick，原值60，4秒）
        summon_interval: 160          # 召唤间隔时间（tick，原值200，8秒）
        summon_range: 6               # 召唤检测范围（格，原值5，提升到6）
      special_abilities:
        # 三大技能完整配置
        lightning_enabled: true       # 是否启用闪电攻击能力
        effect_enabled: true          # 是否启用负面效果能力
        summon_enabled: true          # 是否启用召唤能力
        # 召唤版本控制
        use_user_custom_summon: true  # 召唤使用用户配置版本（true=用户配置，false=默认版本）
      potion_effects:
        speed:
          level: 5                    # 速度等级（原值6，调整为5）
          duration: -1                # 持续时间（永久）
        jump_boost:
          level: 5                    # 跳跃提升等级（原值6，调整为5）
          duration: -1                # 持续时间（永久）

    # ID20 - 气球僵尸覆盖示例（飞行和粒子效果）
    id20:
      enabled: true
      health_override: 60.0           # 覆盖生命值（原值60.0）
      damage_override: 5.0            # 覆盖伤害值
      custom_name_override: "§b§l强化气球僵尸"
      # 气球僵尸通常不需要特殊装备
      skill_cooldown_overrides:
        particle_interval: 8          # 粒子效果间隔（tick，原值10，0.4秒）
      special_abilities:
        flying_enabled: true          # 是否启用飞行能力
        particle_enabled: true        # 是否启用粒子效果
        float_height: 4.0             # 飞行高度（格，原值3.0，提升到4）
      potion_effects:
        speed:
          level: 1                    # 速度等级（飞行时的移动速度）
          duration: -1                # 持续时间（永久）

    # ID21 - 迷雾僵尸覆盖示例（迷雾粒子效果）
    id21:
      enabled: true
      health_override: 70.0           # 覆盖生命值（原值70.0）
      damage_override: 6.0            # 覆盖伤害值
      custom_name_override: "§7§l强化迷雾僵尸"
      # 迷雾僵尸通常不需要特殊装备
      skill_cooldown_overrides:
        fog_interval: 3               # 迷雾效果间隔（tick，原值5，0.15秒）
      special_abilities:
        fog_enabled: true             # 是否启用迷雾效果
        fog_radius: 2.5               # 迷雾半径（格，原值2.0，提升到2.5）
        fog_density: 8                # 迷雾密度（粒子数量，原值5，提升到8）
      potion_effects:
        slowness:
          level: 0                    # 缓慢等级（迷雾中的神秘感）
          duration: -1                # 持续时间（永久）

    # ID22 - 变异雷霆僵尸覆盖示例（最复杂的五重技能系统）
    id22:
      enabled: true
      health_override: 800.0          # 覆盖生命值（原值800.0，最高血量）
      damage_override: 12.0           # 覆盖伤害值（高攻击力）
      custom_name_override: "§e§l终极变异雷霆僵尸"
      weapon_override: "BOW"          # 覆盖武器（原值BOW）
      armor_overrides:
        helmet: "ZOMBIE_HEAD"         # 僵尸头颅（原值ZOMBIE_HEAD）
        chestplate: "CHAINMAIL_CHESTPLATE"  # 锁链胸甲（原值CHAINMAIL_CHESTPLATE）
        leggings: "CHAINMAIL_LEGGINGS"      # 锁链护腿（原值CHAINMAIL_LEGGINGS）
        boots: "CHAINMAIL_BOOTS"            # 锁链靴子（原值CHAINMAIL_BOOTS）
        # 全套保护二附魔
        chestplate_enchantments:
          protection: 2               # 保护附魔
          unbreaking: 3               # 耐久附魔
        leggings_enchantments:
          protection: 2               # 保护附魔
          unbreaking: 3               # 耐久附魔
        boots_enchantments:
          protection: 2               # 保护附魔
          unbreaking: 3               # 耐久附魔
      skill_cooldown_overrides:
        global_lightning_interval: 240    # 全局雷电间隔（tick，原值300，12秒）
        teleport_interval: 240            # 瞬移间隔（tick，原值300，12秒）
        summon_interval: 1000             # 召唤间隔（tick，原值1200，50秒）
        time_slow_interval: 160           # 时间流速控制间隔（tick，原值200，8秒）
        freeze_interval: 2000             # 冻结间隔（tick，原值2400，100秒）
        time_slow_duration: 120           # 时间流速持续时间（tick，原值100，6秒）
        freeze_duration: 120              # 冻结持续时间（tick，原值100，6秒）
        summon_thunder_count: 6           # 召唤雷霆僵尸数量（原值4，提升到6）
        summon_creeper_count: 3           # 召唤变异爬行者数量（原值2，提升到3）
      special_abilities:
        # 五大技能完整配置
        global_lightning_enabled: true   # 是否启用全局雷电攻击
        teleport_enabled: true           # 是否启用瞬移雷击
        summon_enabled: true             # 是否启用召唤能力
        time_slow_enabled: true          # 是否启用时间流速控制
        freeze_enabled: true             # 是否启用冻结能力
        # 伤害配置
        global_lightning_damage: 10.0    # 全局雷电伤害（原值8.0，提升到10）
        teleport_lightning_damage: 8.0   # 瞬移雷电伤害（原值6.0，提升到8）
        # 召唤版本控制
        use_user_custom_summon: true     # 召唤使用用户配置版本（true=用户配置，false=默认版本）
      potion_effects:
        speed:
          level: 1                    # 速度等级（雷霆的迅捷）
          duration: -1                # 持续时间（永久）
        resistance:
          level: 1                    # 抗性等级（雷霆的坚韧）
          duration: -1                # 持续时间（永久）

    # ID23 - 终极毁灭僵尸覆盖示例（最强的四重技能系统）
    id23:
      enabled: true
      health_override: 1000.0         # 覆盖生命值（原值1000.0，最高血量）
      damage_override: 15.0           # 覆盖伤害值（最高攻击力）
      custom_name_override: "§4§l§k|§r§4§l终极毁灭僵尸§k|"
      weapon_override: "DIAMOND_SWORD" # 覆盖武器（原值DIAMOND_SWORD）
      armor_overrides:
        helmet: "PLAYER_HEAD"         # 史蒂夫头颅（原值PLAYER_HEAD）
        chestplate: "DIAMOND_CHESTPLATE"  # 钻石胸甲（原值DIAMOND_CHESTPLATE）
        leggings: "DIAMOND_LEGGINGS"      # 钻石护腿（原值DIAMOND_LEGGINGS）
        boots: "DIAMOND_BOOTS"            # 钻石靴子（原值DIAMOND_BOOTS）
        # 全套保护三附魔
        weapon_enchantments:
          sharpness: 3                # 锋利三附魔
          unbreaking: 3               # 耐久三附魔
        chestplate_enchantments:
          protection: 3               # 保护三附魔
          unbreaking: 3               # 耐久三附魔
        leggings_enchantments:
          protection: 3               # 保护三附魔
          unbreaking: 3               # 耐久三附魔
        boots_enchantments:
          protection: 3               # 保护三附魔
          unbreaking: 3               # 耐久三附魔
      skill_cooldown_overrides:
        summon_interval: 500          # 召唤间隔（tick，原值600，25秒）
        explosion_interval: 600       # 爆炸间隔（tick，原值800，30秒）
        aura_check_interval: 80       # 光环检查间隔（tick，原值100，4秒）
        weakness_level: 1             # 虚弱等级（原值1，虚弱2）
        weakness_duration: 80         # 虚弱持续时间（tick，原值60，4秒）
        summon_count: 4               # 召唤数量（原值3，提升到4）
      special_abilities:
        # 四大技能完整配置
        attack_enhance_enabled: true  # 是否启用攻击强化（虚弱+击退）
        summon_enabled: true          # 是否启用召唤能力
        aura_enabled: true            # 是否启用光环能力
        explosion_enabled: true       # 是否启用爆炸能力
        particle_enabled: true        # 是否启用粒子效果
        # 伤害和效果配置
        explosion_damage: 6.0         # 爆炸伤害（原值5.0，提升到6）
        knockback_distance: 5.0       # 击退距离（原值4.0，提升到5）
        # 召唤版本控制
        use_user_custom_summon: true  # 召唤使用用户配置版本（true=用户配置，false=默认版本）
      potion_effects:
        speed:
          level: 1                    # 速度等级（原值2，调整为1）
          duration: -1                # 持续时间（永久）
        jump_boost:
          level: 1                    # 跳跃提升等级（原值2，调整为1）
          duration: -1                # 持续时间（永久）
        strength:
          level: 0                    # 力量等级（原值1，调整为0）
          duration: -1                # 持续时间（永久）
        resistance:
          level: 2                    # 抗性等级（终极BOSS的坚韧）
          duration: -1                # 持续时间（永久）

    # ID24 - 变异暗影僵尸覆盖示例（三重暗影技能系统）
    id24:
      enabled: true
      health_override: 600.0          # 覆盖生命值（原值600.0）
      damage_override: 10.0           # 覆盖伤害值
      custom_name_override: "§8§l§k|§r§8§l变异暗影僵尸§k|"
      # 暗影僵尸通常不需要特殊装备，保持神秘感
      skill_cooldown_overrides:
        teleport_interval: 80         # 瞬移间隔（tick，原值100，4秒）
        summon_interval: 160          # 召唤间隔（tick，原值200，8秒）
        debuff_interval: 30           # 负面效果间隔（tick，原值40，1.5秒）
        debuff_duration: 50           # 负面效果持续时间（tick，原值40，2.5秒）
        blindness_level: 9            # 失明等级（原值8，失明10）
        nausea_level: 9               # 反胃等级（原值8，反胃10）
      special_abilities:
        # 三大技能完整配置
        teleport_enabled: true        # 是否启用瞬移攻击
        summon_enabled: true          # 是否启用召唤能力
        debuff_enabled: true          # 是否启用全局负面效果
        # 伤害配置
        teleport_damage: 10.0         # 瞬移攻击伤害（原值8.0，提升到10）
        # 召唤版本控制
        use_user_custom_summon: true  # 召唤使用用户配置版本（true=用户配置，false=默认版本）
      potion_effects:
        speed:
          level: 1                    # 速度等级（暗影的迅捷）
          duration: -1                # 持续时间（永久）
        invisibility:
          level: 0                    # 隐身等级（暗影的神秘，可选）
          duration: -1                # 持续时间（永久）
        resistance:
          level: 1                    # 抗性等级（暗影的坚韧）
          duration: -1                # 持续时间（永久）

    # ID25 - 变异博士覆盖示例（七重技能系统）
    id25:
      enabled: true
      health_override: 2048.0         # 覆盖生命值（原值2048.0，最高血量）
      damage_override: 20.0           # 覆盖伤害值（最高攻击力）
      custom_name_override: "§4§l§k|||§r§4§l变异博士§k|||"
      weapon_override: "DIAMOND_SWORD" # 覆盖武器（火焰附加2钻石剑）
      armor_overrides:
        helmet: "PLAYER_HEAD"         # 史蒂夫头颅
        chestplate: "DIAMOND_CHESTPLATE"  # 钻石胸甲
        leggings: "DIAMOND_LEGGINGS"      # 钻石护腿
        boots: "DIAMOND_BOOTS"            # 钻石靴子
        # 全套保护三附魔 + 火焰附加二
        weapon_enchantments:
          fire_aspect: 2              # 火焰附加二附魔
          sharpness: 3                # 锋利三附魔
          unbreaking: 3               # 耐久三附魔
        chestplate_enchantments:
          protection: 3               # 保护三附魔
          unbreaking: 3               # 耐久三附魔
        leggings_enchantments:
          protection: 3               # 保护三附魔
          unbreaking: 3               # 耐久三附魔
        boots_enchantments:
          protection: 3               # 保护三附魔
          unbreaking: 3               # 耐久三附魔
      skill_cooldown_overrides:
        random_summon_interval: 500   # 随机召唤间隔（tick，原值600，25秒）
        auto_attack_interval: 15      # 自动攻击间隔（tick，原值20，0.75秒）
        electric_shock_interval: 3000 # 电击间隔（tick，原值3600，2.5分钟）
        freeze_interval: 4000         # 冻结间隔（tick，原值4800，3.3分钟）
        electric_field_interval: 50  # 电流领域间隔（tick，原值60，2.5秒）
        weakness_level: 1             # 虚弱等级（原值1，虚弱2）
        weakness_duration: 80         # 虚弱持续时间（tick，原值60，4秒）
        freeze_duration: 120          # 冻结持续时间（tick，原值100，6秒）
      special_abilities:
        # 七大技能完整配置
        random_summon_enabled: true   # 是否启用随机召唤（技能1）
        auto_attack_enabled: true     # 是否启用自动攻击（技能2）
        electric_shock_enabled: true  # 是否启用巨额电击（技能3）
        attack_enhance_enabled: true  # 是否启用攻击强化（技能4）
        freeze_enabled: true          # 是否启用冻结技能（技能5）
        electric_field_enabled: true  # 是否启用电流领域（技能6）
        scientist_summon_enabled: true # 是否启用科学家召唤（技能7）
        particle_enabled: true        # 是否启用华丽粒子效果
        # 伤害和效果配置
        scientist_summon_threshold: 40.0 # 科学家召唤阈值（原值50.0，降低到40）
        electric_shock_damage: 18.0   # 电击伤害（原值16.0，提升到18）
        electric_field_damage: 5.0    # 电流领域伤害（原值4.0，提升到5）
        auto_attack_range: 25.0       # 自动攻击范围（原值20.0，提升到25）
        knockback_distance: 5.0       # 击退距离（原值4.0，提升到5）
        # 召唤版本控制
        use_user_custom_summon: true  # 召唤使用用户配置版本（true=用户配置，false=默认版本）
      potion_effects:
        speed:
          level: 5                    # 速度等级（原值6，调整为5）
          duration: -1                # 持续时间（永久）
        strength:
          level: 2                    # 力量等级（博士的强大力量）
          duration: -1                # 持续时间（永久）
        resistance:
          level: 3                    # 抗性等级（终极BOSS的超强坚韧）
          duration: -1                # 持续时间（永久）
        regeneration:
          level: 1                    # 再生等级（博士的自愈能力）
          duration: -1                # 持续时间（永久）

# ========================================
# 用户自定义新僵尸配置
# 完全由配置文件定义的新僵尸类型（暂未实现，预留）
# ========================================
user_custom_zombies:
  # 用户自定义僵尸示例（预留功能）
  uc1:
    enabled: false                   # 暂时禁用，等待后续实现
    name: "自定义战士僵尸"
    display_name: "§e§l自定义战士僵尸"
    health: 80.0
    damage: 10.0
    speed: 1.2
    baby: false
    entity_type: "ZOMBIE"
    equipment:
      helmet: "DIAMOND_HELMET"
      chestplate: "DIAMOND_CHESTPLATE"
      leggings: "DIAMOND_LEGGINGS"
      boots: "DIAMOND_BOOTS"
      mainhand: "DIAMOND_SWORD"
      offhand: "SHIELD"

# ========================================
# 生成系统配置
# ========================================
spawn_system:
  # 生成延迟配置
  spawn_delays:
    default_delay: 0                 # 默认生成延迟（tick）
    user_custom_delay: 5             # 用户自定义僵尸额外延迟
    
  # 生成效果配置
  spawn_effects:
    enable_particles: true           # 是否启用粒子效果
    enable_sounds: true              # 是否启用音效
    particle_type: "VILLAGER_HAPPY"  # 粒子类型
    sound_type: "ENTITY_ZOMBIE_AMBIENT"  # 音效类型
    
  # 元数据标记配置
  metadata_tags:
    default_zombie_tag: "defaultCustomZombie"
    user_custom_tag: "userCustomZombie"
    system_tag: "dualZombieSystem"

# ========================================
# 性能优化配置
# ========================================
performance:
  # 最大同时存在的用户自定义僵尸数量
  max_user_custom_zombies: 50
  
  # 配置文件重载间隔（秒）
  config_reload_interval: 300
  
  # 是否启用配置缓存
  enable_config_cache: true

# ========================================
# 兼容性配置
# ========================================
compatibility:
  # 与原有CustomZombie系统的兼容模式
  legacy_compatibility: true
  
  # 是否保持原有的元数据标记
  preserve_original_metadata: true
  
  # 是否保持原有的命名规则
  preserve_original_naming: true

# ========================================
# 版本信息
# ========================================
version_info:
  config_version: "1.0"
  last_updated: "2025-07-22"
  author: "Ver_zhzh"
  description: "DeathZombieV4双CustomZombie生成系统配置文件"
