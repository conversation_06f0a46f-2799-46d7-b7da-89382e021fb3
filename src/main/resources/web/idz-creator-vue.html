<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>僵尸末日网页编辑器 - IDZ自定义怪物创建器</title>
    <!-- 引入Bootstrap CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <!-- 引入图标 -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <!-- 引入Google字体 -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Microsoft+YaHei:wght@300;400;500;600;700&display=swap">
    <!-- 引入Vue 3 -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <!-- 引入身份验证脚本 -->
    <script src="js/auth.js"></script>
    <!-- 引入自定义样式 -->
    <link rel="stylesheet" href="css/idz-creator.css">
    <!-- 引入字体重置样式 (必须在最后) -->
    <link rel="stylesheet" href="css/font-reset.css">
</head>
<body>
    <div id="app">
        <!-- 导航栏 -->
        <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
            <div class="container-fluid">
                <a class="navbar-brand" href="#">
                    <i class="bi bi-robot"></i>
                    IDZ自定义怪物创建器
                </a>
                <div class="navbar-nav ms-auto">
                    <a class="nav-link" href="zombie-spawn-vue.html">
                        <i class="bi bi-arrow-left"></i> 返回主页
                    </a>
                </div>
            </div>
        </nav>

        <!-- 主要内容 -->
        <div class="container-fluid mt-4">
            <div class="row">
                <!-- 左侧面板 - 实体列表 -->
                <div class="col-md-3">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">
                                <i class="bi bi-list-ul"></i>
                                IDZ实体列表
                            </h5>
                            <button class="btn btn-primary btn-sm" @click="createNewEntity">
                                <i class="bi bi-plus"></i>
                                新建
                            </button>
                        </div>
                        <div class="card-body p-0">
                            <div class="list-group list-group-flush">
                                <div v-for="entity in idzEntities" :key="entity.entityId" 
                                     class="list-group-item list-group-item-action"
                                     :class="{ active: selectedEntity?.entityId === entity.entityId }"
                                     @click="selectEntity(entity)">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <h6 class="mb-1">{{ entity.displayName }}</h6>
                                            <small class="text-muted">{{ entity.entityId }}</small>
                                        </div>
                                        <div class="btn-group btn-group-sm">
                                            <button class="btn btn-outline-danger" @click.stop="deleteEntity(entity.entityId)">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                <div v-if="idzEntities.length === 0" class="list-group-item text-center text-muted">
                                    暂无IDZ实体
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 技能模板库 -->
                    <div class="card mt-3">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="bi bi-magic"></i>
                                技能模板库
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <input type="text" class="form-control form-control-sm" 
                                       placeholder="搜索技能..." v-model="skillSearchKeyword">
                            </div>
                            <div class="accordion" id="skillAccordion">
                                <div v-for="(skills, category) in groupedSkills" :key="category" class="accordion-item">
                                    <h2 class="accordion-header">
                                        <button class="accordion-button collapsed" type="button" 
                                                :data-bs-target="'#collapse' + category" 
                                                data-bs-toggle="collapse">
                                            {{ getCategoryDisplayName(category) }}
                                            <span class="badge bg-secondary ms-2">{{ skills.length }}</span>
                                        </button>
                                    </h2>
                                    <div :id="'collapse' + category" class="accordion-collapse collapse" 
                                         data-bs-parent="#skillAccordion">
                                        <div class="accordion-body p-2">
                                            <div v-for="skill in skills" :key="skill.skillId" 
                                                 class="skill-item p-2 mb-2 border rounded"
                                                 draggable="true" @dragstart="onSkillDragStart($event, skill)">
                                                <div class="d-flex justify-content-between align-items-start">
                                                    <div class="flex-grow-1">
                                                        <h6 class="mb-1">{{ skill.skillName }}</h6>
                                                        <small class="text-muted">{{ skill.description }}</small>
                                                        <div class="mt-1">
                                                            <span class="badge bg-info">{{ skill.sourceEntity }}</span>
                                                        </div>
                                                    </div>
                                                    <button class="btn btn-outline-primary btn-sm" 
                                                            @click="addSkillToEntity(skill)">
                                                        <i class="bi bi-plus"></i>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 中间面板 - 实体配置 -->
                <div class="col-md-6">
                    <div v-if="selectedEntity" class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">
                                <i class="bi bi-gear"></i>
                                {{ selectedEntity.displayName }} - 配置
                            </h5>
                            <div class="btn-group">
                                <button class="btn btn-success btn-sm" @click="saveEntity">
                                    <i class="bi bi-save"></i>
                                    保存
                                </button>
                                <button class="btn btn-info btn-sm" @click="testSpawnEntity">
                                    <i class="bi bi-play"></i>
                                    测试生成
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <!-- 基础属性配置 -->
                            <div class="row mb-4">
                                <div class="col-md-6">
                                    <label class="form-label">实体ID</label>
                                    <input type="text" class="form-control" v-model="selectedEntity.entityId" readonly>
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">显示名称</label>
                                    <input type="text" class="form-control" v-model="selectedEntity.displayName">
                                </div>
                            </div>

                            <div class="row mb-4">
                                <div class="col-md-12">
                                    <label class="form-label">描述</label>
                                    <textarea class="form-control" rows="2" v-model="selectedEntity.description"></textarea>
                                </div>
                            </div>

                            <div class="row mb-4">
                                <div class="col-md-6">
                                    <label class="form-label">实体类型</label>
                                    <select class="form-select" v-model="selectedEntity.entityType">
                                        <option v-for="type in entityTypes" :key="type" :value="type">{{ type }}</option>
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">生命值</label>
                                    <input type="number" class="form-control" v-model.number="selectedEntity.health" min="1" max="10000">
                                </div>
                            </div>

                            <div class="row mb-4">
                                <div class="col-md-4">
                                    <label class="form-label">攻击伤害</label>
                                    <input type="number" class="form-control" v-model.number="selectedEntity.damage" min="0" max="100" step="0.1">
                                </div>
                                <div class="col-md-4">
                                    <label class="form-label">移动速度</label>
                                    <input type="number" class="form-control" v-model.number="selectedEntity.speed" min="0.1" max="5" step="0.1">
                                </div>
                                <div class="col-md-4">
                                    <div class="form-check mt-4">
                                        <input class="form-check-input" type="checkbox" v-model="selectedEntity.hasAI">
                                        <label class="form-check-label">启用AI</label>
                                    </div>
                                </div>
                            </div>

                            <!-- 技能配置 -->
                            <div class="mb-4">
                                <h6 class="border-bottom pb-2">
                                    <i class="bi bi-magic"></i>
                                    技能配置
                                    <span class="badge bg-primary ms-2">{{ selectedEntity.skills.length }}</span>
                                </h6>
                                <div class="skill-drop-zone border border-dashed rounded p-3 mb-3"
                                     @drop="onSkillDrop($event)" @dragover.prevent @dragenter.prevent>
                                    <div v-if="selectedEntity.skills.length === 0" class="text-center text-muted">
                                        <i class="bi bi-magic fs-1"></i>
                                        <p class="mt-2">拖拽技能到这里或点击左侧技能的 + 按钮添加</p>
                                    </div>
                                    <div v-else>
                                        <div v-for="(skill, index) in selectedEntity.skills" :key="skill.skillId" 
                                             class="skill-config-item border rounded p-3 mb-2">
                                            <div class="d-flex justify-content-between align-items-start">
                                                <div class="flex-grow-1">
                                                    <h6 class="mb-2">
                                                        {{ getSkillTemplate(skill.skillId)?.skillName || skill.skillId }}
                                                        <span class="badge bg-success ms-2" v-if="skill.enabled">启用</span>
                                                        <span class="badge bg-secondary ms-2" v-else>禁用</span>
                                                    </h6>
                                                    <p class="text-muted small mb-2">
                                                        {{ getSkillTemplate(skill.skillId)?.description }}
                                                    </p>
                                                    
                                                    <!-- 技能参数配置 -->
                                                    <div v-if="getSkillTemplate(skill.skillId)" class="skill-parameters">
                                                        <div class="row g-2">
                                                            <div v-for="(paramConfig, paramName) in getSkillTemplate(skill.skillId).parameterConfigs" 
                                                                 :key="paramName" class="col-md-6">
                                                                <label class="form-label small">
                                                                    {{ paramConfig.displayName }}
                                                                    <span v-if="paramConfig.unit">({{ paramConfig.unit }})</span>
                                                                </label>
                                                                <input v-if="paramConfig.dataType === 'Integer'" 
                                                                       type="number" class="form-control form-control-sm"
                                                                       v-model.number="skill.parameters[paramName]"
                                                                       :min="paramConfig.minValue" :max="paramConfig.maxValue">
                                                                <input v-else-if="paramConfig.dataType === 'Double'" 
                                                                       type="number" class="form-control form-control-sm"
                                                                       v-model.number="skill.parameters[paramName]"
                                                                       :min="paramConfig.minValue" :max="paramConfig.maxValue" step="0.1">
                                                                <input v-else type="text" class="form-control form-control-sm"
                                                                       v-model="skill.parameters[paramName]">
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="btn-group-vertical btn-group-sm ms-2">
                                                    <button class="btn btn-outline-secondary" @click="skill.enabled = !skill.enabled">
                                                        <i :class="skill.enabled ? 'bi bi-toggle-on' : 'bi bi-toggle-off'"></i>
                                                    </button>
                                                    <button class="btn btn-outline-danger" @click="removeSkill(index)">
                                                        <i class="bi bi-trash"></i>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div v-else class="card">
                        <div class="card-body text-center text-muted">
                            <i class="bi bi-robot fs-1"></i>
                            <h5 class="mt-3">选择或创建一个IDZ实体</h5>
                            <p>从左侧选择现有实体进行编辑，或点击"新建"按钮创建新的IDZ实体</p>
                        </div>
                    </div>
                </div>

                <!-- 右侧面板 - 预览和工具 -->
                <div class="col-md-3">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="bi bi-eye"></i>
                                实体预览
                            </h5>
                        </div>
                        <div class="card-body">
                            <div v-if="selectedEntity" class="entity-preview">
                                <div class="text-center mb-3">
                                    <div class="entity-icon">
                                        <i class="bi bi-robot fs-1"></i>
                                    </div>
                                    <h6 class="mt-2">{{ selectedEntity.displayName }}</h6>
                                    <small class="text-muted">{{ selectedEntity.entityType }}</small>
                                </div>
                                
                                <div class="entity-stats">
                                    <div class="row g-2 text-center">
                                        <div class="col-4">
                                            <div class="stat-item">
                                                <i class="bi bi-heart-fill text-danger"></i>
                                                <div class="stat-value">{{ selectedEntity.health }}</div>
                                                <div class="stat-label">生命值</div>
                                            </div>
                                        </div>
                                        <div class="col-4">
                                            <div class="stat-item">
                                                <i class="bi bi-sword text-warning"></i>
                                                <div class="stat-value">{{ selectedEntity.damage }}</div>
                                                <div class="stat-label">攻击力</div>
                                            </div>
                                        </div>
                                        <div class="col-4">
                                            <div class="stat-item">
                                                <i class="bi bi-speedometer text-info"></i>
                                                <div class="stat-value">{{ selectedEntity.speed }}x</div>
                                                <div class="stat-label">速度</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="mt-3">
                                    <h6>技能列表</h6>
                                    <div v-if="selectedEntity.skills.length === 0" class="text-muted small">
                                        暂无技能
                                    </div>
                                    <div v-else>
                                        <div v-for="skill in selectedEntity.skills" :key="skill.skillId" 
                                             class="skill-preview-item d-flex justify-content-between align-items-center mb-1">
                                            <span class="small">{{ getSkillTemplate(skill.skillId)?.skillName || skill.skillId }}</span>
                                            <span :class="skill.enabled ? 'badge bg-success' : 'badge bg-secondary'">
                                                {{ skill.enabled ? '启用' : '禁用' }}
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div v-else class="text-center text-muted">
                                <i class="bi bi-eye-slash fs-1"></i>
                                <p class="mt-2">选择实体以查看预览</p>
                            </div>
                        </div>
                    </div>

                    <!-- 工具面板 -->
                    <div class="card mt-3">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="bi bi-tools"></i>
                                工具
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="d-grid gap-2">
                                <button class="btn btn-outline-primary" @click="exportEntity" :disabled="!selectedEntity">
                                    <i class="bi bi-download"></i>
                                    导出配置
                                </button>
                                <button class="btn btn-outline-secondary" @click="importEntity">
                                    <i class="bi bi-upload"></i>
                                    导入配置
                                </button>
                                <button class="btn btn-outline-info" @click="validateEntity" :disabled="!selectedEntity">
                                    <i class="bi bi-check-circle"></i>
                                    验证配置
                                </button>
                                <button class="btn btn-outline-warning" @click="showStatistics">
                                    <i class="bi bi-graph-up"></i>
                                    系统统计
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 加载指示器 -->
        <div v-if="loading" class="loading-overlay">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">加载中...</span>
            </div>
        </div>
    </div>

    <!-- 引入Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- 引入IDZ创建器脚本 -->
    <script src="js/idz-creator-vue.js"></script>
</body>
</html>
