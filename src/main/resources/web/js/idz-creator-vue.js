// IDZ自定义怪物创建器 Vue.js 应用
const { createApp, ref, computed, onMounted } = Vue;

// 实体类型列表
const ENTITY_TYPES = [
    'ZOMBIE', 'SKELETON', 'CREEPER', 'SPIDER', '<PERSON><PERSON>ERMA<PERSON>', 'WITCH',
    'BLAZE', 'GHAST', 'SLIME', 'MAGMA_CUBE', 'ENDER_DRAGON', 'WITHER',
    'ZOMBIFIED_PIGLIN', 'PIGLIN', 'HOGLIN', 'ZOGLIN', 'STRIDER',
    'CAVE_SPIDER', 'SILVERFISH', 'ENDERMITE', 'GUARDIAN', 'ELDER_GUARDIAN',
    'SHULKER', 'VEX', 'EVOKER', 'VINDICATOR', 'PILLAGER', 'RAVAGER',
    'DROWNED', 'HUSK', 'STRAY', 'PHANTOM', 'WARDEN'
];

// 技能分类显示名称映射
const CATEGORY_DISPLAY_NAMES = {
    'ATTACK': '攻击类',
    'SUMMON': '召唤类',
    'MOVEMENT': '移动类',
    'EFFECT': '特效类',
    'DEFENSE': '防御类',
    'DEBUFF': '负面类',
    'BUFF': '增益类',
    'ENVIRONMENTAL': '环境类'
};

createApp({
    setup() {
        // 响应式数据
        const loading = ref(false);
        const idzEntities = ref([]);
        const selectedEntity = ref(null);
        const skillTemplates = ref([]);
        const skillSearchKeyword = ref('');
        
        // 计算属性
        const entityTypes = computed(() => ENTITY_TYPES);
        
        const filteredSkills = computed(() => {
            if (!skillSearchKeyword.value) {
                return skillTemplates.value;
            }
            const keyword = skillSearchKeyword.value.toLowerCase();
            return skillTemplates.value.filter(skill => 
                skill.skillName.toLowerCase().includes(keyword) ||
                skill.description.toLowerCase().includes(keyword) ||
                skill.sourceEntity.toLowerCase().includes(keyword)
            );
        });
        
        const groupedSkills = computed(() => {
            const groups = {};
            filteredSkills.value.forEach(skill => {
                const category = skill.category;
                if (!groups[category]) {
                    groups[category] = [];
                }
                groups[category].push(skill);
            });
            return groups;
        });
        
        // 方法
        const getCategoryDisplayName = (category) => {
            return CATEGORY_DISPLAY_NAMES[category] || category;
        };
        
        const getSkillTemplate = (skillId) => {
            return skillTemplates.value.find(skill => skill.skillId === skillId);
        };
        
        const loadIdzEntities = async () => {
            try {
                loading.value = true;
                const response = await fetch('/api/idz/entities');
                if (response.ok) {
                    const data = await response.json();
                    idzEntities.value = data.entities || [];
                } else {
                    console.error('加载IDZ实体失败:', response.statusText);
                }
            } catch (error) {
                console.error('加载IDZ实体出错:', error);
            } finally {
                loading.value = false;
            }
        };
        
        const loadSkillTemplates = async () => {
            try {
                const response = await fetch('/api/idz/skill-templates');
                if (response.ok) {
                    const data = await response.json();
                    skillTemplates.value = data.templates || [];
                } else {
                    console.error('加载技能模板失败:', response.statusText);
                }
            } catch (error) {
                console.error('加载技能模板出错:', error);
            }
        };
        
        const createNewEntity = () => {
            const entityId = 'idz' + (Date.now() % 100000);
            const newEntity = {
                entityId: entityId,
                displayName: '新建IDZ实体',
                description: '',
                createdBy: 'WebUI',
                entityType: 'ZOMBIE',
                health: 20.0,
                damage: 2.0,
                speed: 1.0,
                canPickupItems: false,
                canDespawn: false,
                hasAI: true,
                skills: [],
                equipment: {
                    mainHand: { material: 'AIR' },
                    offHand: { material: 'AIR' },
                    helmet: { material: 'AIR' },
                    chestplate: { material: 'AIR' },
                    leggings: { material: 'AIR' },
                    boots: { material: 'AIR' }
                },
                potionEffects: [],
                appearance: {
                    customNameVisible: false,
                    glowing: false,
                    invisible: false,
                    silent: false,
                    scale: 1.0
                },
                advanced: {
                    persistAcrossRestart: false,
                    maxInstanceCount: -1,
                    spawnConditions: [],
                    customData: {}
                }
            };
            
            idzEntities.value.push(newEntity);
            selectedEntity.value = newEntity;
        };
        
        const selectEntity = (entity) => {
            selectedEntity.value = entity;
        };
        
        const deleteEntity = async (entityId) => {
            if (!confirm('确定要删除这个IDZ实体吗？此操作不可撤销。')) {
                return;
            }
            
            try {
                loading.value = true;
                const response = await fetch(`/api/idz/entities/${entityId}`, {
                    method: 'DELETE'
                });
                
                if (response.ok) {
                    idzEntities.value = idzEntities.value.filter(e => e.entityId !== entityId);
                    if (selectedEntity.value?.entityId === entityId) {
                        selectedEntity.value = null;
                    }
                    alert('IDZ实体已删除');
                } else {
                    alert('删除失败: ' + response.statusText);
                }
            } catch (error) {
                console.error('删除IDZ实体出错:', error);
                alert('删除出错: ' + error.message);
            } finally {
                loading.value = false;
            }
        };
        
        const saveEntity = async () => {
            if (!selectedEntity.value) {
                return;
            }
            
            try {
                loading.value = true;
                const response = await fetch('/api/idz/entities', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(selectedEntity.value)
                });
                
                if (response.ok) {
                    alert('IDZ实体已保存');
                    await loadIdzEntities(); // 重新加载列表
                } else {
                    const errorData = await response.json();
                    alert('保存失败: ' + (errorData.message || response.statusText));
                }
            } catch (error) {
                console.error('保存IDZ实体出错:', error);
                alert('保存出错: ' + error.message);
            } finally {
                loading.value = false;
            }
        };
        
        const testSpawnEntity = async () => {
            if (!selectedEntity.value) {
                return;
            }
            
            try {
                loading.value = true;
                const response = await fetch('/api/idz/spawn', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        entityId: selectedEntity.value.entityId,
                        location: 'player' // 在玩家位置生成
                    })
                });
                
                if (response.ok) {
                    alert('IDZ实体已在游戏中生成！');
                } else {
                    const errorData = await response.json();
                    alert('生成失败: ' + (errorData.message || response.statusText));
                }
            } catch (error) {
                console.error('生成IDZ实体出错:', error);
                alert('生成出错: ' + error.message);
            } finally {
                loading.value = false;
            }
        };
        
        const addSkillToEntity = (skill) => {
            if (!selectedEntity.value) {
                alert('请先选择一个IDZ实体');
                return;
            }
            
            // 检查技能是否已存在
            const existingSkill = selectedEntity.value.skills.find(s => s.skillId === skill.skillId);
            if (existingSkill) {
                alert('该技能已存在');
                return;
            }
            
            // 创建技能配置
            const skillConfig = {
                skillId: skill.skillId,
                enabled: true,
                parameters: {},
                priority: 0
            };
            
            // 设置默认参数
            if (skill.parameterConfigs) {
                Object.entries(skill.parameterConfigs).forEach(([paramName, paramConfig]) => {
                    skillConfig.parameters[paramName] = paramConfig.defaultValue;
                });
            }
            
            selectedEntity.value.skills.push(skillConfig);
        };
        
        const removeSkill = (index) => {
            if (selectedEntity.value) {
                selectedEntity.value.skills.splice(index, 1);
            }
        };
        
        const onSkillDragStart = (event, skill) => {
            event.dataTransfer.setData('application/json', JSON.stringify(skill));
        };
        
        const onSkillDrop = (event) => {
            event.preventDefault();
            try {
                const skillData = JSON.parse(event.dataTransfer.getData('application/json'));
                addSkillToEntity(skillData);
            } catch (error) {
                console.error('拖拽技能失败:', error);
            }
        };
        
        const exportEntity = () => {
            if (!selectedEntity.value) {
                return;
            }
            
            const dataStr = JSON.stringify(selectedEntity.value, null, 2);
            const dataBlob = new Blob([dataStr], { type: 'application/json' });
            const url = URL.createObjectURL(dataBlob);
            
            const link = document.createElement('a');
            link.href = url;
            link.download = `${selectedEntity.value.entityId}.json`;
            link.click();
            
            URL.revokeObjectURL(url);
        };
        
        const importEntity = () => {
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = '.json';
            input.onchange = (event) => {
                const file = event.target.files[0];
                if (file) {
                    const reader = new FileReader();
                    reader.onload = (e) => {
                        try {
                            const entityData = JSON.parse(e.target.result);
                            // 验证数据格式
                            if (entityData.entityId && entityData.displayName) {
                                idzEntities.value.push(entityData);
                                selectedEntity.value = entityData;
                                alert('IDZ实体配置已导入');
                            } else {
                                alert('无效的配置文件格式');
                            }
                        } catch (error) {
                            alert('解析配置文件失败: ' + error.message);
                        }
                    };
                    reader.readAsText(file);
                }
            };
            input.click();
        };
        
        const validateEntity = async () => {
            if (!selectedEntity.value) {
                return;
            }
            
            try {
                const response = await fetch('/api/idz/validate', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(selectedEntity.value)
                });
                
                if (response.ok) {
                    const result = await response.json();
                    if (result.valid) {
                        alert('✅ 配置验证通过！');
                    } else {
                        alert('❌ 配置验证失败:\n' + result.errors.join('\n'));
                    }
                } else {
                    alert('验证请求失败: ' + response.statusText);
                }
            } catch (error) {
                console.error('验证配置出错:', error);
                alert('验证出错: ' + error.message);
            }
        };
        
        const showStatistics = async () => {
            try {
                const response = await fetch('/api/idz/statistics');
                if (response.ok) {
                    const stats = await response.json();
                    let message = '📊 系统统计信息:\n\n';
                    message += `总技能模板数: ${stats.totalSkills}\n`;
                    message += `IDZ实体数: ${stats.idzConfigCount}\n\n`;
                    message += '技能分类统计:\n';
                    Object.entries(stats.skillsByCategory || {}).forEach(([category, count]) => {
                        message += `  ${category}: ${count}个\n`;
                    });
                    alert(message);
                } else {
                    alert('获取统计信息失败: ' + response.statusText);
                }
            } catch (error) {
                console.error('获取统计信息出错:', error);
                alert('获取统计信息出错: ' + error.message);
            }
        };
        
        // 生命周期
        onMounted(async () => {
            await Promise.all([
                loadIdzEntities(),
                loadSkillTemplates()
            ]);
        });
        
        return {
            loading,
            idzEntities,
            selectedEntity,
            skillTemplates,
            skillSearchKeyword,
            entityTypes,
            filteredSkills,
            groupedSkills,
            getCategoryDisplayName,
            getSkillTemplate,
            createNewEntity,
            selectEntity,
            deleteEntity,
            saveEntity,
            testSpawnEntity,
            addSkillToEntity,
            removeSkill,
            onSkillDragStart,
            onSkillDrop,
            exportEntity,
            importEntity,
            validateEntity,
            showStatistics
        };
    }
}).mount('#app');
