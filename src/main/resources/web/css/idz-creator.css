/* IDZ自定义怪物创建器样式 */

/* 全局样式 */
body {
    font-family: 'Microsoft YaHei', sans-serif;
    background-color: #f8f9fa;
}

/* 导航栏样式 */
.navbar-brand {
    font-weight: 600;
}

.navbar-brand i {
    margin-right: 8px;
    color: #007bff;
}

/* 卡片样式 */
.card {
    border: none;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    border-radius: 8px;
}

.card-header {
    background-color: #fff;
    border-bottom: 1px solid #e9ecef;
    font-weight: 600;
}

.card-header h5 {
    color: #495057;
}

.card-header i {
    margin-right: 8px;
    color: #007bff;
}

/* 实体列表样式 */
.list-group-item {
    border: none;
    border-bottom: 1px solid #e9ecef;
    transition: all 0.2s ease;
}

.list-group-item:hover {
    background-color: #f8f9fa;
}

.list-group-item.active {
    background-color: #007bff;
    border-color: #007bff;
}

.list-group-item.active h6,
.list-group-item.active small {
    color: white;
}

/* 技能模板样式 */
.skill-item {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6 !important;
    cursor: grab;
    transition: all 0.2s ease;
}

.skill-item:hover {
    background-color: #e9ecef;
    border-color: #007bff !important;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0,123,255,0.2);
}

.skill-item:active {
    cursor: grabbing;
}

.skill-item h6 {
    color: #495057;
    font-size: 0.9rem;
}

.skill-item small {
    color: #6c757d;
    font-size: 0.8rem;
}

.skill-item .badge {
    font-size: 0.7rem;
}

/* 技能拖拽区域样式 */
.skill-drop-zone {
    min-height: 120px;
    background-color: #f8f9fa;
    border: 2px dashed #dee2e6 !important;
    transition: all 0.3s ease;
}

.skill-drop-zone:hover {
    border-color: #007bff !important;
    background-color: #e7f3ff;
}

.skill-drop-zone.drag-over {
    border-color: #28a745 !important;
    background-color: #d4edda;
}

/* 技能配置项样式 */
.skill-config-item {
    background-color: #fff;
    border: 1px solid #e9ecef !important;
    transition: all 0.2s ease;
}

.skill-config-item:hover {
    border-color: #007bff !important;
    box-shadow: 0 2px 8px rgba(0,123,255,0.1);
}

.skill-config-item h6 {
    color: #495057;
    margin-bottom: 8px;
}

.skill-config-item .badge {
    font-size: 0.75rem;
}

/* 技能参数配置样式 */
.skill-parameters {
    background-color: #f8f9fa;
    border-radius: 6px;
    padding: 12px;
    margin-top: 8px;
}

.skill-parameters .form-label {
    color: #6c757d;
    font-weight: 500;
    margin-bottom: 4px;
}

.skill-parameters .form-control {
    font-size: 0.875rem;
}

/* 实体预览样式 */
.entity-preview {
    text-align: center;
}

.entity-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #007bff, #0056b3);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
    color: white;
    font-size: 2rem;
}

.entity-stats {
    margin-top: 20px;
}

.stat-item {
    padding: 8px;
    background-color: #f8f9fa;
    border-radius: 6px;
    margin-bottom: 8px;
}

.stat-item i {
    font-size: 1.2rem;
    margin-bottom: 4px;
}

.stat-value {
    font-weight: 600;
    font-size: 1.1rem;
    color: #495057;
}

.stat-label {
    font-size: 0.8rem;
    color: #6c757d;
}

/* 技能预览项样式 */
.skill-preview-item {
    padding: 4px 8px;
    background-color: #f8f9fa;
    border-radius: 4px;
    font-size: 0.875rem;
}

/* 表单样式增强 */
.form-label {
    font-weight: 500;
    color: #495057;
    margin-bottom: 6px;
}

.form-control:focus,
.form-select:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
}

/* 按钮样式 */
.btn {
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.2s ease;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.15);
}

.btn-group-sm .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.8rem;
}

/* 手风琴样式 */
.accordion-button {
    font-weight: 500;
    color: #495057;
}

.accordion-button:not(.collapsed) {
    background-color: #e7f3ff;
    color: #0056b3;
}

.accordion-button:focus {
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
}

.accordion-body {
    background-color: #f8f9fa;
}

/* 徽章样式 */
.badge {
    font-weight: 500;
}

.badge.bg-secondary {
    background-color: #6c757d !important;
}

.badge.bg-info {
    background-color: #17a2b8 !important;
}

/* 加载指示器 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.spinner-border {
    width: 3rem;
    height: 3rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container-fluid {
        padding: 0 10px;
    }
    
    .card {
        margin-bottom: 15px;
    }
    
    .skill-item {
        margin-bottom: 8px;
    }
    
    .entity-icon {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }
    
    .stat-item {
        padding: 6px;
    }
    
    .stat-value {
        font-size: 1rem;
    }
}

/* 动画效果 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.card {
    animation: fadeIn 0.3s ease;
}

.skill-item {
    animation: fadeIn 0.2s ease;
}

/* 滚动条样式 */
.card-body {
    scrollbar-width: thin;
    scrollbar-color: #dee2e6 #f8f9fa;
}

.card-body::-webkit-scrollbar {
    width: 6px;
}

.card-body::-webkit-scrollbar-track {
    background: #f8f9fa;
}

.card-body::-webkit-scrollbar-thumb {
    background: #dee2e6;
    border-radius: 3px;
}

.card-body::-webkit-scrollbar-thumb:hover {
    background: #adb5bd;
}

/* 特殊状态样式 */
.skill-config-item.disabled {
    opacity: 0.6;
    background-color: #f8f9fa;
}

.skill-config-item.disabled h6 {
    color: #6c757d;
}

/* 工具提示样式 */
.tooltip {
    font-size: 0.8rem;
}

.tooltip-inner {
    background-color: #495057;
    color: white;
    border-radius: 4px;
}

/* 输入验证样式 */
.is-invalid {
    border-color: #dc3545;
}

.is-valid {
    border-color: #28a745;
}

.invalid-feedback {
    color: #dc3545;
    font-size: 0.8rem;
}

.valid-feedback {
    color: #28a745;
    font-size: 0.8rem;
}
