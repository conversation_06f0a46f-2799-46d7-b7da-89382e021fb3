package org.Ver_zhzh.deathZombieV4.listeners;

import org.Ver_zhzh.deathZombieV4.DeathZombieV4;
import org.Ver_zhzh.deathZombieV4.game.EquipmentConstants;
import org.Ver_zhzh.deathZombieV4.game.GameSessionManager;
import org.Ver_zhzh.deathZombieV4.utils.PlayerInteractionManager;
import org.Ver_zhzh.deathZombieV4.enums.PlayerStatus;
import org.bukkit.ChatColor;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.player.PlayerItemConsumeEvent;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.scheduler.BukkitRunnable;
import org.bukkit.persistence.PersistentDataType;
import org.bukkit.NamespacedKey;

/**
 * 处理物品消耗事件的监听器 主要用于在物品被消耗后将槽位重置为空物品槽
 */
public class ItemConsumeListener implements Listener {

    private final DeathZombieV4 plugin;
    private final PlayerInteractionManager playerManager;
    private final GameSessionManager gameSessionManager;

    /**
     * 构造函数
     *
     * @param plugin 插件实例
     */
    public ItemConsumeListener(DeathZombieV4 plugin) {
        this.plugin = plugin;
        this.playerManager = plugin.getPlayerInteractionManager();
        this.gameSessionManager = plugin.getGameSessionManager();
    }

    /**
     * 处理物品消耗事件
     *
     * @param event 物品消耗事件
     */
    @EventHandler(priority = EventPriority.NORMAL)
    public void onItemConsume(PlayerItemConsumeEvent event) {
        Player player = event.getPlayer();

        // 检查玩家是否在游戏中
        if (!playerManager.isPlayerInStatus(player, PlayerStatus.IN_GAME)) {
            return;
        }

        // 获取玩家所在的游戏
        String gameName = gameSessionManager.getPlayerGame(player);
        if (gameName == null) {
            return;
        }

        // 获取消耗的物品
        ItemStack consumedItem = event.getItem();

        // 获取物品所在的槽位
        int slot = getItemSlot(player, consumedItem);

        // 如果槽位是物品槽位（6-8），则在下一个tick将其替换为空物品槽
        if (slot >= 6 && slot <= 8) {
            // 检查是否是药水类物品
            boolean isPotion = consumedItem.getType() == Material.POTION
                    || consumedItem.getType() == Material.SPLASH_POTION
                    || consumedItem.getType() == Material.LINGERING_POTION;

            // 在下一个tick中处理物品槽重置，确保在物品消耗后执行
            // 如果是药水，我们需要在事件完成后移除玻璃瓶
            if (isPotion) {
                // 注意：我们不取消事件，让药水效果正常应用
                // 但我们会在下一个tick中移除生成的玻璃瓶并放置空物品槽
                if (plugin.getConfig().getBoolean("debug", false)) {
                    plugin.getLogger().info("玩家 " + player.getName() + " 使用了药水，将在下一个tick移除玻璃瓶");
                }
            }

            // 使用延迟任务，确保在物品消耗后执行
            new BukkitRunnable() {
                @Override
                public void run() {
                    // 检查当前槽位的物品
                    ItemStack currentItem = player.getInventory().getItem(slot);

                    // 如果是药水，当前槽位可能是玻璃瓶或空
                    if (isPotion) {
                        // 检查是否是玻璃瓶
                        if (currentItem != null && currentItem.getType() == Material.GLASS_BOTTLE) {
                            if (plugin.getConfig().getBoolean("debug", false)) {
                                plugin.getLogger().info("玩家 " + player.getName() + " 使用药水后，移除玻璃瓶");
                            }
                        }
                    }

                    // 创建空物品槽
                    ItemStack emptySlot = createEmptyItemSlot();

                    // 将槽位设置为空物品槽
                    player.getInventory().setItem(slot, emptySlot);

                    // 更新玩家物品栏
                    player.updateInventory();

                    if (plugin.getConfig().getBoolean("debug", false)) {
                        plugin.getLogger().info("玩家 " + player.getName() + " 使用了物品，已将槽位 " + slot + " 重置为空物品槽");
                    }
                }
            }.runTaskLater(plugin, 1L); // 延迟1个tick执行
        }
    }

    /**
     * 获取物品在玩家物品栏中的槽位
     *
     * @param player 玩家
     * @param item 物品
     * @return 槽位索引，如果未找到则返回-1
     */
    private int getItemSlot(Player player, ItemStack item) {
        ItemStack[] inventory = player.getInventory().getContents();
        for (int i = 0; i < inventory.length; i++) {
            if (inventory[i] != null && inventory[i].equals(item)) {
                return i;
            }
        }
        return -1;
    }

    /**
     * 创建空物品槽物品
     *
     * @return 空物品槽物品
     */
    private ItemStack createEmptyItemSlot() {
        ItemStack emptySlot = new ItemStack(Material.GUNPOWDER);
        ItemMeta meta = emptySlot.getItemMeta();
        meta.setDisplayName(ChatColor.GRAY + "空物品槽");

        // 使用PersistentDataContainer替代已弃用的setLocalizedName
        NamespacedKey key = new NamespacedKey(plugin, "empty_item_slot");
        meta.getPersistentDataContainer().set(key, PersistentDataType.STRING, EquipmentConstants.EMPTY_ITEM_SLOT);

        emptySlot.setItemMeta(meta);
        return emptySlot;
    }
}
