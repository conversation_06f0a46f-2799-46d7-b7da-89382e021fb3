package org.Ver_zhzh.deathZombieV4.listeners;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

import org.Ver_zhzh.deathZombieV4.DeathZombieV4;
import org.Ver_zhzh.deathZombieV4.game.GameSessionManager;
import org.Ver_zhzh.deathZombieV4.game.GameManager;
import org.Ver_zhzh.deathZombieV4.utils.PlayerInteractionManager;
import org.Ver_zhzh.deathZombieV4.enums.PlayerStatus;
import org.Ver_zhzh.deathZombieV4.utils.ShootPluginHelper;
import org.Ver_zhzh.deathZombieV4.utils.MessageManager;

import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.Location;
import org.bukkit.Particle;
import org.bukkit.Sound;
import org.bukkit.World;
import org.bukkit.potion.PotionEffect;
import org.bukkit.potion.PotionEffectType;
import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.entity.Entity;
import org.bukkit.entity.EntityType;
import org.bukkit.entity.LivingEntity;
import org.bukkit.entity.Player;
import org.bukkit.entity.Zombie;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.entity.EntityDamageByEntityEvent;
import org.bukkit.event.entity.EntityDamageEvent;
import org.bukkit.event.entity.EntityDeathEvent;
import org.bukkit.event.entity.PlayerDeathEvent;
import org.bukkit.inventory.ItemStack;
import org.bukkit.metadata.FixedMetadataValue;
import org.bukkit.metadata.MetadataValue;

/**
 * 处理玩家攻击并击杀游戏实体获得金钱的监听器
 */
public class ZombieAttackListener implements Listener {

    private final DeathZombieV4 plugin;
    private final PlayerInteractionManager playerManager;
    private final GameSessionManager gameSessionManager;
    private final GameManager gameManager;
    private final ShootPluginHelper shootPluginHelper;
    private final MessageManager messageManager;
    private PlayerDeathReviveListener deathReviveListener;

    // 计分板管理器引用
    private final org.Ver_zhzh.deathZombieV4.utils.ScoreboardManager scoreboardManager;

    // 记录玩家对实体造成的最后伤害，用于判断击杀奖励
    private final Map<UUID, Map<UUID, Double>> playerEntityDamage = new HashMap<>();

    public ZombieAttackListener(DeathZombieV4 plugin) {
        this.plugin = plugin;
        this.playerManager = plugin.getPlayerInteractionManager();
        this.gameSessionManager = plugin.getGameSessionManager();
        this.gameManager = plugin.getGameManager();
        this.shootPluginHelper = plugin.getShootPluginHelper();
        this.messageManager = plugin.getMessageManager();
        this.scoreboardManager = plugin.getScoreboardManager();
    }

    /**
     * 更新计分板上的僵尸剩余数量
     *
     * @param gameName 游戏名称
     */
    private void updateZombieCountOnScoreboard(String gameName) {
        // 始终输出调试日志，不受debug配置限制
        plugin.getLogger().info("准备更新游戏 " + gameName + " 的计分板僵尸数量");

        try {
            // 检查ScoreboardManager是否已初始化
            if (scoreboardManager == null) {
                plugin.getLogger().warning("无法更新僵尸数量: ScoreboardManager为空");
                return;
            }

            // 获取当前回合
            int currentRound = scoreboardManager.getCurrentRound(gameName);
            plugin.getLogger().info("当前回合: " + currentRound);

            // 获取剩余僵尸数量
            int remainingZombies = scoreboardManager.getRemainingZombiesInRound(gameName);
            plugin.getLogger().info("当前剩余僵尸数量: " + remainingZombies);

            // 获取总僵尸数量，确保我们有正确的总数
            int totalZombies = scoreboardManager.getTotalZombiesForRound(gameName, currentRound);
            plugin.getLogger().info("当前回合总僵尸数量: " + totalZombies);

            // 减少一个僵尸数量
            if (remainingZombies > 0) {
                // 更新计分板上的剩余僵尸数量
                int newCount = remainingZombies - 1;
                plugin.getLogger().info("新的剩余僵尸数量将为: " + newCount);

                // 直接设置新的僵尸数量，并强制更新计分板
                plugin.getLogger().info("开始调用scoreboardManager.setRemainingZombiesInRound()");
                scoreboardManager.setRemainingZombiesInRound(gameName, newCount);
                plugin.getLogger().info("已调用scoreboardManager.setRemainingZombiesInRound()");

                // 强制更新所有玩家的计分板显示
                plugin.getLogger().info("开始调用scoreboardManager.updateZombieCountDisplay()");
                scoreboardManager.updateZombieCountDisplay(gameName);
                plugin.getLogger().info("已调用scoreboardManager.updateZombieCountDisplay()");

                // 如果剩余僵尸数量为0，触发回合检查
                if (newCount <= 0) {
                    plugin.getLogger().info("剩余僵尸数量为0，安排延迟任务检查回合完成情况");
                    // 延迟一秒检查，确保所有僵尸都已经被清理
                    Bukkit.getScheduler().runTaskLater(plugin, () -> {
                        plugin.getLogger().info("开始执行回合完成检查");
                        // 触发僵尸生成管理器的回合检查
                        plugin.getZombieSpawnManager().checkRoundCompletion(gameName);
                        plugin.getLogger().info("已执行回合完成检查");
                    }, 20L); // 20 ticks = 1秒
                }
            } else {
                // 如果剩余僵尸数量已经为0或负数，强制设置为0并更新计分板
                plugin.getLogger().warning("当前剩余僵尸数量已经为0或负数，强制设置为0");
                scoreboardManager.setRemainingZombiesInRound(gameName, 0);
                scoreboardManager.updateZombieCountDisplay(gameName);

                // 检查是否需要触发回合完成
                plugin.getLogger().info("剩余僵尸数量为0，安排延迟任务检查回合完成情况");
                Bukkit.getScheduler().runTaskLater(plugin, () -> {
                    plugin.getLogger().info("开始执行回合完成检查");
                    plugin.getZombieSpawnManager().checkRoundCompletion(gameName);
                    plugin.getLogger().info("已执行回合完成检查");
                }, 20L); // 20 ticks = 1秒
            }
        } catch (Exception e) {
            // 捕获并记录任何异常，防止方法崩溃
            plugin.getLogger().warning("更新僵尸计数时发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 设置死亡复活监听器引用
     *
     * @param listener 死亡复活监听器
     */
    public void setDeathReviveListener(PlayerDeathReviveListener listener) {
        this.deathReviveListener = listener;
    }

    /**
     * 处理玩家攻击实体事件
     *
     * @param event 实体伤害事件
     */
    @EventHandler
    public void onEntityAttacked(EntityDamageByEntityEvent event) {
        // 首先检查是否是玩家攻击变异科学家（优先处理）
        if (isPlayerAttackingScientist(event)) {
            handlePlayerAttackScientist(event);
        }

        // 检查是否是玩家攻击生物
        if (!(event.getDamager() instanceof Player) || !(event.getEntity() instanceof LivingEntity)) {
            // 检查是否是变异博士攻击玩家（最高优先级）
            if (isMutantDoctorZombieAttackingPlayer(event)) {
                handleMutantDoctorZombieAttackPlayer(event);
            }
            // 检查是否是终极毁灭僵尸攻击玩家
            else if (isUltimateDestructionZombieAttackingPlayer(event)) {
                handleUltimateDestructionZombieAttackPlayer(event);
            }
            // 检查是否是僵尸攻击玩家
            else if (isZombieAttackingPlayer(event)) {
                handleZombieAttackPlayer(event);
            }
            // 检查是否是毒箭攻击玩家
            else if (isPoisonArrowAttackingPlayer(event)) {
                handlePoisonArrowAttackPlayer(event);
            }
            return;
        }

        Player player = (Player) event.getDamager();
        LivingEntity entity = (LivingEntity) event.getEntity();
        double damage = event.getFinalDamage();

        // 检查玩家是否在游戏中
        if (!playerManager.isPlayerInStatus(player, PlayerStatus.IN_GAME)) {
            return;
        }

        // 检查实体是否属于游戏
        if (!isGameEntity(entity)) {
            return;
        }

        // 获取玩家所在的游戏
        String gameName = gameSessionManager.getPlayerGame(player);
        if (gameName == null) {
            return;
        }

        // 记录伤害值，用于计算击杀奖励
        recordDamage(player, entity, damage);
    }

    /**
     * 处理实体死亡事件
     *
     * @param event 实体死亡事件
     */
    @EventHandler
    public void onEntityKilled(EntityDeathEvent event) {
        LivingEntity entity = event.getEntity();

        // 跳过玩家死亡
        if (entity instanceof Player) {
            return;
        }

        // 检查实体是否属于游戏
        if (!isGameEntity(entity)) {
            return;
        }

        // 清除游戏实体的掉落物（双重保障）
        event.getDrops().clear();
        event.setDroppedExp(0);

        if (plugin.getConfig().getBoolean("debug", false)) {
            plugin.getLogger().info("ZombieAttackListener: 已清除游戏实体 " + entity.getType() +
                " (名称: " + (entity.getCustomName() != null ? entity.getCustomName() : "无") + ") 的所有掉落物");
        }

        Player killer = entity.getKiller();

        // 如果没有直接的击杀者，检查伤害记录找出最大伤害贡献者
        if (killer == null) {
            killer = getTopDamageDealer(entity);
        }

        // 确定游戏名称
        String gameName = null;

        if (killer != null && playerManager.isPlayerInStatus(killer, PlayerStatus.IN_GAME)) {
            // 如果有击杀者且在游戏中，使用击杀者的游戏
            gameName = gameSessionManager.getPlayerGame(killer);
        } else {
            // 如果没有击杀者或击杀者不在游戏中，尝试从实体元数据获取游戏名称
            gameName = getGameNameFromEntity(entity);
        }

        if (gameName == null) {
            plugin.getLogger().warning("无法确定实体 " + entity.getType() + " 所属的游戏，跳过处理");
            return;
        }

        // 使用批量死亡检测器检查是否应该记录这次死亡
        boolean shouldLog = plugin.getBatchDeathDetector().recordDeath(entity, gameName);

        // 记录僵尸意外死亡（只有在批量死亡检测器允许时才记录）
        if (killer == null && shouldLog) {
            plugin.getLogger().info("检测到僵尸意外死亡：" + entity.getType() +
                " (名称: " + (entity.getCustomName() != null ? entity.getCustomName() : "无") +
                ") 在游戏 " + gameName + " 中");
        }

        // 添加调试日志
        plugin.getLogger().info("ZombieAttackListener: 开始处理实体死亡事件 - 游戏: " + gameName +
            ", 击杀者: " + (killer != null ? killer.getName() : "无") +
            ", 实体: " + entity.getType() +
            ", 枪支特定奖励: " + plugin.getConfig().getBoolean("game.money.weapon_specific_rewards.enabled", false));

        // 先更新计分板上的僵尸剩余数量（确保这个关键功能不受奖励计算影响）
        plugin.getLogger().info("ZombieAttackListener: 开始更新计分板僵尸数量");
        try {
            updateZombieCountOnScoreboard(gameName);
            plugin.getLogger().info("ZombieAttackListener: 成功更新计分板僵尸数量");
        } catch (Exception e) {
            plugin.getLogger().warning("ZombieAttackListener: 更新计分板僵尸数量时发生异常: " + e.getMessage());
            e.printStackTrace();
        }

        // 然后处理击杀奖励（即使这里出错也不会影响僵尸数量更新）
        try {
            // 给予击杀奖励（仅当有击杀者时）
            if (killer != null && playerManager.isPlayerInStatus(killer, PlayerStatus.IN_GAME)) {
                // 检查金钱获取方式，只有在kill模式下才给予击杀奖励
                String moneyWay = plugin.getConfig().getString("game.money.getting_money_way", "kill");
                plugin.getLogger().info("ZombieAttackListener: 金钱获取方式: " + moneyWay);

                if ("kill".equalsIgnoreCase(moneyWay)) {
                    // 计算奖励金额
                    plugin.getLogger().info("ZombieAttackListener: 开始计算击杀奖励");
                    double reward = calculateReward(killer, entity);
                    plugin.getLogger().info("ZombieAttackListener: 计算得到击杀奖励: " + reward);

                    // 给予玩家金钱奖励
                    if (reward > 0) {
                        String entityType = getEntityType(entity);
                        plugin.getLogger().info("ZombieAttackListener: 给予玩家奖励 - 实体类型: " + entityType + ", 奖励: " + reward);
                        rewardPlayer(killer, reward, entityType);
                    }
                } else if ("shoot".equalsIgnoreCase(moneyWay)) {
                    // 在shoot模式下，击杀仍然可以给予少量奖励（如果配置了的话）
                    plugin.getLogger().info("ZombieAttackListener: 开始计算射击模式下的击杀奖励");
                    double killReward = calculateKillRewardInShootMode(killer, entity);
                    plugin.getLogger().info("ZombieAttackListener: 计算得到射击模式击杀奖励: " + killReward);

                    if (killReward > 0) {
                        String entityType = getEntityType(entity);
                        plugin.getLogger().info("ZombieAttackListener: 给予玩家射击模式击杀奖励 - 实体类型: " + entityType + ", 奖励: " + killReward);
                        rewardPlayerKillInShootMode(killer, killReward, entityType);
                    }
                }
            }
        } catch (Exception e) {
            plugin.getLogger().warning("ZombieAttackListener: 处理击杀奖励时发生异常，但僵尸数量已正确更新: " + e.getMessage());
            e.printStackTrace();
        }

        // 清除伤害记录
        clearDamageRecord(entity);
        plugin.getLogger().info("ZombieAttackListener: 实体死亡事件处理完成");
    }

    /**
     * 从实体元数据获取游戏名称
     *
     * @param entity 实体
     * @return 游戏名称，如果无法确定则返回null
     */
    private String getGameNameFromEntity(Entity entity) {
        // 检查实体是否有游戏会话标记
        if (entity.hasMetadata("gameSession")) {
            String entityGameName = entity.getMetadata("gameSession").get(0).asString();
            if (entityGameName != null && !entityGameName.isEmpty()) {
                return entityGameName;
            }
        }

        // 检查实体名称是否包含游戏标识
        if (entity.getCustomName() != null) {
            String customName = entity.getCustomName();
            if (customName.startsWith("游戏_")) {
                // 解析游戏名称，格式：游戏_<游戏名>_<其他信息>
                String[] parts = customName.split("_");
                if (parts.length >= 2) {
                    return parts[1];
                }
            }
        }

        // 尝试从所有正在运行的游戏中查找
        for (String gameName : plugin.getGameSessionManager().getRunningGames()) {
            // 检查实体是否在该游戏的世界中
            // 这是一个备用方法，不是很精确，但可以作为最后的尝试
            if (plugin.getGameSessionManager().getGameState(gameName) ==
                org.Ver_zhzh.deathZombieV4.game.GameSessionManager.GameState.RUNNING) {
                // 如果只有一个正在运行的游戏，假设实体属于该游戏
                // 这不是完美的解决方案，但可以处理大多数情况
                return gameName;
            }
        }

        return null;
    }

    /**
     * 检查实体是否属于游戏
     *
     * @param entity 实体
     * @return 是否是游戏实体
     */
    private boolean isGameEntity(Entity entity) {
        // 跳过玩家
        if (entity instanceof Player) {
            return false;
        }

        // 检查是否是生物实体
        if (!(entity instanceof LivingEntity)) {
            return false;
        }

        // 检查是否有idcZombieEntity标记，如果有则视为游戏实体
        if (entity.hasMetadata("idcZombieEntity")) {
            return true;
        }

        // 检查是否有not_round_zombie标记，如果有则不计入回合生成的僵尸
        if (entity.hasMetadata("not_round_zombie")) {
            return false;
        }

        // 检查是否有游戏标记元数据
        if (entity.hasMetadata("gameEntity")) {
            return true;
        }

        // 检查是否是自定义NPC
        if (entity.hasMetadata("NPC")) {
            return true;
        }

        // 检查是否是自定义僵尸类型
        if (entity.hasMetadata("customZombieType")) {
            for (MetadataValue meta : entity.getMetadata("customZombieType")) {
                if (meta.getOwningPlugin().equals(plugin)) {
                    String value = meta.asString();
                    // 检查是否是idc类型的实体
                    if (value.contains("idc")) {
                        // 添加idcZombieEntity标记，确保后续处理中被识别为僵尸
                        entity.setMetadata("idcZombieEntity", new FixedMetadataValue(plugin, true));
                        return true;
                    }
                }
            }
            return true;
        }

        // 额外检查：实体名称是否有特定前缀或包含特定字符串
        if (entity.getCustomName() != null) {
            String customName = entity.getCustomName();
            // 检查是否是游戏实体的命名格式
            if (customName.startsWith("游戏_")) {
                return true;
            }

            // 检查是否包含id或idc标识
            if (customName.contains("id") || customName.contains("idc")) {
                // 如果是idc类型，添加idcZombieEntity标记
                if (customName.contains("idc")) {
                    entity.setMetadata("idcZombieEntity", new FixedMetadataValue(plugin, true));
                }
                return true;
            }

            // 检查是否包含zombie或僵尸标识
            if (customName.contains("zombie") || customName.contains("僵尸")) {
                return true;
            }
        }

        // 检查实体类型
        EntityType type = entity.getType();
        if (type == EntityType.ZOMBIE) {
            return true;
        }

        return false;
    }

    /**
     * 获取实体标识符（优先返回具体ID，否则返回类型）
     *
     * @param entity 实体
     * @return 实体标识符
     */
    private String getEntityType(Entity entity) {
        boolean debugMode = plugin.getConfig().getBoolean("debug", false);

        if (debugMode) {
            plugin.getLogger().info("ZombieAttackListener.getEntityType: 开始识别实体类型");
            plugin.getLogger().info("实体类型: " + entity.getType());
            plugin.getLogger().info("实体自定义名称: " + entity.getCustomName());
        }

        // 检查是否是自定义NPC
        if (entity.hasMetadata("NPC")) {
            if (debugMode) {
                plugin.getLogger().info("识别为NPC实体");
            }
            return "npc";
        }

        // 检查是否有customZombieType元数据（优先返回具体ID）
        if (entity.hasMetadata("customZombieType")) {
            for (MetadataValue meta : entity.getMetadata("customZombieType")) {
                if (meta.getOwningPlugin().equals(plugin)) {
                    String value = meta.asString();
                    if (debugMode) {
                        plugin.getLogger().info("从customZombieType元数据获取类型: " + value);
                    }

                    // 清理可能的前缀，确保返回标准格式
                    String cleanedType = cleanEntityTypeId(value);
                    if (debugMode) {
                        plugin.getLogger().info("清理后的类型: " + cleanedType);
                    }
                    return cleanedType;
                }
            }
        }

        // 检查自定义名称中是否包含ID信息
        if (entity.getCustomName() != null) {
            String name = entity.getCustomName();
            if (debugMode) {
                plugin.getLogger().info("分析自定义名称: " + name);
            }

            // 尝试从名称中提取ID
            if (name.contains("游戏_")) {
                String[] parts = name.split("_");
                if (parts.length >= 4) {
                    // 格式：游戏_<游戏名>_<类型>_<ID>
                    String extractedId = parts[3];
                    if (debugMode) {
                        plugin.getLogger().info("从名称中提取的ID: " + extractedId);
                    }
                    return cleanEntityTypeId(extractedId);
                }
            }

            // 根据名称内容判断类型
            if (name.contains("变异") || name.contains("mutant")) {
                if (debugMode) {
                    plugin.getLogger().info("根据名称识别为变异类型");
                }
                return "mutant";
            } else if (name.contains("特殊") || name.contains("special")) {
                if (debugMode) {
                    plugin.getLogger().info("根据名称识别为特殊类型");
                }
                return "special";
            }
        }

        // 检查是否是idc类型的实体（通过idcZombieEntity标记）
        if (entity.hasMetadata("idcZombieEntity")) {
            if (debugMode) {
                plugin.getLogger().info("识别为idc类型实体");
            }
            return "zombie"; // 将idc类型实体归类为zombie，这样它们会被当做僵尸处理
        }

        // 检查是否是特殊变异实体
        if (entity.hasMetadata("mutantEntity")) {
            if (debugMode) {
                plugin.getLogger().info("识别为变异实体");
            }
            return "mutant";
        }

        // 默认按实体类型分类
        EntityType type = entity.getType();
        if (debugMode) {
            plugin.getLogger().info("使用默认实体类型分类: " + type.name());
        }

        if (type == EntityType.ZOMBIE) {
            return "zombie";
        } else if (type == EntityType.SKELETON) {
            return "skeleton";
        } else if (type == EntityType.CREEPER) {
            return "creeper";
        } else {
            return "monster";
        }
    }

    /**
     * 清理实体类型ID，移除不必要的前缀
     *
     * @param rawId 原始ID
     * @return 清理后的ID
     */
    private String cleanEntityTypeId(String rawId) {
        if (rawId == null || rawId.isEmpty()) {
            return "zombie";
        }

        // 移除可能的前缀
        String cleaned = rawId;
        if (cleaned.startsWith("zombie_")) {
            cleaned = cleaned.substring(7); // 移除 "zombie_" 前缀
        } else if (cleaned.startsWith("entity_")) {
            cleaned = cleaned.substring(7); // 移除 "entity_" 前缀
        } else if (cleaned.startsWith("npc_")) {
            cleaned = cleaned.substring(4); // 移除 "npc_" 前缀
        }

        // 确保ID格式正确（id1, idc2, idn3等）
        if (cleaned.matches("\\d+")) {
            // 如果只是数字，默认添加id前缀
            cleaned = "id" + cleaned;
        }

        return cleaned;
    }

    /**
     * 记录玩家对实体的伤害
     *
     * @param player 玩家
     * @param entity 实体
     * @param damage 伤害值
     */
    private void recordDamage(Player player, Entity entity, double damage) {
        UUID entityId = entity.getUniqueId();
        UUID playerId = player.getUniqueId();

        // 初始化伤害记录
        if (!playerEntityDamage.containsKey(entityId)) {
            playerEntityDamage.put(entityId, new HashMap<>());
        }

        Map<UUID, Double> damageMap = playerEntityDamage.get(entityId);
        double currentDamage = damageMap.getOrDefault(playerId, 0.0);
        damageMap.put(playerId, currentDamage + damage);
    }

    /**
     * 获取对实体造成最大伤害的玩家
     *
     * @param entity 实体
     * @return 造成最大伤害的玩家，如果没有则返回null
     */
    private Player getTopDamageDealer(Entity entity) {
        UUID entityId = entity.getUniqueId();
        if (!playerEntityDamage.containsKey(entityId)) {
            return null;
        }

        Map<UUID, Double> damageMap = playerEntityDamage.get(entityId);
        UUID topDamageDealerId = null;
        double maxDamage = 0;

        for (Map.Entry<UUID, Double> entry : damageMap.entrySet()) {
            if (entry.getValue() > maxDamage) {
                maxDamage = entry.getValue();
                topDamageDealerId = entry.getKey();
            }
        }

        if (topDamageDealerId != null) {
            return plugin.getServer().getPlayer(topDamageDealerId);
        }
        return null;
    }

    /**
     * 计算击杀奖励金额
     *
     * @param player 玩家
     * @param entity 被击杀的实体
     * @return 奖励金额
     */
    private double calculateReward(Player player, Entity entity) {
        // 获取配置中的奖励金额
        FileConfiguration config = plugin.getConfig();
        double baseReward = 0;

        String entityType = getEntityType(entity);

        // 检查是否启用枪支特定奖励
        boolean weaponSpecificEnabled = config.getBoolean("game.money.weapon_specific_rewards.enabled", false);

        if (weaponSpecificEnabled) {
            // 获取玩家当前使用的枪支ID
            String gunId = shootPluginHelper.getPlayerCurrentGunId(player);

            if (gunId != null) {
                // 使用枪支特定的奖励配置
                baseReward = getWeaponSpecificKillReward(gunId, entityType);

                if (plugin.getConfig().getBoolean("debug", false)) {
                    plugin.getLogger().info("ZombieAttackListener: 使用枪支特定击杀奖励 - 枪支ID: " + gunId +
                        ", 实体类型: " + entityType + ", 基础奖励: " + baseReward);
                }
            } else {
                // 如果无法获取枪支ID，使用全局配置
                baseReward = getGlobalKillReward(entityType);

                if (plugin.getConfig().getBoolean("debug", false)) {
                    plugin.getLogger().info("ZombieAttackListener: 无法获取枪支ID，使用全局击杀奖励配置");
                }
            }
        } else {
            // 使用全局配置
            baseReward = getGlobalKillReward(entityType);
        }

        // 根据武器类型计算额外奖励
        double bonusMultiplier = 1.0;
        ItemStack weapon = player.getInventory().getItemInMainHand();

        if (weapon != null && !weapon.getType().isAir()) {
            String weaponType = weapon.getType().toString();

            // 近战武器额外奖励
            if (weaponType.contains("SWORD") || weaponType.contains("AXE")) {
                bonusMultiplier += config.getDouble("game.money.melee_weapon_bonus", 0.3);
            } // 远程武器额外奖励
            else if (weaponType.contains("BOW") || weaponType.contains("CROSSBOW")) {
                bonusMultiplier += config.getDouble("game.money.ranged_weapon_bonus", 0.1);
            }
        }

        // 计算最终奖励
        double finalReward = baseReward * bonusMultiplier;

        // 应用最小/最大限制
        double minReward = config.getDouble("game.money.min_reward", 5);
        double maxReward = config.getDouble("game.money.max_reward", 100);

        finalReward = Math.max(minReward, Math.min(finalReward, maxReward));

        // 随机波动 ±10%
        double randomFactor = 0.9 + Math.random() * 0.2; // 0.9 到 1.1
        finalReward *= randomFactor;

        // 四舍五入到整数
        return Math.round(finalReward);
    }

    /**
     * 在射击模式下计算击杀奖励金额（通常为0或很少）
     *
     * @param player 玩家
     * @param entity 被击杀的实体
     * @return 奖励金额
     */
    private double calculateKillRewardInShootMode(Player player, Entity entity) {
        // 获取配置中的击杀奖励金额（在射击模式下通常设置为0）
        FileConfiguration config = plugin.getConfig();
        double baseReward = 0;

        String entityType = getEntityType(entity);

        // 检查是否启用枪支特定奖励
        boolean weaponSpecificEnabled = config.getBoolean("game.money.weapon_specific_rewards.enabled", false);

        if (weaponSpecificEnabled) {
            // 获取玩家当前使用的枪支ID
            String gunId = shootPluginHelper.getPlayerCurrentGunId(player);

            if (gunId != null) {
                // 使用枪支特定的奖励配置（在shoot模式下的击杀奖励通常为0）
                baseReward = getWeaponSpecificKillReward(gunId, entityType);

                if (plugin.getConfig().getBoolean("debug", false)) {
                    plugin.getLogger().info("ZombieAttackListener: 射击模式下使用枪支特定击杀奖励 - 枪支ID: " + gunId +
                        ", 实体类型: " + entityType + ", 基础奖励: " + baseReward);
                }
            } else {
                // 如果无法获取枪支ID，使用全局配置
                baseReward = getGlobalKillRewardInShootMode(entityType);

                if (plugin.getConfig().getBoolean("debug", false)) {
                    plugin.getLogger().info("ZombieAttackListener: 射击模式下无法获取枪支ID，使用全局击杀奖励配置");
                }
            }
        } else {
            // 使用全局配置
            baseReward = getGlobalKillRewardInShootMode(entityType);
        }

        // 四舍五入到整数
        return Math.round(baseReward);
    }

    /**
     * 在射击模式下给玩家击杀奖励金钱
     *
     * @param player 玩家
     * @param amount 金额
     * @param entityType 实体类型
     */
    private void rewardPlayerKillInShootMode(Player player, double amount, String entityType) {
        if (amount <= 0) {
            return;
        }

        // 使用ShootPluginHelper给玩家加钱
        shootPluginHelper.addPlayerMoney(player, amount);

        // 记录统计数据
        String gameName = gameSessionManager.getPlayerGame(player);
        if (gameName != null) {
            // 记录单局游戏统计数据
            plugin.getPlayerInteractionManager().addPlayerZombieKills(player, gameName, 1);
            plugin.getPlayerInteractionManager().addPlayerMoneyEarned(player, gameName, amount);

            // 记录累计统计数据
            plugin.getPlayerStatisticsManager().addZombieKills(player, 1);
            plugin.getPlayerStatisticsManager().updateHighestMoney(player, shootPluginHelper.getPlayerMoney(player));
        }

        // 使用MessageManager发送消息
        messageManager.sendKillMessage(player, entityType, (int) amount, true);

        // 调试信息
        if (plugin.getConfig().getBoolean("debug", false)) {
            String entityTypeText = messageManager.getEntityDisplayName(entityType);
            plugin.getLogger().info("ZombieAttackListener: 玩家 " + player.getName() +
                " 在射击模式下击杀 " + entityTypeText + " 获得 " + amount + " 金钱");
        }
    }

    /**
     * 给玩家奖励金钱
     *
     * @param player 玩家
     * @param amount 金额
     * @param entityType 实体类型
     */
    private void rewardPlayer(Player player, double amount, String entityType) {
        // 使用ShootPluginHelper给玩家加钱
        shootPluginHelper.addPlayerMoney(player, amount);

        // 记录统计数据
        String gameName = gameSessionManager.getPlayerGame(player);
        if (gameName != null) {
            // 记录单局游戏统计数据
            plugin.getPlayerInteractionManager().addPlayerZombieKills(player, gameName, 1);
            plugin.getPlayerInteractionManager().addPlayerMoneyEarned(player, gameName, amount);

            // 记录累计统计数据
            plugin.getPlayerStatisticsManager().addZombieKills(player, 1);
            plugin.getPlayerStatisticsManager().updateHighestMoney(player, shootPluginHelper.getPlayerMoney(player));
        }

        // 使用MessageManager发送消息
        messageManager.sendKillMessage(player, entityType, (int) amount, false);

        // 检查玩家是否开启了击杀title显示
        if (plugin.getPlayerDisplaySettingsManager().getPlayerSettings(player).isKillTitle()) {
            // 构建Title消息
            String entityTypeText = messageManager.getEntityDisplayName(entityType);
            String title = ChatColor.RED + "击杀" + entityTypeText + "!";
            String subtitle = ChatColor.GOLD + "+" + ChatColor.GREEN + (int) amount + ChatColor.GOLD + " 金钱";

            // 发送Title通知（使用原生方法）
            try {
                player.sendTitle(title, subtitle, 5, 20, 5);
                plugin.getLogger().info("发送了击杀奖励通知");
            } catch (Exception e) {
                plugin.getLogger().warning("发送击杀Title失败: " + e.getMessage());
                // 如果标题发送失败，不做额外处理，因为消息已经发送
            }
        }
    }

    /**
     * 获取全局击杀奖励配置
     *
     * @param entityType 实体类型
     * @return 基础奖励金额
     */
    private double getGlobalKillReward(String entityType) {
        FileConfiguration config = plugin.getConfig();

        if (entityType.equals("npc")) {
            return config.getDouble("game.money.npc_kill", 50);
        } else if (entityType.equals("mutant")) {
            return config.getDouble("game.money.mutant_kill", 30);
        } else if (entityType.equals("zombie")) {
            return config.getDouble("game.money.zombie_kill", 10);
        } else if (entityType.startsWith("id")) { // 自定义僵尸ID，归类为zombie
            return config.getDouble("game.money.zombie_kill", 10);
        } else {
            return config.getDouble("game.money.monster_kill", 15);
        }
    }

    /**
     * 获取全局射击模式下的击杀奖励配置
     *
     * @param entityType 实体类型
     * @return 基础奖励金额
     */
    private double getGlobalKillRewardInShootMode(String entityType) {
        FileConfiguration config = plugin.getConfig();

        if (entityType.equals("npc")) {
            return config.getDouble("game.money.npc_kill_in_shoot_mode", 0);
        } else if (entityType.equals("mutant")) {
            return config.getDouble("game.money.mutant_kill_in_shoot_mode", 0);
        } else if (entityType.equals("zombie")) {
            return config.getDouble("game.money.zombie_kill_in_shoot_mode", 0);
        } else if (entityType.startsWith("id")) { // 自定义僵尸ID，归类为zombie
            return config.getDouble("game.money.zombie_kill_in_shoot_mode", 0);
        } else {
            return config.getDouble("game.money.monster_kill_in_shoot_mode", 0);
        }
    }

    /**
     * 获取枪支特定的击杀奖励配置
     *
     * @param gunId 枪支ID
     * @param entityType 实体类型
     * @return 基础奖励金额
     */
    private double getWeaponSpecificKillReward(String gunId, String entityType) {
        FileConfiguration config = plugin.getConfig();
        String basePath = "game.money.weapon_specific_rewards.weapons." + gunId + ".kill.";

        if (entityType.equals("npc")) {
            return config.getDouble(basePath + "npc_kill", getGlobalKillReward(entityType));
        } else if (entityType.equals("mutant")) {
            return config.getDouble(basePath + "mutant_kill", getGlobalKillReward(entityType));
        } else if (entityType.equals("zombie")) {
            return config.getDouble(basePath + "zombie_kill", getGlobalKillReward(entityType));
        } else if (entityType.startsWith("id")) { // 自定义僵尸ID，归类为zombie
            return config.getDouble(basePath + "zombie_kill", getGlobalKillReward("zombie"));
        } else {
            return config.getDouble(basePath + "monster_kill", getGlobalKillReward(entityType));
        }
    }

    /**
     * 清除实体的伤害记录
     *
     * @param entity 实体
     */
    private void clearDamageRecord(Entity entity) {
        playerEntityDamage.remove(entity.getUniqueId());
    }

    /**
     * 检查是否是僵尸攻击玩家
     *
     * @param event 实体伤害事件
     * @return 是否是僵尸攻击玩家
     */
    private boolean isZombieAttackingPlayer(EntityDamageByEntityEvent event) {
        // 检查被攻击者是否是玩家
        if (!(event.getEntity() instanceof Player)) {
            return false;
        }

        // 检查攻击者是否是游戏实体（僵尸/变异生物）
        Entity damager = event.getDamager();
        return isGameEntity(damager);
    }

    /**
     * 处理僵尸攻击玩家事件
     *
     * @param event 实体伤害事件
     */
    private void handleZombieAttackPlayer(EntityDamageByEntityEvent event) {
        Player player = (Player) event.getEntity();
        Entity zombie = event.getDamager();

        plugin.getLogger().info("=== 僵尸攻击玩家事件 ===");
        plugin.getLogger().info("玩家: " + player.getName());
        plugin.getLogger().info("攻击者: " + zombie.getType().name() + " (名称: " + (zombie.getCustomName() != null ? zombie.getCustomName() : "无") + ")");

        // 检查是否是剧毒僵尸攻击，如果是则应用毒性效果
        // 注意：剧毒攻击是僵尸的基本能力，不依赖于玩家是否在游戏中
        handlePoisonZombieAttack(zombie, player);

        // 检查玩家是否在游戏中
        if (!playerManager.isPlayerInStatus(player, PlayerStatus.IN_GAME)) {
            plugin.getLogger().info("玩家不在游戏中，跳过游戏相关处理");
            return;
        }

        // 获取玩家所在的游戏
        String gameName = gameSessionManager.getPlayerGame(player);
        if (gameName == null) {
            plugin.getLogger().info("无法获取玩家所在游戏，跳过处理");
            return;
        }

        plugin.getLogger().info("玩家在游戏中: " + gameName);

        // 检查玩家是否会被这次攻击击杀
        if (player.getHealth() <= event.getFinalDamage()) {
            // 取消原始伤害事件，防止玩家真正死亡
            event.setCancelled(true);

            // 如果死亡复活监听器已设置，则处理玩家被僵尸击杀
            if (deathReviveListener != null) {
                deathReviveListener.handlePlayerKilledByZombie(player, zombie);

                // 减少击杀者的金钱（如果有配置）
                double moneyPenalty = plugin.getConfig().getDouble("game.money.player_killed_penalty", 0);
                if (moneyPenalty > 0) {
                    // 获取玩家当前金钱
                    double currentMoney = shootPluginHelper.getPlayerMoney(player);
                    // 确保不会扣成负数
                    double newMoney = Math.max(0, currentMoney - moneyPenalty);
                    // 设置新的金钱数量
                    shootPluginHelper.setPlayerMoney(player, newMoney);

                    // 使用MessageManager发送惩罚消息
                    messageManager.sendPlayerKilledPenaltyMessage(player, (int) moneyPenalty);
                }
            }
        }
    }



    /**
     * 处理僵尸受到伤害事件 - 用于变异博士的技能2和技能8
     *
     * @param event 实体伤害事件
     */
    @EventHandler(priority = EventPriority.MONITOR)
    public void onZombieDamaged(EntityDamageEvent event) {
        // 检查被伤害的实体是否是僵尸
        if (!(event.getEntity() instanceof Zombie)) {
            return;
        }

        // 检查事件是否被取消
        if (event.isCancelled()) {
            return;
        }

        Zombie zombie = (Zombie) event.getEntity();

        // 检查是否是变异博士
        if (zombie.hasMetadata("mutantDoctorZombie")) {
            // 获取MutantDoctorSkillHandler并处理伤害
            if (plugin instanceof org.Ver_zhzh.deathZombieV4.DeathZombieV4) {
                org.Ver_zhzh.deathZombieV4.DeathZombieV4 dzPlugin = (org.Ver_zhzh.deathZombieV4.DeathZombieV4) plugin;
                if (dzPlugin.getDualZombieSystemManager() != null &&
                    dzPlugin.getDualZombieSystemManager().getUserCustomZombie() != null) {

                    // 通过反射获取MutantDoctorSkillHandler
                    try {
                        Object userCustomZombie = dzPlugin.getDualZombieSystemManager().getUserCustomZombie();
                        java.lang.reflect.Field advancedSkillHandlerField =
                            userCustomZombie.getClass().getDeclaredField("advancedSkillHandler");
                        advancedSkillHandlerField.setAccessible(true);
                        Object advancedSkillHandler = advancedSkillHandlerField.get(userCustomZombie);

                        java.lang.reflect.Field mutantDoctorSkillHandlerField =
                            advancedSkillHandler.getClass().getDeclaredField("mutantDoctorSkillHandler");
                        mutantDoctorSkillHandlerField.setAccessible(true);
                        Object mutantDoctorSkillHandler = mutantDoctorSkillHandlerField.get(advancedSkillHandler);

                        // 调用handleDamage方法
                        java.lang.reflect.Method handleDamageMethod =
                            mutantDoctorSkillHandler.getClass().getDeclaredMethod("handleDamage", Zombie.class, double.class);
                        handleDamageMethod.setAccessible(true);
                        handleDamageMethod.invoke(mutantDoctorSkillHandler, zombie, event.getFinalDamage());

                    } catch (Exception e) {
                        plugin.getLogger().warning("处理变异博士伤害事件失败: " + e.getMessage());
                    }
                }
            }
        }

        // 检查是否是变异科学家（原有逻辑）
        if (zombie.hasMetadata("mutantScientistZombie")) {
            // 变异科学家的伤害处理逻辑已经在其他地方实现
            // 这里可以添加额外的处理逻辑如果需要
        }
    }

    /**
     * 处理玩家受到环境伤害事件
     *
     * @param event 实体伤害事件
     */
    @EventHandler
    public void onPlayerDamageByEnvironment(EntityDamageEvent event) {
        // 检查被伤害的实体是否是玩家
        if (!(event.getEntity() instanceof Player)) {
            return;
        }

        // 如果是实体攻击，由其他方法处理
        if (event instanceof EntityDamageByEntityEvent) {
            return;
        }

        Player player = (Player) event.getEntity();

        // 检查玩家是否在游戏中
        if (!playerManager.isPlayerInStatus(player, PlayerStatus.IN_GAME)) {
            return;
        }

        // 获取玩家所在的游戏
        String gameName = gameSessionManager.getPlayerGame(player);
        if (gameName == null) {
            return;
        }

        // 特殊处理虚空伤害
        if (event.getCause() == EntityDamageEvent.DamageCause.VOID) {
            handleVoidDamage(player, event, gameName);
            return;
        }

        // 检查玩家是否会被这次伤害击杀
        if (player.getHealth() <= event.getFinalDamage()) {
            // 取消原始伤害事件，防止玩家真正死亡
            event.setCancelled(true);

            // 记录死亡原因
            plugin.getLogger().info("玩家 " + player.getName() + " 被环境伤害击杀，原因: " + event.getCause().name());

            // 如果死亡复活监听器已设置，则处理玩家死亡
            if (deathReviveListener != null) {
                deathReviveListener.handlePlayerKilledByZombie(player, null);

                // 减少玩家的金钱（如果有配置）
                double moneyPenalty = plugin.getConfig().getDouble("game.money.player_killed_penalty", 0);
                if (moneyPenalty > 0) {
                    // 获取玩家当前金钱
                    double currentMoney = shootPluginHelper.getPlayerMoney(player);
                    // 确保不会扣成负数
                    double newMoney = Math.max(0, currentMoney - moneyPenalty);
                    // 设置新的金钱数量
                    shootPluginHelper.setPlayerMoney(player, newMoney);

                    // 使用MessageManager发送惩罚消息
                    messageManager.sendPlayerKilledPenaltyMessage(player, (int) moneyPenalty);
                }
            }
        }
    }

    /**
     * 处理玩家虚空伤害
     *
     * @param player 玩家
     * @param event 伤害事件
     * @param gameName 游戏名称
     */
    private void handleVoidDamage(Player player, EntityDamageEvent event, String gameName) {
        // 取消虚空伤害事件
        event.setCancelled(true);

        // 检查玩家当前状态，防止重复处理
        if (deathReviveListener != null) {
            String currentStatus = deathReviveListener.getPlayerStatus(player.getUniqueId());
            if ("downed".equals(currentStatus) || "dead".equals(currentStatus)) {
                // 玩家已经处于倒下或死亡状态，不再重复处理
                plugin.getLogger().info("玩家 " + player.getName() + " 已处于 " + currentStatus + " 状态，跳过虚空伤害处理");
                return;
            }
        }

        // 获取游戏出生点位置
        Location spawnLocation = getGameSpawnLocation(gameName);

        // 检查是否启用虚空传送到出生点
        boolean teleportOnVoid = plugin.getConfig().getBoolean("game.void_handling.teleport_to_spawn", true);
        boolean downsOnVoid = plugin.getConfig().getBoolean("game.void_handling.downs_player", true);

        if (teleportOnVoid && spawnLocation != null) {
            // 传送玩家到出生点
            player.teleport(spawnLocation);

            // 恢复玩家生命值
            player.setHealth(player.getMaxHealth());

            // 发送提示消息
            String voidMessage = plugin.getConfig().getString("game.void_handling.teleport_message", "&c你掉入了虚空，已传送回出生点！");
            player.sendMessage(ChatColor.translateAlternateColorCodes('&', voidMessage));

            plugin.getLogger().info("玩家 " + player.getName() + " 掉入虚空，已传送回出生点");

            // 如果配置为虚空也会导致倒下，则处理倒下逻辑
            if (downsOnVoid && deathReviveListener != null) {
                // 延迟一点时间再处理倒下，确保传送完成
                plugin.getServer().getScheduler().runTaskLater(plugin, () -> {
                    deathReviveListener.handlePlayerKilledByZombie(player, null);
                }, 5L); // 延迟5tick
            }
        } else {
            // 如果没有出生点或不启用传送，直接处理为死亡
            plugin.getLogger().warning("玩家 " + player.getName() + " 掉入虚空，但无法获取出生点位置或未启用传送");

            if (deathReviveListener != null) {
                deathReviveListener.handlePlayerKilledByZombie(player, null);
            }
        }
    }

    /**
     * 获取游戏出生点位置
     *
     * @param gameName 游戏名称
     * @return 出生点位置，如果未设置则返回null
     */
    private Location getGameSpawnLocation(String gameName) {
        ConfigurationSection config = gameManager.getGameConfig(gameName);
        if (config == null || !config.contains("spawn")) {
            return null;
        }

        ConfigurationSection spawnSection = config.getConfigurationSection("spawn");
        if (spawnSection == null) {
            return null;
        }

        String worldName = spawnSection.getString("world");
        if (worldName == null) {
            return null;
        }

        World world = Bukkit.getWorld(worldName);
        if (world == null) {
            return null;
        }

        double x = spawnSection.getDouble("x");
        double y = spawnSection.getDouble("y");
        double z = spawnSection.getDouble("z");

        return new Location(world, x, y, z);
    }

    /**
     * 处理玩家死亡事件
     * 使用NORMAL优先级，在PlayerPvPListener之后处理现在
     *
     * @param event 玩家死亡事件
     */
    @EventHandler(priority = EventPriority.NORMAL)
    public void onPlayerDeath(PlayerDeathEvent event) {
        // 如果事件已经被取消（比如被PlayerPvPListener处理），则不再处理
        if (event.isCancelled()) {
            return;
        }

        Player player = event.getEntity();

        // 检查玩家是否在游戏中
        if (!playerManager.isPlayerInStatus(player, PlayerStatus.IN_GAME)) {
            return;
        }

        // 获取玩家所在的游戏
        String gameName = gameSessionManager.getPlayerGame(player);
        if (gameName == null) {
            return;
        }

        // 获取击杀者
        Player killer = player.getKiller();

        // 如果是被玩家击杀，检查是否启用友伤
        if (killer != null) {
            boolean friendlyFire = plugin.getConfig().getBoolean("game.friendly_fire", false);
            if (friendlyFire) {
                // 如果启用友伤，由PvP监听器处理
                return;
            } else {
                // 如果禁用友伤，但玩家仍然死亡，将其视为普通死亡处理
                // 这种情况不应该发生，因为友伤禁用时应该取消伤害事件
                // 但为了安全起见，仍然处理这种情况
                plugin.getLogger().warning("玩家 " + player.getName() + " 被其他玩家击杀，但友伤已禁用，将作为普通死亡处理");
            }
        }

        // 取消死亡事件，防止玩家真正死亡和复活到世界出生点
        event.setCancelled(true);

        // 取消死亡事件的消息广播
        event.setDeathMessage(null);

        // 重要：恢复玩家生命值，防止触发重生机制
        player.setHealth(1.0);

        // 检查最后一次伤害是否来自实体
        EntityDamageEvent lastDamage = player.getLastDamageCause();
        Entity damager = null;

        if (lastDamage instanceof EntityDamageByEntityEvent) {
            EntityDamageByEntityEvent damageByEntity = (EntityDamageByEntityEvent) lastDamage;
            damager = damageByEntity.getDamager();

            // 如果是游戏实体（僵尸/变异生物）击杀
            if (isGameEntity(damager)) {
                plugin.getLogger().info("玩家 " + player.getName() + " 被游戏实体击杀: " + damager.getType().name() +
                    " (名称: " + (damager.getCustomName() != null ? damager.getCustomName() : "无") + ")");
            } else {
                plugin.getLogger().info("玩家 " + player.getName() + " 被其他实体击杀: " + damager.getType().name());
            }
        } else if (lastDamage != null) {
            // 记录死亡原因
            plugin.getLogger().info("玩家 " + player.getName() + " 死亡，原因: " + lastDamage.getCause().name());
        } else {
            plugin.getLogger().info("玩家 " + player.getName() + " 死亡，无法确定原因");
        }

        // 检查玩家是否已经被其他事件处理过（防止重复处理）
        if (deathReviveListener != null) {
            String currentStatus = deathReviveListener.getPlayerStatus(player.getUniqueId());
            if ("downed".equals(currentStatus) || "dead".equals(currentStatus)) {
                // 玩家已经被其他事件处理过，不再重复处理
                plugin.getLogger().info("玩家 " + player.getName() + " 已被其他事件处理为 " + currentStatus + " 状态，跳过PlayerDeathEvent重复处理");
                return;
            }

            // 处理玩家死亡，将其视为被僵尸击杀
            deathReviveListener.handlePlayerKilledByZombie(player, damager);

            // 如果是被游戏实体（僵尸/变异生物）击杀，减少玩家金钱
            if (damager != null && isGameEntity(damager)) {
                // 减少击杀者的金钱（如果有配置）
                double moneyPenalty = plugin.getConfig().getDouble("game.money.player_killed_penalty", 0);
                if (moneyPenalty > 0) {
                    // 获取玩家当前金钱
                    double currentMoney = shootPluginHelper.getPlayerMoney(player);
                    // 确保不会扣成负数
                    double newMoney = Math.max(0, currentMoney - moneyPenalty);
                    // 设置新的金钱数量
                    shootPluginHelper.setPlayerMoney(player, newMoney);

                    // 使用MessageManager发送惩罚消息
                    messageManager.sendPlayerKilledPenaltyMessage(player, (int) moneyPenalty);
                }
            }
        }
    }

    /**
     * 处理剧毒僵尸攻击
     *
     * @param zombie 攻击的僵尸
     * @param player 被攻击的玩家
     */
    private void handlePoisonZombieAttack(Entity zombie, Player player) {
        // 修复：基于僵尸ID而非名称来确定是否为剧毒僵尸
        boolean isPoisonZombie = false;

        // 优先检查zombieId元数据
        if (zombie.hasMetadata("zombieId")) {
            String zombieId = zombie.getMetadata("zombieId").get(0).asString();
            isPoisonZombie = "id5".equals(zombieId);
        }

        // 备用检查：poisonZombie元数据
        if (!isPoisonZombie && zombie.hasMetadata("poisonZombie")) {
            isPoisonZombie = true;
        }

        if (!isPoisonZombie) {
            return;
        }

        // 对于ID5剧毒僵尸，默认启用毒性攻击
        // 对于其他剧毒僵尸，检查是否启用了毒性攻击
        boolean poisonAttackEnabled = true; // ID5默认启用
        if (zombie.hasMetadata("poisonAttackEnabled")) {
            poisonAttackEnabled = zombie.getMetadata("poisonAttackEnabled").get(0).asBoolean();
        }

        if (!poisonAttackEnabled) {
            return;
        }

        try {
            // 获取毒性参数
            int poisonDuration = 60; // 默认3秒
            int poisonLevel = 0; // 默认等级0

            if (zombie.hasMetadata("poisonDuration")) {
                poisonDuration = zombie.getMetadata("poisonDuration").get(0).asInt();
            }

            if (zombie.hasMetadata("poisonLevel")) {
                poisonLevel = zombie.getMetadata("poisonLevel").get(0).asInt();
            }

            // 应用毒性效果
            PotionEffect poisonEffect = new PotionEffect(PotionEffectType.POISON, poisonDuration, poisonLevel);
            player.addPotionEffect(poisonEffect);

            // 发送毒性攻击消息
            player.sendMessage("§5§l你被剧毒僵尸感染了！");

            // 播放毒性音效
            player.playSound(player.getLocation(), Sound.ENTITY_SPIDER_AMBIENT, 1.0f, 0.8f);

            // 生成毒性粒子效果
            player.getWorld().spawnParticle(Particle.SMOKE, player.getLocation().add(0, 1, 0), 10, 0.5, 0.5, 0.5, 0.1);

            if (plugin.getConfig().getBoolean("debug", false)) {
                plugin.getLogger().info("剧毒僵尸攻击玩家 " + player.getName() +
                    "，应用毒性效果：持续时间=" + poisonDuration + "tick，等级=" + poisonLevel);
            }

        } catch (Exception e) {
            plugin.getLogger().warning("处理剧毒僵尸攻击时出错: " + e.getMessage());
        }
    }

    /**
     * 检查是否是毒箭攻击玩家
     *
     * @param event 实体伤害事件
     * @return 是否是毒箭攻击玩家
     */
    private boolean isPoisonArrowAttackingPlayer(EntityDamageByEntityEvent event) {
        // 检查被攻击者是否是玩家
        if (!(event.getEntity() instanceof Player)) {
            return false;
        }

        // 检查攻击者是否是箭矢
        if (!(event.getDamager() instanceof org.bukkit.entity.Arrow)) {
            return false;
        }

        org.bukkit.entity.Arrow arrow = (org.bukkit.entity.Arrow) event.getDamager();

        // 检查箭矢是否有毒箭标记
        return arrow.hasMetadata("poisonZombieArrow");
    }

    /**
     * 处理毒箭攻击玩家事件
     *
     * @param event 实体伤害事件
     */
    private void handlePoisonArrowAttackPlayer(EntityDamageByEntityEvent event) {
        Player player = (Player) event.getEntity();
        org.bukkit.entity.Arrow arrow = (org.bukkit.entity.Arrow) event.getDamager();

        plugin.getLogger().info("=== 毒箭攻击玩家事件 ===");
        plugin.getLogger().info("玩家: " + player.getName());
        plugin.getLogger().info("毒箭攻击");

        try {
            // 应用毒性效果
            int poisonDuration = 100; // 默认5秒
            int poisonLevel = 1; // 默认2级毒性（0-based）

            // 检查箭矢是否有自定义毒性参数（通过射手僵尸的元数据）
            if (arrow.getShooter() instanceof org.bukkit.entity.Zombie) {
                org.bukkit.entity.Zombie shooter = (org.bukkit.entity.Zombie) arrow.getShooter();

                // 检查射手是否有毒性配置
                if (shooter.hasMetadata("poison_arrow_duration")) {
                    poisonDuration = shooter.getMetadata("poison_arrow_duration").get(0).asInt();
                }
                if (shooter.hasMetadata("poison_arrow_level")) {
                    poisonLevel = shooter.getMetadata("poison_arrow_level").get(0).asInt();
                }
            }

            // 应用毒性效果
            player.addPotionEffect(new org.bukkit.potion.PotionEffect(
                org.bukkit.potion.PotionEffectType.POISON,
                poisonDuration,
                poisonLevel,
                false,
                true
            ));

            // 播放毒药音效
            player.getWorld().playSound(player.getLocation(), org.bukkit.Sound.ENTITY_WITCH_DRINK, 0.5f, 1.0f);

            // 显示毒药粒子
            player.getWorld().spawnParticle(org.bukkit.Particle.DRAGON_BREATH,
                player.getLocation().add(0, 1, 0), 10, 0.3, 0.3, 0.3, 0.05);

            // 发送提示信息
            player.sendMessage("§2你被毒箭击中了！§a(" + (poisonLevel + 1) + "级剧毒，持续" + (poisonDuration / 20) + "秒)");

            if (plugin.getConfig().getBoolean("debug", false)) {
                plugin.getLogger().info("毒箭攻击玩家 " + player.getName() +
                    "，应用毒性效果：持续时间=" + poisonDuration + "tick，等级=" + poisonLevel);
            }

        } catch (Exception e) {
            plugin.getLogger().warning("处理毒箭攻击时出错: " + e.getMessage());
        }
    }

    /**
     * 检查是否是玩家攻击变异科学家
     *
     * @param event 实体伤害事件
     * @return 是否是玩家攻击变异科学家
     */
    private boolean isPlayerAttackingScientist(EntityDamageByEntityEvent event) {
        // 检查攻击者是否是玩家
        if (!(event.getDamager() instanceof Player)) {
            return false;
        }

        // 检查被攻击者是否是变异科学家
        if (!(event.getEntity() instanceof org.bukkit.entity.Zombie)) {
            return false;
        }

        org.bukkit.entity.Zombie zombie = (org.bukkit.entity.Zombie) event.getEntity();

        // 检查僵尸是否有变异科学家标记
        return zombie.hasMetadata("mutantScientist");
    }

    /**
     * 处理玩家攻击变异科学家事件
     *
     * @param event 实体伤害事件
     */
    private void handlePlayerAttackScientist(EntityDamageByEntityEvent event) {
        Player player = (Player) event.getDamager();
        org.bukkit.entity.Zombie zombie = (org.bukkit.entity.Zombie) event.getEntity();
        double damage = event.getFinalDamage();

        plugin.getLogger().info("=== 玩家攻击变异科学家事件 ===");
        plugin.getLogger().info("玩家: " + player.getName());
        plugin.getLogger().info("伤害: " + damage);
        plugin.getLogger().info("僵尸是否有召唤标记: " + zombie.hasMetadata("scientist_summon_enabled"));

        try {
            // 只有启用了双系统的变异科学家才处理伤害召唤
            if (zombie.hasMetadata("scientist_summon_enabled")) {
                // 获取双系统管理器并处理科学家伤害
                if (plugin instanceof org.Ver_zhzh.deathZombieV4.DeathZombieV4) {
                    org.Ver_zhzh.deathZombieV4.DeathZombieV4 dzPlugin = (org.Ver_zhzh.deathZombieV4.DeathZombieV4) plugin;
                    if (dzPlugin.getDualZombieSystemManager() != null &&
                        dzPlugin.getDualZombieSystemManager().getUserCustomZombie() != null &&
                        dzPlugin.getDualZombieSystemManager().getUserCustomZombie().getAdvancedSkillHandler() != null) {

                        dzPlugin.getDualZombieSystemManager().getUserCustomZombie()
                               .getAdvancedSkillHandler().handleScientistDamage(zombie, damage);

                        plugin.getLogger().info("已调用伤害处理方法");
                    } else {
                        plugin.getLogger().warning("双系统管理器或组件为null");
                    }
                } else {
                    plugin.getLogger().warning("插件类型不匹配");
                }
            } else {
                plugin.getLogger().info("变异科学家未启用召唤功能，跳过伤害处理");
            }

        } catch (Exception e) {
            plugin.getLogger().warning("处理变异科学家伤害时出错: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 处理实体死亡事件
     */
    @EventHandler
    public void onEntityDeath(EntityDeathEvent event) {
        // 检查是否是变异科学家死亡
        if (event.getEntity() instanceof Zombie) {
            Zombie zombie = (Zombie) event.getEntity();

            if (zombie.hasMetadata("mutantScientist")) {
                // 检查是否使用双系统并且启用了死亡召唤
                if (zombie.hasMetadata("scientist_death_summon_enabled")) {
                    // 用户配置的双系统，检查配置是否启用
                    boolean deathSummonEnabled = zombie.getMetadata("scientist_death_summon_enabled").get(0).asBoolean();
                    if (deathSummonEnabled) {
                        handleScientistDeath(zombie);
                    } else {
                        plugin.getLogger().info("变异科学家死亡召唤已在用户配置中禁用");
                    }
                } else {
                    // 原有系统，使用默认行为
                    plugin.getLogger().info("变异科学家使用原有系统，不处理死亡召唤");
                }
            }
        }
    }

    /**
     * 处理变异科学家死亡召唤
     */
    private void handleScientistDeath(Zombie zombie) {
        try {
            int summonCount = zombie.hasMetadata("scientist_death_summon_count") ?
                             zombie.getMetadata("scientist_death_summon_count").get(0).asInt() : 4;

            plugin.getLogger().info("=== 变异科学家死亡召唤 ===");
            plugin.getLogger().info("召唤数量: " + summonCount);
            plugin.getLogger().info("是否启用双系统: " + (plugin instanceof org.Ver_zhzh.deathZombieV4.DeathZombieV4));

            Location deathLocation = zombie.getLocation();

            // 召唤指定数量的暗影僵尸
            for (int i = 0; i < summonCount; i++) {
                // 计算随机位置偏移
                double offsetX = (Math.random() - 0.5) * 6;
                double offsetZ = (Math.random() - 0.5) * 6;
                Location summonLoc = deathLocation.clone().add(offsetX, 0, offsetZ);

                // 确保生成位置有效
                while (!summonLoc.getBlock().getType().isSolid() && summonLoc.getBlockY() > 0) {
                    summonLoc.setY(summonLoc.getY() - 1);
                }
                summonLoc.setY(summonLoc.getY() + 1);

                // 延迟生成暗影僵尸
                final Location finalSummonLoc = summonLoc;
                final int index = i;

                Bukkit.getScheduler().runTaskLater(plugin, () -> {
                    try {
                        // 根据配置选择召唤方式
                        boolean useUserCustom = zombie.hasMetadata("scientist_use_user_custom_death_summon") ?
                                               zombie.getMetadata("scientist_use_user_custom_death_summon").get(0).asBoolean() : true;

                        plugin.getLogger().info("死亡召唤配置 - 使用用户配置版本: " + useUserCustom);

                        // 生成暗影僵尸
                        if (plugin instanceof org.Ver_zhzh.deathZombieV4.DeathZombieV4) {
                            org.Ver_zhzh.deathZombieV4.DeathZombieV4 dzPlugin = (org.Ver_zhzh.deathZombieV4.DeathZombieV4) plugin;

                            Zombie summonedZombie = null;

                            if (useUserCustom && dzPlugin.getDualZombieSystemManager() != null) {
                                // 使用双系统召唤（用户配置版本）
                                summonedZombie = dzPlugin.getDualZombieSystemManager().spawnCustomZombieDirect(finalSummonLoc, "id15");
                                plugin.getLogger().info("使用双系统召唤了用户配置版本的暗影僵尸");
                            } else {
                                // 使用原有系统召唤（默认版本）
                                if (dzPlugin.getCustomZombie() != null) {
                                    summonedZombie = dzPlugin.getCustomZombie().spawnCustomZombieDirect(finalSummonLoc, "id15");
                                    plugin.getLogger().info("使用原有系统召唤了默认版本的暗影僵尸");
                                }
                            }

                            if (summonedZombie != null) {
                                // 显示召唤效果
                                Location effectLoc = finalSummonLoc.clone().add(0, 1, 0);
                                finalSummonLoc.getWorld().spawnParticle(Particle.PORTAL, effectLoc, 20, 0.5, 0.5, 0.5, 0.1);
                                finalSummonLoc.getWorld().playSound(finalSummonLoc, Sound.ENTITY_ENDERMAN_TELEPORT, 1.0f, 0.8f);

                                plugin.getLogger().info("变异科学家死亡召唤了第 " + (index + 1) + " 个暗影僵尸 " +
                                                      "(版本: " + (useUserCustom ? "用户配置" : "默认") + ")");
                            } else {
                                plugin.getLogger().warning("死亡召唤失败：生成的暗影僵尸为null");
                            }
                        }
                    } catch (Exception e) {
                        plugin.getLogger().warning("变异科学家死亡召唤失败: " + e.getMessage());
                    }
                }, index * 10L); // 每个僵尸间隔0.5秒生成
            }

        } catch (Exception e) {
            plugin.getLogger().warning("处理变异科学家死亡时出错: " + e.getMessage());
        }
    }

    /**
     * 检查是否是终极毁灭僵尸攻击玩家
     *
     * @param event 实体伤害事件
     * @return 是否是终极毁灭僵尸攻击玩家
     */
    private boolean isUltimateDestructionZombieAttackingPlayer(EntityDamageByEntityEvent event) {
        // 检查被攻击者是否是玩家
        if (!(event.getEntity() instanceof Player)) {
            return false;
        }

        // 检查攻击者是否是僵尸
        if (!(event.getDamager() instanceof Zombie)) {
            return false;
        }

        Zombie zombie = (Zombie) event.getDamager();

        // 检查是否有终极毁灭僵尸标记
        return zombie.hasMetadata("ultimateDestructionZombie");
    }

    /**
     * 处理终极毁灭僵尸攻击玩家事件
     *
     * @param event 实体伤害事件
     */
    private void handleUltimateDestructionZombieAttackPlayer(EntityDamageByEntityEvent event) {
        Player player = (Player) event.getEntity();
        Zombie zombie = (Zombie) event.getDamager();

        plugin.getLogger().info("=== 终极毁灭僵尸攻击玩家事件 ===");
        plugin.getLogger().info("玩家: " + player.getName());

        // 检查是否启用攻击强化
        if (!zombie.hasMetadata("ultimate_attack_enhance_enabled")) {
            plugin.getLogger().info("终极毁灭僵尸攻击强化未启用");
            return;
        }

        // 获取配置参数
        int weaknessLevel = 1; // 默认虚弱2等级
        int weaknessDuration = 60; // 默认3秒持续时间
        double knockbackDistance = 4.0; // 默认4格击退距离

        if (zombie.hasMetadata("ultimate_weakness_level")) {
            weaknessLevel = zombie.getMetadata("ultimate_weakness_level").get(0).asInt();
        }
        if (zombie.hasMetadata("ultimate_weakness_duration")) {
            weaknessDuration = zombie.getMetadata("ultimate_weakness_duration").get(0).asInt();
        }
        if (zombie.hasMetadata("ultimate_knockback_distance")) {
            knockbackDistance = zombie.getMetadata("ultimate_knockback_distance").get(0).asDouble();
        }

        // 施加虚弱效果
        player.addPotionEffect(new PotionEffect(PotionEffectType.WEAKNESS, weaknessDuration, weaknessLevel));

        // 计算击退方向
        org.bukkit.util.Vector direction = player.getLocation().toVector().subtract(zombie.getLocation().toVector()).normalize();
        direction.setY(0.3); // 添加一点向上的力
        direction = direction.multiply(knockbackDistance * 0.5); // 调整击退强度

        // 应用击退
        player.setVelocity(direction);

        // 播放击退音效
        player.getWorld().playSound(player.getLocation(), Sound.ENTITY_PLAYER_HURT, 1.0f, 0.8f);
        player.getWorld().playSound(player.getLocation(), Sound.ENTITY_GENERIC_EXPLODE, 0.5f, 1.5f);

        // 击退粒子效果
        player.getWorld().spawnParticle(Particle.EXPLOSION, player.getLocation(), 3, 0.5, 0.5, 0.5, 0.1);

        plugin.getLogger().info("终极毁灭僵尸对玩家 " + player.getName() + " 施加了虚弱" + (weaknessLevel + 1) +
                               "效果，持续" + (weaknessDuration / 20.0) + "秒，击退距离" + knockbackDistance + "格");
    }

    /**
     * 检查是否是变异博士攻击玩家
     *
     * @param event 实体伤害事件
     * @return 是否是变异博士攻击玩家
     */
    private boolean isMutantDoctorZombieAttackingPlayer(EntityDamageByEntityEvent event) {
        // 检查被攻击者是否是玩家
        if (!(event.getEntity() instanceof Player)) {
            return false;
        }

        // 检查攻击者是否是僵尸
        if (!(event.getDamager() instanceof Zombie)) {
            return false;
        }

        Zombie zombie = (Zombie) event.getDamager();

        // 检查是否有变异博士标记
        return zombie.hasMetadata("mutantDoctorZombie");
    }

    /**
     * 处理变异博士攻击玩家事件 - 技能5：攻击强化
     *
     * @param event 实体伤害事件
     */
    private void handleMutantDoctorZombieAttackPlayer(EntityDamageByEntityEvent event) {
        Player player = (Player) event.getEntity();
        Zombie zombie = (Zombie) event.getDamager();

        plugin.getLogger().info("=== 变异博士攻击玩家事件 ===");
        plugin.getLogger().info("玩家: " + player.getName());

        // 检查是否启用攻击强化
        if (!zombie.hasMetadata("doctor_attack_enhance_enabled")) {
            plugin.getLogger().info("变异博士攻击强化未启用");
            return;
        }

        // 获取配置参数
        int weaknessLevel = 1; // 默认虚弱2等级
        int weaknessDuration = 60; // 默认3秒持续时间
        double knockbackDistance = 4.0; // 默认4格击退距离

        if (zombie.hasMetadata("doctor_weakness_level")) {
            weaknessLevel = zombie.getMetadata("doctor_weakness_level").get(0).asInt();
        }
        if (zombie.hasMetadata("doctor_weakness_duration")) {
            weaknessDuration = zombie.getMetadata("doctor_weakness_duration").get(0).asInt();
        }
        if (zombie.hasMetadata("doctor_knockback_distance")) {
            knockbackDistance = zombie.getMetadata("doctor_knockback_distance").get(0).asDouble();
        }

        // 施加虚弱效果
        player.addPotionEffect(new PotionEffect(PotionEffectType.WEAKNESS, weaknessDuration, weaknessLevel));

        // 计算击退方向
        org.bukkit.util.Vector direction = player.getLocation().toVector().subtract(zombie.getLocation().toVector()).normalize();
        direction.setY(0.3); // 添加一点向上的力
        direction = direction.multiply(knockbackDistance * 0.5); // 调整击退强度

        // 应用击退
        player.setVelocity(direction);

        // 播放击退音效
        player.getWorld().playSound(player.getLocation(), Sound.ENTITY_PLAYER_HURT, 1.0f, 0.8f);
        player.getWorld().playSound(player.getLocation(), Sound.ENTITY_GENERIC_EXPLODE, 0.5f, 1.5f);

        // 击退粒子效果
        player.getWorld().spawnParticle(Particle.EXPLOSION, player.getLocation(), 3, 0.5, 0.5, 0.5, 0.1);

        plugin.getLogger().info("变异博士对玩家 " + player.getName() + " 施加了虚弱" + (weaknessLevel + 1) +
                               "效果，持续" + (weaknessDuration / 20.0) + "秒，击退距离" + knockbackDistance + "格");
    }
}
