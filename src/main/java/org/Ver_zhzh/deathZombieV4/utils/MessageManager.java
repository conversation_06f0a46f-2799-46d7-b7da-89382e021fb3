package org.Ver_zhzh.deathZombieV4.utils;

import org.bukkit.ChatColor;
import org.bukkit.Sound;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.configuration.file.YamlConfiguration;
import org.bukkit.entity.Player;
import org.Ver_zhzh.deathZombieV4.DeathZombieV4;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

/**
 * 消息管理器
 * 负责管理插件的所有消息配置，支持变量替换和颜色代码
 */
public class MessageManager {

    private final DeathZombieV4 plugin;
    private File messageFile;
    private FileConfiguration messageConfig;

    /**
     * 构造函数
     *
     * @param plugin 插件实例
     */
    public MessageManager(DeathZombieV4 plugin) {
        this.plugin = plugin;
        this.messageFile = new File(plugin.getDataFolder(), "message.yml");
        
        // 初始化配置文件
        initMessageFile();
        
        // 加载配置
        reloadConfig();
    }

    /**
     * 初始化消息配置文件
     */
    private void initMessageFile() {
        // 如果文件不存在，从资源中复制
        if (!messageFile.exists()) {
            plugin.saveResource("message.yml", false);
            plugin.getLogger().info("已创建message.yml文件");
        }
    }

    /**
     * 重新加载配置文件
     */
    public void reloadConfig() {
        // 加载配置文件
        messageConfig = YamlConfiguration.loadConfiguration(messageFile);
        
        // 确保使用UTF-8编码
        try (InputStream inputStream = plugin.getResource("message.yml")) {
            if (inputStream != null) {
                YamlConfiguration defaultConfig = YamlConfiguration.loadConfiguration(
                        new InputStreamReader(inputStream, StandardCharsets.UTF_8));
                messageConfig.setDefaults(defaultConfig);
            }
        } catch (IOException e) {
            plugin.getLogger().warning("加载默认message.yml时出错: " + e.getMessage());
        }
        
        plugin.getLogger().info("已重新加载消息配置文件");
    }

    /**
     * 保存配置文件
     */
    public void saveConfig() {
        try {
            messageConfig.save(messageFile);
        } catch (IOException e) {
            plugin.getLogger().severe("保存消息配置文件时出错: " + e.getMessage());
        }
    }

    /**
     * 获取射击命中消息
     *
     * @param hitType 命中类型 ("head" 或 "body")
     * @param entityIdentifier 实体标识符（可以是类型或具体ID）
     * @param amount 金钱数量
     * @return 格式化后的消息
     */
    public String getShootingMessage(String hitType, String entityIdentifier, int amount) {
        String message;

        // 根据命中类型选择消息模板
        if ("head".equals(hitType)) {
            message = messageConfig.getString("shooting.headshot_message");
            if (message == null || message.isEmpty()) {
                message = messageConfig.getString("shooting.hit_message", "&a{hit_type} {entity_name} +&6{amount}&a 金钱");
            }
        } else {
            message = messageConfig.getString("shooting.bodyshot_message");
            if (message == null || message.isEmpty()) {
                message = messageConfig.getString("shooting.hit_message", "&a{hit_type} {entity_name} +&6{amount}&a 金钱");
            }
        }

        // 创建变量映射
        Map<String, String> variables = new HashMap<>();
        variables.put("hit_type", getHitTypeDisplayName(hitType));
        variables.put("entity_name", getEntityDisplayName(entityIdentifier));
        variables.put("entity_type", getEntityDisplayName(entityIdentifier)); // 保持向后兼容
        variables.put("amount", String.valueOf(amount));

        return formatMessage(message, variables);
    }

    /**
     * 获取击杀消息
     *
     * @param entityIdentifier 实体标识符（可以是类型或具体ID）
     * @param amount 金钱数量
     * @param isShootMode 是否为射击模式
     * @return 格式化后的消息
     */
    public String getKillMessage(String entityIdentifier, int amount, boolean isShootMode) {
        String message;

        if (isShootMode) {
            message = messageConfig.getString("kill.kill_in_shoot_mode_message", "&7击杀 {entity_name} +&6{amount}&7 金钱");
        } else {
            message = messageConfig.getString("kill.kill_message", "&e击杀 {entity_name} +&6{amount}&e 金钱");
        }

        // 创建变量映射
        Map<String, String> variables = new HashMap<>();
        variables.put("entity_name", getEntityDisplayName(entityIdentifier));
        variables.put("entity_type", getEntityDisplayName(entityIdentifier)); // 保持向后兼容
        variables.put("amount", String.valueOf(amount));

        return formatMessage(message, variables);
    }

    /**
     * 获取玩家被击杀惩罚消息
     *
     * @param amount 扣除的金钱数量
     * @return 格式化后的消息
     */
    public String getPlayerKilledPenaltyMessage(int amount) {
        String message = messageConfig.getString("penalty.player_killed_penalty", "&c你被僵尸击杀，失去了 &6{amount}&c 金钱!");
        
        Map<String, String> variables = new HashMap<>();
        variables.put("amount", String.valueOf(amount));
        
        return formatMessage(message, variables);
    }

    /**
     * 获取窗户修复奖励消息
     *
     * @param amount 奖励金钱数量
     * @return 格式化后的消息
     */
    public String getWindowRepairMessage(int amount) {
        String message = messageConfig.getString("window_repair.repair_success", "&a你修复了一块窗户，获得了 &6{amount} &a金钱！");

        Map<String, String> variables = new HashMap<>();
        variables.put("amount", String.valueOf(amount));

        return formatMessage(message, variables);
    }

    /**
     * 获取窗户完全修复消息
     *
     * @return 格式化后的消息
     */
    public String getWindowFullyRepairedMessage() {
        String message = messageConfig.getString("window_repair.fully_repaired", "&a恭喜！你已经完全修复了这个窗户！");
        return formatMessage(message, new HashMap<>());
    }

    /**
     * 获取窗户已完全修复消息
     *
     * @return 格式化后的消息
     */
    public String getWindowAlreadyRepairedMessage() {
        String message = messageConfig.getString("window_repair.already_repaired", "&a窗户已完全修复！");
        return formatMessage(message, new HashMap<>());
    }

    /**
     * 获取附近有怪物无法修复消息
     *
     * @return 格式化后的消息
     */
    public String getWindowMonstersNearbyMessage() {
        String message = messageConfig.getString("window_repair.monsters_nearby", "&c附近有怪物，无法修复窗户！");
        return formatMessage(message, new HashMap<>());
    }

    /**
     * 获取修复冷却消息
     *
     * @return 格式化后的消息
     */
    public String getWindowRepairCooldownMessage() {
        String message = messageConfig.getString("window_repair.repair_cooldown", "&c修复冷却中，请稍后再试！");
        return formatMessage(message, new HashMap<>());
    }

    /**
     * 获取回合完成奖励消息
     *
     * @param round 回合数
     * @param amount 奖励金钱数量
     * @return 格式化后的消息
     */
    public String getRoundCompletionMessage(int round, int amount) {
        String message = messageConfig.getString("reward.round_completion", "&e完成第 {round} 回合 +&6{amount}&e 金钱");
        
        Map<String, String> variables = new HashMap<>();
        variables.put("round", String.valueOf(round));
        variables.put("amount", String.valueOf(amount));
        
        return formatMessage(message, variables);
    }

    /**
     * 获取游戏完成奖励消息
     *
     * @param amount 奖励金钱数量
     * @return 格式化后的消息
     */
    public String getGameCompletionMessage(int amount) {
        String message = messageConfig.getString("reward.game_completion", "&6游戏胜利 +&6{amount}&6 金钱!");

        Map<String, String> variables = new HashMap<>();
        variables.put("amount", String.valueOf(amount));

        return formatMessage(message, variables);
    }

    /**
     * 获取友伤被禁用时的提示消息
     *
     * @return 格式化后的消息
     */
    public String getFriendlyFireDisabledMessage() {
        String message = messageConfig.getString("friendly_fire.disabled_message", "&c友伤已禁用，你不能攻击队友！");

        // 友伤消息不需要变量替换，直接格式化颜色代码
        return ChatColor.translateAlternateColorCodes('&', message);
    }

    /**
     * 获取实体显示名称
     *
     * @param entityIdentifier 实体标识符（可以是类型或具体ID）
     * @return 显示名称
     */
    public String getEntityDisplayName(String entityIdentifier) {
        if (entityIdentifier == null || entityIdentifier.isEmpty()) {
            return "未知实体";
        }

        boolean debugMode = plugin.getConfig().getBoolean("debug", false);
        if (debugMode) {
            plugin.getLogger().info("MessageManager.getEntityDisplayName: 查找实体显示名称，标识符: " + entityIdentifier);
        }

        String lowerIdentifier = entityIdentifier.toLowerCase();

        // 首先尝试直接匹配（支持id1, idc2等具体ID）
        String displayName = messageConfig.getString("entity_names." + lowerIdentifier, null);
        if (displayName != null && !displayName.isEmpty()) {
            if (debugMode) {
                plugin.getLogger().info("找到直接匹配的显示名称: " + displayName);
            }
            return displayName;
        }

        // 尝试清理标识符后再匹配
        String cleanedIdentifier = cleanIdentifierForLookup(lowerIdentifier);
        if (!cleanedIdentifier.equals(lowerIdentifier)) {
            displayName = messageConfig.getString("entity_names." + cleanedIdentifier, null);
            if (displayName != null && !displayName.isEmpty()) {
                if (debugMode) {
                    plugin.getLogger().info("使用清理后的标识符 '" + cleanedIdentifier + "' 找到显示名称: " + displayName);
                }
                return displayName;
            }
        }

        // 尝试根据ID前缀进行类型映射
        String typeMapping = getTypeMappingFromId(lowerIdentifier);
        if (typeMapping != null) {
            displayName = messageConfig.getString("entity_names." + typeMapping, null);
            if (displayName != null && !displayName.isEmpty()) {
                if (debugMode) {
                    plugin.getLogger().info("使用类型映射 '" + typeMapping + "' 找到显示名称: " + displayName);
                }
                return displayName;
            }
        }

        // 最后的回退：返回友好的默认名称
        String fallbackName = getFallbackDisplayName(lowerIdentifier);
        if (debugMode) {
            plugin.getLogger().info("使用回退显示名称: " + fallbackName);
        }
        return fallbackName;
    }

    /**
     * 清理标识符用于查找
     *
     * @param identifier 原始标识符
     * @return 清理后的标识符
     */
    private String cleanIdentifierForLookup(String identifier) {
        if (identifier == null || identifier.isEmpty()) {
            return identifier;
        }

        // 移除可能的前缀
        String cleaned = identifier;
        if (cleaned.startsWith("zombie_")) {
            cleaned = cleaned.substring(7);
        } else if (cleaned.startsWith("entity_")) {
            cleaned = cleaned.substring(7);
        } else if (cleaned.startsWith("npc_")) {
            cleaned = cleaned.substring(4);
        }

        return cleaned;
    }

    /**
     * 根据ID获取类型映射
     *
     * @param identifier 标识符
     * @return 类型映射，如果无法确定则返回null
     */
    private String getTypeMappingFromId(String identifier) {
        if (identifier == null || identifier.isEmpty()) {
            return null;
        }

        if (identifier.startsWith("id")) {
            return "zombie";
        } else if (identifier.startsWith("idc")) {
            return "mutant";
        } else if (identifier.startsWith("idn")) {
            return "npc";
        }

        return null;
    }

    /**
     * 获取回退显示名称
     *
     * @param identifier 标识符
     * @return 友好的回退名称
     */
    private String getFallbackDisplayName(String identifier) {
        if (identifier == null || identifier.isEmpty()) {
            return "未知实体";
        }

        // 根据标识符前缀提供友好的默认名称
        if (identifier.startsWith("id")) {
            return "僵尸 " + identifier.substring(2);
        } else if (identifier.startsWith("idc")) {
            return "变异僵尸 " + identifier.substring(3);
        } else if (identifier.startsWith("idn")) {
            return "NPC " + identifier.substring(3);
        } else if (identifier.equals("zombie")) {
            return "僵尸";
        } else if (identifier.equals("mutant")) {
            return "变异怪物";
        } else if (identifier.equals("special")) {
            return "特殊僵尸";
        } else if (identifier.equals("npc")) {
            return "感染者";
        } else if (identifier.equals("monster")) {
            return "怪物";
        }

        // 最终回退
        return identifier;
    }

    /**
     * 获取命中类型显示名称
     *
     * @param hitType 命中类型
     * @return 显示名称
     */
    public String getHitTypeDisplayName(String hitType) {
        return messageConfig.getString("hit_types." + hitType.toLowerCase(), hitType);
    }

    /**
     * 格式化消息，替换变量并应用颜色代码
     *
     * @param message 原始消息
     * @param variables 变量映射
     * @return 格式化后的消息
     */
    private String formatMessage(String message, Map<String, String> variables) {
        if (message == null || message.isEmpty()) {
            return "";
        }
        
        // 替换变量
        for (Map.Entry<String, String> entry : variables.entrySet()) {
            message = message.replace("{" + entry.getKey() + "}", entry.getValue());
        }
        
        // 应用颜色代码
        return ChatColor.translateAlternateColorCodes('&', message);
    }

    /**
     * 检查消息类型是否启用
     *
     * @param messageType 消息类型
     * @return 是否启用
     */
    public boolean isMessageEnabled(String messageType) {
        return messageConfig.getBoolean("message_enabled." + messageType, true);
    }

    /**
     * 检查子消息是否启用
     *
     * @param messageType 消息类型
     * @param subMessageType 子消息类型
     * @return 是否启用
     */
    public boolean isSubMessageEnabled(String messageType, String subMessageType) {
        // 首先检查主消息类型是否启用
        if (!isMessageEnabled(messageType)) {
            return false;
        }

        // 然后检查子消息是否启用
        return messageConfig.getBoolean(messageType + ".enabled." + subMessageType, true);
    }

    /**
     * 发送射击命中消息给玩家
     *
     * @param player 玩家
     * @param hitType 命中类型
     * @param entityIdentifier 实体标识符
     * @param amount 金钱数量
     */
    public void sendShootingMessage(Player player, String hitType, String entityIdentifier, int amount) {
        // 检查是否启用hit_message
        boolean useHitMessage = isSubMessageEnabled("shooting", "hit_message");

        if (!useHitMessage) {
            // 如果hit_message被禁用，使用具体的爆头或身体消息
            String subMessageType;
            if ("head".equals(hitType)) {
                subMessageType = "headshot_message";
            } else {
                subMessageType = "bodyshot_message";
            }

            // 检查具体消息类型是否可用（这里不需要检查开关，因为hit_message关闭时自动使用）
        } else {
            // 如果hit_message启用，直接使用通用消息
        }

        String message = getShootingMessage(hitType, entityIdentifier, amount);
        player.sendMessage(message);

        // 播放音效
        if (messageConfig.getBoolean("format.sound_enabled", true)) {
            String soundName;
            if ("head".equals(hitType)) {
                soundName = messageConfig.getString("format.headshot_sound", "entity.player.levelup");
            } else {
                soundName = messageConfig.getString("format.hit_sound", "entity.experience_orb.pickup");
            }

            try {
                Sound sound = Sound.valueOf(soundName.toUpperCase().replace(".", "_"));
                player.playSound(player.getLocation(), sound, 1.0f, 1.0f);
            } catch (IllegalArgumentException e) {
                plugin.getLogger().warning("无效的音效名称: " + soundName);
            }
        }

        // 动作条显示（如果启用）
        if (messageConfig.getBoolean("format.action_bar_enabled", false)) {
            try {
                player.spigot().sendMessage(net.md_5.bungee.api.ChatMessageType.ACTION_BAR,
                    net.md_5.bungee.api.chat.TextComponent.fromLegacyText(message));
            } catch (Exception e) {
                // 如果动作条发送失败，忽略错误
            }
        }
    }

    /**
     * 发送击杀消息给玩家
     *
     * @param player 玩家
     * @param entityIdentifier 实体标识符
     * @param amount 金钱数量
     * @param isShootMode 是否为射击模式
     */
    public void sendKillMessage(Player player, String entityIdentifier, int amount, boolean isShootMode) {
        // 根据模式确定子消息类型
        String subMessageType = isShootMode ? "kill_in_shoot_mode_message" : "kill_message";

        // 检查子消息是否启用
        if (!isSubMessageEnabled("kill", subMessageType)) {
            return;
        }

        String message = getKillMessage(entityIdentifier, amount, isShootMode);
        player.sendMessage(message);

        // 播放音效
        if (messageConfig.getBoolean("format.sound_enabled", true)) {
            String soundName = messageConfig.getString("format.kill_sound", "entity.experience_orb.pickup");

            try {
                Sound sound = Sound.valueOf(soundName.toUpperCase().replace(".", "_"));
                player.playSound(player.getLocation(), sound, 1.0f, 1.0f);
            } catch (IllegalArgumentException e) {
                plugin.getLogger().warning("无效的音效名称: " + soundName);
            }
        }
    }

    /**
     * 发送玩家被击杀惩罚消息
     *
     * @param player 玩家
     * @param amount 扣除的金钱数量
     */
    public void sendPlayerKilledPenaltyMessage(Player player, int amount) {
        // 检查子消息是否启用
        if (!isSubMessageEnabled("penalty", "player_killed_penalty")) {
            return;
        }

        String message = getPlayerKilledPenaltyMessage(amount);
        player.sendMessage(message);
    }

    /**
     * 发送窗户修复消息
     *
     * @param player 玩家
     * @param amount 奖励金钱数量
     */
    public void sendWindowRepairMessage(Player player, int amount) {
        // 检查子消息是否启用
        if (!isSubMessageEnabled("window_repair", "repair_success")) {
            return;
        }

        String message = getWindowRepairMessage(amount);
        player.sendMessage(message);
    }

    /**
     * 发送窗户完全修复消息
     *
     * @param player 玩家
     */
    public void sendWindowFullyRepairedMessage(Player player) {
        // 检查子消息是否启用
        if (!isSubMessageEnabled("window_repair", "fully_repaired")) {
            return;
        }

        String message = getWindowFullyRepairedMessage();
        player.sendMessage(message);
    }

    /**
     * 发送窗户已完全修复消息
     *
     * @param player 玩家
     */
    public void sendWindowAlreadyRepairedMessage(Player player) {
        // 检查子消息是否启用
        if (!isSubMessageEnabled("window_repair", "already_repaired")) {
            return;
        }

        String message = getWindowAlreadyRepairedMessage();
        player.sendMessage(message);
    }

    /**
     * 发送附近有怪物无法修复消息
     *
     * @param player 玩家
     */
    public void sendWindowMonstersNearbyMessage(Player player) {
        // 检查子消息是否启用
        if (!isSubMessageEnabled("window_repair", "monsters_nearby")) {
            return;
        }

        String message = getWindowMonstersNearbyMessage();
        player.sendMessage(message);
    }

    /**
     * 发送回合完成奖励消息
     *
     * @param player 玩家
     * @param round 回合数
     * @param amount 奖励金钱数量
     */
    public void sendRoundCompletionMessage(Player player, int round, int amount) {
        // 检查子消息是否启用
        if (!isSubMessageEnabled("reward", "round_completion")) {
            return;
        }

        String message = getRoundCompletionMessage(round, amount);
        player.sendMessage(message);
    }

    /**
     * 发送游戏完成奖励消息
     *
     * @param player 玩家
     * @param amount 奖励金钱数量
     */
    public void sendGameCompletionMessage(Player player, int amount) {
        // 检查子消息是否启用
        if (!isSubMessageEnabled("reward", "game_completion")) {
            return;
        }

        String message = getGameCompletionMessage(amount);
        player.sendMessage(message);
    }

    /**
     * 发送友伤被禁用时的提示消息
     *
     * @param player 玩家
     */
    public void sendFriendlyFireDisabledMessage(Player player) {
        // 检查子消息是否启用
        if (!isSubMessageEnabled("friendly_fire", "disabled_message")) {
            return;
        }

        String message = getFriendlyFireDisabledMessage();
        player.sendMessage(message);
    }

    /**
     * 获取回合标题消息
     *
     * @param round 回合数
     * @param gameName 游戏名称
     * @return 标题消息对象
     */
    public TitleMessage getRoundTitleMessage(int round, String gameName) {
        // 检查是否启用回合标题
        if (!messageConfig.getBoolean("title.round_title.enabled", true)) {
            return null;
        }

        String title = messageConfig.getString("title.round_title.title", "&6第 {round} 回合");
        String subtitle = messageConfig.getString("title.round_title.subtitle", "&e准备迎战新的挑战！");
        int fadeIn = messageConfig.getInt("title.round_title.fade_in", 10);
        int stay = messageConfig.getInt("title.round_title.stay", 70);
        int fadeOut = messageConfig.getInt("title.round_title.fade_out", 20);

        // 创建变量映射
        Map<String, String> variables = new HashMap<>();
        variables.put("round", String.valueOf(round));
        variables.put("game_name", gameName != null ? gameName : "");

        // 格式化标题和副标题
        String formattedTitle = formatMessage(title, variables);
        String formattedSubtitle = formatMessage(subtitle, variables);

        return new TitleMessage(formattedTitle, formattedSubtitle, fadeIn, stay, fadeOut);
    }

    /**
     * 获取加入游戏标题消息
     *
     * @param gameName 游戏名称
     * @param playerCount 当前玩家数量
     * @param maxPlayers 最大玩家数量
     * @return 标题消息对象
     */
    public TitleMessage getJoinGameTitleMessage(String gameName, int playerCount, int maxPlayers) {
        // 检查是否启用加入游戏标题
        if (!messageConfig.getBoolean("title.join_game_title.enabled", true)) {
            return null;
        }

        String title = messageConfig.getString("title.join_game_title.title", "&a欢迎加入游戏");
        String subtitle = messageConfig.getString("title.join_game_title.subtitle", "&7游戏: &f{game_name} &7玩家: &f{player_count}/{max_players}");
        int fadeIn = messageConfig.getInt("title.join_game_title.fade_in", 10);
        int stay = messageConfig.getInt("title.join_game_title.stay", 60);
        int fadeOut = messageConfig.getInt("title.join_game_title.fade_out", 20);

        // 创建变量映射
        Map<String, String> variables = new HashMap<>();
        variables.put("game_name", gameName != null ? gameName : "");
        variables.put("player_count", String.valueOf(playerCount));
        variables.put("max_players", String.valueOf(maxPlayers));

        // 格式化标题和副标题
        String formattedTitle = formatMessage(title, variables);
        String formattedSubtitle = formatMessage(subtitle, variables);

        return new TitleMessage(formattedTitle, formattedSubtitle, fadeIn, stay, fadeOut);
    }

    /**
     * 发送回合标题消息给玩家
     *
     * @param player 玩家
     * @param round 回合数
     * @param gameName 游戏名称
     */
    public void sendRoundTitleMessage(Player player, int round, String gameName) {
        TitleMessage titleMessage = getRoundTitleMessage(round, gameName);
        if (titleMessage != null) {
            sendTitle(player, titleMessage);
        }
    }

    /**
     * 发送加入游戏标题消息给玩家
     *
     * @param player 玩家
     * @param gameName 游戏名称
     * @param playerCount 当前玩家数量
     * @param maxPlayers 最大玩家数量
     */
    public void sendJoinGameTitleMessage(Player player, String gameName, int playerCount, int maxPlayers) {
        TitleMessage titleMessage = getJoinGameTitleMessage(gameName, playerCount, maxPlayers);
        if (titleMessage != null) {
            sendTitle(player, titleMessage);
        }
    }

    /**
     * 获取游戏结束标题消息
     *
     * @param endType 结束类型 (victory, defeat, timeout)
     * @param gameName 游戏名称
     * @param duration 游戏耗时
     * @param rounds 坚持回合数
     * @return 标题消息对象
     */
    public TitleMessage getGameEndTitleMessage(String endType, String gameName, String duration, int rounds) {
        // 检查是否启用游戏结束标题
        if (!messageConfig.getBoolean("title.game_end_title." + endType + ".enabled", true)) {
            return null;
        }

        String title = messageConfig.getString("title.game_end_title." + endType + ".title", "&c&l游戏结束");
        String subtitle = messageConfig.getString("title.game_end_title." + endType + ".subtitle", "&e耗时: &f{duration} &e坚持: &f{rounds} &e回合");
        int fadeIn = messageConfig.getInt("title.game_end_title." + endType + ".fade_in", 10);
        int stay = messageConfig.getInt("title.game_end_title." + endType + ".stay", 100);
        int fadeOut = messageConfig.getInt("title.game_end_title." + endType + ".fade_out", 20);

        // 创建变量映射
        Map<String, String> variables = new HashMap<>();
        variables.put("game_name", gameName != null ? gameName : "");
        variables.put("duration", duration != null ? duration : "00:00");
        variables.put("rounds", String.valueOf(rounds));

        // 格式化标题和副标题
        String formattedTitle = formatMessage(title, variables);
        String formattedSubtitle = formatMessage(subtitle, variables);

        return new TitleMessage(formattedTitle, formattedSubtitle, fadeIn, stay, fadeOut);
    }

    /**
     * 发送游戏结束标题消息给玩家
     *
     * @param player 玩家
     * @param endType 结束类型 (victory, defeat, timeout)
     * @param gameName 游戏名称
     * @param duration 游戏耗时
     * @param rounds 坚持回合数
     */
    public void sendGameEndTitleMessage(Player player, String endType, String gameName, String duration, int rounds) {
        TitleMessage titleMessage = getGameEndTitleMessage(endType, gameName, duration, rounds);
        if (titleMessage != null) {
            sendTitle(player, titleMessage);
        }
    }

    /**
     * 发送标题给玩家
     *
     * @param player 玩家
     * @param titleMessage 标题消息对象
     */
    private void sendTitle(Player player, TitleMessage titleMessage) {
        if (player == null || !player.isOnline() || titleMessage == null) {
            return;
        }

        try {
            player.sendTitle(
                titleMessage.getTitle(),
                titleMessage.getSubtitle(),
                titleMessage.getFadeIn(),
                titleMessage.getStay(),
                titleMessage.getFadeOut()
            );
        } catch (Exception e) {
            plugin.getLogger().warning("发送标题失败: " + e.getMessage());
        }
    }

    /**
     * 获取配置文件
     *
     * @return 配置文件
     */
    public FileConfiguration getConfig() {
        return messageConfig;
    }
}
