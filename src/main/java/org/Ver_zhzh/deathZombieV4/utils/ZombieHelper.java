package org.Ver_zhzh.deathZombieV4.utils;

import java.util.HashMap;
import java.util.Map;
import java.util.logging.Logger;

import org.Ver_zhzh.customZombie.CustomZombie;
import org.Ver_zhzh.customZombie.ParticleHelper;
import org.Ver_zhzh.customZombie.ZombieGUIManager;
import org.Ver_zhzh.deathZombieV4.DeathZombieV4;
import org.bukkit.ChatColor;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.enchantments.Enchantment;
import org.bukkit.entity.*;
import org.bukkit.entity.PigZombie;
import org.bukkit.inventory.ItemStack;
import org.bukkit.potion.PotionEffect;
import org.bukkit.potion.PotionEffectType;
import org.bukkit.scheduler.BukkitRunnable;
import org.bukkit.scheduler.BukkitTask;

/**
 * ZombieHelper类 - 自定义僵尸功能的桥接器 使用新的CustomZombie实现，不再依赖于JavaPlugin特性
 */
public class ZombieHelper {

    private DeathZombieV4 plugin;
    private static ZombieHelper instance;
    private Logger logger;

    // CustomZombie组件及其辅助组件
    private CustomZombie customZombie;
    private ParticleHelper particleHelper;
    private ZombieGUIManager guiManager;
    private AdvancedEntitySpawner advancedEntitySpawner;

    // 双CustomZombie系统管理器
    private org.Ver_zhzh.customZombie.DualZombieSystemManager dualZombieSystemManager;

    /**
     * 获取ZombieHelper单例实例
     *
     * @return ZombieHelper实例
     */
    public static ZombieHelper getInstance() {
        return instance;
    }

    /**
     * 构造函数
     *
     * @param plugin DeathZombieV4插件实例
     */
    public ZombieHelper(DeathZombieV4 plugin) {
        this.plugin = plugin;
        this.logger = plugin.getLogger();
        instance = this;
        initialize();
    }

    /**
     * 初始化ZombieHelper
     */
    private void initialize() {
        try {
            logger.info("初始化ZombieHelper...");

            // 创建CustomZombie组件，传入插件引用
            customZombie = new CustomZombie(plugin);

            // 初始化CustomZombie组件
            customZombie.initialize();

            // 获取已初始化的辅助组件引用
            particleHelper = customZombie.getParticleHelper();
            guiManager = customZombie.getGuiManager();

            // 初始化高级实体生成器
            advancedEntitySpawner = new AdvancedEntitySpawner(plugin);

            // 注册命令，使用CustomZombie作为执行器，添加null检查
            if (plugin.getCommand("czm") != null) {
                plugin.getCommand("czm").setExecutor(customZombie);
            } else {
                logger.warning("未在plugin.yml中注册'czm'命令，相关功能将不可用");
            }

            // 初始化双CustomZombie系统管理器
            dualZombieSystemManager = new org.Ver_zhzh.customZombie.DualZombieSystemManager(plugin, customZombie);
            logger.info("双CustomZombie系统管理器初始化完成");

            logger.info("成功初始化ZombieHelper和CustomZombie组件");
        } catch (Exception e) {
            logger.severe("初始化ZombieHelper时出错: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 生成自定义僵尸（使用双系统管理器）
     *
     * @param location 生成位置
     * @param zombieId 僵尸ID
     * @return 生成的僵尸实体
     */
    public Zombie spawnCustomZombie(Location location, String zombieId) {
        // 优先使用双系统管理器
        if (dualZombieSystemManager != null && dualZombieSystemManager.isInitialized()) {
            try {
                logger.info("使用双系统管理器生成自定义僵尸: " + zombieId + " 在位置 " + formatLocation(location));
                return dualZombieSystemManager.spawnCustomZombieDirect(location, zombieId);
            } catch (Exception e) {
                logger.warning("双系统管理器生成失败，回退到原有系统: " + e.getMessage());
            }
        }

        // 回退到原有CustomZombie系统
        if (customZombie == null) {
            logger.severe("CustomZombie组件未初始化，无法生成僵尸");
            return null;
        }

        try {
            logger.info("使用原有系统生成自定义僵尸: " + zombieId + " 在位置 " + formatLocation(location));
            return customZombie.spawnCustomZombieDirect(location, zombieId);
        } catch (Exception e) {
            logger.severe("生成自定义僵尸时出错: " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 生成其他实体
     *
     * @param location 生成位置
     * @param entityId 实体ID
     * @return 是否生成成功
     */
    public boolean spawnOtherEntity(Location location, String entityId) {
        try {
            if (location == null) {
                plugin.getLogger().severe("生成实体失败: 位置不能为null");
                return false;
            }

            plugin.getLogger().info("尝试在位置 " + formatLocation(location) + " 生成实体类型: " + entityId);

            // 对于idc类型的实体ID，优先尝试使用UserCustomEntity系统
            if (entityId != null && entityId.startsWith("idc")) {
                plugin.getLogger().info("检测到idc类型的实体ID: " + entityId);

                // 检查是否启用游戏中用户自定义实体生成
                if (customZombie != null && customZombie.getEntityIntegration() != null) {
                    // 获取UserCustomEntity实例并检查配置
                    try {
                        java.lang.reflect.Method getUserCustomEntityMethod = customZombie.getEntityIntegration().getClass().getMethod("getUserCustomEntity");
                        Object userCustomEntity = getUserCustomEntityMethod.invoke(customZombie.getEntityIntegration());

                        if (userCustomEntity != null) {
                            java.lang.reflect.Method isEnableInGameMethod = userCustomEntity.getClass().getMethod("isEnableInGameUserEntities");
                            boolean enableInGame = (Boolean) isEnableInGameMethod.invoke(userCustomEntity);

                            if (enableInGame) {
                                plugin.getLogger().info("游戏中用户自定义实体已启用，尝试使用UserCustomEntity系统生成: " + entityId);
                                org.bukkit.entity.LivingEntity userEntity = customZombie.getEntityIntegration().spawnCustomEntity(location, entityId);
                                if (userEntity != null) {
                                    plugin.getLogger().info("UserCustomEntity系统成功生成: " + entityId);
                                    return true;
                                } else {
                                    plugin.getLogger().info("UserCustomEntity系统生成失败或不支持: " + entityId + "，尝试原版系统");
                                }
                            } else {
                                plugin.getLogger().info("游戏中用户自定义实体已禁用，直接使用原版系统生成: " + entityId);
                            }
                        }
                    } catch (Exception e) {
                        plugin.getLogger().warning("检查游戏中用户实体配置时出错: " + e.getMessage() + "，使用原版系统");
                    }
                }

                // 如果UserCustomEntity系统失败，使用原版spawnOtherEntityAtLocation方法
                plugin.getLogger().info("使用原版系统生成: " + entityId);

                // 特殊处理idc18铁傀儡，添加更详细的日志
                if ("idc18".equals(entityId)) {
                    plugin.getLogger().info("===== 开始生成变异铁傀儡(idc18) =====");
                    plugin.getLogger().info("生成位置: " + formatLocation(location));

                    // 检查CustomZombie实例
                    if (customZombie == null) {
                        plugin.getLogger().severe("CustomZombie实例为null，无法生成变异铁傀儡");
                        return false;
                    }

                    // 检查MutantIronGolemManager
                    try {
                        java.lang.reflect.Method getMutantIronGolemManagerMethod = customZombie.getClass().getMethod("getMutantIronGolemManager");
                        Object manager = getMutantIronGolemManagerMethod.invoke(customZombie);
                        if (manager == null) {
                            plugin.getLogger().severe("MutantIronGolemManager为null，无法生成变异铁傀儡");
                        } else {
                            plugin.getLogger().info("MutantIronGolemManager实例存在，类型: " + manager.getClass().getName());
                        }
                    } catch (Exception e) {
                        plugin.getLogger().severe("获取MutantIronGolemManager时出错: " + e.getMessage());
                        e.printStackTrace();
                    }
                }

                return spawnOtherEntityAtLocation(location, entityId);
            }

            // 对于其他类型的实体ID，使用CustomZombie的spawnCustomZombieDirect方法
            if (customZombie != null) {
                plugin.getLogger().info("使用CustomZombie的spawnCustomZombieDirect方法生成实体: " + entityId);
                Entity entity = customZombie.spawnCustomZombieDirect(location, entityId);
                return entity != null;
            } else {
                plugin.getLogger().warning("CustomZombie实例未初始化，无法生成实体");
                return false;
            }
        } catch (Exception e) {
            plugin.getLogger().severe("生成实体时发生错误: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 在指定位置生成其他类型的实体 不依赖于Player对象，避免空指针异常
     *
     * @param location 生成位置
     * @param entityId 实体ID
     * @return 是否成功生成
     */
    public boolean spawnOtherEntityAtLocation(Location location, String entityId) {
        if (customZombie == null) {
            logger.severe("CustomZombie组件未初始化，无法在位置生成实体");
            return false;
        }

        try {
            logger.info("直接在位置生成实体: " + entityId + " 在 " + formatLocation(location));

            // 根据entityId判断生成什么类型的实体
            switch (entityId) {
                case "idc1": // 变异僵尸01（僵尸猪人）
                    // 生成僵尸猪人实体
                    PigZombie pigZombie = (PigZombie) location.getWorld().spawnEntity(location, EntityType.ZOMBIFIED_PIGLIN);

                    // 设置自定义名称和生命值
                    pigZombie.setCustomName("§6变异僵尸01");
                    pigZombie.setCustomNameVisible(false); // 默认隐藏，由EntityNameDisplayListener控制显示
                    pigZombie.setMaxHealth(100.0);
                    pigZombie.setHealth(100.0);

                    // 创建锋利1的铁剑
                    ItemStack sword = new ItemStack(Material.IRON_SWORD);
                    sword.addEnchantment(Enchantment.SHARPNESS, 1);

                    pigZombie.getEquipment().setItemInMainHand(sword);
                    pigZombie.getEquipment().setItemInMainHandDropChance(0.0F);

                    // 设置皮革套装
                    pigZombie.getEquipment().setHelmet(new ItemStack(Material.LEATHER_HELMET));
                    pigZombie.getEquipment().setChestplate(new ItemStack(Material.LEATHER_CHESTPLATE));
                    pigZombie.getEquipment().setLeggings(new ItemStack(Material.LEATHER_LEGGINGS));
                    pigZombie.getEquipment().setBoots(new ItemStack(Material.LEATHER_BOOTS));

                    // 添加速度2和跳跃提升2的效果
                    pigZombie.addPotionEffect(new PotionEffect(PotionEffectType.SPEED, Integer.MAX_VALUE, 1));
                    pigZombie.addPotionEffect(new PotionEffect(PotionEffectType.JUMP_BOOST, Integer.MAX_VALUE, 1));

                    // 标记为变异僵尸01
                    pigZombie.setMetadata("mutantZombie01", new org.bukkit.metadata.FixedMetadataValue(plugin, true));

                    // 添加idcZombieEntity元数据标记，确保实体被识别为僵尸
                    pigZombie.setMetadata("idcZombieEntity", new org.bukkit.metadata.FixedMetadataValue(plugin, true));
                    logger.info("已为变异僵尸01添加idcZombieEntity标记");

                    // 添加游戏实体标记，确保能被WindowManager识别
                    pigZombie.setMetadata("gameEntity", new org.bukkit.metadata.FixedMetadataValue(plugin, true));

                    // 启用敌对AI，确保主动攻击玩家
                    if (plugin instanceof org.Ver_zhzh.deathZombieV4.DeathZombieV4) {
                        org.Ver_zhzh.deathZombieV4.DeathZombieV4 dzPlugin = (org.Ver_zhzh.deathZombieV4.DeathZombieV4) plugin;
                        if (dzPlugin.getHostileAIManager() != null) {
                            dzPlugin.getHostileAIManager().enableHostileAI(pigZombie);
                        }
                    }

                    // 启动粒子效果任务
                    startMutantZombie01ParticleTask(pigZombie);

                    return true;

                case "idc2": // 变异僵尸02（骷髅变种）
                    Skeleton skeleton = (Skeleton) location.getWorld().spawnEntity(location, EntityType.SKELETON);

                    // 设置骷髅属性
                    skeleton.setCustomName("§d变异僵尸02");
                    skeleton.setCustomNameVisible(false); // 默认隐藏，由EntityNameDisplayListener控制显示
                    skeleton.setMaxHealth(100.0);
                    skeleton.setHealth(100.0);

                    // 设置装备
                    ItemStack bow = new ItemStack(Material.BOW);
                    bow.addEnchantment(Enchantment.PUNCH, 2);
                    skeleton.getEquipment().setItemInMainHand(bow);
                    skeleton.getEquipment().setItemInMainHandDropChance(0.0F);
                    skeleton.getEquipment().setHelmet(new ItemStack(Material.CREEPER_HEAD));
                    skeleton.getEquipment().setHelmetDropChance(0.0F);

                    // 添加效果
                    skeleton.addPotionEffect(new PotionEffect(PotionEffectType.SPEED, Integer.MAX_VALUE, 1));

                    // 添加idcZombieEntity元数据标记，确保实体被识别为僵尸
                    skeleton.setMetadata("idcZombieEntity", new org.bukkit.metadata.FixedMetadataValue(plugin, true));
                    logger.info("已为变异僵尸02添加idcZombieEntity标记");

                    // 添加游戏实体标记，确保能被WindowManager识别
                    skeleton.setMetadata("gameEntity", new org.bukkit.metadata.FixedMetadataValue(plugin, true));

                    // 启用敌对AI，确保主动攻击玩家
                    if (plugin instanceof org.Ver_zhzh.deathZombieV4.DeathZombieV4) {
                        org.Ver_zhzh.deathZombieV4.DeathZombieV4 dzPlugin = (org.Ver_zhzh.deathZombieV4.DeathZombieV4) plugin;
                        if (dzPlugin.getHostileAIManager() != null) {
                            dzPlugin.getHostileAIManager().enableHostileAI(skeleton);
                        }
                    }

                    // 启动特殊粒子效果
                    startMutantParticleEffects(skeleton);

                    // 激活特殊能力
                    activateSpecialAbilities(location, entityId);

                    return true;

                case "idc3": // 变异烈焰人
                    Blaze blaze = (Blaze) location.getWorld().spawnEntity(location, EntityType.BLAZE);

                    // 设置属性
                    blaze.setCustomName("§c变异烈焰人");
                    blaze.setCustomNameVisible(false); // 默认隐藏，由EntityNameDisplayListener控制显示
                    blaze.setMaxHealth(100.0);
                    blaze.setHealth(100.0);

                    // 添加效果
                    blaze.addPotionEffect(new PotionEffect(PotionEffectType.SPEED, Integer.MAX_VALUE, 3));

                    // 标记为变异烈焰人
                    blaze.setMetadata("isMutantBlaze", new org.bukkit.metadata.FixedMetadataValue(plugin, true));

                    // 添加idcZombieEntity元数据标记，确保实体被识别为僵尸
                    blaze.setMetadata("idcZombieEntity", new org.bukkit.metadata.FixedMetadataValue(plugin, true));
                    logger.info("已为变异烈焰人添加idcZombieEntity标记");

                    // 添加游戏实体标记，确保能被WindowManager识别
                    blaze.setMetadata("gameEntity", new org.bukkit.metadata.FixedMetadataValue(plugin, true));

                    // 启用敌对AI，确保主动攻击玩家
                    if (plugin instanceof org.Ver_zhzh.deathZombieV4.DeathZombieV4) {
                        org.Ver_zhzh.deathZombieV4.DeathZombieV4 dzPlugin = (org.Ver_zhzh.deathZombieV4.DeathZombieV4) plugin;
                        if (dzPlugin.getHostileAIManager() != null) {
                            dzPlugin.getHostileAIManager().enableHostileAI(blaze);
                        }
                    }

                    // 开始变异烈焰人的特效任务
                    startMutantBlazeEffects(blaze);

                    // 激活特殊能力
                    activateSpecialAbilities(location, entityId);

                    return true;

                case "idc4": // 变异爬行者
                    Creeper creeper = (Creeper) location.getWorld().spawnEntity(location, EntityType.CREEPER);

                    // 设置为带电爬行者
                    creeper.setPowered(true);

                    // 设置属性
                    creeper.setCustomName("§b变异爬行者");
                    creeper.setCustomNameVisible(false); // 默认隐藏，由EntityNameDisplayListener控制显示
                    creeper.setMaxHealth(80.0);
                    creeper.setHealth(80.0);
                    creeper.setExplosionRadius(12);

                    // 添加效果
                    creeper.addPotionEffect(new PotionEffect(PotionEffectType.SPEED, Integer.MAX_VALUE, 4));

                    // 标记为变异爬行者
                    creeper.setMetadata("isMutantCreeper", new org.bukkit.metadata.FixedMetadataValue(plugin, true));

                    // 添加idcZombieEntity元数据标记，确保实体被识别为僵尸
                    creeper.setMetadata("idcZombieEntity", new org.bukkit.metadata.FixedMetadataValue(plugin, true));
                    logger.info("已为变异爬行者添加idcZombieEntity标记");

                    // 添加游戏实体标记，确保能被WindowManager识别
                    creeper.setMetadata("gameEntity", new org.bukkit.metadata.FixedMetadataValue(plugin, true));

                    // 启用敌对AI，确保主动攻击玩家
                    if (plugin instanceof org.Ver_zhzh.deathZombieV4.DeathZombieV4) {
                        org.Ver_zhzh.deathZombieV4.DeathZombieV4 dzPlugin = (org.Ver_zhzh.deathZombieV4.DeathZombieV4) plugin;
                        if (dzPlugin.getHostileAIManager() != null) {
                            dzPlugin.getHostileAIManager().enableHostileAI(creeper);
                        }
                    }

                    // 开始变异爬行者的特殊任务
                    startMutantCreeperTasks(creeper);

                    // 激活特殊能力
                    activateSpecialAbilities(location, entityId);

                    return true;

                case "idc5": // 变异末影螨
                    Endermite endermite = (Endermite) location.getWorld().spawnEntity(location, EntityType.ENDERMITE);

                    // 设置属性
                    endermite.setCustomName("§5变异末影螨");
                    endermite.setCustomNameVisible(false); // 默认隐藏，由EntityNameDisplayListener控制显示
                    endermite.setMaxHealth(150.0);
                    endermite.setHealth(150.0);

                    // 添加效果
                    endermite.addPotionEffect(new PotionEffect(PotionEffectType.SPEED, Integer.MAX_VALUE, 4));
                    endermite.addPotionEffect(new PotionEffect(PotionEffectType.REGENERATION, Integer.MAX_VALUE, 1));

                    // 添加idcZombieEntity元数据标记，确保实体被识别为僵尸
                    endermite.setMetadata("idcZombieEntity", new org.bukkit.metadata.FixedMetadataValue(plugin, true));
                    logger.info("已为变异末影螨添加idcZombieEntity标记");

                    // 添加游戏实体标记，确保能被WindowManager识别
                    endermite.setMetadata("gameEntity", new org.bukkit.metadata.FixedMetadataValue(plugin, true));

                    // 启用敌对AI，确保主动攻击玩家
                    if (plugin instanceof org.Ver_zhzh.deathZombieV4.DeathZombieV4) {
                        org.Ver_zhzh.deathZombieV4.DeathZombieV4 dzPlugin = (org.Ver_zhzh.deathZombieV4.DeathZombieV4) plugin;
                        if (dzPlugin.getHostileAIManager() != null) {
                            dzPlugin.getHostileAIManager().enableHostileAI(endermite);
                        }
                    }

                    // 开始变异末影螨的特殊任务
                    startMutantEndermiteTasks(endermite);

                    return true;

                case "idc6": // 变异蜘蛛
                    CaveSpider spider = (CaveSpider) location.getWorld().spawnEntity(location, EntityType.CAVE_SPIDER);

                    // 设置属性
                    spider.setCustomName("§3变异蜘蛛");
                    spider.setCustomNameVisible(false); // 默认隐藏，由EntityNameDisplayListener控制显示
                    spider.setMaxHealth(300.0);
                    spider.setHealth(300.0);

                    // 添加效果
                    spider.addPotionEffect(new PotionEffect(PotionEffectType.SPEED, Integer.MAX_VALUE, 3));
                    spider.addPotionEffect(new PotionEffect(PotionEffectType.STRENGTH, Integer.MAX_VALUE, 2));

                    // 添加idcZombieEntity元数据标记，确保实体被识别为僵尸
                    spider.setMetadata("idcZombieEntity", new org.bukkit.metadata.FixedMetadataValue(plugin, true));
                    logger.info("已为变异蜘蛛添加idcZombieEntity标记");

                    // 添加游戏实体标记，确保能被WindowManager识别
                    spider.setMetadata("gameEntity", new org.bukkit.metadata.FixedMetadataValue(plugin, true));

                    // 启用敌对AI，确保主动攻击玩家
                    if (plugin instanceof org.Ver_zhzh.deathZombieV4.DeathZombieV4) {
                        org.Ver_zhzh.deathZombieV4.DeathZombieV4 dzPlugin = (org.Ver_zhzh.deathZombieV4.DeathZombieV4) plugin;
                        if (dzPlugin.getHostileAIManager() != null) {
                            dzPlugin.getHostileAIManager().enableHostileAI(spider);
                        }
                    }

                    // 开始变异蜘蛛的特殊任务
                    startMutantSpiderTasks(spider);

                    // 激活特殊能力
                    activateSpecialAbilities(location, entityId);

                    return true;

                case "idc13": // 变异僵尸3
                    // 直接在这里实现变异僵尸3的生成，而不是通过反射调用
                    logger.info("直接生成变异僵尸3实体");

                    try {
                        // 检查服务器是否支持BOGGED实体类型（Minecraft 1.21+）
                        boolean hasBoggedType = false;
                        try {
                            // 尝试获取BOGGED实体类型
                            EntityType boggedType = EntityType.valueOf("BOGGED");
                            hasBoggedType = true;
                            logger.info("服务器支持BOGGED实体类型，使用原生BOGGED实体");
                        } catch (IllegalArgumentException e) {
                            // 服务器版本不支持BOGGED实体类型
                            logger.warning("服务器不支持BOGGED实体类型（需要Minecraft 1.21+），将使用STRAY作为替代");
                            hasBoggedType = false;
                        }

                        // 根据服务器支持情况生成实体
                        Entity entity;
                        if (hasBoggedType) {
                            // 使用原生BOGGED实体类型
                            entity = location.getWorld().spawnEntity(location, EntityType.valueOf("BOGGED"));
                        } else {
                            // 使用STRAY作为替代
                            entity = location.getWorld().spawnEntity(location, EntityType.STRAY);
                        }

                        // 设置变异僵尸3属性
                        entity.setCustomName("§3变异僵尸3");
                        entity.setCustomNameVisible(false); // 默认隐藏，由EntityNameDisplayListener控制显示

                        // 设置生命值
                        if (entity instanceof org.bukkit.entity.LivingEntity) {
                            org.bukkit.entity.LivingEntity livingEntity = (org.bukkit.entity.LivingEntity) entity;
                            livingEntity.setMaxHealth(200.0);
                            livingEntity.setHealth(200.0);

                            // 创建强力2冲击1的弓
                            ItemStack bowItem = new ItemStack(Material.BOW);
                            org.bukkit.inventory.meta.ItemMeta bowMeta = bowItem.getItemMeta();
                            bowMeta.setDisplayName("§3变异僵尸3之弓");
                            bowMeta.addEnchant(Enchantment.SHARPNESS, 3, true); // 锋利附魔
                            bowMeta.addEnchant(Enchantment.PUNCH, 1, true); // 冲击附魔
                            bowItem.setItemMeta(bowMeta);

                            // 创建黑色皮革靴子
                            ItemStack blackBoots = new ItemStack(Material.LEATHER_BOOTS);
                            org.bukkit.inventory.meta.LeatherArmorMeta bootsMeta = (org.bukkit.inventory.meta.LeatherArmorMeta) blackBoots.getItemMeta();
                            bootsMeta.setColor(org.bukkit.Color.BLACK);
                            bootsMeta.addEnchant(Enchantment.PROTECTION, 2, true);
                            blackBoots.setItemMeta(bootsMeta);

                            // 设置装备
                            livingEntity.getEquipment().setItemInMainHand(bowItem);
                            livingEntity.getEquipment().setBoots(blackBoots);

                            // 设置装备不掉落
                            livingEntity.getEquipment().setItemInMainHandDropChance(0.0F);
                            livingEntity.getEquipment().setBootsDropChance(0.0F);

                            // 添加速度2效果
                            livingEntity.addPotionEffect(new PotionEffect(PotionEffectType.SPEED, Integer.MAX_VALUE, 1)); // 速度II
                        }

                        // 给实体添加元数据标记
                        entity.setMetadata("swampBoggedMonster", new org.bukkit.metadata.FixedMetadataValue(plugin, true));

                        // 添加idcZombieEntity元数据标记，确保实体被识别为僵尸
                        entity.setMetadata("idcZombieEntity", new org.bukkit.metadata.FixedMetadataValue(plugin, true));
                        logger.info("已为变异僵尸3添加idcZombieEntity标记");

                        // 添加游戏实体标记，确保能被WindowManager识别
                        entity.setMetadata("gameEntity", new org.bukkit.metadata.FixedMetadataValue(plugin, true));

                        // 启用敌对AI，确保主动攻击玩家
                        if (entity instanceof org.bukkit.entity.LivingEntity) {
                            if (plugin instanceof org.Ver_zhzh.deathZombieV4.DeathZombieV4) {
                                org.Ver_zhzh.deathZombieV4.DeathZombieV4 dzPlugin = (org.Ver_zhzh.deathZombieV4.DeathZombieV4) plugin;
                                if (dzPlugin.getHostileAIManager() != null) {
                                    dzPlugin.getHostileAIManager().enableHostileAI((org.bukkit.entity.LivingEntity) entity);
                                }
                            }
                        }

                        // 激活特殊能力
                        activateSpecialAbilities(location, entityId);

                        logger.info("成功生成变异僵尸3实体");
                        return true;
                    } catch (Exception e) {
                        logger.severe("生成变异僵尸3实体时发生错误: " + e.getMessage());
                        e.printStackTrace();
                        return false;
                    }

                case "idc7": // 灾厄卫道士
                case "idc8": // 灾厄唤魔者
                case "idc9": // 灾厄劫掠兽
                case "idc10": // 变异僵尸马
                case "idc11": // 变异岩浆怪
                case "idc12": // 变异尸壳
                case "idc14": // 变异僵尸04
                case "idc15": // 鲜血猪灵
                case "idc16": // 暗影潜影贝
                case "idc17": // 变异雪傀儡
                case "idc19": // 变异僵尸Max
                case "idc20": // 灵魂坚守者
                case "idc21": // 凋零领主
                case "idc22": // 异变之王
                    // 对于高级实体，使用直接生成方法，避免卡顿和提示信息
                    logger.info("使用直接生成方法生成高级实体: " + entityId);
                    if (customZombie != null) {
                        try {
                            boolean success = spawnAdvancedEntityDirect(location, entityId);

                            // 如果生成成功，为附近的实体添加idcZombieEntity标记
                            if (success) {
                                addIdcZombieEntityMetadataToNearbyEntities(location, entityId);
                            }

                            return success;
                        } catch (Exception e) {
                            logger.severe("生成高级实体时发生异常: " + e.getMessage());
                            e.printStackTrace();
                            return false;
                        }
                    } else {
                        logger.warning("CustomZombie实例为null，无法生成高级实体");
                        return false;
                    }

                case "idc18": // 变异铁傀儡
                    logger.info("===== 开始生成变异铁傀儡(idc18) - spawnOtherEntityAtLocation =====");

                    try {
                        // 检查MutantIronGolemManager
                        Object manager = customZombie.getMutantIronGolemManager();
                        if (manager == null) {
                            logger.severe("无法生成变异铁傀儡：MutantIronGolemManager为null");
                            return false;
                        }

                        logger.info("MutantIronGolemManager实例存在，准备调用spawnMutantIronGolem方法");

                        // 调用spawnMutantIronGolem方法
                        boolean result = customZombie.spawnMutantIronGolem(location);

                        if (result) {
                            logger.info("变异铁傀儡生成成功");
                            // 为附近的铁傀儡实体添加idcZombieEntity标记
                            addIdcZombieEntityMetadataToNearbyEntities(location, entityId);
                        } else {
                            logger.severe("变异铁傀儡生成失败：spawnMutantIronGolem方法返回false");
                        }

                        return result;
                    } catch (Exception e) {
                        logger.severe("生成变异铁傀儡时发生异常: " + e.getMessage());
                        e.printStackTrace();
                        return false;
                    }

                default:
                    logger.warning("未知的实体ID: " + entityId);
                    return false;
            }
        } catch (Exception e) {
            logger.severe("在位置生成实体时出错: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 打开自定义僵尸GUI菜单
     *
     * @param player 玩家
     * @return 是否成功打开
     */
    public boolean openGUI(Player player) {
        if (guiManager == null) {
            logger.severe("ZombieGUIManager未初始化，无法打开GUI");
            player.sendMessage(ChatColor.RED + "GUI管理器未初始化，无法打开GUI界面");
            return false;
        }

        try {
            // 直接调用ZombieGUIManager的openGUI方法
            guiManager.openGUI(player);
            return true;
        } catch (Exception e) {
            logger.severe("打开GUI时出错: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 创建感染者1 NPC
     *
     * @param location 生成位置
     * @return 是否成功创建
     */
    public boolean createInfectedNPC1(Location location) {
        return createNPC(location, "idn1");
    }

    /**
     * 创建感染者2号 NPC
     *
     * @param location 生成位置
     * @return 是否成功创建
     */
    public boolean createInfectedNPC2(Location location) {
        return createNPC(location, "idn2");
    }

    /**
     * 创建感染者农民 NPC
     *
     * @param location 生成位置
     * @return 是否成功创建
     */
    public boolean createInfectedFarmer(Location location) {
        return createNPC(location, "idn3");
    }

    /**
     * 创建感染者居民 NPC
     *
     * @param location 生成位置
     * @return 是否成功创建
     */
    public boolean createInfectedResident(Location location) {
        return createNPC(location, "idn4");
    }

    /**
     * 创建感染猪 NPC
     *
     * @param location 生成位置
     * @return 是否成功创建
     */
    public boolean createInfectedPig(Location location) {
        return createNPC(location, "idn5");
    }

    /**
     * 在指定位置创建NPC
     *
     * @param location 生成位置
     * @param npcId NPC ID
     * @return 是否创建成功
     */
    public boolean createNPC(Location location, String npcId) {
        try {
            if (location == null) {
                logger.severe("创建NPC失败: 位置不能为null");
                return false;
            }

            logger.info("尝试在位置 " + formatLocation(location) + " 创建NPC类型: " + npcId);

            // 检查Citizens插件是否可用
            if (org.bukkit.Bukkit.getPluginManager().getPlugin("Citizens") == null) {
                logger.warning("Citizens插件不可用，尝试使用替代方法创建NPC");
                return createFallbackNPC(location, npcId);
            }

            // 使用CustomZombie类的createNPCAtLocation方法直接在指定位置创建NPC
            boolean success = customZombie.createNPCAtLocation(npcId, location);
            if (!success) {
                logger.warning("CustomZombie创建NPC失败，尝试使用替代方法");
                return createFallbackNPC(location, npcId);
            }
            return true;
        } catch (Exception e) {
            logger.severe("创建NPC时发生错误: " + e.getMessage());
            e.printStackTrace();
            // 尝试使用替代方法
            return createFallbackNPC(location, npcId);
        }
    }

    /**
     * 当Citizens插件不可用或创建NPC失败时的替代方法
     *
     * @param location 生成位置
     * @param npcId NPC ID
     * @return 是否创建成功
     */
    private boolean createFallbackNPC(Location location, String npcId) {
        try {
            logger.info("使用替代方法创建NPC: " + npcId);

            // 根据不同的NPC ID生成不同类型的实体
            Entity entity;
            String npcName = "感染者";

            // 根据不同的NPC ID选择不同的实体类型和名称
            EntityType entityType = EntityType.ZOMBIE;
            boolean isBaby = false;

            // 根据不同的NPC ID选择不同的实体类型和名称
            switch (npcId) {
                case "idn1":
                    npcName = "感染者1";
                    entityType = EntityType.ZOMBIE;
                    break;
                case "idn2":
                    npcName = "感染者2";
                    entityType = EntityType.ZOMBIE;
                    isBaby = true;
                    break;
                case "idn3":
                    npcName = "感染者农民";
                    entityType = EntityType.ZOMBIE_VILLAGER;
                    break;
                case "idn4":
                    npcName = "感染者居民";
                    entityType = EntityType.ZOMBIE_VILLAGER;
                    break;
                case "idn5":
                    npcName = "感染猪";
                    entityType = EntityType.PIG;
                    break;
                default:
                    npcName = "感染者";
                    entityType = EntityType.ZOMBIE;
                    break;
            }

            // 生成实体
            entity = location.getWorld().spawnEntity(location, entityType);

            // 设置实体属性
            entity.setCustomName("§c" + npcName);
            entity.setCustomNameVisible(false); // 默认隐藏，由EntityNameDisplayListener控制显示

            // 对于僵尸类型，设置是否为幼年僵尸
            if (entity instanceof Zombie) {
                ((Zombie) entity).setBaby(isBaby);
            }

            // 添加元数据标记这是由插件生成的NPC
            entity.setMetadata("DeathZombieNPC", new org.bukkit.metadata.FixedMetadataValue(plugin, npcId));
            logger.info("成功使用替代方法创建" + npcId + "类型的NPC");
            return true;

        } catch (Exception e) {
            logger.severe("使用替代方法创建NPC时发生错误: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 检查CustomZombie是否可用
     *
     * @return CustomZombie是否可用
     */
    public boolean isCustomZombieAvailable() {
        return customZombie != null && particleHelper != null;
    }

    /**
     * 获取CustomZombie实例
     *
     * @return CustomZombie实例
     */
    public CustomZombie getCustomZombie() {
        return customZombie;
    }

    /**
     * 为附近的实体添加idcZombieEntity元数据标记
     * 用于确保高级变异实体能够破坏窗户
     *
     * @param location 生成位置
     * @param entityId 实体ID
     */
    private void addIdcZombieEntityMetadataToNearbyEntities(Location location, String entityId) {
        try {
            // 在生成位置附近5格范围内查找新生成的实体
            for (Entity entity : location.getWorld().getNearbyEntities(location, 5, 5, 5)) {
                // 跳过玩家
                if (entity instanceof Player) {
                    continue;
                }

                // 检查是否是生物实体
                if (!(entity instanceof LivingEntity)) {
                    continue;
                }

                // 检查实体是否已经有idcZombieEntity标记
                if (entity.hasMetadata("idcZombieEntity")) {
                    continue;
                }

                // 检查实体名称是否包含变异、idc等关键词，或者是特定的实体类型
                boolean shouldAddMetadata = false;

                if (entity.getCustomName() != null) {
                    String customName = entity.getCustomName();
                    if (customName.contains("变异") || customName.contains("idc") ||
                        customName.contains("灾厄") || customName.contains("凋零") ||
                        customName.contains("暗影") || customName.contains("鲜血") ||
                        customName.contains("异变") || customName.contains("灵魂")) {
                        shouldAddMetadata = true;
                    }
                }

                // 检查特定的实体类型
                EntityType type = entity.getType();
                if (type == EntityType.VINDICATOR || type == EntityType.EVOKER ||
                    type == EntityType.RAVAGER || type == EntityType.ZOMBIE_HORSE ||
                    type == EntityType.MAGMA_CUBE || type == EntityType.HUSK ||
                    type == EntityType.ZOMBIFIED_PIGLIN || type == EntityType.SHULKER ||
                    type == EntityType.SNOW_GOLEM || type == EntityType.WITHER_SKELETON ||
                    type == EntityType.WITHER || type == EntityType.ENDER_DRAGON ||
                    type == EntityType.IRON_GOLEM || type == EntityType.PILLAGER ||
                    type == EntityType.DROWNED || type == EntityType.PIGLIN) {
                    shouldAddMetadata = true;
                }

                // 检查是否是Warden实体类型（如果服务器支持）
                try {
                    EntityType wardenType = EntityType.valueOf("WARDEN");
                    if (type == wardenType) {
                        shouldAddMetadata = true;
                    }
                } catch (IllegalArgumentException e) {
                    // 服务器不支持Warden实体类型，忽略
                }

                if (shouldAddMetadata) {
                    // 添加idcZombieEntity标记
                    entity.setMetadata("idcZombieEntity", new org.bukkit.metadata.FixedMetadataValue(plugin, true));
                    logger.info("为高级变异实体 " + entity.getType() +
                        " (名称: " + (entity.getCustomName() != null ? entity.getCustomName() : "无") +
                        ") 添加了idcZombieEntity标记，确保其能够破坏窗户");
                }
            }
        } catch (Exception e) {
            logger.warning("为附近实体添加idcZombieEntity标记时出错: " + e.getMessage());
        }
    }

    /**
     * 直接生成灾厄卫道士 - 使用AdvancedEntitySpawner的正确实现
     */
    private boolean spawnVindicatorDirect(Location location) {
        try {
            logger.info("开始生成灾厄卫道士 (idc7) - 使用AdvancedEntitySpawner");

            // 检查AdvancedEntitySpawner是否可用
            if (advancedEntitySpawner == null) {
                logger.severe("AdvancedEntitySpawner未初始化，无法生成灾厄卫道士");
                return false;
            }

            // 使用AdvancedEntitySpawner的正确方法生成灾厄卫道士
            boolean success = advancedEntitySpawner.spawnDisasterGuardian(location);

            if (success) {
                logger.info("灾厄卫道士生成成功");
            } else {
                logger.warning("灾厄卫道士生成失败");
            }

            return success;
        } catch (Exception e) {
            logger.severe("生成灾厄卫道士时发生异常: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 直接生成灾厄唤魔者 - 使用AdvancedEntitySpawner的正确实现
     */
    private boolean spawnEvokerDirect(Location location) {
        try {
            logger.info("开始生成灾厄唤魔者 (idc8) - 使用AdvancedEntitySpawner");

            // 检查AdvancedEntitySpawner是否可用
            if (advancedEntitySpawner == null) {
                logger.severe("AdvancedEntitySpawner未初始化，无法生成灾厄唤魔者");
                return false;
            }

            // 使用AdvancedEntitySpawner的正确方法生成灾厄唤魔者
            boolean success = advancedEntitySpawner.spawnDisasterSummoner(location);

            if (success) {
                logger.info("灾厄唤魔者生成成功");
            } else {
                logger.warning("灾厄唤魔者生成失败");
            }

            return success;
        } catch (Exception e) {
            logger.severe("生成灾厄唤魔者时发生异常: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 直接生成灾厄劫掠兽 - 使用AdvancedEntitySpawner的正确实现
     */
    private boolean spawnRavagerDirect(Location location) {
        try {
            logger.info("开始生成灾厄劫掠兽 (idc9) - 使用AdvancedEntitySpawner");

            // 检查AdvancedEntitySpawner是否可用
            if (advancedEntitySpawner == null) {
                logger.severe("AdvancedEntitySpawner未初始化，无法生成灾厄劫掠兽");
                return false;
            }

            // 使用AdvancedEntitySpawner的正确方法生成灾厄劫掠兽
            boolean success = advancedEntitySpawner.spawnDisasterRavagerBeast(location);

            if (success) {
                logger.info("灾厄劫掠兽生成成功");
            } else {
                logger.warning("灾厄劫掠兽生成失败");
            }

            return success;
        } catch (Exception e) {
            logger.severe("生成灾厄劫掠兽时发生异常: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 直接生成变异僵尸马 - 使用AdvancedEntitySpawner的正确实现
     */
    private boolean spawnZombieHorseDirect(Location location) {
        try {
            logger.info("开始生成变异僵尸马 (idc10) - 使用AdvancedEntitySpawner");

            // 检查AdvancedEntitySpawner是否可用
            if (advancedEntitySpawner == null) {
                logger.severe("AdvancedEntitySpawner未初始化，无法生成变异僵尸马");
                return false;
            }

            // 使用AdvancedEntitySpawner的正确方法生成变异僵尸马
            boolean success = advancedEntitySpawner.spawnMutantZombieHorse(location);

            if (success) {
                logger.info("变异僵尸马生成成功");
            } else {
                logger.warning("变异僵尸马生成失败");
            }

            return success;
        } catch (Exception e) {
            logger.severe("生成变异僵尸马时发生异常: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 直接生成变异岩浆怪 - 使用AdvancedEntitySpawner的正确实现
     */
    private boolean spawnMagmaCubeDirect(Location location) {
        try {
            logger.info("开始生成变异岩浆怪 (idc11) - 使用AdvancedEntitySpawner");

            // 检查AdvancedEntitySpawner是否可用
            if (advancedEntitySpawner == null) {
                logger.severe("AdvancedEntitySpawner未初始化，无法生成变异岩浆怪");
                return false;
            }

            // 使用AdvancedEntitySpawner的正确方法生成变异岩浆怪
            boolean success = advancedEntitySpawner.spawnMutantMagmaCube(location);

            if (success) {
                logger.info("变异岩浆怪生成成功");
            } else {
                logger.warning("变异岩浆怪生成失败");
            }

            return success;
        } catch (Exception e) {
            logger.severe("生成变异岩浆怪时发生异常: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 直接生成变异尸壳 - 使用AdvancedEntitySpawner的正确实现
     */
    private boolean spawnHuskDirect(Location location) {
        try {
            logger.info("开始生成变异尸壳 (idc12) - 使用AdvancedEntitySpawner");

            // 检查AdvancedEntitySpawner是否可用
            if (advancedEntitySpawner == null) {
                logger.severe("AdvancedEntitySpawner未初始化，无法生成变异尸壳");
                return false;
            }

            // 使用AdvancedEntitySpawner的正确方法生成变异尸壳
            boolean success = advancedEntitySpawner.spawnMutantHusk(location);

            if (success) {
                logger.info("变异尸壳生成成功");
            } else {
                logger.warning("变异尸壳生成失败");
            }

            return success;
        } catch (Exception e) {
            logger.severe("生成变异尸壳时发生异常: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 格式化位置信息
     *
     * @param location 位置
     * @return 格式化的字符串
     */
    private String formatLocation(Location location) {
        if (location == null) {
            return "null";
        }
        return String.format("世界=%s, x=%.2f, y=%.2f, z=%.2f",
                location.getWorld() != null ? location.getWorld().getName() : "null",
                location.getX(), location.getY(), location.getZ());
    }

    /**
     * 直接生成变异僵尸04 - 使用AdvancedEntitySpawner的正确实现
     */
    private boolean spawnMutantZombie04Direct(Location location) {
        try {
            logger.info("开始生成变异僵尸04 (idc14) - 使用AdvancedEntitySpawner");

            // 检查AdvancedEntitySpawner是否可用
            if (advancedEntitySpawner == null) {
                logger.severe("AdvancedEntitySpawner未初始化，无法生成变异僵尸04");
                return false;
            }

            // 使用AdvancedEntitySpawner的正确方法生成变异僵尸04
            boolean success = advancedEntitySpawner.spawnMutantZombie04(location);

            if (success) {
                logger.info("变异僵尸04生成成功");
            } else {
                logger.warning("变异僵尸04生成失败");
            }

            return success;
        } catch (Exception e) {
            logger.severe("生成变异僵尸04时发生异常: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 直接生成鲜血猪灵 - 使用AdvancedEntitySpawner的正确实现
     */
    private boolean spawnBloodPiglinDirect(Location location) {
        try {
            logger.info("开始生成鲜血猪灵 (idc15) - 使用AdvancedEntitySpawner");

            // 检查AdvancedEntitySpawner是否可用
            if (advancedEntitySpawner == null) {
                logger.severe("AdvancedEntitySpawner未初始化，无法生成鲜血猪灵");
                return false;
            }

            // 使用AdvancedEntitySpawner的正确方法生成鲜血猪灵
            boolean success = advancedEntitySpawner.spawnBloodPiglin(location);

            if (success) {
                logger.info("鲜血猪灵生成成功");
            } else {
                logger.warning("鲜血猪灵生成失败");
            }

            return success;
        } catch (Exception e) {
            logger.severe("生成鲜血猪灵时发生异常: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 直接生成暗影潜影贝 - 使用AdvancedEntitySpawner的正确实现
     */
    private boolean spawnShadowShulkerDirect(Location location) {
        try {
            logger.info("开始生成暗影潜影贝 (idc16) - 使用AdvancedEntitySpawner");

            // 检查AdvancedEntitySpawner是否可用
            if (advancedEntitySpawner == null) {
                logger.severe("AdvancedEntitySpawner未初始化，无法生成暗影潜影贝");
                return false;
            }

            // 使用AdvancedEntitySpawner的正确方法生成暗影潜影贝
            boolean success = advancedEntitySpawner.spawnShadowShulker(location);

            if (success) {
                logger.info("暗影潜影贝生成成功");
            } else {
                logger.warning("暗影潜影贝生成失败");
            }

            return success;
        } catch (Exception e) {
            logger.severe("生成暗影潜影贝时发生异常: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 直接生成变异雪傀儡 - 使用AdvancedEntitySpawner的正确实现
     */
    private boolean spawnMutantSnowGolemDirect(Location location) {
        try {
            logger.info("开始生成变异雪傀儡 (idc17) - 使用AdvancedEntitySpawner");

            // 检查AdvancedEntitySpawner是否可用
            if (advancedEntitySpawner == null) {
                logger.severe("AdvancedEntitySpawner未初始化，无法生成变异雪傀儡");
                return false;
            }

            // 使用AdvancedEntitySpawner的正确方法生成变异雪傀儡
            boolean success = advancedEntitySpawner.spawnMutantSnowman(location);

            if (success) {
                logger.info("变异雪傀儡生成成功");
            } else {
                logger.warning("变异雪傀儡生成失败");
            }

            return success;
        } catch (Exception e) {
            logger.severe("生成变异雪傀儡时发生异常: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 直接生成变异僵尸Max - 使用AdvancedEntitySpawner的正确实现
     */
    private boolean spawnMutantZombieMaxDirect(Location location) {
        try {
            logger.info("开始生成变异僵尸Max (idc19) - 使用AdvancedEntitySpawner");

            // 检查AdvancedEntitySpawner是否可用
            if (advancedEntitySpawner == null) {
                logger.severe("AdvancedEntitySpawner未初始化，无法生成变异僵尸Max");
                return false;
            }

            // 使用AdvancedEntitySpawner的正确方法生成变异僵尸Max
            boolean success = advancedEntitySpawner.spawnMutantZombieMax(location);

            if (success) {
                logger.info("变异僵尸Max生成成功");
            } else {
                logger.warning("变异僵尸Max生成失败");
            }

            return success;
        } catch (Exception e) {
            logger.severe("生成变异僵尸Max时发生异常: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 直接生成灵魂坚守者 - 使用AdvancedEntitySpawner的正确实现
     */
    private boolean spawnSoulGuardianDirect(Location location) {
        try {
            logger.info("开始生成灵魂坚守者 (idc20) - 使用AdvancedEntitySpawner");

            // 检查AdvancedEntitySpawner是否可用
            if (advancedEntitySpawner == null) {
                logger.severe("AdvancedEntitySpawner未初始化，无法生成灵魂坚守者");
                return false;
            }

            // 使用AdvancedEntitySpawner的正确方法生成灵魂坚守者
            boolean success = advancedEntitySpawner.spawnSoulGuardian(location);

            if (success) {
                logger.info("灵魂坚守者生成成功");
            } else {
                logger.warning("灵魂坚守者生成失败");
            }

            return success;
        } catch (Exception e) {
            logger.severe("生成灵魂坚守者时发生异常: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 直接生成凋零领主 - 使用AdvancedEntitySpawner的正确实现
     */
    private boolean spawnWitherLordDirect(Location location) {
        try {
            logger.info("开始生成凋零领主 (idc21) - 使用AdvancedEntitySpawner");

            // 检查AdvancedEntitySpawner是否可用
            if (advancedEntitySpawner == null) {
                logger.severe("AdvancedEntitySpawner未初始化，无法生成凋零领主");
                return false;
            }

            // 使用AdvancedEntitySpawner的正确方法生成凋零领主
            boolean success = advancedEntitySpawner.spawnWitherLord(location);

            if (success) {
                logger.info("凋零领主生成成功");
            } else {
                logger.warning("凋零领主生成失败");
            }

            return success;
        } catch (Exception e) {
            logger.severe("生成凋零领主时发生异常: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 直接生成异变之王 - 使用AdvancedEntitySpawner的正确实现
     */
    private boolean spawnMutationKingDirect(Location location) {
        try {
            logger.info("开始生成异变之王 (idc22) - 使用AdvancedEntitySpawner");

            // 检查AdvancedEntitySpawner是否可用
            if (advancedEntitySpawner == null) {
                logger.severe("AdvancedEntitySpawner未初始化，无法生成异变之王");
                return false;
            }

            // 使用AdvancedEntitySpawner的正确方法生成异变之王
            boolean success = advancedEntitySpawner.spawnMutationKing(location);

            if (success) {
                logger.info("异变之王生成成功");
            } else {
                logger.warning("异变之王生成失败");
            }

            return success;
        } catch (Exception e) {
            logger.severe("生成异变之王时发生异常: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 清理资源
     */
    public void dispose() {
        // 清理资源
        logger.info("正在清理ZombieHelper资源...");

        // 清理CustomZombie组件
        if (customZombie != null) {
            customZombie.dispose();
        }

        // 释放引用
        customZombie = null;
        particleHelper = null;
        guiManager = null;
        instance = null;
    }

    /**
     * 清理所有由插件创建的NPC
     *
     * @return 清理的NPC数量
     */
    public int cleanupAllNPCs() {
        if (customZombie == null) {
            logger.severe("CustomZombie组件未初始化，无法清理NPC");
            return 0;
        }

        try {
            // 检查Citizens插件是否可用
            if (org.bukkit.Bukkit.getPluginManager().getPlugin("Citizens") == null) {
                logger.warning("Citizens插件不可用，无法清理NPC");
                return 0;
            }

            logger.info("开始清理所有由插件创建的NPC...");

            // 获取NPCs注册表
            net.citizensnpcs.api.CitizensAPI.getNPCRegistry().deregisterAll();

            logger.info("NPC清理完成");
            return 1; // 成功清理
        } catch (Exception e) {
            logger.severe("清理NPC时出错: " + e.getMessage());
            e.printStackTrace();
            return 0;
        }
    }

    /**
     * 生成指定ID的僵尸
     *
     * @param location 生成位置
     * @param id 僵尸ID (1-17)
     * @return 生成的僵尸实体
     */
    public Entity spawnZombie(Location location, int id) {
        // 检查ID范围
        if (id < 1 || id > 17) {
            plugin.getLogger().warning("尝试生成无效ID的僵尸: " + id);
            id = 1; // 默认使用ID 1
        }

        // 调用现有方法生成僵尸
        String zombieId = "id" + id;
        return spawnCustomZombie(location, zombieId);
    }

    /**
     * 生成特殊实体（变异体）
     *
     * @param location 生成位置
     * @param type 变异体类型
     * @return 生成的实体
     */
    public Entity spawnSpecialEntity(Location location, String type) {
        // 转换为对应的entityId格式
        String entityId;
        if (type.equalsIgnoreCase("mutant01")) {
            entityId = "idc1";
        } else if (type.equalsIgnoreCase("mutant02")) {
            entityId = "idc2";
        } else if (type.equalsIgnoreCase("mutant_blaze")) {
            entityId = "idc3";
        } else if (type.equalsIgnoreCase("mutant_creeper")) {
            entityId = "idc4";
        } else {
            entityId = "idc1"; // 默认使用变异体01
        }

        // 调用现有方法生成特殊实体
        spawnOtherEntity(location, entityId);

        // 查找刚生成的实体
        Entity entity = null;
        double closestDistance = Double.MAX_VALUE;

        for (Entity e : location.getWorld().getEntities()) {
            if (e.getLocation().distanceSquared(location) < 4) { // 2格范围内
                double distance = e.getLocation().distanceSquared(location);
                if (distance < closestDistance) {
                    closestDistance = distance;
                    entity = e;
                }
            }
        }

        return entity;
    }

    /**
     * 生成NPC
     *
     * @param location 生成位置
     * @param type NPC类型 (1-5)
     * @return 生成的NPC实体
     */
    public Entity spawnNPC(Location location, int type) {
        // 转换为对应的npcId格式
        String npcId;
        switch (type) {
            case 1:
                npcId = "idn1"; // 感染者1
                break;
            case 2:
                npcId = "idn2"; // 感染者2号
                break;
            case 3:
                npcId = "idn3"; // 感染者农民
                break;
            case 4:
                npcId = "idn4"; // 感染者居民
                break;
            case 5:
                npcId = "idn5"; // 感染猪
                break;
            default:
                npcId = "idn1"; // 默认使用NPC1
                logger.warning("未知的NPC类型: " + type + ", 使用默认类型1");
                break;
        }

        logger.info("尝试生成NPC: " + npcId + " 在位置 " + formatLocation(location));

        // 先尝试使用createNPC方法
        boolean success = createNPC(location, npcId);
        if (!success) {
            // 如果失败，尝试直接生成实体
            logger.warning("createNPC方法失败，尝试直接生成实体");

            // 根据不同类型生成不同实体
            Entity directEntity = null;
            try {
                // 这里可以根据需要添加直接生成实体的逻辑
                // 例如生成僵尸或其他实体
                Zombie zombie = (Zombie) location.getWorld().spawnEntity(location, EntityType.ZOMBIE);
                zombie.setCustomName("NPC_" + npcId);
                zombie.setCustomNameVisible(false); // 默认隐藏，由EntityNameDisplayListener控制显示
                directEntity = zombie;
            } catch (Exception e) {
                logger.severe("直接生成实体失败: " + e.getMessage());
                return null;
            }

            return directEntity;
        }

        // 查找刚生成的实体
        Entity entity = null;
        double closestDistance = Double.MAX_VALUE;

        for (Entity e : location.getWorld().getEntities()) {
            if (e.getLocation().distanceSquared(location) < 4) { // 2格范围内
                double distance = e.getLocation().distanceSquared(location);
                if (distance < closestDistance) {
                    closestDistance = distance;
                    entity = e;
                }
            }
        }

        if (entity != null) {
            logger.info("成功找到生成的NPC实体: " + entity.getType() + " 在位置 " + formatLocation(entity.getLocation()));
        } else {
            logger.warning("无法找到刚刚生成的NPC实体");
        }

        return entity;
    }

    /**
     * 激活实体的特殊能力
     * 用于在游戏中生成idc实体时激活它们的特殊能力
     *
     * @param location 实体生成位置
     * @param entityId 实体ID
     */
    private void activateSpecialAbilities(Location location, String entityId) {
        try {
            // 延迟一个tick执行，确保实体已经完全生成
            org.bukkit.Bukkit.getScheduler().runTaskLater(plugin, () -> {
                try {
                    // 查找刚生成的实体
                    Entity targetEntity = null;
                    double closestDistance = Double.MAX_VALUE;

                    for (Entity entity : location.getWorld().getEntities()) {
                        if (entity.getLocation().distanceSquared(location) < 4) { // 2格范围内
                            double distance = entity.getLocation().distanceSquared(location);
                            if (distance < closestDistance) {
                                closestDistance = distance;
                                targetEntity = entity;
                            }
                        }
                    }

                    if (targetEntity == null) {
                        logger.warning("无法找到刚生成的实体来激活特殊能力: " + entityId);
                        return;
                    }

                    // 根据实体ID激活相应的特殊能力
                    switch (entityId) {
                        case "idc7": // 灾厄卫道士
                            if (targetEntity instanceof org.bukkit.entity.LivingEntity) {
                                org.bukkit.entity.LivingEntity livingEntity = (org.bukkit.entity.LivingEntity) targetEntity;
                                // 确保元数据标记
                                if (!targetEntity.hasMetadata("idcZombieEntity")) {
                                    targetEntity.setMetadata("idcZombieEntity", new org.bukkit.metadata.FixedMetadataValue(plugin, true));
                                }
                                // 启动灾厄卫道士技能系统
                                if (customZombie != null && customZombie.getEntityIntegration() != null) {
                                    try {
                                        customZombie.getEntityIntegration().spawnCustomEntity(livingEntity.getLocation(), "idc7");
                                        logger.info("已启动灾厄卫道士技能系统");
                                    } catch (Exception e) {
                                        logger.warning("启动灾厄卫道士技能系统失败: " + e.getMessage());
                                    }
                                }
                            }
                            break;

                        case "idc8": // 灾厄唤魔者
                            if (targetEntity instanceof org.bukkit.entity.LivingEntity) {
                                org.bukkit.entity.LivingEntity livingEntity = (org.bukkit.entity.LivingEntity) targetEntity;
                                // 确保元数据标记
                                if (!targetEntity.hasMetadata("idcZombieEntity")) {
                                    targetEntity.setMetadata("idcZombieEntity", new org.bukkit.metadata.FixedMetadataValue(plugin, true));
                                }
                                // 启动灾厄唤魔者技能系统
                                if (customZombie != null && customZombie.getEntityIntegration() != null) {
                                    try {
                                        customZombie.getEntityIntegration().spawnCustomEntity(livingEntity.getLocation(), "idc8");
                                        logger.info("已启动灾厄唤魔者技能系统");
                                    } catch (Exception e) {
                                        logger.warning("启动灾厄唤魔者技能系统失败: " + e.getMessage());
                                    }
                                }
                            }
                            break;

                        case "idc9": // 灾厄劫掠兽
                        case "idc10": // 变异僵尸马
                        case "idc11": // 变异岩浆怪
                        case "idc12": // 变异尸壳
                        case "idc14": // 变异僵尸04
                        case "idc15": // 鲜血猪灵
                        case "idc16": // 暗影潜影贝
                        case "idc17": // 变异雪傅儡
                        case "idc19": // 变异僵尸Max
                        case "idc20": // 灵魂坚守者
                        case "idc21": // 凋零领主
                        case "idc22": // 异变之王
                            if (targetEntity instanceof org.bukkit.entity.LivingEntity) {
                                org.bukkit.entity.LivingEntity livingEntity = (org.bukkit.entity.LivingEntity) targetEntity;
                                // 确保元数据标记
                                if (!targetEntity.hasMetadata("idcZombieEntity")) {
                                    targetEntity.setMetadata("idcZombieEntity", new org.bukkit.metadata.FixedMetadataValue(plugin, true));
                                }
                                // 尝试启动技能系统
                                if (customZombie != null && customZombie.getEntityIntegration() != null) {
                                    try {
                                        customZombie.getEntityIntegration().spawnCustomEntity(livingEntity.getLocation(), entityId);
                                        logger.info("已启动 " + entityId + " 技能系统");
                                    } catch (Exception e) {
                                        logger.warning("启动 " + entityId + " 技能系统失败: " + e.getMessage());
                                    }
                                }
                            }
                            break;

                        case "idc18": // 变异铁傅儡
                            // 变异铁傅儡的特殊能力已经在MutantIronGolem类中实现
                            if (!targetEntity.hasMetadata("mutantIronGolem")) {
                                targetEntity.setMetadata("mutantIronGolem", new org.bukkit.metadata.FixedMetadataValue(plugin, true));
                            }
                            if (!targetEntity.hasMetadata("idcZombieEntity")) {
                                targetEntity.setMetadata("idcZombieEntity", new org.bukkit.metadata.FixedMetadataValue(plugin, true));
                            }
                            logger.info("为变异铁傅儡添加特殊标记");
                            break;

                        case "idc2": // 变异僵尸02
                        case "idc3": // 变异烈焰人
                        case "idc4": // 变异爬行者
                        case "idc5": // 变异末影螨
                        case "idc6": // 变异蜘蛛
                        case "idc13": // 变异僵尸03
                            // 这些实体的特殊能力已经在生成时设置，只需要确保标记正确
                            if (!targetEntity.hasMetadata("idcZombieEntity")) {
                                targetEntity.setMetadata("idcZombieEntity", new org.bukkit.metadata.FixedMetadataValue(plugin, true));
                                logger.info("为实体 " + entityId + " 添加idcZombieEntity标记");
                            }
                            break;

                        default:
                            logger.info("实体 " + entityId + " 不需要特殊能力激活");
                            break;
                    }

                    logger.info("已为实体 " + entityId + " 激活特殊能力");
                } catch (Exception e) {
                    logger.severe("激活实体特殊能力时发生异常: " + e.getMessage());
                    e.printStackTrace();
                }
            }, 1L);
        } catch (Exception e) {
            logger.severe("调度特殊能力激活任务时发生异常: " + e.getMessage());
            e.printStackTrace();
        }
    }



    /**
     * 直接生成高级实体，避免命令处理的卡顿和提示信息
     *
     * @param location 生成位置
     * @param entityId 实体ID
     * @return 是否成功生成
     */
    private boolean spawnAdvancedEntityDirect(Location location, String entityId) {
        try {
            logger.info("开始直接生成高级实体: " + entityId + " 在位置: " + formatLocation(location));

            boolean success = false;

            switch (entityId) {
                case "idc7": // 灾厄卫道士
                    success = spawnVindicatorDirect(location);
                    break;
                case "idc8": // 灾厄唤魔者
                    success = spawnEvokerDirect(location);
                    break;
                case "idc9": // 灾厄劫掠兽
                    success = spawnRavagerDirect(location);
                    break;
                case "idc10": // 变异僵尸马
                    success = spawnZombieHorseDirect(location);
                    break;
                case "idc11": // 变异岩浆怪
                    success = spawnMagmaCubeDirect(location);
                    break;
                case "idc12": // 变异尸壳
                    success = spawnHuskDirect(location);
                    break;
                case "idc14": // 变异僵尸04
                    success = spawnMutantZombie04Direct(location);
                    break;
                case "idc15": // 鲜血猪灵
                    success = spawnBloodPiglinDirect(location);
                    break;
                case "idc16": // 暗影潜影贝
                    success = spawnShadowShulkerDirect(location);
                    break;
                case "idc17": // 变异雪傀儡
                    success = spawnMutantSnowGolemDirect(location);
                    break;
                case "idc19": // 变异僵尸Max
                    success = spawnMutantZombieMaxDirect(location);
                    break;
                case "idc20": // 灵魂坚守者
                    success = spawnSoulGuardianDirect(location);
                    break;
                case "idc21": // 凋零领主
                    success = spawnWitherLordDirect(location);
                    break;
                case "idc22": // 异变之王
                    success = spawnMutationKingDirect(location);
                    break;
                default:
                    logger.warning("未知的高级实体ID: " + entityId);
                    return false;
            }

            if (success) {
                logger.info("成功直接生成高级实体: " + entityId);
                // 激活特殊能力
                activateSpecialAbilities(location, entityId);
            } else {
                logger.warning("直接生成高级实体失败: " + entityId);
            }

            return success;
        } catch (Exception e) {
            logger.severe("直接生成高级实体时发生异常: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    // ==================== 特殊能力方法 ====================

    // 存储各种实体的任务
    private final Map<Entity, org.bukkit.scheduler.BukkitTask> mutantZombie01Tasks = new HashMap<>();
    private final Map<Entity, org.bukkit.scheduler.BukkitTask> mutantParticleTasks = new HashMap<>();
    private final Map<Entity, org.bukkit.scheduler.BukkitTask> mutantBlazeTasks = new HashMap<>();
    private final Map<Entity, org.bukkit.scheduler.BukkitTask> mutantCreeperTasks = new HashMap<>();
    private final Map<Entity, org.bukkit.scheduler.BukkitTask> mutantEndermiteTasks = new HashMap<>();
    private final Map<Entity, org.bukkit.scheduler.BukkitTask> mutantSpiderTasks = new HashMap<>();

    /**
     * 启动变异僵尸01的粒子效果任务
     *
     * @param pigZombie 变异僵尸01实体
     */
    private void startMutantZombie01ParticleTask(PigZombie pigZombie) {
        org.bukkit.scheduler.BukkitTask task = new org.bukkit.scheduler.BukkitRunnable() {
            @Override
            public void run() {
                if (pigZombie == null || pigZombie.isDead()) {
                    this.cancel();
                    mutantZombie01Tasks.remove(pigZombie);
                    return;
                }

                // 生成黑色烟雾粒子围成的正方体"气场"
                Location location = pigZombie.getLocation();

                // 底部
                for (double x = -1; x <= 1; x += 0.25) {
                    for (double z = -1; z <= 1; z += 0.25) {
                        if (Math.abs(x) > 0.9 || Math.abs(z) > 0.9) { // 只在边缘生成
                            Location particleLoc = location.clone().add(x, 0, z);
                            if (particleHelper != null) {
                                particleHelper.displaySmokeParticle(org.bukkit.Bukkit.getOnlinePlayers(), particleLoc, 0, 0, 0, 0, 1);
                            }
                        }
                    }
                }

                // 顶部
                for (double x = -1; x <= 1; x += 0.25) {
                    for (double z = -1; z <= 1; z += 0.25) {
                        if (Math.abs(x) > 0.9 || Math.abs(z) > 0.9) { // 只在边缘生成
                            Location particleLoc = location.clone().add(x, 2, z);
                            if (particleHelper != null) {
                                particleHelper.displaySmokeParticle(org.bukkit.Bukkit.getOnlinePlayers(), particleLoc, 0, 0, 0, 0, 1);
                            }
                        }
                    }
                }

                // 四个立柱
                for (double y = 0; y <= 2; y += 0.25) {
                    Location corner1 = location.clone().add(-1, y, -1);
                    Location corner2 = location.clone().add(-1, y, 1);
                    Location corner3 = location.clone().add(1, y, -1);
                    Location corner4 = location.clone().add(1, y, 1);

                    if (particleHelper != null) {
                        particleHelper.displaySmokeParticle(org.bukkit.Bukkit.getOnlinePlayers(), corner1, 0, 0, 0, 0, 1);
                        particleHelper.displaySmokeParticle(org.bukkit.Bukkit.getOnlinePlayers(), corner2, 0, 0, 0, 0, 1);
                        particleHelper.displaySmokeParticle(org.bukkit.Bukkit.getOnlinePlayers(), corner3, 0, 0, 0, 0, 1);
                        particleHelper.displaySmokeParticle(org.bukkit.Bukkit.getOnlinePlayers(), corner4, 0, 0, 0, 0, 1);
                    }
                }
            }
        }.runTaskTimer(plugin, 0, 5); // 立即开始，每5tick执行一次

        // 保存任务以便后续取消
        mutantZombie01Tasks.put(pigZombie, task);
    }

    /**
     * 为变异实体添加特殊粒子效果
     *
     * @param entity 变异实体
     */
    private void startMutantParticleEffects(Entity entity) {
        org.bukkit.scheduler.BukkitTask task = new org.bukkit.scheduler.BukkitRunnable() {
            double time = 0;

            @Override
            public void run() {
                if (entity == null || entity.isDead()) {
                    cancel();
                    mutantParticleTasks.remove(entity);
                    return;
                }

                // 创建螺旋上升的粒子效果
                Location baseLocation = entity.getLocation().add(0, 0.5, 0);
                time += 0.3;

                for (double y = 0; y < 2; y += 0.2) {
                    double radius = 0.8 - y * 0.2;
                    double x = Math.cos(time + y * 3) * radius;
                    double z = Math.sin(time + y * 3) * radius;

                    Location particleLoc = baseLocation.clone().add(x, y, z);

                    // 根据实体类型显示不同颜色的粒子
                    if (entity instanceof Skeleton) {
                        // 变异僵尸02 - 紫色粒子
                        entity.getWorld().spawnParticle(org.bukkit.Particle.DUST, particleLoc, 1, 0, 0, 0, 0,
                                new org.bukkit.Particle.DustOptions(org.bukkit.Color.PURPLE, 1.0f));
                    } else {
                        // 默认 - 红色粒子
                        entity.getWorld().spawnParticle(org.bukkit.Particle.DUST, particleLoc, 1, 0, 0, 0, 0,
                                new org.bukkit.Particle.DustOptions(org.bukkit.Color.RED, 1.0f));
                    }
                }
            }
        }.runTaskTimer(plugin, 5, 5); // 立即开始，每0.25秒更新一次

        mutantParticleTasks.put(entity, task);
    }

    /**
     * 开始变异烈焰人的特效任务
     *
     * @param blaze 烈焰人实体
     */
    private void startMutantBlazeEffects(Blaze blaze) {
        org.bukkit.scheduler.BukkitTask task = new org.bukkit.scheduler.BukkitRunnable() {
            @Override
            public void run() {
                if (blaze == null || blaze.isDead()) {
                    cancel();
                    mutantBlazeTasks.remove(blaze);
                    return;
                }

                // 查找15格内最近的玩家
                Player target = null;
                double closestDistance = 16.0;

                for (Entity entity : blaze.getNearbyEntities(15, 15, 15)) {
                    if (entity instanceof Player) {
                        double distance = blaze.getLocation().distance(entity.getLocation());
                        if (distance < closestDistance) {
                            closestDistance = distance;
                            target = (Player) entity;
                        }
                    }
                }

                if (target != null) {
                    // 让烈焰人面向目标玩家
                    blaze.teleport(blaze.getLocation().setDirection(
                            target.getLocation().subtract(blaze.getLocation()).toVector()));

                    // 发射烈焰粒子
                    for (int i = 0; i < 5; i++) {
                        new org.bukkit.scheduler.BukkitRunnable() {
                            double distance = 0;
                            Location particleLoc;

                            @Override
                            public void run() {
                                if (blaze == null || blaze.isDead()) {
                                    cancel();
                                    return;
                                }

                                if (distance == 0) {
                                    // 初始化粒子位置
                                    particleLoc = blaze.getLocation().add(0, 1, 0).clone();
                                }

                                // 移动粒子
                                particleLoc.add(blaze.getLocation().getDirection().multiply(0.7));
                                distance += 0.7;

                                // 显示火焰粒子
                                blaze.getWorld().spawnParticle(org.bukkit.Particle.FLAME, particleLoc, 5, 0.1, 0.1, 0.1, 0.02);

                                // 检测是否击中玩家
                                for (Entity entity : blaze.getWorld().getNearbyEntities(particleLoc, 0.8, 0.8, 0.8)) {
                                    if (entity instanceof Player) {
                                        Player player = (Player) entity;
                                        player.damage(4.0, blaze); // 造成4点伤害
                                        player.setFireTicks(60); // 着火3秒

                                        // 显示命中效果
                                        blaze.getWorld().spawnParticle(org.bukkit.Particle.EXPLOSION, particleLoc, 1, 0, 0, 0, 0);
                                        blaze.getWorld().playSound(particleLoc, org.bukkit.Sound.ENTITY_PLAYER_HURT_ON_FIRE, 1.0f, 1.0f);

                                        cancel();
                                        return;
                                    }
                                }

                                // 超过最大距离或碰到非透明方块时停止
                                if (distance > 15 || !particleLoc.getBlock().isPassable()) {
                                    // 显示命中效果
                                    blaze.getWorld().spawnParticle(org.bukkit.Particle.EXPLOSION, particleLoc, 3, 0.2, 0.2, 0.2, 0.02);
                                    cancel();
                                }
                            }
                        }.runTaskTimer(plugin, i * 5, 1); // 每个粒子流间隔5刻启动，每刻更新一次
                    }

                    // 播放攻击音效
                    blaze.getWorld().playSound(blaze.getLocation(), org.bukkit.Sound.ENTITY_BLAZE_SHOOT, 1.0f, 1.0f);
                }
            }
        }.runTaskTimer(plugin, 20, 60); // 1秒后开始，每3秒执行一次

        mutantBlazeTasks.put(blaze, task);
    }

    /**
     * 开始变异爬行者的特殊任务
     *
     * @param creeper 爬行者实体
     */
    private void startMutantCreeperTasks(Creeper creeper) {
        org.bukkit.scheduler.BukkitTask task = new org.bukkit.scheduler.BukkitRunnable() {
            @Override
            public void run() {
                if (creeper == null || creeper.isDead()) {
                    cancel();
                    mutantCreeperTasks.remove(creeper);
                    return;
                }

                // 检查5格内是否有玩家
                for (Entity entity : creeper.getNearbyEntities(5, 5, 5)) {
                    if (entity instanceof Player) {
                        // 有玩家在附近，准备自爆

                        // 播放TNT点燃音效
                        creeper.getWorld().playSound(creeper.getLocation(), org.bukkit.Sound.ENTITY_TNT_PRIMED, 1.0f, 1.0f);

                        // 显示自爆提示 (红色粒子)
                        Location loc = creeper.getLocation().add(0, 1, 0);
                        if (particleHelper != null) {
                            particleHelper.displayCircle(loc, 1.0f, org.bukkit.Particle.FLAME);
                        }

                        // 2秒后自爆
                        new org.bukkit.scheduler.BukkitRunnable() {
                            @Override
                            public void run() {
                                if (creeper == null || creeper.isDead()) {
                                    return;
                                }

                                // 获取爆炸位置
                                Location explodeLocation = creeper.getLocation();

                                // 移除爬行者
                                creeper.remove();
                                mutantCreeperTasks.remove(creeper);
                                cancel();

                                // 不破坏方块的爆炸效果
                                explodeLocation.getWorld().createExplosion(explodeLocation, 0F, false, false);

                                // 对附近玩家造成10点伤害
                                for (Entity nearby : explodeLocation.getWorld().getNearbyEntities(explodeLocation, 5, 5, 5)) {
                                    if (nearby instanceof Player) {
                                        ((Player) nearby).damage(10.0);
                                    }
                                }

                                // 显示爆炸粒子效果
                                if (particleHelper != null) {
                                    particleHelper.displayExplosion(explodeLocation, org.bukkit.Particle.EXPLOSION, 50);
                                }
                            }
                        }.runTaskLater(plugin, 40); // 2秒后爆炸

                        // 一旦进入自爆程序，就取消当前任务
                        cancel();
                        mutantCreeperTasks.remove(creeper);
                        return;
                    }
                }
            }
        }.runTaskTimer(plugin, 10, 10); // 0.5秒后开始，每0.5秒检查一次

        mutantCreeperTasks.put(creeper, task);
    }

    /**
     * 开始变异末影螨的特殊任务
     *
     * @param endermite 末影螨实体
     */
    private void startMutantEndermiteTasks(Endermite endermite) {
        org.bukkit.scheduler.BukkitTask task = new org.bukkit.scheduler.BukkitRunnable() {
            @Override
            public void run() {
                if (endermite == null || endermite.isDead()) {
                    cancel();
                    mutantEndermiteTasks.remove(endermite);
                    return;
                }

                Location loc = endermite.getLocation();

                // 释放电流粒子效果
                endermite.getWorld().spawnParticle(org.bukkit.Particle.ENCHANTED_HIT, loc, 50, 0, 0, 0, 1);

                // 对10格范围内的玩家造成4点伤害
                for (Entity entity : endermite.getNearbyEntities(10, 10, 10)) {
                    if (entity instanceof Player) {
                        Player player = (Player) entity;
                        player.damage(4.0, endermite);

                        // 添加中毒效果
                        player.addPotionEffect(new PotionEffect(PotionEffectType.POISON, 60, 1)); // 3秒中毒
                    }
                }

                // 播放电击音效
                endermite.getWorld().playSound(loc, org.bukkit.Sound.ENTITY_LIGHTNING_BOLT_THUNDER, 0.5f, 2.0f);
            }
        }.runTaskTimer(plugin, 0, 60); // 立即开始，每3秒执行一次

        mutantEndermiteTasks.put(endermite, task);
    }

    /**
     * 开始变异蜘蛛的特殊任务
     *
     * @param spider 蜘蛛实体
     */
    private void startMutantSpiderTasks(CaveSpider spider) {
        org.bukkit.scheduler.BukkitTask task = new org.bukkit.scheduler.BukkitRunnable() {
            @Override
            public void run() {
                if (spider == null || spider.isDead()) {
                    cancel();
                    mutantSpiderTasks.remove(spider);
                    return;
                }

                // 查找15格内最近的玩家
                Player target = null;
                double closestDistance = 16.0;

                for (Entity entity : spider.getNearbyEntities(15, 15, 15)) {
                    if (entity instanceof Player) {
                        double distance = spider.getLocation().distance(entity.getLocation());
                        if (distance < closestDistance) {
                            closestDistance = distance;
                            target = (Player) entity;
                        }
                    }
                }

                if (target != null) {
                    // 蜘蛛网攻击
                    Location spiderLoc = spider.getLocation();
                    Location targetLoc = target.getLocation();

                    // 在目标玩家脚下生成蜘蛛网
                    if (targetLoc.getBlock().getType() == Material.AIR) {
                        targetLoc.getBlock().setType(Material.COBWEB);

                        // 3秒后移除蜘蛛网
                        new org.bukkit.scheduler.BukkitRunnable() {
                            @Override
                            public void run() {
                                if (targetLoc.getBlock().getType() == Material.COBWEB) {
                                    targetLoc.getBlock().setType(Material.AIR);
                                }
                            }
                        }.runTaskLater(plugin, 60); // 3秒后移除
                    }

                    // 给玩家添加缓慢效果
                    target.addPotionEffect(new PotionEffect(PotionEffectType.SLOWNESS, 100, 2)); // 5秒缓慢III

                    // 显示蜘蛛网粒子效果
                    spider.getWorld().spawnParticle(org.bukkit.Particle.ITEM, targetLoc, 20, 0.5, 0.5, 0.5, 0.1,
                            new org.bukkit.inventory.ItemStack(Material.COBWEB));

                    // 播放蜘蛛攻击音效
                    spider.getWorld().playSound(spiderLoc, org.bukkit.Sound.ENTITY_SPIDER_AMBIENT, 1.0f, 0.8f);
                }
            }
        }.runTaskTimer(plugin, 40, 80); // 2秒后开始，每4秒执行一次

        mutantSpiderTasks.put(spider, task);
    }

    /**
     * 获取双CustomZombie系统管理器
     *
     * @return 双系统管理器实例
     */
    public org.Ver_zhzh.customZombie.DualZombieSystemManager getDualZombieSystemManager() {
        return dualZombieSystemManager;
    }

    /**
     * 重载双系统配置
     */
    public void reloadDualSystemConfig() {
        if (dualZombieSystemManager != null) {
            dualZombieSystemManager.reloadConfig();
            logger.info("双CustomZombie系统配置已重载");
        } else {
            logger.warning("双系统管理器未初始化，无法重载配置");
        }

    }

    /**
     * 获取双系统状态信息
     *
     * @return 状态信息字符串
     */
    public String getDualSystemStatus() {
        if (dualZombieSystemManager != null) {
            return dualZombieSystemManager.getSystemStatus();
        } else {
            return "§c双CustomZombie系统未初始化";
        }
    }
}
