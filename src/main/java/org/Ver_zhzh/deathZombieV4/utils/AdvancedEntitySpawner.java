package org.Ver_zhzh.deathZombieV4.utils;

import org.bukkit.Color;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.enchantments.Enchantment;
import org.bukkit.entity.*;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.inventory.meta.LeatherArmorMeta;
import org.bukkit.metadata.FixedMetadataValue;
import org.bukkit.potion.PotionEffect;
import org.bukkit.potion.PotionEffectType;
import org.bukkit.scheduler.BukkitRunnable;
import org.bukkit.scheduler.BukkitTask;
import org.Ver_zhzh.deathZombieV4.DeathZombieV4;

import java.util.HashMap;
import java.util.Map;
import java.util.logging.Logger;

/**
 * 高级实体生成器
 * 负责直接生成IDC高级实体，不依赖玩家对象
 */
public class AdvancedEntitySpawner {
    
    private final DeathZombieV4 plugin;
    private final Logger logger;

    // 存储各种高级实体的任务
    private final Map<Entity, BukkitTask> advancedEntityTasks = new HashMap<>();

    public AdvancedEntitySpawner(DeathZombieV4 plugin) {
        this.plugin = plugin;
        this.logger = plugin.getLogger();
    }
    
    /**
     * 生成灾厄卫道士 (idc7)
     */
    public boolean spawnDisasterGuardian(Location location) {
        try {
            // 生成掠夺者实体
            Pillager pillager = (Pillager) location.getWorld().spawnEntity(location, EntityType.PILLAGER);

            // 设置灾厄卫道士属性
            pillager.setCustomName("§4灾厄卫道士");
            pillager.setCustomNameVisible(false); // 默认隐藏，由EntityNameDisplayListener控制显示
            pillager.setMaxHealth(540.0);
            pillager.setHealth(540.0);

            // 添加效果
            pillager.addPotionEffect(new PotionEffect(PotionEffectType.RESISTANCE, Integer.MAX_VALUE, 1)); // 抗性提升II

            // 给掠夺者装备下界合金斧
            ItemStack netheriteAxe = new ItemStack(Material.NETHERITE_AXE);
            ItemMeta axeMeta = netheriteAxe.getItemMeta();
            axeMeta.setDisplayName("§4灾厄之斧");
            axeMeta.addEnchant(Enchantment.SHARPNESS, 5, true); // 锋利V
            netheriteAxe.setItemMeta(axeMeta);

            // 设置装备
            pillager.getEquipment().setItemInMainHand(netheriteAxe);
            pillager.getEquipment().setItemInMainHandDropChance(0.0F); // 不掉落

            // 给掠夺者添加元数据标记
            pillager.setMetadata("disasterGuardian", new FixedMetadataValue(plugin, true));

            // 添加idcZombieEntity元数据标记，确保实体被识别为僵尸
            pillager.setMetadata("idcZombieEntity", new FixedMetadataValue(plugin, true));
            logger.info("已为灾厄卫道士添加idcZombieEntity标记");

            // 启动完整的灾厄卫道士技能系统
            if (plugin instanceof org.Ver_zhzh.deathZombieV4.DeathZombieV4) {
                org.Ver_zhzh.deathZombieV4.DeathZombieV4 dzPlugin = (org.Ver_zhzh.deathZombieV4.DeathZombieV4) plugin;
                try {
                    // 获取UserCustomEntity系统并启动技能
                    if (dzPlugin.getZombieHelper() != null &&
                        dzPlugin.getZombieHelper().getCustomZombie() != null &&
                        dzPlugin.getZombieHelper().getCustomZombie().getEntityIntegration() != null) {

                        // 通过反射获取UserCustomEntity实例
                        java.lang.reflect.Method getUserCustomEntityMethod =
                            dzPlugin.getZombieHelper().getCustomZombie().getEntityIntegration().getClass().getMethod("getUserCustomEntity");
                        Object userCustomEntity = getUserCustomEntityMethod.invoke(dzPlugin.getZombieHelper().getCustomZombie().getEntityIntegration());

                        if (userCustomEntity != null) {
                            // 通过反射调用私有的enableEntitySkills方法
                            java.lang.reflect.Method enableSkillsMethod =
                                userCustomEntity.getClass().getDeclaredMethod("enableEntitySkills",
                                    org.bukkit.entity.LivingEntity.class, String.class,
                                    userCustomEntity.getClass().getDeclaredClasses()[0]); // EntityOverrideConfig
                            enableSkillsMethod.setAccessible(true);

                            // 创建默认配置
                            java.lang.reflect.Method createDefaultConfigMethod =
                                userCustomEntity.getClass().getDeclaredMethod("createDefaultConfig", String.class);
                            createDefaultConfigMethod.setAccessible(true);
                            Object config = createDefaultConfigMethod.invoke(userCustomEntity, "idc7");

                            enableSkillsMethod.invoke(userCustomEntity, pillager, "idc7", config);
                            logger.info("已启动灾厄卫道士完整技能系统");
                        } else {
                            logger.warning("UserCustomEntity实例为null，使用基础属性");
                        }
                    }
                } catch (Exception e) {
                    logger.warning("启动灾厄卫道士完整技能系统失败，使用基础属性: " + e.getMessage());
                }
            }

            return true;
        } catch (Exception e) {
            logger.severe("生成灾厄卫道士时出错: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }
    
    /**
     * 生成灾厄唤魔者 (idc8)
     */
    public boolean spawnDisasterSummoner(Location location) {
        try {
            // 生成唤魔者实体
            Evoker evoker = (Evoker) location.getWorld().spawnEntity(location, EntityType.EVOKER);

            // 设置灾厄唤魔者属性
            evoker.setCustomName("§5灾厄唤魔者");
            evoker.setCustomNameVisible(false); // 默认隐藏，由EntityNameDisplayListener控制显示
            evoker.setMaxHealth(600.0);
            evoker.setHealth(600.0);

            // 添加速度2效果
            evoker.addPotionEffect(new PotionEffect(PotionEffectType.SPEED, Integer.MAX_VALUE, 1)); // 速度II

            // 给唤魔者添加元数据标记
            evoker.setMetadata("disasterSummoner", new FixedMetadataValue(plugin, true));

            // 添加idcZombieEntity元数据标记，确保实体被识别为僵尸
            evoker.setMetadata("idcZombieEntity", new FixedMetadataValue(plugin, true));
            logger.info("已为灾厄唤魔者添加idcZombieEntity标记");

            // 启动完整的灾厄唤魔者技能系统
            if (plugin instanceof org.Ver_zhzh.deathZombieV4.DeathZombieV4) {
                org.Ver_zhzh.deathZombieV4.DeathZombieV4 dzPlugin = (org.Ver_zhzh.deathZombieV4.DeathZombieV4) plugin;
                try {
                    // 获取UserCustomEntity系统并启动技能
                    if (dzPlugin.getZombieHelper() != null &&
                        dzPlugin.getZombieHelper().getCustomZombie() != null &&
                        dzPlugin.getZombieHelper().getCustomZombie().getEntityIntegration() != null) {

                        // 通过反射获取UserCustomEntity实例
                        java.lang.reflect.Method getUserCustomEntityMethod =
                            dzPlugin.getZombieHelper().getCustomZombie().getEntityIntegration().getClass().getMethod("getUserCustomEntity");
                        Object userCustomEntity = getUserCustomEntityMethod.invoke(dzPlugin.getZombieHelper().getCustomZombie().getEntityIntegration());

                        if (userCustomEntity != null) {
                            // 调用UserCustomEntity的技能启用方法
                            java.lang.reflect.Method enableSkillsMethod =
                                userCustomEntity.getClass().getMethod("enableEntitySkills", org.bukkit.entity.LivingEntity.class, String.class);
                            enableSkillsMethod.invoke(userCustomEntity, evoker, "idc8");
                            logger.info("已启动灾厄唤魔者完整技能系统");
                        } else {
                            logger.warning("UserCustomEntity实例为null，使用基础属性");
                        }
                    }
                } catch (Exception e) {
                    logger.warning("启动灾厄唤魔者完整技能系统失败，使用基础属性: " + e.getMessage());
                }
            }

            return true;
        } catch (Exception e) {
            logger.severe("生成灾厄唤魔者时出错: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }
    
    /**
     * 生成灾厄劫掠兽 (idc9)
     */
    public boolean spawnDisasterRavagerBeast(Location location) {
        try {
            // 生成劫掠兽实体
            Ravager ravager = (Ravager) location.getWorld().spawnEntity(location, EntityType.RAVAGER);

            // 设置灾厄劫掠兽属性
            ravager.setCustomName("§4灾厄劫掠兽");
            ravager.setCustomNameVisible(false); // 默认隐藏，由EntityNameDisplayListener控制显示
            ravager.setMaxHealth(1000.0);
            ravager.setHealth(1000.0);

            // 添加跳跃提升3和速度1效果
            ravager.addPotionEffect(new PotionEffect(PotionEffectType.JUMP_BOOST, Integer.MAX_VALUE, 2)); // 跳跃提升III
            ravager.addPotionEffect(new PotionEffect(PotionEffectType.SPEED, Integer.MAX_VALUE, 0)); // 速度I

            // 给劫掠兽添加元数据标记
            ravager.setMetadata("disasterRavagerBeast", new FixedMetadataValue(plugin, true));

            // 添加idcZombieEntity元数据标记，确保实体被识别为僵尸
            ravager.setMetadata("idcZombieEntity", new FixedMetadataValue(plugin, true));
            logger.info("已为灾厄劫掠兽添加idcZombieEntity标记");

            // 启动完整的灾厄劫掠兽技能系统
            if (plugin instanceof org.Ver_zhzh.deathZombieV4.DeathZombieV4) {
                org.Ver_zhzh.deathZombieV4.DeathZombieV4 dzPlugin = (org.Ver_zhzh.deathZombieV4.DeathZombieV4) plugin;
                try {
                    // 获取UserCustomEntity系统并启动技能
                    if (dzPlugin.getZombieHelper() != null &&
                        dzPlugin.getZombieHelper().getCustomZombie() != null &&
                        dzPlugin.getZombieHelper().getCustomZombie().getEntityIntegration() != null) {

                        // 通过反射获取UserCustomEntity实例
                        java.lang.reflect.Method getUserCustomEntityMethod =
                            dzPlugin.getZombieHelper().getCustomZombie().getEntityIntegration().getClass().getMethod("getUserCustomEntity");
                        Object userCustomEntity = getUserCustomEntityMethod.invoke(dzPlugin.getZombieHelper().getCustomZombie().getEntityIntegration());

                        if (userCustomEntity != null) {
                            // 调用UserCustomEntity的技能启用方法
                            java.lang.reflect.Method enableSkillsMethod =
                                userCustomEntity.getClass().getMethod("enableEntitySkills", org.bukkit.entity.LivingEntity.class, String.class);
                            enableSkillsMethod.invoke(userCustomEntity, ravager, "idc9");
                            logger.info("已启动灾厄劫掠兽完整技能系统");
                        } else {
                            logger.warning("UserCustomEntity实例为null，使用基础属性");
                        }
                    }
                } catch (Exception e) {
                    logger.warning("启动灾厄劫掠兽完整技能系统失败，使用基础属性: " + e.getMessage());
                }
            }

            return true;
        } catch (Exception e) {
            logger.severe("生成灾厄劫掠兽时出错: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }
    
    /**
     * 生成变异僵尸马 (idc10)
     */
    public boolean spawnMutantZombieHorse(Location location) {
        try {
            // 生成僵尸马实体
            ZombieHorse zombieHorse = (ZombieHorse) location.getWorld().spawnEntity(location, EntityType.ZOMBIE_HORSE);

            // 设置变异僵尸马属性
            zombieHorse.setCustomName("§2变异僵尸马");
            zombieHorse.setCustomNameVisible(false); // 默认隐藏，由EntityNameDisplayListener控制显示
            zombieHorse.setMaxHealth(300.0);
            zombieHorse.setHealth(300.0);

            // 添加速度5效果
            zombieHorse.addPotionEffect(new PotionEffect(PotionEffectType.SPEED, Integer.MAX_VALUE, 4)); // 速度V

            // 禁用AI - 完全禁止原版AI干扰我们的自定义行为
            zombieHorse.setAI(false);

            // 设置为不会被驯服
            zombieHorse.setTamed(false);

            // 防止实体消失
            zombieHorse.setRemoveWhenFarAway(false);

            // 禁用重力 - 防止实体下落
            zombieHorse.setGravity(false);

            // 给僵尸马添加元数据标记
            zombieHorse.setMetadata("mutantZombieHorse", new FixedMetadataValue(plugin, true));

            // 添加idcZombieEntity元数据标记，确保实体被识别为僵尸
            zombieHorse.setMetadata("idcZombieEntity", new FixedMetadataValue(plugin, true));
            logger.info("已为变异僵尸马添加idcZombieEntity标记");

            // 启动飞行能力
            startMutantZombieHorseFlying(zombieHorse);

            return true;
        } catch (Exception e) {
            logger.severe("生成变异僵尸马时出错: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }
    
    /**
     * 生成变异岩浆怪 (idc11)
     */
    public boolean spawnMutantMagmaCube(Location location) {
        try {
            // 生成岩浆怪实体
            MagmaCube magmaCube = (MagmaCube) location.getWorld().spawnEntity(location, EntityType.MAGMA_CUBE);

            // 设置变异岩浆怪属性
            magmaCube.setCustomName("§c变异岩浆怪");
            magmaCube.setCustomNameVisible(false); // 默认隐藏，由EntityNameDisplayListener控制显示
            magmaCube.setMaxHealth(100.0);
            magmaCube.setHealth(100.0);
            magmaCube.setSize(3); // 设置为较大的尺寸

            // 添加速度3效果
            magmaCube.addPotionEffect(new PotionEffect(PotionEffectType.SPEED, Integer.MAX_VALUE, 2)); // 速度III

            // 给岩浆怪添加元数据标记
            magmaCube.setMetadata("mutantMagmaCube", new FixedMetadataValue(plugin, true));

            // 添加idcZombieEntity元数据标记，确保实体被识别为僵尸
            magmaCube.setMetadata("idcZombieEntity", new FixedMetadataValue(plugin, true));
            logger.info("已为变异岩浆怪添加idcZombieEntity标记");

            // 启动完整的变异岩浆怪技能系统
            if (plugin instanceof org.Ver_zhzh.deathZombieV4.DeathZombieV4) {
                org.Ver_zhzh.deathZombieV4.DeathZombieV4 dzPlugin = (org.Ver_zhzh.deathZombieV4.DeathZombieV4) plugin;
                try {
                    // 获取UserCustomEntity系统并启动技能
                    if (dzPlugin.getZombieHelper() != null &&
                        dzPlugin.getZombieHelper().getCustomZombie() != null &&
                        dzPlugin.getZombieHelper().getCustomZombie().getEntityIntegration() != null) {

                        // 通过反射获取UserCustomEntity实例
                        java.lang.reflect.Method getUserCustomEntityMethod =
                            dzPlugin.getZombieHelper().getCustomZombie().getEntityIntegration().getClass().getMethod("getUserCustomEntity");
                        Object userCustomEntity = getUserCustomEntityMethod.invoke(dzPlugin.getZombieHelper().getCustomZombie().getEntityIntegration());

                        if (userCustomEntity != null) {
                            // 调用UserCustomEntity的技能启用方法
                            java.lang.reflect.Method enableSkillsMethod =
                                userCustomEntity.getClass().getMethod("enableEntitySkills", org.bukkit.entity.LivingEntity.class, String.class);
                            enableSkillsMethod.invoke(userCustomEntity, magmaCube, "idc11");
                            logger.info("已启动变异岩浆怪完整技能系统");
                        } else {
                            logger.warning("UserCustomEntity实例为null，使用基础属性");
                        }
                    }
                } catch (Exception e) {
                    logger.warning("启动变异岩浆怪完整技能系统失败，使用基础属性: " + e.getMessage());
                }
            }

            return true;
        } catch (Exception e) {
            logger.severe("生成变异岩浆怪时出错: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }
    
    /**
     * 生成变异尸壳 (idc12)
     */
    public boolean spawnMutantHusk(Location location) {
        try {
            // 生成尸壳实体
            Husk husk = (Husk) location.getWorld().spawnEntity(location, EntityType.HUSK);

            // 设置变异尸壳属性
            husk.setCustomName("§e变异尸壳");
            husk.setCustomNameVisible(false); // 默认隐藏，由EntityNameDisplayListener控制显示
            husk.setMaxHealth(100.0);
            husk.setHealth(100.0);

            // 创建锋利1铁剑
            ItemStack ironSword = new ItemStack(Material.IRON_SWORD);
            ItemMeta swordMeta = ironSword.getItemMeta();
            swordMeta.addEnchant(Enchantment.SHARPNESS, 1, true);
            ironSword.setItemMeta(swordMeta);

            // 创建保护10粉色皮革胸甲
            ItemStack leatherChestplate = new ItemStack(Material.LEATHER_CHESTPLATE);
            LeatherArmorMeta chestMeta = (LeatherArmorMeta) leatherChestplate.getItemMeta();
            chestMeta.setColor(Color.fromRGB(255, 105, 180)); // 粉色
            chestMeta.addEnchant(Enchantment.PROTECTION, 10, true);
            leatherChestplate.setItemMeta(chestMeta);

            // 设置装备
            husk.getEquipment().setItemInMainHand(ironSword);
            husk.getEquipment().setChestplate(leatherChestplate);

            // 设置装备不掉落
            husk.getEquipment().setItemInMainHandDropChance(0.0F);
            husk.getEquipment().setChestplateDropChance(0.0F);

            // 添加速度2和跳跃提升2效果
            husk.addPotionEffect(new PotionEffect(PotionEffectType.SPEED, Integer.MAX_VALUE, 1)); // 速度II
            husk.addPotionEffect(new PotionEffect(PotionEffectType.JUMP_BOOST, Integer.MAX_VALUE, 1)); // 跳跃提升II

            // 给尸壳添加元数据标记
            husk.setMetadata("mutantHusk", new FixedMetadataValue(plugin, true));

            // 添加idcZombieEntity元数据标记，确保实体被识别为僵尸
            husk.setMetadata("idcZombieEntity", new FixedMetadataValue(plugin, true));
            logger.info("已为变异尸壳添加idcZombieEntity标记");

            // 启动完整的变异尸壳技能系统
            if (plugin instanceof org.Ver_zhzh.deathZombieV4.DeathZombieV4) {
                org.Ver_zhzh.deathZombieV4.DeathZombieV4 dzPlugin = (org.Ver_zhzh.deathZombieV4.DeathZombieV4) plugin;
                try {
                    // 获取UserCustomEntity系统并启动技能
                    if (dzPlugin.getZombieHelper() != null &&
                        dzPlugin.getZombieHelper().getCustomZombie() != null &&
                        dzPlugin.getZombieHelper().getCustomZombie().getEntityIntegration() != null) {

                        // 通过反射获取UserCustomEntity实例
                        java.lang.reflect.Method getUserCustomEntityMethod =
                            dzPlugin.getZombieHelper().getCustomZombie().getEntityIntegration().getClass().getMethod("getUserCustomEntity");
                        Object userCustomEntity = getUserCustomEntityMethod.invoke(dzPlugin.getZombieHelper().getCustomZombie().getEntityIntegration());

                        if (userCustomEntity != null) {
                            // 调用UserCustomEntity的技能启用方法
                            java.lang.reflect.Method enableSkillsMethod =
                                userCustomEntity.getClass().getMethod("enableEntitySkills", org.bukkit.entity.LivingEntity.class, String.class);
                            enableSkillsMethod.invoke(userCustomEntity, husk, "idc12");
                            logger.info("已启动变异尸壳完整技能系统");
                        } else {
                            logger.warning("UserCustomEntity实例为null，使用基础属性");
                        }
                    }
                } catch (Exception e) {
                    logger.warning("启动变异尸壳完整技能系统失败，使用基础属性: " + e.getMessage());
                }
            }

            return true;
        } catch (Exception e) {
            logger.severe("生成变异尸壳时出错: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 生成变异僵尸04 (idc14)
     */
    public boolean spawnMutantZombie04(Location location) {
        try {
            // 生成凋零骷髅实体
            WitherSkeleton witherSkeleton = (WitherSkeleton) location.getWorld().spawnEntity(location, EntityType.WITHER_SKELETON);

            // 设置变异僵尸04属性
            witherSkeleton.setCustomName("§8变异僵尸04");
            witherSkeleton.setCustomNameVisible(false); // 默认隐藏，由EntityNameDisplayListener控制显示
            witherSkeleton.setMaxHealth(200.0);
            witherSkeleton.setHealth(200.0);

            // 创建全套染黑色的皮革装备，并添加保护3附魔
            // 头盔
            ItemStack blackHelmet = new ItemStack(Material.LEATHER_HELMET);
            LeatherArmorMeta helmetMeta = (LeatherArmorMeta) blackHelmet.getItemMeta();
            helmetMeta.setColor(Color.BLACK);
            helmetMeta.addEnchant(Enchantment.PROTECTION, 3, true);
            blackHelmet.setItemMeta(helmetMeta);

            // 胸甲
            ItemStack blackChestplate = new ItemStack(Material.LEATHER_CHESTPLATE);
            LeatherArmorMeta chestplateMeta = (LeatherArmorMeta) blackChestplate.getItemMeta();
            chestplateMeta.setColor(Color.BLACK);
            chestplateMeta.addEnchant(Enchantment.PROTECTION, 3, true);
            blackChestplate.setItemMeta(chestplateMeta);

            // 护腿
            ItemStack blackLeggings = new ItemStack(Material.LEATHER_LEGGINGS);
            LeatherArmorMeta leggingsMeta = (LeatherArmorMeta) blackLeggings.getItemMeta();
            leggingsMeta.setColor(Color.BLACK);
            leggingsMeta.addEnchant(Enchantment.PROTECTION, 3, true);
            blackLeggings.setItemMeta(leggingsMeta);

            // 靴子
            ItemStack blackBoots = new ItemStack(Material.LEATHER_BOOTS);
            LeatherArmorMeta bootsMeta = (LeatherArmorMeta) blackBoots.getItemMeta();
            bootsMeta.setColor(Color.BLACK);
            bootsMeta.addEnchant(Enchantment.PROTECTION, 3, true);
            blackBoots.setItemMeta(bootsMeta);

            // 设置装备
            witherSkeleton.getEquipment().setHelmet(blackHelmet);
            witherSkeleton.getEquipment().setChestplate(blackChestplate);
            witherSkeleton.getEquipment().setLeggings(blackLeggings);
            witherSkeleton.getEquipment().setBoots(blackBoots);

            // 设置装备不掉落
            witherSkeleton.getEquipment().setHelmetDropChance(0.0F);
            witherSkeleton.getEquipment().setChestplateDropChance(0.0F);
            witherSkeleton.getEquipment().setLeggingsDropChance(0.0F);
            witherSkeleton.getEquipment().setBootsDropChance(0.0F);

            // 给凋零骷髅添加元数据标记
            witherSkeleton.setMetadata("mutantZombie04", new FixedMetadataValue(plugin, true));

            // 添加idcZombieEntity元数据标记，确保实体被识别为僵尸
            witherSkeleton.setMetadata("idcZombieEntity", new FixedMetadataValue(plugin, true));
            logger.info("已为变异僵尸04添加idcZombieEntity标记");

            // 添加游戏实体标记，确保能被WindowManager识别
            witherSkeleton.setMetadata("gameEntity", new FixedMetadataValue(plugin, true));

            // 启用敌对AI，确保主动攻击玩家
            if (plugin instanceof org.Ver_zhzh.deathZombieV4.DeathZombieV4) {
                org.Ver_zhzh.deathZombieV4.DeathZombieV4 dzPlugin = (org.Ver_zhzh.deathZombieV4.DeathZombieV4) plugin;
                if (dzPlugin.getHostileAIManager() != null) {
                    dzPlugin.getHostileAIManager().enableHostileAI(witherSkeleton);
                }
            }

            // 启动机枪攻击能力
            startMutantZombie04MachineGun(witherSkeleton);

            return true;
        } catch (Exception e) {
            logger.severe("生成变异僵尸04时出错: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 生成鲜血猪灵 (idc15)
     */
    public boolean spawnBloodPiglin(Location location) {
        try {
            // 生成猪灵实体
            Piglin bloodPiglin = (Piglin) location.getWorld().spawnEntity(location, EntityType.PIGLIN);

            // 设置鲜血猪灵属性
            bloodPiglin.setCustomName("§c鲜血猪灵");
            bloodPiglin.setCustomNameVisible(false); // 默认隐藏，由EntityNameDisplayListener控制显示
            bloodPiglin.setMaxHealth(100.0);
            bloodPiglin.setHealth(100.0);

            // 创建锋利2的下界合金剑
            ItemStack netheriteSword = new ItemStack(Material.NETHERITE_SWORD);
            ItemMeta netheriteSwordMeta = netheriteSword.getItemMeta();
            netheriteSwordMeta.setDisplayName("§c鲜血之剑");
            netheriteSwordMeta.addEnchant(Enchantment.SHARPNESS, 2, true);
            netheriteSword.setItemMeta(netheriteSwordMeta);

            // 创建全套染红色的皮革装备，保护20
            ItemStack redHelmet = new ItemStack(Material.LEATHER_HELMET);
            LeatherArmorMeta redHelmetMeta = (LeatherArmorMeta) redHelmet.getItemMeta();
            redHelmetMeta.setColor(Color.RED);
            redHelmetMeta.setDisplayName("§c鲜血头盔");
            redHelmetMeta.addEnchant(Enchantment.PROTECTION, 20, true);
            redHelmet.setItemMeta(redHelmetMeta);

            ItemStack redChestplate = new ItemStack(Material.LEATHER_CHESTPLATE);
            LeatherArmorMeta redChestMeta = (LeatherArmorMeta) redChestplate.getItemMeta();
            redChestMeta.setColor(Color.RED);
            redChestMeta.setDisplayName("§c鲜血胸甲");
            redChestMeta.addEnchant(Enchantment.PROTECTION, 20, true);
            redChestplate.setItemMeta(redChestMeta);

            ItemStack redLeggings = new ItemStack(Material.LEATHER_LEGGINGS);
            LeatherArmorMeta redLeggingsMeta = (LeatherArmorMeta) redLeggings.getItemMeta();
            redLeggingsMeta.setColor(Color.RED);
            redLeggingsMeta.setDisplayName("§c鲜血护腿");
            redLeggingsMeta.addEnchant(Enchantment.PROTECTION, 20, true);
            redLeggings.setItemMeta(redLeggingsMeta);

            ItemStack redBoots = new ItemStack(Material.LEATHER_BOOTS);
            LeatherArmorMeta redBootsMeta = (LeatherArmorMeta) redBoots.getItemMeta();
            redBootsMeta.setColor(Color.RED);
            redBootsMeta.setDisplayName("§c鲜血靴子");
            redBootsMeta.addEnchant(Enchantment.PROTECTION, 20, true);
            redBoots.setItemMeta(redBootsMeta);

            // 设置装备
            bloodPiglin.getEquipment().setItemInMainHand(netheriteSword);
            bloodPiglin.getEquipment().setHelmet(redHelmet);
            bloodPiglin.getEquipment().setChestplate(redChestplate);
            bloodPiglin.getEquipment().setLeggings(redLeggings);
            bloodPiglin.getEquipment().setBoots(redBoots);

            // 设置装备不掉落
            bloodPiglin.getEquipment().setItemInMainHandDropChance(0.0F);
            bloodPiglin.getEquipment().setHelmetDropChance(0.0F);
            bloodPiglin.getEquipment().setChestplateDropChance(0.0F);
            bloodPiglin.getEquipment().setLeggingsDropChance(0.0F);
            bloodPiglin.getEquipment().setBootsDropChance(0.0F);

            // 设置猪灵不会变成僵尸猪灵（免疫主世界转换）
            bloodPiglin.setImmuneToZombification(true);

            // 给鲜血猪灵添加元数据标记
            bloodPiglin.setMetadata("bloodPiglin", new FixedMetadataValue(plugin, true));

            // 添加idcZombieEntity元数据标记，确保实体被识别为僵尸
            bloodPiglin.setMetadata("idcZombieEntity", new FixedMetadataValue(plugin, true));
            logger.info("已为鲜血猪灵添加idcZombieEntity标记");

            // 启动完整的鲜血猪灵技能系统
            if (plugin instanceof org.Ver_zhzh.deathZombieV4.DeathZombieV4) {
                org.Ver_zhzh.deathZombieV4.DeathZombieV4 dzPlugin = (org.Ver_zhzh.deathZombieV4.DeathZombieV4) plugin;
                try {
                    // 获取UserCustomEntity系统并启动技能
                    if (dzPlugin.getZombieHelper() != null &&
                        dzPlugin.getZombieHelper().getCustomZombie() != null &&
                        dzPlugin.getZombieHelper().getCustomZombie().getEntityIntegration() != null) {

                        // 通过反射获取UserCustomEntity实例
                        java.lang.reflect.Method getUserCustomEntityMethod =
                            dzPlugin.getZombieHelper().getCustomZombie().getEntityIntegration().getClass().getMethod("getUserCustomEntity");
                        Object userCustomEntity = getUserCustomEntityMethod.invoke(dzPlugin.getZombieHelper().getCustomZombie().getEntityIntegration());

                        if (userCustomEntity != null) {
                            // 调用UserCustomEntity的技能启用方法
                            java.lang.reflect.Method enableSkillsMethod =
                                userCustomEntity.getClass().getMethod("enableEntitySkills", org.bukkit.entity.LivingEntity.class, String.class);
                            enableSkillsMethod.invoke(userCustomEntity, bloodPiglin, "idc15");
                            logger.info("已启动鲜血猪灵完整技能系统");
                        } else {
                            logger.warning("UserCustomEntity实例为null，使用基础属性");
                        }
                    }
                } catch (Exception e) {
                    logger.warning("启动鲜血猪灵完整技能系统失败，使用基础属性: " + e.getMessage());
                }
            }

            return true;
        } catch (Exception e) {
            logger.severe("生成鲜血猪灵时出错: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 生成暗影潜影贝 (idc16)
     */
    public boolean spawnShadowShulker(Location location) {
        try {
            // 生成潜影贝实体
            Shulker shadowShulker = (Shulker) location.getWorld().spawnEntity(location, EntityType.SHULKER);

            // 设置暗影潜影贝属性
            shadowShulker.setCustomName("§8暗影潜影贝");
            shadowShulker.setCustomNameVisible(false); // 默认隐藏，由EntityNameDisplayListener控制显示
            shadowShulker.setMaxHealth(50.0);
            shadowShulker.setHealth(50.0);

            // 设置潜影贝颜色为黑色
            shadowShulker.setColor(org.bukkit.DyeColor.BLACK);

            // 添加隐身效果（1分钟 = 1200 ticks）
            shadowShulker.addPotionEffect(new PotionEffect(PotionEffectType.INVISIBILITY, 1200, 0));

            // 给暗影潜影贝添加元数据标记
            shadowShulker.setMetadata("shadowShulker", new FixedMetadataValue(plugin, true));

            // 添加idcZombieEntity元数据标记，确保实体被识别为僵尸
            shadowShulker.setMetadata("idcZombieEntity", new FixedMetadataValue(plugin, true));
            logger.info("已为暗影潜影贝添加idcZombieEntity标记");

            // 启动完整的暗影潜影贝技能系统
            if (plugin instanceof org.Ver_zhzh.deathZombieV4.DeathZombieV4) {
                org.Ver_zhzh.deathZombieV4.DeathZombieV4 dzPlugin = (org.Ver_zhzh.deathZombieV4.DeathZombieV4) plugin;
                try {
                    // 获取UserCustomEntity系统并启动技能
                    if (dzPlugin.getZombieHelper() != null &&
                        dzPlugin.getZombieHelper().getCustomZombie() != null &&
                        dzPlugin.getZombieHelper().getCustomZombie().getEntityIntegration() != null) {

                        // 通过反射获取UserCustomEntity实例
                        java.lang.reflect.Method getUserCustomEntityMethod =
                            dzPlugin.getZombieHelper().getCustomZombie().getEntityIntegration().getClass().getMethod("getUserCustomEntity");
                        Object userCustomEntity = getUserCustomEntityMethod.invoke(dzPlugin.getZombieHelper().getCustomZombie().getEntityIntegration());

                        if (userCustomEntity != null) {
                            // 调用UserCustomEntity的技能启用方法
                            java.lang.reflect.Method enableSkillsMethod =
                                userCustomEntity.getClass().getMethod("enableEntitySkills", org.bukkit.entity.LivingEntity.class, String.class);
                            enableSkillsMethod.invoke(userCustomEntity, shadowShulker, "idc16");
                            logger.info("已启动暗影潜影贝完整技能系统");
                        } else {
                            logger.warning("UserCustomEntity实例为null，使用基础属性");
                        }
                    }
                } catch (Exception e) {
                    logger.warning("启动暗影潜影贝完整技能系统失败，使用基础属性: " + e.getMessage());
                }
            }

            return true;
        } catch (Exception e) {
            logger.severe("生成暗影潜影贝时出错: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 生成变异雪傀儡 (idc17)
     */
    public boolean spawnMutantSnowman(Location location) {
        try {
            // 生成雪傀儡实体
            Snowman snowman = (Snowman) location.getWorld().spawnEntity(location, EntityType.SNOW_GOLEM);

            // 设置变异雪傀儡属性
            snowman.setCustomName("§f变异雪傀儡");
            snowman.setCustomNameVisible(false); // 默认隐藏，由EntityNameDisplayListener控制显示
            snowman.setMaxHealth(200.0);
            snowman.setHealth(200.0);

            // 添加速度5效果
            snowman.addPotionEffect(new PotionEffect(PotionEffectType.SPEED, Integer.MAX_VALUE, 4)); // 速度5

            // 给变异雪傀儡添加元数据标记
            snowman.setMetadata("mutantSnowman", new FixedMetadataValue(plugin, true));

            // 添加idcZombieEntity元数据标记，确保实体被识别为僵尸
            snowman.setMetadata("idcZombieEntity", new FixedMetadataValue(plugin, true));
            logger.info("已为变异雪傀儡添加idcZombieEntity标记");

            // 添加游戏实体标记，确保能被WindowManager识别
            snowman.setMetadata("gameEntity", new FixedMetadataValue(plugin, true));

            // 启用敌对AI，确保主动攻击玩家
            if (plugin instanceof org.Ver_zhzh.deathZombieV4.DeathZombieV4) {
                org.Ver_zhzh.deathZombieV4.DeathZombieV4 dzPlugin = (org.Ver_zhzh.deathZombieV4.DeathZombieV4) plugin;
                if (dzPlugin.getHostileAIManager() != null) {
                    dzPlugin.getHostileAIManager().enableHostileAI(snowman);
                }

                // 启动完整的变异雪傀儡技能系统
                try {
                    // 获取UserCustomEntity系统并启动技能
                    if (dzPlugin.getZombieHelper() != null &&
                        dzPlugin.getZombieHelper().getCustomZombie() != null &&
                        dzPlugin.getZombieHelper().getCustomZombie().getEntityIntegration() != null) {

                        // 通过反射获取UserCustomEntity实例
                        java.lang.reflect.Method getUserCustomEntityMethod =
                            dzPlugin.getZombieHelper().getCustomZombie().getEntityIntegration().getClass().getMethod("getUserCustomEntity");
                        Object userCustomEntity = getUserCustomEntityMethod.invoke(dzPlugin.getZombieHelper().getCustomZombie().getEntityIntegration());

                        if (userCustomEntity != null) {
                            // 调用UserCustomEntity的技能启用方法
                            java.lang.reflect.Method enableSkillsMethod =
                                userCustomEntity.getClass().getMethod("enableEntitySkills", org.bukkit.entity.LivingEntity.class, String.class);
                            enableSkillsMethod.invoke(userCustomEntity, snowman, "idc17");
                            logger.info("已启动变异雪傀儡完整技能系统");
                        } else {
                            logger.warning("UserCustomEntity实例为null，使用基础属性");
                        }
                    }
                } catch (Exception e) {
                    logger.warning("启动变异雪傀儡完整技能系统失败，使用基础属性: " + e.getMessage());
                }
            }

            return true;
        } catch (Exception e) {
            logger.severe("生成变异雪傀儡时出错: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 生成变异僵尸Max (idc19)
     */
    public boolean spawnMutantZombieMax(Location location) {
        try {
            // 生成溺尸实体
            Drowned mutantZombieMax = (Drowned) location.getWorld().spawnEntity(location, EntityType.DROWNED);

            // 设置变异僵尸Max属性
            mutantZombieMax.setCustomName("§b变异僵尸Max");
            mutantZombieMax.setCustomNameVisible(false); // 默认隐藏，由EntityNameDisplayListener控制显示
            mutantZombieMax.setMaxHealth(2000.0);
            mutantZombieMax.setHealth(2000.0);

            // 创建附魔三叉戟 - 引雷20级、忠诚10级、穿透
            ItemStack trident = new ItemStack(Material.TRIDENT);
            ItemMeta tridentMeta = trident.getItemMeta();
            tridentMeta.setDisplayName("§b雷霆三叉戟");
            tridentMeta.addEnchant(Enchantment.CHANNELING, 20, true); // 引雷20级
            tridentMeta.addEnchant(Enchantment.LOYALTY, 10, true); // 忠诚10级
            tridentMeta.addEnchant(Enchantment.PIERCING, 4, true); // 穿透4级
            trident.setItemMeta(tridentMeta);

            // 设置装备
            mutantZombieMax.getEquipment().setItemInMainHand(trident);
            mutantZombieMax.getEquipment().setItemInMainHandDropChance(0.0F); // 不掉落

            // 添加速度9和跳跃提升3的buff
            mutantZombieMax.addPotionEffect(new PotionEffect(PotionEffectType.SPEED, Integer.MAX_VALUE, 8)); // 速度9
            mutantZombieMax.addPotionEffect(new PotionEffect(PotionEffectType.JUMP_BOOST, Integer.MAX_VALUE, 2)); // 跳跃提升3

            // 给变异僵尸Max添加元数据标记
            mutantZombieMax.setMetadata("mutantZombieMax", new FixedMetadataValue(plugin, true));

            // 添加idcZombieEntity元数据标记，确保实体被识别为僵尸
            mutantZombieMax.setMetadata("idcZombieEntity", new FixedMetadataValue(plugin, true));
            logger.info("已为变异僵尸Max添加idcZombieEntity标记");

            // 添加游戏实体标记，确保能被WindowManager识别
            mutantZombieMax.setMetadata("gameEntity", new FixedMetadataValue(plugin, true));

            // 启用敌对AI，确保主动攻击玩家
            if (plugin instanceof org.Ver_zhzh.deathZombieV4.DeathZombieV4) {
                org.Ver_zhzh.deathZombieV4.DeathZombieV4 dzPlugin = (org.Ver_zhzh.deathZombieV4.DeathZombieV4) plugin;
                if (dzPlugin.getHostileAIManager() != null) {
                    dzPlugin.getHostileAIManager().enableHostileAI(mutantZombieMax);
                }
            }

            // 启动雷电攻击能力
            startMutantZombieMaxLightning(mutantZombieMax);

            return true;
        } catch (Exception e) {
            logger.severe("生成变异僵尸Max时出错: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 生成灵魂坚守者 (idc20)
     */
    public boolean spawnSoulGuardian(Location location) {
        try {
            // 尝试生成Warden实体
            Entity entity;
            try {
                // 尝试使用Warden实体类型（Minecraft 1.19+）
                logger.info("尝试在世界 " + location.getWorld().getName() + " 生成Warden实体");
                entity = location.getWorld().spawnEntity(location, EntityType.valueOf("WARDEN"));
                logger.info("Warden实体生成成功，UUID: " + entity.getUniqueId());
            } catch (IllegalArgumentException e) {
                // 如果服务器不支持Warden实体类型，则使用铁傀儡作为后备
                logger.warning("服务器不支持Warden实体类型（需要Minecraft 1.19+），将使用铁傀儡作为替代");
                entity = location.getWorld().spawnEntity(location, EntityType.IRON_GOLEM);
                logger.info("铁傀儡实体（作为Warden替代）生成成功，UUID: " + entity.getUniqueId());
            }

            if (!(entity instanceof LivingEntity)) {
                logger.severe("生成的实体不是LivingEntity类型");
                return false;
            }

            LivingEntity livingEntity = (LivingEntity) entity;

            // 设置灵魂坚守者属性
            livingEntity.setCustomName("§5灵魂坚守者");
            livingEntity.setCustomNameVisible(false); // 默认隐藏，由EntityNameDisplayListener控制显示
            livingEntity.setMaxHealth(5000.0);
            livingEntity.setHealth(5000.0);

            // 添加速度2效果
            livingEntity.addPotionEffect(new PotionEffect(PotionEffectType.SPEED, Integer.MAX_VALUE, 1)); // 速度II

            // 添加额外生命30级和伤害吸收30级
            livingEntity.addPotionEffect(new PotionEffect(PotionEffectType.HEALTH_BOOST, Integer.MAX_VALUE, 29)); // 额外生命30级
            livingEntity.addPotionEffect(new PotionEffect(PotionEffectType.ABSORPTION, Integer.MAX_VALUE, 29)); // 伤害吸收30级

            // 给灵魂坚守者添加元数据标记
            livingEntity.setMetadata("soulGuardian", new FixedMetadataValue(plugin, true));

            // 添加idcZombieEntity元数据标记，确保实体被识别为僵尸
            livingEntity.setMetadata("idcZombieEntity", new FixedMetadataValue(plugin, true));
            logger.info("已为灵魂坚守者添加idcZombieEntity标记");

            // 添加游戏实体标记，确保能被WindowManager识别
            livingEntity.setMetadata("gameEntity", new FixedMetadataValue(plugin, true));

            // 启用敌对AI，确保主动攻击玩家
            if (plugin instanceof org.Ver_zhzh.deathZombieV4.DeathZombieV4) {
                org.Ver_zhzh.deathZombieV4.DeathZombieV4 dzPlugin = (org.Ver_zhzh.deathZombieV4.DeathZombieV4) plugin;
                if (dzPlugin.getHostileAIManager() != null) {
                    dzPlugin.getHostileAIManager().enableHostileAI(livingEntity);
                }
            }

            // 启动音波攻击能力
            startSoulGuardianSonicAttack(livingEntity);

            return true;
        } catch (Exception e) {
            logger.severe("生成灵魂坚守者时出错: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 生成凋零领主 (idc21)
     */
    public boolean spawnWitherLord(Location location) {
        try {
            // 生成凋零实体
            Wither witherLord = (Wither) location.getWorld().spawnEntity(location, EntityType.WITHER);

            // 设置凋零领主属性
            witherLord.setCustomName("§5凋零领主");
            witherLord.setCustomNameVisible(false); // 默认隐藏，由EntityNameDisplayListener控制显示
            witherLord.setMaxHealth(6999.0);
            witherLord.setHealth(6999.0);

            // 防止实体消失
            witherLord.setRemoveWhenFarAway(false);

            // 给凋零领主添加元数据标记
            witherLord.setMetadata("witherLord", new FixedMetadataValue(plugin, true));

            // 添加idcZombieEntity元数据标记，确保实体被识别为僵尸
            witherLord.setMetadata("idcZombieEntity", new FixedMetadataValue(plugin, true));
            logger.info("已为凋零领主添加idcZombieEntity标记");

            // 添加游戏实体标记，确保能被WindowManager识别
            witherLord.setMetadata("gameEntity", new FixedMetadataValue(plugin, true));

            // 启用敌对AI，确保主动攻击玩家
            if (plugin instanceof org.Ver_zhzh.deathZombieV4.DeathZombieV4) {
                org.Ver_zhzh.deathZombieV4.DeathZombieV4 dzPlugin = (org.Ver_zhzh.deathZombieV4.DeathZombieV4) plugin;
                if (dzPlugin.getHostileAIManager() != null) {
                    dzPlugin.getHostileAIManager().enableHostileAI(witherLord);
                }
            }

            // 启动凋零领主的特殊攻击能力
            startWitherLordAttacks(witherLord);

            return true;
        } catch (Exception e) {
            logger.severe("生成凋零领主时出错: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 生成异变之王 (idc22)
     */
    public boolean spawnMutationKing(Location location) {
        try {
            // 生成末影龙实体
            EnderDragon dragon = (EnderDragon) location.getWorld().spawnEntity(location, EntityType.ENDER_DRAGON);

            // 设置自定义名称和生命值
            dragon.setCustomName("§4异变之王");
            dragon.setCustomNameVisible(false); // 默认隐藏，由EntityNameDisplayListener控制显示
            dragon.setMaxHealth(10999.0);
            dragon.setHealth(10999.0);

            // 添加速度3效果
            dragon.addPotionEffect(new PotionEffect(PotionEffectType.SPEED, Integer.MAX_VALUE, 2, false, false));

            // 防止实体消失
            dragon.setRemoveWhenFarAway(false);

            // 添加元数据标记，用于识别
            dragon.setMetadata("mutationKing", new FixedMetadataValue(plugin, true));

            // 添加idcZombieEntity元数据标记，确保实体被识别为僵尸
            dragon.setMetadata("idcZombieEntity", new FixedMetadataValue(plugin, true));
            logger.info("已为异变之王添加idcZombieEntity标记");

            // 添加游戏实体标记，确保能被WindowManager识别
            dragon.setMetadata("gameEntity", new FixedMetadataValue(plugin, true));

            // 启用敌对AI，确保主动攻击玩家
            if (plugin instanceof org.Ver_zhzh.deathZombieV4.DeathZombieV4) {
                org.Ver_zhzh.deathZombieV4.DeathZombieV4 dzPlugin = (org.Ver_zhzh.deathZombieV4.DeathZombieV4) plugin;
                if (dzPlugin.getHostileAIManager() != null) {
                    dzPlugin.getHostileAIManager().enableHostileAI(dragon);
                }
            }

            // 启动异变之王的龙息攻击能力
            startMutationKingBreathAttack(dragon);

            // 启动完整的异变之王技能系统（使用MutationKing类）
            if (plugin instanceof org.Ver_zhzh.deathZombieV4.DeathZombieV4) {
                org.Ver_zhzh.deathZombieV4.DeathZombieV4 dzPlugin = (org.Ver_zhzh.deathZombieV4.DeathZombieV4) plugin;
                try {
                    // 获取MutationKing实例并启动完整技能系统
                    if (dzPlugin.getZombieHelper() != null &&
                        dzPlugin.getZombieHelper().getCustomZombie() != null) {

                        // 通过反射获取MutationKing实例
                        java.lang.reflect.Field mutationKingField =
                            dzPlugin.getZombieHelper().getCustomZombie().getClass().getDeclaredField("mutationKing");
                        mutationKingField.setAccessible(true);
                        Object mutationKingInstance = mutationKingField.get(dzPlugin.getZombieHelper().getCustomZombie());

                        if (mutationKingInstance != null) {
                            // 调用MutationKing的startDragonTasks方法
                            java.lang.reflect.Method startTasksMethod =
                                mutationKingInstance.getClass().getMethod("startDragonTasks", org.bukkit.entity.EnderDragon.class);
                            startTasksMethod.invoke(mutationKingInstance, dragon);
                            logger.info("已启动异变之王完整技能系统（血条、AI、技能）");
                        } else {
                            logger.warning("MutationKing实例为null，使用基础龙息攻击");
                        }
                    }
                } catch (Exception e) {
                    logger.warning("启动异变之王完整技能系统失败，使用基础龙息攻击: " + e.getMessage());
                }
            }

            return true;
        } catch (Exception e) {
            logger.severe("生成异变之王时出错: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    // ==================== 高级实体特殊能力方法 ====================

    /**
     * 启动变异僵尸马的飞行能力
     */
    private void startMutantZombieHorseFlying(ZombieHorse zombieHorse) {
        BukkitTask task = new BukkitRunnable() {
            @Override
            public void run() {
                if (zombieHorse == null || zombieHorse.isDead()) {
                    cancel();
                    advancedEntityTasks.remove(zombieHorse);
                    return;
                }

                // 查找15格内最近的玩家
                Player target = null;
                double closestDistance = 16.0;

                for (Entity entity : zombieHorse.getNearbyEntities(15, 15, 15)) {
                    if (entity instanceof Player) {
                        double distance = zombieHorse.getLocation().distance(entity.getLocation());
                        if (distance < closestDistance) {
                            closestDistance = distance;
                            target = (Player) entity;
                        }
                    }
                }

                if (target != null) {
                    // 飞向目标玩家
                    Location targetLoc = target.getLocation().add(0, 3, 0); // 飞到玩家上方3格
                    Location horseLoc = zombieHorse.getLocation();

                    // 计算移动向量
                    org.bukkit.util.Vector direction = targetLoc.toVector().subtract(horseLoc.toVector()).normalize();
                    direction.multiply(0.5); // 控制飞行速度

                    // 移动僵尸马
                    zombieHorse.setVelocity(direction);

                    // 显示飞行粒子效果
                    zombieHorse.getWorld().spawnParticle(org.bukkit.Particle.CLOUD, horseLoc, 10, 0.5, 0.5, 0.5, 0.1);

                    // 播放飞行音效
                    zombieHorse.getWorld().playSound(horseLoc, org.bukkit.Sound.ENTITY_BAT_TAKEOFF, 0.5f, 1.5f);
                }
            }
        }.runTaskTimer(plugin, 20, 40); // 1秒后开始，每2秒执行一次

        advancedEntityTasks.put(zombieHorse, task);
    }

    /**
     * 启动变异僵尸04的机枪攻击
     */
    private void startMutantZombie04MachineGun(WitherSkeleton witherSkeleton) {
        BukkitTask task = new BukkitRunnable() {
            @Override
            public void run() {
                if (witherSkeleton == null || witherSkeleton.isDead()) {
                    cancel();
                    advancedEntityTasks.remove(witherSkeleton);
                    return;
                }

                // 查找20格内最近的玩家
                Player target = null;
                double closestDistance = 21.0;

                for (Entity entity : witherSkeleton.getNearbyEntities(20, 20, 20)) {
                    if (entity instanceof Player) {
                        double distance = witherSkeleton.getLocation().distance(entity.getLocation());
                        if (distance < closestDistance) {
                            closestDistance = distance;
                            target = (Player) entity;
                        }
                    }
                }

                if (target != null) {
                    // 让凋零骷髅面向目标
                    witherSkeleton.teleport(witherSkeleton.getLocation().setDirection(
                            target.getLocation().subtract(witherSkeleton.getLocation()).toVector()));

                    // 发射连续的箭矢（机枪效果）
                    for (int i = 0; i < 5; i++) {
                        new BukkitRunnable() {
                            @Override
                            public void run() {
                                if (witherSkeleton == null || witherSkeleton.isDead()) {
                                    return;
                                }

                                // 发射箭矢
                                Arrow arrow = witherSkeleton.launchProjectile(Arrow.class);
                                arrow.setVelocity(witherSkeleton.getLocation().getDirection().multiply(2.0));
                                arrow.setMetadata("mutantZombie04Arrow", new FixedMetadataValue(plugin, true));

                                // 播放射击音效
                                witherSkeleton.getWorld().playSound(witherSkeleton.getLocation(),
                                        org.bukkit.Sound.ENTITY_SKELETON_SHOOT, 1.0f, 1.5f);
                            }
                        }.runTaskLater(plugin, i * 2); // 每2tick发射一支箭
                    }
                }
            }
        }.runTaskTimer(plugin, 40, 100); // 2秒后开始，每5秒执行一次

        advancedEntityTasks.put(witherSkeleton, task);
    }

    /**
     * 启动变异僵尸Max的雷电攻击
     */
    private void startMutantZombieMaxLightning(Drowned mutantZombieMax) {
        BukkitTask task = new BukkitRunnable() {
            @Override
            public void run() {
                if (mutantZombieMax == null || mutantZombieMax.isDead()) {
                    cancel();
                    advancedEntityTasks.remove(mutantZombieMax);
                    return;
                }

                // 查找25格内最近的玩家
                Player target = null;
                double closestDistance = 26.0;

                for (Entity entity : mutantZombieMax.getNearbyEntities(25, 25, 25)) {
                    if (entity instanceof Player) {
                        double distance = mutantZombieMax.getLocation().distance(entity.getLocation());
                        if (distance < closestDistance) {
                            closestDistance = distance;
                            target = (Player) entity;
                        }
                    }
                }

                if (target != null) {
                    // 在目标位置召唤雷电
                    Location targetLoc = target.getLocation();
                    targetLoc.getWorld().strikeLightning(targetLoc);

                    // 显示雷电粒子效果
                    targetLoc.getWorld().spawnParticle(org.bukkit.Particle.ELECTRIC_SPARK, targetLoc, 50, 1, 3, 1, 0.1);

                    // 播放雷电音效
                    targetLoc.getWorld().playSound(targetLoc, org.bukkit.Sound.ENTITY_LIGHTNING_BOLT_THUNDER, 1.0f, 1.0f);

                    // 对目标造成额外伤害
                    target.damage(8.0, mutantZombieMax);
                }
            }
        }.runTaskTimer(plugin, 60, 120); // 3秒后开始，每6秒执行一次

        advancedEntityTasks.put(mutantZombieMax, task);
    }

    /**
     * 启动灵魂坚守者的音波攻击
     */
    private void startSoulGuardianSonicAttack(LivingEntity soulGuardian) {
        BukkitTask task = new BukkitRunnable() {
            @Override
            public void run() {
                if (soulGuardian == null || soulGuardian.isDead()) {
                    cancel();
                    advancedEntityTasks.remove(soulGuardian);
                    return;
                }

                // 查找30格内的所有玩家
                for (Entity entity : soulGuardian.getNearbyEntities(30, 30, 30)) {
                    if (entity instanceof Player) {
                        Player player = (Player) entity;

                        // 音波攻击效果
                        player.damage(6.0, soulGuardian);
                        player.addPotionEffect(new PotionEffect(PotionEffectType.SLOWNESS, 100, 2)); // 缓慢III 5秒
                        player.addPotionEffect(new PotionEffect(PotionEffectType.NAUSEA, 100, 1)); // 恶心II 5秒

                        // 显示音波粒子效果
                        Location playerLoc = player.getLocation();
                        playerLoc.getWorld().spawnParticle(org.bukkit.Particle.SONIC_BOOM, playerLoc, 1, 0, 0, 0, 0);
                        playerLoc.getWorld().spawnParticle(org.bukkit.Particle.VIBRATION, playerLoc, 20, 2, 2, 2, 0.1);
                    }
                }

                // 播放音波攻击音效
                soulGuardian.getWorld().playSound(soulGuardian.getLocation(),
                        org.bukkit.Sound.ENTITY_WARDEN_SONIC_BOOM, 1.0f, 0.8f);

                // 显示灵魂坚守者周围的粒子效果
                Location guardianLoc = soulGuardian.getLocation();
                guardianLoc.getWorld().spawnParticle(org.bukkit.Particle.SOUL, guardianLoc, 50, 3, 3, 3, 0.1);
            }
        }.runTaskTimer(plugin, 80, 160); // 4秒后开始，每8秒执行一次

        advancedEntityTasks.put(soulGuardian, task);
    }

    /**
     * 启动凋零领主的特殊攻击
     */
    private void startWitherLordAttacks(Wither witherLord) {
        BukkitTask task = new BukkitRunnable() {
            @Override
            public void run() {
                if (witherLord == null || witherLord.isDead()) {
                    cancel();
                    advancedEntityTasks.remove(witherLord);
                    return;
                }

                // 查找35格内最近的玩家
                Player target = null;
                double closestDistance = 36.0;

                for (Entity entity : witherLord.getNearbyEntities(35, 35, 35)) {
                    if (entity instanceof Player) {
                        double distance = witherLord.getLocation().distance(entity.getLocation());
                        if (distance < closestDistance) {
                            closestDistance = distance;
                            target = (Player) entity;
                        }
                    }
                }

                if (target != null) {
                    // 发射强化凋零头颅
                    final Player finalTarget = target; // 创建final变量供内部类使用
                    for (int i = 0; i < 3; i++) {
                        new BukkitRunnable() {
                            @Override
                            public void run() {
                                if (witherLord == null || witherLord.isDead()) {
                                    return;
                                }

                                // 发射凋零头颅
                                WitherSkull skull = witherLord.launchProjectile(WitherSkull.class);
                                skull.setDirection(finalTarget.getLocation().subtract(witherLord.getLocation()).toVector().normalize());
                                skull.setYield(2.0f); // 增强爆炸威力
                                skull.setCharged(true); // 蓝色头颅
                                skull.setMetadata("witherLordSkull", new FixedMetadataValue(plugin, true));
                            }
                        }.runTaskLater(plugin, i * 10); // 每10tick发射一个头颅
                    }

                    // 播放凋零攻击音效
                    witherLord.getWorld().playSound(witherLord.getLocation(),
                            org.bukkit.Sound.ENTITY_WITHER_SHOOT, 1.0f, 0.8f);

                    // 显示凋零粒子效果
                    Location witherLoc = witherLord.getLocation();
                    witherLoc.getWorld().spawnParticle(org.bukkit.Particle.SMOKE, witherLoc, 30, 2, 2, 2, 0.1);
                }
            }
        }.runTaskTimer(plugin, 100, 140); // 5秒后开始，每7秒执行一次

        advancedEntityTasks.put(witherLord, task);
    }

    /**
     * 启动异变之王的龙息攻击
     */
    private void startMutationKingBreathAttack(EnderDragon dragon) {
        BukkitTask task = new BukkitRunnable() {
            @Override
            public void run() {
                if (dragon == null || dragon.isDead()) {
                    cancel();
                    advancedEntityTasks.remove(dragon);
                    return;
                }

                // 查找40格内最近的玩家
                Player target = null;
                double closestDistance = 41.0;

                for (Entity entity : dragon.getNearbyEntities(40, 40, 40)) {
                    if (entity instanceof Player) {
                        double distance = dragon.getLocation().distance(entity.getLocation());
                        if (distance < closestDistance) {
                            closestDistance = distance;
                            target = (Player) entity;
                        }
                    }
                }

                if (target != null) {
                    // 创建龙息云
                    Location targetLoc = target.getLocation();
                    AreaEffectCloud breathCloud = (AreaEffectCloud) targetLoc.getWorld().spawnEntity(targetLoc, EntityType.AREA_EFFECT_CLOUD);
                    breathCloud.setRadius(4.0f);
                    breathCloud.setDuration(100); // 5秒持续时间
                    breathCloud.addCustomEffect(new PotionEffect(PotionEffectType.INSTANT_DAMAGE, 1, 1), true);
                    breathCloud.addCustomEffect(new PotionEffect(PotionEffectType.WITHER, 100, 2), true);
                    breathCloud.setParticle(org.bukkit.Particle.DRAGON_BREATH);
                    breathCloud.setMetadata("mutationKingBreath", new FixedMetadataValue(plugin, true));

                    // 播放龙息音效
                    dragon.getWorld().playSound(dragon.getLocation(),
                            org.bukkit.Sound.ENTITY_ENDER_DRAGON_SHOOT, 1.0f, 0.8f);

                    // 显示龙息轨迹粒子
                    Location dragonLoc = dragon.getLocation();
                    dragonLoc.getWorld().spawnParticle(org.bukkit.Particle.DRAGON_BREATH, dragonLoc, 50, 3, 3, 3, 0.1);
                }
            }
        }.runTaskTimer(plugin, 120, 180); // 6秒后开始，每9秒执行一次

        advancedEntityTasks.put(dragon, task);
    }
}
