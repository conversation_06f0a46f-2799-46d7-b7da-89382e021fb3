package org.Ver_zhzh.customZombie;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.Set;
import java.util.logging.Logger;

import org.bukkit.Bukkit;
import org.bukkit.Color;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.Particle;
import org.bukkit.Sound;
import org.bukkit.World;
import org.bukkit.boss.BarColor;
import org.bukkit.boss.BarStyle;
import org.bukkit.boss.BossBar;
import org.bukkit.entity.AreaEffectCloud;
import org.bukkit.entity.ArmorStand;
import org.bukkit.entity.EnderCrystal;
import org.bukkit.entity.EnderDragon;
import org.bukkit.entity.Entity;
import org.bukkit.entity.EntityType;
import org.bukkit.entity.FallingBlock;
import org.bukkit.entity.Player;
import org.bukkit.metadata.FixedMetadataValue;
import org.bukkit.plugin.Plugin;
import org.bukkit.potion.PotionEffect;
import org.bukkit.potion.PotionEffectType;
import org.bukkit.scheduler.BukkitRunnable;
import org.bukkit.scheduler.BukkitTask;
import org.bukkit.util.Vector;

/**
 * MutationKing类 - 异变之王（末影龙）实体管理器 负责生成和管理异变之王实体及其特殊能力
 */
public class MutationKing {

    private Plugin plugin;
    private Logger logger;
    private ParticleHelper particleHelper;
    private Random random = new Random();
    private CustomZombie customZombie;

    // 存储异变之王实体及其相关任务
    private Map<EnderDragon, List<BukkitTask>> dragonTasks = new HashMap<>();

    // 存储异变之王的Boss血条
    private Map<EnderDragon, BossBar> dragonBossBars = new HashMap<>();

    // 末影水晶攻击冷却时间（毫秒）
    private static final long CRYSTAL_ATTACK_COOLDOWN = 15000;
    private Map<EnderDragon, Long> lastCrystalAttackTime = new HashMap<>();

    // 龙息攻击冷却时间（毫秒）
    private static final long BREATH_ATTACK_COOLDOWN = 10000;
    private Map<EnderDragon, Long> lastBreathAttackTime = new HashMap<>();

    // 末影粒子场冷却时间（毫秒）
    private static final long ENDER_FIELD_COOLDOWN = 20000;
    private Map<EnderDragon, Long> lastEnderFieldTime = new HashMap<>();

    // 召唤变异生物冷却时间（毫秒）
    private static final long SUMMON_COOLDOWN = 4000;
    private Map<EnderDragon, Long> lastSummonTime = new HashMap<>();

    // 黑曜石方块攻击冷却时间（毫秒）
    private static final long OBSIDIAN_ATTACK_COOLDOWN = 5000; // 从8000减少到5000，提高频率
    private Map<EnderDragon, Long> lastObsidianAttackTime = new HashMap<>();

    // 下界栅栏攻击冷却时间（毫秒）
    private static final long NETHER_FENCE_ATTACK_COOLDOWN = 6000; // 从10000减少到6000，提高频率
    private Map<EnderDragon, Long> lastNetherFenceAttackTime = new HashMap<>();

    // 存储临时方块位置，用于清理
    private Map<EnderDragon, List<Location>> obsidianBlockLocations = new HashMap<>();
    private Map<EnderDragon, List<Location>> netherFenceLocations = new HashMap<>();
    private Map<Location, Material> originalBlockMaterials = new HashMap<>();

    /**
     * 构造函数
     *
     * @param plugin 宿主插件实例
     * @param particleHelper 粒子效果助手
     * @param customZombie CustomZombie实例，用于召唤变异生物
     */
    public MutationKing(Plugin plugin, ParticleHelper particleHelper, CustomZombie customZombie) {
        this.plugin = plugin;
        this.logger = plugin.getLogger();
        this.particleHelper = particleHelper;
        this.customZombie = customZombie;

        logger.info("MutationKing管理器已初始化");
    }

    /**
     * 生成异变之王（末影龙）
     *
     * @param location 生成位置
     * @return 生成的末影龙实体
     */
    public EnderDragon spawnMutationKing(Location location) {
        try {
            // 生成末影龙实体
            EnderDragon dragon = (EnderDragon) location.getWorld().spawnEntity(location, EntityType.ENDER_DRAGON);

            // 设置自定义名称和生命值
            dragon.setCustomName("§4异变之王");
            dragon.setCustomNameVisible(false); // 默认隐藏，由EntityNameDisplayListener控制显示
            dragon.setMaxHealth(10999.0);
            dragon.setHealth(10999.0);

            // 添加速度3效果
            dragon.addPotionEffect(new PotionEffect(PotionEffectType.SPEED, Integer.MAX_VALUE, 2, false, false));

            // 添加元数据标记，用于识别
            dragon.setMetadata("mutationKing", new FixedMetadataValue(plugin, true));

            // 添加idcZombieEntity元数据标记，确保实体被识别为僵尸
            dragon.setMetadata("idcZombieEntity", new FixedMetadataValue(plugin, true));
            dragon.setMetadata("gameEntity", new FixedMetadataValue(plugin, true));
            logger.info("已为异变之王添加idcZombieEntity标记");

            // 启用敌对AI，确保主动攻击玩家
            if (plugin instanceof org.Ver_zhzh.deathZombieV4.DeathZombieV4) {
                org.Ver_zhzh.deathZombieV4.DeathZombieV4 dzPlugin = (org.Ver_zhzh.deathZombieV4.DeathZombieV4) plugin;
                if (dzPlugin.getHostileAIManager() != null) {
                    dzPlugin.getHostileAIManager().enableHostileAI(dragon);
                    logger.info("已为异变之王启用敌对AI");
                }
            }

            // 创建BOSS血条（检查是否已存在，避免重复创建）
            if (!dragon.hasMetadata("mutationKingBossBar")) {
                createBossBar(dragon);
                logger.info("原版MutationKing为异变之王创建Boss血条");
            } else {
                logger.info("异变之王已存在Boss血条，跳过创建");
            }

            // 启动AI任务
            startDragonTasks(dragon);

            // 播放生成音效和粒子效果
            location.getWorld().playSound(location, Sound.ENTITY_ENDER_DRAGON_GROWL, 3.0f, 0.5f);
            particleHelper.displayParticle(Bukkit.getOnlinePlayers(), location, 3.0f, 3.0f, 3.0f, 0.1f, 100, Particle.DRAGON_BREATH);

            logger.info("异变之王已生成，UUID: " + dragon.getUniqueId());
            return dragon;
        } catch (Exception e) {
            logger.severe("生成异变之王时出错: " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 创建BOSS血条
     *
     * @param dragon 异变之王实体
     */
    private void createBossBar(EnderDragon dragon) {
        // 创建BOSS血条
        BossBar bossBar = Bukkit.createBossBar(
                "§4异变之王 §7- §c" + (int) dragon.getHealth() + "/" + (int) dragon.getMaxHealth() + " HP",
                BarColor.RED,
                BarStyle.SEGMENTED_20
        );

        // 显示给所有玩家
        for (Player player : Bukkit.getOnlinePlayers()) {
            bossBar.addPlayer(player);
        }

        // 保存BOSS血条引用
        dragonBossBars.put(dragon, bossBar);

        // 设置元数据标记，避免重复创建
        dragon.setMetadata("mutationKingBossBar", new FixedMetadataValue(plugin, bossBar));
    }

    /**
     * 启动异变之王的AI任务
     *
     * @param dragon 异变之王实体
     */
    private void startDragonTasks(EnderDragon dragon) {
        // 清理之前可能存在的任务
        if (dragonTasks.containsKey(dragon)) {
            List<BukkitTask> tasks = dragonTasks.get(dragon);
            for (BukkitTask task : tasks) {
                if (task != null && !task.isCancelled()) {
                    task.cancel();
                }
            }
            dragonTasks.remove(dragon);
        }

        // 初始化冷却时间
        lastEnderFieldTime.put(dragon, 0L);
        lastSummonTime.put(dragon, 0L);

        // 创建新的任务列表
        List<BukkitTask> newTasks = new ArrayList<>();

        // 任务0：更新BOSS血条
        BukkitTask bossBarTask = new BukkitRunnable() {
            @Override
            public void run() {
                if (dragon == null || !dragon.isValid() || dragon.isDead()) {
                    this.cancel();
                    cleanupTasks(dragon);
                    return;
                }

                // 更新BOSS血条
                BossBar bossBar = dragonBossBars.get(dragon);
                if (bossBar != null) {
                    // 更新血条标题
                    bossBar.setTitle("§4异变之王 §7- §c" + (int) dragon.getHealth() + "/" + (int) dragon.getMaxHealth() + " HP");

                    // 更新血条进度
                    double progress = dragon.getHealth() / dragon.getMaxHealth();
                    bossBar.setProgress(Math.max(0, Math.min(1, progress)));

                    // 确保所有玩家都能看到血条
                    for (Player player : Bukkit.getOnlinePlayers()) {
                        if (!bossBar.getPlayers().contains(player)) {
                            bossBar.addPlayer(player);
                        }
                    }
                }
            }
        }.runTaskTimer(plugin, 0L, 20L); // 立即开始，每秒更新一次
        newTasks.add(bossBarTask);

        // 任务1：移动和追踪玩家
        BukkitTask movementTask = new BukkitRunnable() {
            private Player currentTarget = null;
            private int updateTargetCounter = 0;
            private final int UPDATE_TARGET_INTERVAL = 40; // 每40次移动更新（约10秒）重新选择目标
            private int tickCounter = 0;
            private int chargeCooldown = 0;

            @Override
            public void run() {
                tickCounter++;

                if (dragon == null || !dragon.isValid() || dragon.isDead()) {
                    this.cancel();
                    cleanupTasks(dragon);
                    logger.info("末影龙移动模拟任务取消：实体无效或已死亡");
                    return;
                }

                // 冷却时间计数
                if (chargeCooldown > 0) {
                    chargeCooldown--;
                }

                // 每UPDATE_TARGET_INTERVAL次移动更新一次目标，或者当前没有目标时
                if (currentTarget == null || !currentTarget.isOnline()
                        || currentTarget.isDead() || updateTargetCounter >= UPDATE_TARGET_INTERVAL) {

                    // 重置计数器
                    updateTargetCounter = 0;

                    // 查找50格范围内最近的玩家
                    Player nearestPlayer = null;
                    double nearestDistance = 50.0;

                    for (Entity entity : dragon.getNearbyEntities(50, 50, 50)) {
                        if (entity instanceof Player) {
                            Player player = (Player) entity;
                            // 只考虑生存模式的玩家
                            if (player.getGameMode() == org.bukkit.GameMode.SURVIVAL) {
                                double distance = dragon.getLocation().distance(player.getLocation());
                                if (distance < nearestDistance) {
                                    nearestDistance = distance;
                                    nearestPlayer = player;
                                }
                            }
                        }
                    }

                    // 更新当前目标
                    currentTarget = nearestPlayer;
                } else {
                    // 增加计数器
                    updateTargetCounter++;
                }

                // 如果有目标，移动末影龙向玩家
                if (currentTarget != null) {
                    // 计算到目标的距离
                    double distance = dragon.getLocation().distance(currentTarget.getLocation());

                    // 计算方向向量
                    Vector direction = currentTarget.getLocation().subtract(dragon.getLocation()).toVector().normalize();

                    // 设置末影龙的朝向
                    Location newLoc = dragon.getLocation().clone();
                    newLoc.setDirection(direction);
                    dragon.teleport(newLoc);

                    // 获取当前位置
                    Location currentLoc = dragon.getLocation();

                    // 检查龙是否在地面上或接近地面
                    boolean isNearGround = isNearGround(currentLoc, 2.0);

                    // 确保末影龙保持在适当的高度 (至少在地面以上10格)
                    double minHeight = getGroundLevel(currentLoc) + 10;
                    double targetY = Math.max(currentTarget.getLocation().getY() + 5, minHeight);

                    // 计算Y轴修正向量 - 如果接近地面，强制上升
                    double yCorrection = 0;
                    if (isNearGround) {
                        yCorrection = 1.0; // 强制上升
                        // 记录日志
                        logger.info("末影龙接近地面，强制上升");
                        // 播放起飞音效
                        dragon.getWorld().playSound(dragon.getLocation(), Sound.ENTITY_ENDER_DRAGON_FLAP, 1.5f, 0.8f);
                    } else if (currentLoc.getY() < targetY - 2) {
                        yCorrection = 0.5; // 上升
                    } else if (currentLoc.getY() > targetY + 2) {
                        yCorrection = -0.3; // 下降
                    }

                    // 如果龙在地面上或接近地面，强制起飞
                    if (isNearGround) {
                        // 强制向上飞行
                        Vector upVector = new Vector(
                                direction.getX() * 0.5,
                                1.5, // 强制上升
                                direction.getZ() * 0.5
                        );
                        dragon.setVelocity(upVector);

                        // 记录日志
                        logger.info("末影龙接近地面，强制上升（追击模式）");

                        // 播放起飞音效
                        dragon.getWorld().playSound(dragon.getLocation(), Sound.ENTITY_ENDER_DRAGON_FLAP, 1.5f, 0.8f);
                        dragon.getWorld().playSound(dragon.getLocation(), Sound.ENTITY_ENDER_DRAGON_FLAP, 1.5f, 1.0f);

                        // 添加粒子效果
                        particleHelper.displayParticle(Bukkit.getOnlinePlayers(),
                                dragon.getLocation(), 1.5f, 0.5f, 1.5f, 0.1f, 30, Particle.CLOUD);

                        // 跳过本次其他移动逻辑
                        return;
                    }

                    // 根据距离选择不同的移动策略
                    if (distance > 20.0) {
                        // 距离很远，快速飞行接近
                        Vector moveVector = direction.clone().multiply(1.8);
                        moveVector.setY(moveVector.getY() + yCorrection);
                        dragon.setVelocity(moveVector);

                        // 每5次移动播放一次飞行音效
                        if (tickCounter % 5 == 0) {
                            dragon.getWorld().playSound(dragon.getLocation(), Sound.ENTITY_ENDER_DRAGON_FLAP, 1.0f, 1.0f);
                        }

                        // 添加飞行粒子效果
                        if (tickCounter % 3 == 0) {
                            particleHelper.displayParticle(Bukkit.getOnlinePlayers(),
                                    dragon.getLocation().clone().subtract(direction.clone().multiply(2)),
                                    0.5f, 0.5f, 0.5f, 0.05f, 10, Particle.CLOUD);
                        }
                    } else if (distance > 10.0) {
                        // 距离较远，中速飞行接近
                        Vector moveVector = direction.clone().multiply(1.2);
                        moveVector.setY(moveVector.getY() + yCorrection);
                        dragon.setVelocity(moveVector);

                        // 每8次移动播放一次飞行音效
                        if (tickCounter % 8 == 0) {
                            dragon.getWorld().playSound(dragon.getLocation(), Sound.ENTITY_ENDER_DRAGON_FLAP, 0.8f, 1.2f);
                        }
                    } else if (distance > 5.0) {
                        // 中等距离，慢速接近，准备攻击
                        Vector moveVector = direction.clone().multiply(0.8);
                        moveVector.setY(moveVector.getY() + yCorrection);
                        dragon.setVelocity(moveVector);

                        // 播放警告音效
                        if (tickCounter % 10 == 0) {
                            dragon.getWorld().playSound(dragon.getLocation(), Sound.ENTITY_ENDER_DRAGON_GROWL, 0.7f, 0.9f);
                        }

                        // 随机喷吐龙息粒子
                        if (random.nextInt(15) == 0) {
                            particleHelper.displayParticle(Bukkit.getOnlinePlayers(),
                                    dragon.getLocation().clone().add(direction.clone().multiply(3)),
                                    0.5f, 0.5f, 0.5f, 0.05f, 10, Particle.DRAGON_BREATH);
                        }
                    } else {
                        // 距离较近，执行攻击行为
                        if (chargeCooldown <= 0 && random.nextInt(10) < 4) { // 40%几率冲锋
                            // 冲锋攻击
                            Vector chargeVector = direction.clone().multiply(2.5);
                            chargeVector.setY(chargeVector.getY() * 0.5); // 减少Y轴分量，使冲锋更加水平
                            dragon.setVelocity(chargeVector);

                            // 播放冲锋音效
                            dragon.getWorld().playSound(dragon.getLocation(), Sound.ENTITY_ENDER_DRAGON_GROWL, 1.5f, 0.8f);

                            // 冲锋粒子效果
                            particleHelper.displayParticle(Bukkit.getOnlinePlayers(), dragon.getLocation(), 1.0f, 1.0f, 1.0f, 0.1f, 30, Particle.DRAGON_BREATH);

                            // 设置冲锋冷却
                            chargeCooldown = 30; // 7.5秒冷却
                        } else {
                            // 悬停或盘旋
                            // 计算盘旋向量 - 在目标周围盘旋
                            double angle = (tickCounter % 360) * Math.PI / 180;
                            double radius = 5.0;
                            double circleX = Math.cos(angle) * radius;
                            double circleZ = Math.sin(angle) * radius;

                            // 确保盘旋时也保持一定高度，并添加小幅度的上下波动
                            double circleY = yCorrection + Math.sin(angle * 0.5) * 0.3;

                            Vector circleVector = new Vector(circleX, circleY, circleZ).normalize().multiply(0.7);
                            dragon.setVelocity(circleVector);

                            // 随机播放龙的声音
                            if (random.nextInt(20) == 0) {
                                Sound[] dragonSounds = {
                                    Sound.ENTITY_ENDER_DRAGON_AMBIENT,
                                    Sound.ENTITY_ENDER_DRAGON_GROWL,
                                    Sound.ENTITY_ENDER_DRAGON_FLAP
                                };
                                dragon.getWorld().playSound(dragon.getLocation(),
                                        dragonSounds[random.nextInt(dragonSounds.length)],
                                        0.8f, 0.9f + random.nextFloat() * 0.2f);
                            }
                        }
                    }

                    // 如果距离小于15格，每10次移动更新（约2.5秒）尝试一次特殊攻击
                    if (distance < 15.0 && tickCounter % 10 == 0) {
                        // 随机选择攻击方式
                        int attackChoice = random.nextInt(4); // 增加到4种攻击方式
                        switch (attackChoice) {
                            case 0:
                                // 尝试使用末影水晶攻击
                                if (System.currentTimeMillis() - getLastCrystalAttackTime(dragon) > CRYSTAL_ATTACK_COOLDOWN) {
                                    performCrystalAttack(dragon, currentTarget);
                                }
                                break;
                            case 1:
                                // 尝试使用龙息攻击
                                if (System.currentTimeMillis() - getLastBreathAttackTime(dragon) > BREATH_ATTACK_COOLDOWN) {
                                    performBreathAttack(dragon, currentTarget);
                                }
                                break;
                            case 2:
                                // 黑曜石方块攻击
                                if (random.nextInt(3) == 0) { // 33%几率
                                    shootObsidianBlocks(dragon, currentTarget);
                                }
                                break;
                            case 3:
                                // 新增：组合攻击（黑曜石方块+下界栅栏）
                                if (System.currentTimeMillis() - lastObsidianAttackTime.getOrDefault(dragon, 0L) > OBSIDIAN_ATTACK_COOLDOWN) {
                                    // 执行组合攻击
                                    createObsidianPillarAttack(dragon, currentTarget);

                                    // 1秒后执行下界栅栏攻击
                                    new BukkitRunnable() {
                                        @Override
                                        public void run() {
                                            if (dragon != null && !dragon.isDead() && currentTarget != null && currentTarget.isOnline()) {
                                                shootNetherFenceAttack(dragon, currentTarget);
                                            }
                                        }
                                    }.runTaskLater(plugin, 20L); // 1秒后执行
                                }
                                break;
                        }
                    }
                } else {
                    // 如果没有玩家，执行巡逻飞行模式
                    Location dragonLoc = dragon.getLocation();

                    // 检查龙是否在地面上或接近地面
                    boolean isNearGround = isNearGround(dragonLoc, 2.0);

                    // 确保末影龙保持在适当的高度范围内
                    double groundLevel = getGroundLevel(dragonLoc);
                    double minHeight = groundLevel + 10;
                    double maxHeight = groundLevel + 30;

                    // 计算Y轴修正向量
                    double yCorrection = 0;
                    if (isNearGround) {
                        yCorrection = 1.0; // 强制上升
                        // 记录日志
                        logger.info("末影龙接近地面，强制上升（巡逻模式）");
                        // 播放起飞音效
                        dragon.getWorld().playSound(dragon.getLocation(), Sound.ENTITY_ENDER_DRAGON_FLAP, 1.5f, 0.8f);

                        // 强制向上飞行
                        Vector upVector = new Vector(
                                (random.nextDouble() - 0.5) * 0.5,
                                1.0, // 强制上升
                                (random.nextDouble() - 0.5) * 0.5
                        );
                        dragon.setVelocity(upVector);

                        // 添加粒子效果
                        particleHelper.displayParticle(Bukkit.getOnlinePlayers(),
                                dragonLoc, 1.0f, 0.5f, 1.0f, 0.1f, 20, Particle.CLOUD);

                        // 跳过本次其他移动逻辑，直接进入下一个tick
                        return;
                    } else if (dragonLoc.getY() < minHeight) {
                        yCorrection = 0.5; // 上升
                    } else if (dragonLoc.getY() > maxHeight) {
                        yCorrection = -0.3; // 下降
                    }

                    // 每5秒改变一次飞行方向
                    if (tickCounter % 100 == 0) {
                        // 生成一个新的随机目标点
                        double angle = random.nextDouble() * 2 * Math.PI;
                        double distance = 20 + random.nextDouble() * 20; // 20-40格范围内
                        double x = dragonLoc.getX() + Math.cos(angle) * distance;
                        double z = dragonLoc.getZ() + Math.sin(angle) * distance;

                        // 保存为临时目标点
                        Location targetLoc = new Location(dragonLoc.getWorld(), x, dragonLoc.getY(), z);
                        dragon.setMetadata("patrolTarget", new FixedMetadataValue(plugin, targetLoc));

                        // 播放飞行音效
                        dragon.getWorld().playSound(dragonLoc, Sound.ENTITY_ENDER_DRAGON_FLAP, 1.0f, 0.8f);
                    }

                    // 获取当前的巡逻目标
                    Location patrolTarget = null;
                    if (dragon.hasMetadata("patrolTarget")) {
                        patrolTarget = (Location) dragon.getMetadata("patrolTarget").get(0).value();
                    }

                    // 如果有巡逻目标，向目标移动
                    if (patrolTarget != null) {
                        // 计算方向向量
                        Vector direction = patrolTarget.clone().subtract(dragonLoc).toVector().normalize();

                        // 设置末影龙的朝向
                        Location lookLoc = dragonLoc.clone();
                        lookLoc.setDirection(direction);
                        dragon.teleport(lookLoc);

                        // 计算飞行向量
                        Vector flyVector = direction.clone().multiply(1.0);
                        flyVector.setY(flyVector.getY() + yCorrection);
                        dragon.setVelocity(flyVector);

                        // 每10次移动播放一次飞行音效
                        if (tickCounter % 10 == 0) {
                            dragon.getWorld().playSound(dragonLoc, Sound.ENTITY_ENDER_DRAGON_FLAP, 0.5f, 1.0f);
                        }

                        // 随机喷吐龙息粒子
                        if (random.nextInt(20) == 0) {
                            particleHelper.displayParticle(Bukkit.getOnlinePlayers(),
                                    dragonLoc.clone().add(direction.clone().multiply(3)),
                                    1.0f, 1.0f, 1.0f, 0.1f, 15, Particle.DRAGON_BREATH);
                        }
                    } else {
                        // 如果没有巡逻目标，随机移动
                        randomMovement(dragon, yCorrection);
                    }
                }
            }
        }.runTaskTimer(plugin, 5, 5); // 0.25秒后开始，每0.25秒执行一次
        newTasks.add(movementTask);

        // 任务2：近战攻击检测
        BukkitTask attackTask = new BukkitRunnable() {
            @Override
            public void run() {
                if (dragon == null || !dragon.isValid() || dragon.isDead()) {
                    this.cancel();
                    cleanupTasks(dragon);
                    return;
                }

                // 获取附近的玩家
                for (Player player : Bukkit.getOnlinePlayers()) {
                    if (player.getWorld().equals(dragon.getWorld())
                            && player.getLocation().distance(dragon.getLocation()) < 5.0) {

                        // 造成伤害
                        player.damage(10.0, dragon);

                        // 击退效果
                        Vector knockback = player.getLocation().toVector().subtract(dragon.getLocation().toVector()).normalize().multiply(1.5).setY(0.5);
                        player.setVelocity(knockback);

                        // 播放攻击音效
                        player.getWorld().playSound(player.getLocation(), Sound.ENTITY_ENDER_DRAGON_HURT, 0.5f, 1.0f);
                    }
                }
            }
        }.runTaskTimer(plugin, 10L, 10L); // 0.5秒后开始，每0.5秒执行一次
        newTasks.add(attackTask);

        // 任务3：末影水晶爆炸检测
        BukkitTask crystalTask = new BukkitRunnable() {
            @Override
            public void run() {
                if (dragon == null || !dragon.isValid() || dragon.isDead()) {
                    this.cancel();
                    cleanupTasks(dragon);
                    return;
                }

                // 检查附近的末影水晶
                for (Entity entity : dragon.getNearbyEntities(20, 20, 20)) {
                    if (entity instanceof EnderCrystal && entity.hasMetadata("mutationKingCrystal")) {
                        EnderCrystal crystal = (EnderCrystal) entity;

                        // 检查是否接近玩家
                        for (Entity nearby : crystal.getNearbyEntities(2, 2, 2)) {
                            if (nearby instanceof Player) {
                                Player player = (Player) nearby;

                                // 爆炸效果
                                Location explodeLoc = crystal.getLocation();
                                crystal.getWorld().createExplosion(explodeLoc, 0.0f, false, false);

                                // 造成伤害
                                player.damage(20.0, dragon);

                                // 添加效果
                                player.addPotionEffect(new PotionEffect(PotionEffectType.BLINDNESS, 60, 0)); // 3秒失明
                                player.addPotionEffect(new PotionEffect(PotionEffectType.NAUSEA, 100, 0)); // 5秒反胃

                                // 粒子效果
                                particleHelper.displayParticle(Bukkit.getOnlinePlayers(), explodeLoc, 3.0f, 3.0f, 3.0f, 0.1f, 100, Particle.EXPLOSION);

                                // 移除水晶
                                crystal.remove();

                                // 治疗龙
                                if (dragon != null && !dragon.isDead()) {
                                    double currentHealth = dragon.getHealth();
                                    double maxHealth = dragon.getMaxHealth();
                                    if (currentHealth < maxHealth) {
                                        dragon.setHealth(Math.min(currentHealth + 100.0, maxHealth)); // 恢复100点生命值

                                        // 显示治疗效果
                                        particleHelper.displayParticle(Bukkit.getOnlinePlayers(), dragon.getLocation(), 2.0f, 2.0f, 2.0f, 0.1f, 30, Particle.HEART);
                                    }
                                }

                                break;
                            }
                        }
                    }
                }
            }
        }.runTaskTimer(plugin, 5L, 5L); // 0.25秒后开始，每0.25秒执行一次
        newTasks.add(crystalTask);

        // 任务4：末影粒子场
        BukkitTask enderFieldTask = new BukkitRunnable() {
            @Override
            public void run() {
                if (dragon == null || !dragon.isValid() || dragon.isDead()) {
                    this.cancel();
                    cleanupTasks(dragon);
                    return;
                }

                // 创建末影粒子场
                createEnderField(dragon);

                // 更新上次使用时间
                lastEnderFieldTime.put(dragon, System.currentTimeMillis());
            }
        }.runTaskTimer(plugin, 100L, 400L); // 5秒后开始，每20秒执行一次
        newTasks.add(enderFieldTask);

        // 任务5：随机召唤变异生物
        BukkitTask summonTask = new BukkitRunnable() {
            @Override
            public void run() {
                if (dragon == null || !dragon.isValid() || dragon.isDead()) {
                    this.cancel();
                    cleanupTasks(dragon);
                    return;
                }

                // 随机召唤变异生物
                summonRandomMutant(dragon);

                // 更新上次召唤时间
                lastSummonTime.put(dragon, System.currentTimeMillis());
            }
        }.runTaskTimer(plugin, 200L, 80L); // 10秒后开始，每4秒执行一次
        newTasks.add(summonTask);

        // 保存任务列表
        dragonTasks.put(dragon, newTasks);
    }

    /**
     * 随机移动
     *
     * @param dragon 末影龙实体
     * @param yCorrection Y轴修正值（可选）
     */
    private void randomMovement(EnderDragon dragon, double... yCorrection) {
        // 生成随机移动向量
        double dx = (random.nextDouble() - 0.5) * 0.8;
        double dy = (random.nextDouble() - 0.5) * 0.5;
        double dz = (random.nextDouble() - 0.5) * 0.8;

        // 应用Y轴修正（如果提供）
        if (yCorrection.length > 0) {
            dy += yCorrection[0];
        }

        Vector randomVector = new Vector(dx, dy, dz);
        dragon.setVelocity(randomVector);

        // 随机播放飞行音效
        if (random.nextInt(5) == 0) {
            dragon.getWorld().playSound(dragon.getLocation(), Sound.ENTITY_ENDER_DRAGON_FLAP, 0.3f, 1.2f);
        }
    }

    /**
     * 发射黑曜石方块攻击，增加数量和持续时间，确保所有方块都被清理
     *
     * @param dragon 异变之王实体
     * @param target 目标玩家
     */
    private void shootObsidianBlocks(EnderDragon dragon, Player target) {
        // 获取龙的位置和方向
        Location dragonLoc = dragon.getLocation();

        // 播放发射音效
        dragon.getWorld().playSound(dragonLoc, Sound.ENTITY_WITHER_SHOOT, 1.0f, 0.5f);
        dragon.getWorld().playSound(dragonLoc, Sound.ENTITY_WITHER_AMBIENT, 1.0f, 0.7f);

        // 存储所有生成的黑曜石方块
        Set<FallingBlock> activeBlocks = new HashSet<>();

        // 持续发射黑曜石方块
        BukkitTask mainTask = new BukkitRunnable() {
            private int wavesRemaining = 5; // 发射5波黑曜石方块

            @Override
            public void run() {
                if (wavesRemaining <= 0 || dragon == null || !dragon.isValid() || dragon.isDead()) {
                    // 清理所有剩余的方块
                    cleanupRemainingBlocks();
                    this.cancel();
                    return;
                }

                // 每波发射多个黑曜石方块
                int blockCount = 5 + random.nextInt(4); // 5-8个方块，增加数量

                // 重新计算方向，以便追踪移动的玩家
                Vector currentDirection = target.getLocation().subtract(dragon.getLocation()).toVector().normalize();

                // 播放波次开始音效
                dragon.getWorld().playSound(dragon.getLocation(), Sound.ENTITY_WITHER_SHOOT, 1.0f, 0.7f);

                for (int i = 0; i < blockCount; i++) {
                    // 延迟发射，使方块不会同时到达
                    new BukkitRunnable() {
                        @Override
                        public void run() {
                            if (dragon == null || !dragon.isValid() || dragon.isDead() || !target.isOnline()) {
                                return;
                            }

                            // 计算发射位置，从龙的当前位置发射
                            Location currentDragonLoc = dragon.getLocation();
                            Location spawnLoc = currentDragonLoc.clone().add(currentDirection.clone().multiply(5));
                            spawnLoc.add(Math.random() * 6 - 3, Math.random() * 3, Math.random() * 6 - 3); // 增加散布范围

                            try {
                                // 创建黑曜石方块
                                FallingBlock block = spawnLoc.getWorld().spawnFallingBlock(spawnLoc, Material.OBSIDIAN.createBlockData());

                                // 添加到活动方块集合
                                activeBlocks.add(block);

                                // 设置方块属性
                                block.setDropItem(false);
                                block.setHurtEntities(true);
                                block.setFallDistance(100.0f); // 增加伤害

                                // 重新计算方向，以便更准确地瞄准玩家
                                Vector targetDirection = target.getLocation().subtract(spawnLoc).toVector().normalize();

                                // 设置方块速度，增加速度
                                Vector velocity = targetDirection.clone().multiply(2.0); // 增加速度
                                velocity.add(new Vector(Math.random() * 0.3 - 0.15, Math.random() * 0.2, Math.random() * 0.3 - 0.15));
                                block.setVelocity(velocity);

                                // 播放发射音效
                                spawnLoc.getWorld().playSound(spawnLoc, Sound.ENTITY_WITHER_SHOOT, 0.5f, 0.8f + random.nextFloat() * 0.4f);

                                // 添加元数据标记
                                block.setMetadata("mutationKingBlock", new FixedMetadataValue(plugin, true));

                                // 添加粒子效果
                                spawnLoc.getWorld().spawnParticle(Particle.FLAME,
                                        spawnLoc, 10, 0.2, 0.2, 0.2, 0.05);

                                // 创建方块追踪任务
                                new BukkitRunnable() {
                                    private int ticks = 0;
                                    private final int MAX_TICKS = 100; // 5秒生命周期

                                    @Override
                                    public void run() {
                                        if (block == null || block.isDead() || ticks >= MAX_TICKS) {
                                            this.cancel();
                                            if (block != null && !block.isDead()) {
                                                // 如果方块还存在但达到最大生命周期，爆炸消失
                                                block.getWorld().createExplosion(block.getLocation(), 0.0f, false, false);
                                                block.getWorld().spawnParticle(Particle.EXPLOSION,
                                                        block.getLocation(), 5, 0.2, 0.2, 0.2, 0.05);

                                                // 从活动方块集合中移除
                                                activeBlocks.remove(block);

                                                // 移除方块
                                                block.remove();
                                            }
                                            return;
                                        }

                                        // 每10ticks（0.5秒）添加追踪效果
                                        if (ticks % 10 == 0 && target.isOnline()) {
                                            // 添加追踪粒子效果
                                            block.getWorld().spawnParticle(Particle.FLAME,
                                                    block.getLocation(), 5, 0.1, 0.1, 0.1, 0.01);
                                        }

                                        // 检查是否击中玩家
                                        for (Entity entity : block.getNearbyEntities(2, 2, 2)) {
                                            if (entity instanceof Player) {
                                                Player player = (Player) entity;

                                                // 造成伤害
                                                player.damage(20.0, dragon); // 增加伤害

                                                // 击退效果
                                                Vector knockback = player.getLocation().toVector().subtract(block.getLocation().toVector()).normalize().multiply(1.5).setY(0.7);
                                                player.setVelocity(knockback);

                                                // 爆炸效果
                                                block.getWorld().createExplosion(block.getLocation(), 0.0f, false, false);

                                                // 粒子效果
                                                block.getWorld().spawnParticle(Particle.EXPLOSION,
                                                        block.getLocation(), 30, 0.5, 0.5, 0.5, 0.1);

                                                // 添加燃烧效果
                                                player.setFireTicks(60); // 3秒燃烧

                                                // 从活动方块集合中移除
                                                activeBlocks.remove(block);

                                                // 移除方块
                                                block.remove();
                                                this.cancel();
                                                return;
                                            }
                                        }

                                        // 检查方块是否已经落地变成了实体方块
                                        if (block.isDead() && !activeBlocks.contains(block)) {
                                            // 检查方块落地的位置
                                            Location landedLoc = block.getLocation().clone();

                                            // 向下搜索几个方块，找到可能的黑曜石
                                            for (int y = 0; y >= -5; y--) {
                                                Location checkLoc = landedLoc.clone().add(0, y, 0);
                                                if (checkLoc.getBlock().getType() == Material.OBSIDIAN) {
                                                    // 移除黑曜石方块
                                                    checkLoc.getBlock().setType(Material.AIR);

                                                    // 显示破碎粒子效果
                                                    checkLoc.getWorld().spawnParticle(Particle.LAVA,
                                                            checkLoc.clone().add(0.5, 0.5, 0.5), 5,
                                                            0.2, 0.2, 0.2, 0);

                                                    break;
                                                }
                                            }

                                            this.cancel();
                                            return;
                                        }

                                        ticks++;
                                    }
                                }.runTaskTimer(plugin, 5, 2); // 0.25秒后开始，每0.1秒执行一次
                            } catch (Exception e) {
                                logger.warning("发射黑曜石方块时出错: " + e.getMessage());
                            }
                        }
                    }.runTaskLater(plugin, i * 3); // 每0.15秒发射一个方块，提高频率
                }

                // 减少剩余波次
                wavesRemaining--;
            }

            // 清理所有剩余的方块
            private void cleanupRemainingBlocks() {
                for (FallingBlock block : new HashSet<>(activeBlocks)) {
                    if (block != null && !block.isDead()) {
                        // 爆炸效果
                        block.getWorld().spawnParticle(Particle.EXPLOSION,
                                block.getLocation(), 5, 0.2, 0.2, 0.2, 0.05);

                        // 移除方块
                        block.remove();
                    }
                }

                // 清空集合
                activeBlocks.clear();

                // 创建一个延迟任务，检查并清理可能已经落地的黑曜石方块
                new BukkitRunnable() {
                    @Override
                    public void run() {
                        // 获取龙的位置
                        if (dragon == null || !dragon.isValid() || dragon.isDead()) {
                            return;
                        }

                        Location center = dragon.getLocation();
                        World world = center.getWorld();

                        // 在龙周围50格范围内搜索黑曜石方块
                        for (int x = -50; x <= 50; x += 5) {
                            for (int y = -50; y <= 50; y += 5) {
                                for (int z = -50; z <= 50; z += 5) {
                                    Location checkLoc = center.clone().add(x, y, z);

                                    // 只检查已加载的区块
                                    if (world.isChunkLoaded(checkLoc.getBlockX() >> 4, checkLoc.getBlockZ() >> 4)) {
                                        // 检查5x5x5的区域
                                        for (int dx = 0; dx < 5; dx++) {
                                            for (int dy = 0; dy < 5; dy++) {
                                                for (int dz = 0; dz < 5; dz++) {
                                                    Location blockLoc = checkLoc.clone().add(dx, dy, dz);
                                                    if (blockLoc.getBlock().getType() == Material.OBSIDIAN) {
                                                        // 检查这个黑曜石是否有mutationKingBlock元数据
                                                        // 或者在最近10秒内生成的（没有好的方法检查，所以我们假设所有在范围内的黑曜石都是）

                                                        // 移除黑曜石方块
                                                        blockLoc.getBlock().setType(Material.AIR);

                                                        // 显示破碎粒子效果
                                                        blockLoc.getWorld().spawnParticle(Particle.LAVA,
                                                                blockLoc.clone().add(0.5, 0.5, 0.5), 5,
                                                                0.2, 0.2, 0.2, 0);
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }.runTaskLater(plugin, 100L); // 5秒后执行清理
            }
        }.runTaskTimer(plugin, 0L, 40L); // 立即开始，每2秒发射一波

        // 创建一个主清理任务，确保所有方块都被清理，并添加特殊的收回效果
        new BukkitRunnable() {
            @Override
            public void run() {
                // 如果主任务还在运行，取消它
                if (!mainTask.isCancelled()) {
                    mainTask.cancel();
                }

                // 收集所有活动的黑曜石方块
                List<FallingBlock> activeBlocksList = new ArrayList<>(activeBlocks);

                // 如果有活动的黑曜石方块，创建特殊的收回效果
                if (!activeBlocksList.isEmpty() && dragon != null && dragon.isValid() && !dragon.isDead()) {
                    // 播放特殊音效
                    dragon.getWorld().playSound(dragon.getLocation(), Sound.ENTITY_WITHER_AMBIENT, 1.5f, 0.5f);
                    dragon.getWorld().playSound(dragon.getLocation(), Sound.ENTITY_ENDER_DRAGON_GROWL, 1.0f, 0.7f);

                    // 在龙的位置创建特殊的收回效果
                    createObsidianRecallEffect(dragon.getLocation(), activeBlocksList);
                } else {
                    // 如果没有活动的黑曜石方块或龙已经不存在，直接清理
                    cleanupAllObsidianBlocks();
                }
            }

            // 创建黑曜石收回特效
            private void createObsidianRecallEffect(Location center, List<FallingBlock> blocks) {
                // 清理所有飞行中的黑曜石方块
                for (FallingBlock block : blocks) {
                    if (block != null && !block.isDead()) {
                        // 爆炸效果
                        block.getWorld().spawnParticle(Particle.EXPLOSION,
                                block.getLocation(), 5, 0.2, 0.2, 0.2, 0.05);

                        // 移除方块
                        block.remove();
                    }
                }

                // 清空集合
                activeBlocks.clear();

                // 创建360度的黑曜石柱子
                List<Location> pillarLocations = new ArrayList<>();
                int numPillars = 12; // 12个方向，每30度一个
                int pillarHeight = 5; // 每个柱子的高度
                int pillarLength = 8; // 每个柱子的长度

                // 播放开始效果音效
                center.getWorld().playSound(center, Sound.ENTITY_WITHER_SHOOT, 1.5f, 0.5f);

                // 创建柱子任务
                new BukkitRunnable() {
                    private int stage = 0;
                    private final int GROW_STAGE = 0;
                    private final int HOLD_STAGE = 1;
                    private final int EXPLODE_STAGE = 2;
                    private int tick = 0;
                    private final int GROW_TICKS = 20; // 1秒生长
                    private final int HOLD_TICKS = 20; // 1秒保持
                    private final int EXPLODE_TICKS = 20; // 1秒爆炸

                    @Override
                    public void run() {
                        if (stage == GROW_STAGE) {
                            // 生长阶段 - 创建柱子
                            if (tick == 0) {
                                // 第一帧，播放开始音效
                                center.getWorld().playSound(center, Sound.ENTITY_ENDER_DRAGON_SHOOT, 1.0f, 0.5f);
                                center.getWorld().spawnParticle(Particle.DRAGON_BREATH, center, 50, 2, 2, 2, 0.1);
                            }

                            // 计算当前应该生长的长度
                            float growthProgress = (float) tick / GROW_TICKS;
                            int currentLength = Math.round(pillarLength * growthProgress);

                            // 创建柱子
                            for (int i = 0; i < numPillars; i++) {
                                double angle = (Math.PI * 2 * i) / numPillars;
                                double x = Math.cos(angle);
                                double z = Math.sin(angle);

                                // 为每个方向创建一个柱子
                                for (int len = 0; len <= currentLength; len++) {
                                    Location pillarBase = center.clone().add(x * len, 0, z * len);

                                    // 创建垂直柱子
                                    for (int h = 0; h < pillarHeight; h++) {
                                        Location blockLoc = pillarBase.clone().add(0, h, 0);

                                        // 只在空气或可替换方块处放置黑曜石
                                        if (blockLoc.getBlock().getType() == Material.AIR
                                                || blockLoc.getBlock().getType() == Material.CAVE_AIR
                                                || blockLoc.getBlock().getType() == Material.SHORT_GRASS
                                                || blockLoc.getBlock().getType() == Material.TALL_GRASS
                                                || blockLoc.getBlock().getType() == Material.WATER) {

                                            // 只在新位置放置方块
                                            if (len == currentLength) {
                                                blockLoc.getBlock().setType(Material.OBSIDIAN);
                                                pillarLocations.add(blockLoc.clone());

                                                // 添加粒子效果
                                                blockLoc.getWorld().spawnParticle(Particle.FLAME,
                                                        blockLoc.clone().add(0.5, 0.5, 0.5), 3,
                                                        0.2, 0.2, 0.2, 0.01);
                                            }
                                        }
                                    }
                                }
                            }

                            // 播放生长音效
                            if (tick % 5 == 0) {
                                center.getWorld().playSound(center, Sound.BLOCK_STONE_PLACE, 0.8f, 0.5f);
                            }

                            // 检查是否完成生长阶段
                            if (tick >= GROW_TICKS) {
                                stage = HOLD_STAGE;
                                tick = 0;

                                // 播放完成音效
                                center.getWorld().playSound(center, Sound.ENTITY_WITHER_AMBIENT, 1.0f, 0.7f);
                                center.getWorld().spawnParticle(Particle.DRAGON_BREATH, center, 100, 3, 3, 3, 0.1);
                            }
                        } else if (stage == HOLD_STAGE) {
                            // 保持阶段 - 柱子保持不变，添加粒子效果
                            if (tick % 5 == 0) {
                                // 每5帧添加一次粒子效果
                                for (Location loc : pillarLocations) {
                                    loc.getWorld().spawnParticle(Particle.FLAME,
                                            loc.clone().add(0.5, 0.5, 0.5), 2,
                                            0.2, 0.2, 0.2, 0.01);
                                }

                                // 播放音效
                                center.getWorld().playSound(center, Sound.BLOCK_FIRE_AMBIENT, 0.5f, 0.8f);
                            }

                            // 检查是否完成保持阶段
                            if (tick >= HOLD_TICKS) {
                                stage = EXPLODE_STAGE;
                                tick = 0;

                                // 播放爆炸开始音效
                                center.getWorld().playSound(center, Sound.ENTITY_WITHER_DEATH, 1.0f, 0.5f);
                            }
                        } else if (stage == EXPLODE_STAGE) {
                            // 爆炸阶段 - 从外到内爆炸清理柱子

                            // 计算当前应该爆炸的长度
                            float explosionProgress = (float) tick / EXPLODE_TICKS;
                            int currentExplodeLength = Math.round(pillarLength * (1 - explosionProgress));

                            // 创建爆炸效果
                            for (int i = 0; i < numPillars; i++) {
                                double angle = (Math.PI * 2 * i) / numPillars;
                                double x = Math.cos(angle);
                                double z = Math.sin(angle);

                                // 计算当前爆炸位置
                                Location explodeLoc = center.clone().add(x * currentExplodeLength, 0, z * currentExplodeLength);

                                // 创建爆炸效果
                                explodeLoc.getWorld().createExplosion(explodeLoc, 0.0f, false, false);
                                explodeLoc.getWorld().spawnParticle(Particle.EXPLOSION,
                                        explodeLoc, 5, 0.5, 0.5, 0.5, 0.1);

                                // 清理该位置附近的黑曜石
                                for (int dx = -1; dx <= 1; dx++) {
                                    for (int dy = 0; dy < pillarHeight; dy++) {
                                        for (int dz = -1; dz <= 1; dz++) {
                                            Location blockLoc = explodeLoc.clone().add(dx, dy, dz);
                                            if (blockLoc.getBlock().getType() == Material.OBSIDIAN) {
                                                blockLoc.getBlock().setType(Material.AIR);

                                                // 显示破碎粒子效果
                                                blockLoc.getWorld().spawnParticle(Particle.LAVA,
                                                        blockLoc.clone().add(0.5, 0.5, 0.5), 5,
                                                        0.2, 0.2, 0.2, 0);
                                            }
                                        }
                                    }
                                }
                            }

                            // 播放爆炸音效
                            if (tick % 4 == 0) {
                                center.getWorld().playSound(center, Sound.ENTITY_GENERIC_EXPLODE, 0.7f, 0.8f + (tick * 0.02f));
                            }

                            // 检查是否完成爆炸阶段
                            if (tick >= EXPLODE_TICKS) {
                                // 最终爆炸效果
                                center.getWorld().createExplosion(center, 0.0f, false, false);
                                center.getWorld().spawnParticle(Particle.EXPLOSION,
                                        center, 20, 1.0, 1.0, 1.0, 0.2);
                                center.getWorld().playSound(center, Sound.ENTITY_GENERIC_EXPLODE, 1.5f, 0.5f);
                                center.getWorld().playSound(center, Sound.ENTITY_ENDER_DRAGON_GROWL, 1.0f, 0.7f);

                                // 清理所有剩余的黑曜石方块
                                cleanupAllObsidianBlocks();

                                // 取消任务
                                this.cancel();
                                return;
                            }
                        }

                        // 增加计时器
                        tick++;
                    }
                }.runTaskTimer(plugin, 0L, 1L); // 每tick执行一次
            }

            // 清理所有黑曜石方块
            private void cleanupAllObsidianBlocks() {
                // 清理所有剩余的飞行中的黑曜石方块
                for (FallingBlock block : new HashSet<>(activeBlocks)) {
                    if (block != null && !block.isDead()) {
                        // 爆炸效果
                        block.getWorld().spawnParticle(Particle.EXPLOSION,
                                block.getLocation(), 5, 0.2, 0.2, 0.2, 0.05);

                        // 移除方块
                        block.remove();
                    }
                }

                // 清空集合
                activeBlocks.clear();

                // 获取龙的位置
                if (dragon == null || !dragon.isValid() || dragon.isDead()) {
                    return;
                }

                Location center = dragon.getLocation();
                World world = center.getWorld();

                // 在龙周围100格范围内搜索黑曜石方块
                for (int x = -100; x <= 100; x += 10) {
                    for (int y = -100; y <= 100; y += 10) {
                        for (int z = -100; z <= 100; z += 10) {
                            Location checkLoc = center.clone().add(x, y, z);

                            // 只检查已加载的区块
                            if (world.isChunkLoaded(checkLoc.getBlockX() >> 4, checkLoc.getBlockZ() >> 4)) {
                                // 检查10x10x10的区域
                                for (int dx = 0; dx < 10; dx++) {
                                    for (int dy = 0; dy < 10; dy++) {
                                        for (int dz = 0; dz < 10; dz++) {
                                            Location blockLoc = checkLoc.clone().add(dx, dy, dz);
                                            if (blockLoc.getBlock().getType() == Material.OBSIDIAN) {
                                                // 移除黑曜石方块
                                                blockLoc.getBlock().setType(Material.AIR);

                                                // 显示破碎粒子效果
                                                blockLoc.getWorld().spawnParticle(Particle.LAVA,
                                                        blockLoc.clone().add(0.5, 0.5, 0.5), 5,
                                                        0.2, 0.2, 0.2, 0);
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }.runTaskLater(plugin, 400L); // 20秒后强制清理所有方块
    }

    /**
     * 清理实体相关的任务和资源
     *
     * @param dragon 异变之王实体
     */
    private void cleanupTasks(EnderDragon dragon) {
        // 取消所有任务
        if (dragonTasks.containsKey(dragon)) {
            List<BukkitTask> tasks = dragonTasks.get(dragon);
            for (BukkitTask task : tasks) {
                if (task != null && !task.isCancelled()) {
                    task.cancel();
                }
            }
            dragonTasks.remove(dragon);
        }

        // 移除BOSS血条
        if (dragonBossBars.containsKey(dragon)) {
            BossBar bossBar = dragonBossBars.get(dragon);
            if (bossBar != null) {
                // 从所有玩家移除血条
                bossBar.removeAll();
                // 移除血条引用
                dragonBossBars.remove(dragon);
            }
        }

        // 清理黑曜石方块
        if (obsidianBlockLocations.containsKey(dragon)) {
            List<Location> locations = new ArrayList<>(obsidianBlockLocations.get(dragon));
            for (Location loc : locations) {
                if (loc.getBlock().getType() == Material.OBSIDIAN) {
                    loc.getBlock().setType(Material.AIR);
                    // 显示破碎粒子效果
                    loc.getWorld().spawnParticle(Particle.LAVA,
                            loc.clone().add(0.5, 0.5, 0.5), 5,
                            0.2, 0.2, 0.2, 0);
                }
            }
            obsidianBlockLocations.remove(dragon);
        }

        // 清理下界栅栏
        if (netherFenceLocations.containsKey(dragon)) {
            List<Location> locations = new ArrayList<>(netherFenceLocations.get(dragon));
            for (Location loc : locations) {
                if (loc.getBlock().getType() == Material.NETHER_BRICK_FENCE) {
                    // 恢复原始方块类型
                    Material originalType = originalBlockMaterials.getOrDefault(loc, Material.AIR);
                    loc.getBlock().setType(originalType);
                    // 显示破碎粒子效果
                    loc.getWorld().spawnParticle(Particle.LAVA,
                            loc.clone().add(0.5, 0.5, 0.5), 5,
                            0.2, 0.2, 0.2, 0);
                }
            }
            netherFenceLocations.remove(dragon);
        }

        // 清理其他映射
        lastCrystalAttackTime.remove(dragon);
        lastBreathAttackTime.remove(dragon);
        lastEnderFieldTime.remove(dragon);
        lastSummonTime.remove(dragon);
        lastObsidianAttackTime.remove(dragon);
        lastNetherFenceAttackTime.remove(dragon);

        logger.info("异变之王资源已清理，UUID: " + (dragon != null ? dragon.getUniqueId() : "null"));
    }

    /**
     * 在玩家脚下创建2*2黑曜石方块攻击，可以无限向上延伸直到收回
     *
     * @param dragon 异变之王实体
     * @param target 目标玩家
     */
    private void createObsidianPillarAttack(EnderDragon dragon, Player target) {
        // 更新上次攻击时间
        lastObsidianAttackTime.put(dragon, System.currentTimeMillis());

        // 获取玩家位置
        Location playerLoc = target.getLocation().clone();

        // 播放攻击音效
        playerLoc.getWorld().playSound(playerLoc, Sound.ENTITY_WITHER_BREAK_BLOCK, 1.0f, 0.5f);
        playerLoc.getWorld().playSound(playerLoc, Sound.ENTITY_WITHER_AMBIENT, 1.0f, 0.7f);

        // 创建一个列表来存储所有黑曜石方块的位置
        List<Location> blockLocations = new ArrayList<>();

        // 如果dragon不在映射中，添加一个新列表
        if (!obsidianBlockLocations.containsKey(dragon)) {
            obsidianBlockLocations.put(dragon, new ArrayList<>());
        }

        // 创建多个2*2的黑曜石柱，无限向上延伸直到收回
        int pillarCount = 3; // 创建3个黑曜石柱
        List<Location> pillarBaseLocations = new ArrayList<>();

        // 主柱 - 玩家位置
        pillarBaseLocations.add(playerLoc.clone());

        // 额外的柱子 - 在玩家周围随机位置
        for (int i = 1; i < pillarCount; i++) {
            Location randomLoc = playerLoc.clone().add(
                    (random.nextDouble() * 6) - 3,
                    0,
                    (random.nextDouble() * 6) - 3);
            pillarBaseLocations.add(randomLoc);
        }

        // 为每个柱子创建任务
        for (Location baseLoc : pillarBaseLocations) {
            createSingleObsidianPillar(dragon, baseLoc, blockLocations);
        }

        // 创建一个主任务来确保所有黑曜石方块最终都被清理
        new BukkitRunnable() {
            @Override
            public void run() {
                // 检查是否还有黑曜石方块未被清理
                if (obsidianBlockLocations.containsKey(dragon)) {
                    List<Location> remainingBlocks = obsidianBlockLocations.get(dragon);
                    if (!remainingBlocks.isEmpty()) {
                        // 清理所有剩余的黑曜石方块
                        for (Location loc : new ArrayList<>(remainingBlocks)) {
                            if (loc.getBlock().getType() == Material.OBSIDIAN) {
                                loc.getBlock().setType(Material.AIR);
                                loc.getWorld().spawnParticle(Particle.LAVA,
                                        loc.clone().add(0.5, 0.5, 0.5), 5,
                                        0.2, 0.2, 0.2, 0);
                            }
                        }
                        remainingBlocks.clear();
                    }
                }
            }
        }.runTaskLater(plugin, 600L); // 30秒后强制清理所有黑曜石方块
    }

    /**
     * 创建单个黑曜石柱
     *
     * @param dragon 异变之王实体
     * @param baseLoc 基础位置
     * @param blockLocations 方块位置列表
     */
    private void createSingleObsidianPillar(EnderDragon dragon, Location baseLoc, List<Location> blockLocations) {
        // 创建2*2的黑曜石柱，无限向上延伸直到收回
        new BukkitRunnable() {
            private int height = 0;
            private int growthTicks = 0;
            private final int maxGrowthTicks = 160; // 生长持续8秒(160 ticks)，然后开始收回
            private boolean isGrowing = true; // 是否处于生长阶段
            private List<Location> pillarBlocks = new ArrayList<>(); // 这个柱子的方块

            @Override
            public void run() {
                // 检查龙是否还存在
                if (dragon == null || !dragon.isValid() || dragon.isDead()) {
                    // 立即清理黑曜石方块
                    cleanupObsidianBlocks();
                    this.cancel();
                    return;
                }

                // 增加计时器
                growthTicks++;

                // 如果达到最大生长时间，开始收回
                if (growthTicks >= maxGrowthTicks && isGrowing) {
                    isGrowing = false;
                    // 播放收回开始音效
                    baseLoc.getWorld().playSound(baseLoc, Sound.ENTITY_WITHER_AMBIENT, 1.0f, 0.5f);
                }

                if (isGrowing) {
                    // 生长阶段 - 向上延伸
                    growObsidianPillar();
                } else {
                    // 收回阶段 - 从顶部开始消除
                    if (pillarBlocks.isEmpty()) {
                        // 所有方块都已清理，取消任务
                        this.cancel();
                        return;
                    }
                    shrinkObsidianPillar();
                }
            }

            // 生长黑曜石柱
            private void growObsidianPillar() {
                // 在2*2区域创建黑曜石方块
                for (int x = 0; x < 2; x++) {
                    for (int z = 0; z < 2; z++) {
                        Location blockLoc = baseLoc.clone().add(x, height, z);

                        // 如果位置是空气或可替换方块，替换为黑曜石
                        if (blockLoc.getBlock().getType() == Material.AIR
                                || blockLoc.getBlock().getType() == Material.CAVE_AIR
                                || blockLoc.getBlock().getType() == Material.SHORT_GRASS
                                || blockLoc.getBlock().getType() == Material.TALL_GRASS
                                || blockLoc.getBlock().getType() == Material.WATER) {

                            blockLoc.getBlock().setType(Material.OBSIDIAN);
                            blockLocations.add(blockLoc.clone());
                            pillarBlocks.add(blockLoc.clone());

                            // 添加到全局映射
                            obsidianBlockLocations.get(dragon).add(blockLoc.clone());

                            // 播放方块放置音效
                            if (height % 2 == 0) { // 每隔一层播放一次音效
                                blockLoc.getWorld().playSound(blockLoc, Sound.BLOCK_STONE_PLACE, 0.5f, 0.8f);
                            }

                            // 检查是否有玩家在这个位置
                            for (Entity entity : blockLoc.getWorld().getNearbyEntities(blockLoc.clone().add(0.5, 0.5, 0.5), 0.8, 0.8, 0.8)) {
                                if (entity instanceof Player) {
                                    Player player = (Player) entity;

                                    // 造成伤害
                                    player.damage(20.0, dragon);

                                    // 向上击飞玩家
                                    Vector knockback = new Vector(0, 1.5, 0);
                                    player.setVelocity(knockback);

                                    // 播放受伤音效
                                    player.getWorld().playSound(player.getLocation(), Sound.ENTITY_PLAYER_HURT, 1.0f, 0.8f);

                                    // 显示受伤粒子效果
                                    player.getWorld().spawnParticle(Particle.DAMAGE_INDICATOR,
                                            player.getLocation().add(0, 1, 0), 10, 0.5, 0.5, 0.5, 0.1);
                                }
                            }
                        } else if (blockLoc.getBlock().getType().isSolid() && blockLoc.getBlock().getType() != Material.OBSIDIAN) {
                            // 如果遇到实体方块（非黑曜石），停止向上生长
                            return;
                        }
                    }
                }

                // 增加高度
                height++;

                // 播放生长音效
                baseLoc.getWorld().playSound(baseLoc.clone().add(0.5, height, 0.5),
                        Sound.BLOCK_STONE_PLACE, 0.8f, 0.5f);

                // 显示生长粒子效果
                baseLoc.getWorld().spawnParticle(Particle.LAVA,
                        baseLoc.clone().add(1.0, height, 1.0), 20,
                        1.0, 0.1, 1.0, 0);
            }

            // 收缩黑曜石柱
            private void shrinkObsidianPillar() {
                // 获取最高的4个方块（2x2区域的顶层）
                List<Location> topBlocks = new ArrayList<>();
                int maxY = -1;

                // 找出最高的Y坐标
                for (Location loc : pillarBlocks) {
                    if (loc.getBlockY() > maxY) {
                        maxY = loc.getBlockY();
                    }
                }

                // 收集顶层方块
                for (Location loc : new ArrayList<>(pillarBlocks)) {
                    if (loc.getBlockY() == maxY) {
                        topBlocks.add(loc);
                        pillarBlocks.remove(loc);
                        blockLocations.remove(loc);

                        // 移除黑曜石方块
                        if (loc.getBlock().getType() == Material.OBSIDIAN) {
                            loc.getBlock().setType(Material.AIR);

                            // 显示破碎粒子效果
                            loc.getWorld().spawnParticle(Particle.LAVA,
                                    loc.clone().add(0.5, 0.5, 0.5), 10,
                                    0.3, 0.3, 0.3, 0);

                            // 从全局映射中移除
                            if (obsidianBlockLocations.containsKey(dragon)) {
                                obsidianBlockLocations.get(dragon).remove(loc);
                            }
                        }
                    }
                }

                // 播放收缩音效
                if (!topBlocks.isEmpty()) {
                    Location soundLoc = topBlocks.get(0);
                    soundLoc.getWorld().playSound(soundLoc, Sound.BLOCK_STONE_BREAK, 0.5f, 0.8f);
                }
            }

            // 清理所有黑曜石方块
            private void cleanupObsidianBlocks() {
                for (Location loc : pillarBlocks) {
                    if (loc.getBlock().getType() == Material.OBSIDIAN) {
                        loc.getBlock().setType(Material.AIR);
                        loc.getWorld().spawnParticle(Particle.LAVA,
                                loc.clone().add(0.5, 0.5, 0.5), 10,
                                0.3, 0.3, 0.3, 0);
                    }
                    blockLocations.remove(loc);
                }

                // 从映射中移除这些位置
                if (obsidianBlockLocations.containsKey(dragon)) {
                    obsidianBlockLocations.get(dragon).removeAll(pillarBlocks);
                }

                pillarBlocks.clear();
            }
        }.runTaskTimer(plugin, 0L, 2L); // 立即开始，每0.1秒执行一次（进一步提高频率）
    }

    /**
     * 发射下界栅栏攻击，从36°的方向各延伸栅栏
     *
     * @param dragon 异变之王实体
     * @param target 目标玩家
     */
    private void shootNetherFenceAttack(EnderDragon dragon, Player target) {
        // 更新上次攻击时间
        lastNetherFenceAttackTime.put(dragon, System.currentTimeMillis());

        // 获取龙的位置和方向
        Location dragonLoc = dragon.getLocation();
        Vector mainDirection = target.getLocation().subtract(dragonLoc).toVector().normalize();

        // 播放攻击音效
        dragonLoc.getWorld().playSound(dragonLoc, Sound.ENTITY_WITHER_SHOOT, 1.0f, 0.5f);
        dragonLoc.getWorld().playSound(dragonLoc, Sound.ENTITY_WITHER_AMBIENT, 1.0f, 0.7f);

        // 创建一个列表来存储所有下界栅栏的位置
        List<Location> fenceLocations = new ArrayList<>();

        // 如果dragon不在映射中，添加一个新列表
        if (!netherFenceLocations.containsKey(dragon)) {
            netherFenceLocations.put(dragon, new ArrayList<>());
        }

        // 计算多个方向向量（主方向和两侧各36°）
        List<Vector> directions = new ArrayList<>();

        // 主方向
        directions.add(mainDirection);

        // 左侧36°
        Vector leftDirection = rotateVector(mainDirection.clone(), 36);
        directions.add(leftDirection);

        // 右侧36°
        Vector rightDirection = rotateVector(mainDirection.clone(), -36);
        directions.add(rightDirection);

        // 左侧72°
        Vector farLeftDirection = rotateVector(mainDirection.clone(), 72);
        directions.add(farLeftDirection);

        // 右侧72°
        Vector farRightDirection = rotateVector(mainDirection.clone(), -72);
        directions.add(farRightDirection);

        // 为每个方向创建下界栅栏射线
        for (Vector direction : directions) {
            createFenceRay(dragon, dragonLoc, direction, fenceLocations);
        }
    }

    /**
     * 旋转向量（在XZ平面上）
     *
     * @param vector 原始向量
     * @param degrees 旋转角度（度）
     * @return 旋转后的向量
     */
    private Vector rotateVector(Vector vector, double degrees) {
        double radians = Math.toRadians(degrees);
        double x = vector.getX();
        double z = vector.getZ();

        double cos = Math.cos(radians);
        double sin = Math.sin(radians);

        double newX = x * cos - z * sin;
        double newZ = x * sin + z * cos;

        return new Vector(newX, vector.getY(), newZ).normalize();
    }

    /**
     * 创建单条下界栅栏射线
     *
     * @param dragon 异变之王实体
     * @param startLoc 起始位置
     * @param direction 方向向量
     * @param fenceLocations 栅栏位置列表（用于清理）
     */
    private void createFenceRay(EnderDragon dragon, Location startLoc, Vector direction, List<Location> fenceLocations) {
        // 创建下界栅栏射线
        new BukkitRunnable() {
            private int distance = 0;
            private final int maxDistance = 25; // 增加最大射程
            private boolean hitObstacle = false;

            @Override
            public void run() {
                if (distance >= maxDistance || dragon == null || !dragon.isValid() || dragon.isDead() || hitObstacle) {
                    if (!hitObstacle) {
                        // 如果是因为达到最大距离而停止，播放消散音效
                        Location endLoc = startLoc.clone().add(direction.clone().multiply(distance));
                        endLoc.getWorld().playSound(endLoc, Sound.BLOCK_FIRE_EXTINGUISH, 0.5f, 1.2f);

                        // 显示消散粒子效果
                        endLoc.getWorld().spawnParticle(Particle.SMOKE,
                                endLoc, 15, 0.3, 0.3, 0.3, 0.05);
                    }

                    // 延迟移除下界栅栏
                    new BukkitRunnable() {
                        @Override
                        public void run() {
                            for (Location loc : new ArrayList<>(fenceLocations)) {
                                if (loc.getBlock().getType() == Material.NETHER_BRICK_FENCE) {
                                    // 恢复原始方块类型
                                    Material originalType = originalBlockMaterials.getOrDefault(loc, Material.AIR);
                                    loc.getBlock().setType(originalType);

                                    // 显示破碎粒子效果
                                    loc.getWorld().spawnParticle(Particle.LAVA,
                                            loc.clone().add(0.5, 0.5, 0.5), 5,
                                            0.2, 0.2, 0.2, 0);

                                    // 从列表中移除
                                    fenceLocations.remove(loc);

                                    // 从全局映射中移除
                                    if (netherFenceLocations.containsKey(dragon)) {
                                        netherFenceLocations.get(dragon).remove(loc);
                                    }
                                }
                            }
                        }
                    }.runTaskLater(plugin, 60); // 3秒后移除下界栅栏

                    this.cancel();
                    return;
                }

                // 计算当前位置
                Location currentLoc = startLoc.clone().add(direction.clone().multiply(distance));

                // 检查当前位置是否是实体方块
                if (currentLoc.getBlock().getType().isSolid()
                        && currentLoc.getBlock().getType() != Material.NETHER_BRICK_FENCE) {
                    // 如果碰到墙壁，标记为碰到障碍物
                    hitObstacle = true;

                    // 播放碰撞音效
                    currentLoc.getWorld().playSound(currentLoc, Sound.BLOCK_STONE_BREAK, 1.0f, 0.8f);

                    // 显示碰撞粒子效果
                    currentLoc.getWorld().spawnParticle(Particle.EXPLOSION,
                            currentLoc, 5, 0.2, 0.2, 0.2, 0.05);

                    return;
                }

                // 如果位置是空气或其他可替换的方块，替换为下界栅栏
                if (currentLoc.getBlock().getType() == Material.AIR
                        || currentLoc.getBlock().getType() == Material.CAVE_AIR
                        || currentLoc.getBlock().getType() == Material.SHORT_GRASS
                        || currentLoc.getBlock().getType() == Material.TALL_GRASS
                        || currentLoc.getBlock().getType() == Material.WATER) {

                    // 保存原始方块类型
                    originalBlockMaterials.put(currentLoc.clone(), currentLoc.getBlock().getType());

                    // 替换为下界栅栏
                    currentLoc.getBlock().setType(Material.NETHER_BRICK_FENCE);
                    fenceLocations.add(currentLoc.clone());

                    // 添加到全局映射
                    netherFenceLocations.get(dragon).add(currentLoc.clone());

                    // 显示粒子效果
                    currentLoc.getWorld().spawnParticle(Particle.FLAME,
                            currentLoc.clone().add(0.5, 0.5, 0.5), 5,
                            0.2, 0.2, 0.2, 0.05);

                    // 检查是否有玩家在这个位置
                    for (Entity entity : currentLoc.getWorld().getNearbyEntities(currentLoc.clone().add(0.5, 0.5, 0.5), 1.0, 1.0, 1.0)) {
                        if (entity instanceof Player) {
                            Player player = (Player) entity;

                            // 造成伤害
                            player.damage(15.0, dragon); // 增加伤害

                            // 添加减速效果
                            player.addPotionEffect(new PotionEffect(PotionEffectType.SLOWNESS, 120, 2)); // 减速III，持续6秒

                            // 添加虚弱效果
                            player.addPotionEffect(new PotionEffect(PotionEffectType.WEAKNESS, 100, 1)); // 虚弱II，持续5秒

                            // 播放受伤音效
                            player.getWorld().playSound(player.getLocation(), Sound.ENTITY_PLAYER_HURT, 1.0f, 0.8f);
                            player.getWorld().playSound(player.getLocation(), Sound.BLOCK_FIRE_AMBIENT, 1.0f, 0.5f);

                            // 显示受伤粒子效果
                            player.getWorld().spawnParticle(Particle.DAMAGE_INDICATOR,
                                    player.getLocation().add(0, 1, 0), 15, 0.5, 0.5, 0.5, 0.1);
                        }
                    }
                }

                // 增加距离
                distance++;

                // 每3格播放一次音效
                if (distance % 3 == 0) {
                    startLoc.getWorld().playSound(currentLoc, Sound.BLOCK_FIRE_AMBIENT, 0.3f, 0.8f);
                }
            }
        }.runTaskTimer(plugin, 0L, 1L); // 立即开始，每0.05秒延伸一格（提高速度）
    }

    /**
     * 执行组合攻击：先黑曜石方块攻击，然后是下界栅栏攻击
     *
     * @param dragon 异变之王实体
     * @param target 目标玩家
     */
    private void performCombinedAttack(EnderDragon dragon, Player target) {
        // 首先执行黑曜石方块攻击
        createObsidianPillarAttack(dragon, target);

        // 1秒后执行下界栅栏攻击
        new BukkitRunnable() {
            @Override
            public void run() {
                if (dragon != null && !dragon.isDead() && target != null && target.isOnline()) {
                    shootNetherFenceAttack(dragon, target);
                }
            }
        }.runTaskLater(plugin, 20L); // 1秒后执行
    }

    /**
     * 执行末影水晶攻击 - 增强版，发射多个子弹状水晶
     *
     * @param dragon 异变之王实体
     * @param target 目标玩家
     */
    private void performCrystalAttack(EnderDragon dragon, Player target) {
        // 更新上次攻击时间
        lastCrystalAttackTime.put(dragon, System.currentTimeMillis());

        // 获取龙的位置
        Location dragonLoc = dragon.getLocation();

        // 播放主要攻击音效
        dragon.getWorld().playSound(dragonLoc, Sound.ENTITY_ENDER_DRAGON_SHOOT, 2.0f, 0.5f);
        dragon.getWorld().playSound(dragonLoc, Sound.ENTITY_ENDERMAN_TELEPORT, 1.5f, 0.7f);

        // 显示准备攻击的粒子效果
        particleHelper.displayParticle(Bukkit.getOnlinePlayers(), dragonLoc, 2.0f, 2.0f, 2.0f, 0.1f, 80, Particle.END_ROD);
        particleHelper.displayParticle(Bukkit.getOnlinePlayers(), dragonLoc, 1.5f, 1.5f, 1.5f, 0.05f, 50, Particle.PORTAL);

        // 创建环绕龙的末影水晶
        int orbitalCrystals = 3; // 环绕的水晶数量
        List<EnderCrystal> orbitalCrystalList = new ArrayList<>();

        // 创建环绕水晶
        for (int i = 0; i < orbitalCrystals; i++) {
            double angle = (Math.PI * 2 * i) / orbitalCrystals;
            double x = Math.cos(angle) * 3;
            double z = Math.sin(angle) * 3;

            Location orbitalLoc = dragonLoc.clone().add(x, 1, z);

            // 生成环绕水晶
            EnderCrystal orbitalCrystal = (EnderCrystal) dragon.getWorld().spawnEntity(orbitalLoc, EntityType.END_CRYSTAL);
            orbitalCrystal.setShowingBottom(false);
            orbitalCrystal.setGlowing(true);
            orbitalCrystal.setInvulnerable(true);
            orbitalCrystal.setMetadata("mutationKingOrbitalCrystal", new FixedMetadataValue(plugin, true));

            // 添加到列表
            orbitalCrystalList.add(orbitalCrystal);

            // 显示生成效果
            particleHelper.displayParticle(Bukkit.getOnlinePlayers(), orbitalLoc, 1.0f, 1.0f, 1.0f, 0.1f, 30, Particle.END_ROD);
            orbitalLoc.getWorld().playSound(orbitalLoc, Sound.ENTITY_ENDERMAN_TELEPORT, 0.8f, 1.2f);
        }

        // 创建环绕任务
        new BukkitRunnable() {
            private int ticks = 0;
            private final int MAX_TICKS = 200; // 10秒生命周期
            private final int SHOOT_INTERVAL = 20; // 每1秒发射一次
            private double orbitalAngle = 0;

            @Override
            public void run() {
                // 检查龙是否还存在
                if (dragon == null || !dragon.isValid() || dragon.isDead() || ticks >= MAX_TICKS) {
                    // 移除所有环绕水晶
                    for (EnderCrystal crystal : orbitalCrystalList) {
                        if (crystal != null && !crystal.isDead()) {
                            // 爆炸效果
                            Location explodeLoc = crystal.getLocation();
                            explodeLoc.getWorld().spawnParticle(Particle.EXPLOSION, explodeLoc, 10, 0.5, 0.5, 0.5, 0.1);
                            explodeLoc.getWorld().playSound(explodeLoc, Sound.ENTITY_GENERIC_EXPLODE, 0.8f, 1.0f);
                            crystal.remove();
                        }
                    }
                    this.cancel();
                    return;
                }

                // 更新龙的当前位置
                Location currentDragonLoc = dragon.getLocation();

                // 更新环绕角度
                orbitalAngle += Math.PI / 16; // 每次旋转11.25度

                // 更新环绕水晶位置
                for (int i = 0; i < orbitalCrystalList.size(); i++) {
                    EnderCrystal crystal = orbitalCrystalList.get(i);

                    if (crystal != null && !crystal.isDead()) {
                        // 计算新位置
                        double angle = orbitalAngle + ((Math.PI * 2 * i) / orbitalCrystals);
                        double x = Math.cos(angle) * 3;
                        double y = Math.sin(angle * 0.5) * 0.5 + 1; // 添加上下波动
                        double z = Math.sin(angle) * 3;

                        // 设置新位置
                        Location newLoc = currentDragonLoc.clone().add(x, y, z);
                        crystal.teleport(newLoc);

                        // 添加粒子效果
                        if (ticks % 5 == 0) {
                            crystal.getWorld().spawnParticle(Particle.END_ROD,
                                    crystal.getLocation(), 5, 0.2, 0.2, 0.2, 0.02);
                        }

                        // 治疗龙
                        if (ticks % 40 == 0) { // 每2秒治疗一次
                            double currentHealth = dragon.getHealth();
                            double maxHealth = dragon.getMaxHealth();
                            if (currentHealth < maxHealth) {
                                dragon.setHealth(Math.min(currentHealth + 50.0, maxHealth)); // 恢复50点生命值

                                // 显示治疗效果
                                particleHelper.displayParticle(Bukkit.getOnlinePlayers(), dragon.getLocation(), 1.5f, 1.5f, 1.5f, 0.1f, 20, Particle.HEART);
                                dragon.getWorld().playSound(dragon.getLocation(), Sound.ENTITY_PLAYER_LEVELUP, 0.5f, 0.8f);
                            }
                        }
                    }
                }

                // 定期发射水晶子弹
                if (ticks % SHOOT_INTERVAL == 0 && target != null && target.isOnline()) {
                    // 从每个环绕水晶发射一个子弹
                    for (EnderCrystal crystal : orbitalCrystalList) {
                        if (crystal != null && !crystal.isDead()) {
                            shootCrystalBullet(dragon, crystal.getLocation(), target);
                        }
                    }
                }

                ticks++;
            }
        }.runTaskTimer(plugin, 5, 1); // 0.25秒后开始，每0.05秒执行一次
    }

    /**
     * 从指定位置发射末影水晶子弹
     *
     * @param dragon 异变之王实体
     * @param startLoc 起始位置
     * @param target 目标玩家
     */
    private void shootCrystalBullet(EnderDragon dragon, Location startLoc, Player target) {
        // 计算方向向量
        Vector direction = target.getLocation().add(0, 1, 0).subtract(startLoc).toVector().normalize();

        // 播放发射音效
        startLoc.getWorld().playSound(startLoc, Sound.ENTITY_ENDERMAN_TELEPORT, 1.0f, 1.5f);

        // 显示发射粒子效果
        startLoc.getWorld().spawnParticle(Particle.END_ROD, startLoc, 15, 0.2, 0.2, 0.2, 0.1);

        // 创建子弹实体（使用末影珍珠作为视觉效果）
        ArmorStand bullet = (ArmorStand) startLoc.getWorld().spawnEntity(startLoc, EntityType.ARMOR_STAND);
        bullet.setSmall(true);
        bullet.setInvisible(true);
        bullet.setInvulnerable(true);
        bullet.setGravity(false);
        bullet.setMarker(true);
        bullet.setMetadata("mutationKingCrystalBullet", new FixedMetadataValue(plugin, true));

        // 创建子弹追踪任务
        new BukkitRunnable() {
            private int ticks = 0;
            private final int MAX_TICKS = 60; // 3秒生命周期
            private Vector velocity = direction.clone().multiply(1.2); // 增加速度

            @Override
            public void run() {
                if (bullet == null || !bullet.isValid() || ticks >= MAX_TICKS) {
                    if (bullet != null && bullet.isValid()) {
                        // 爆炸效果
                        Location explodeLoc = bullet.getLocation();
                        explodeLoc.getWorld().createExplosion(explodeLoc, 0.0f, false, false);
                        explodeLoc.getWorld().spawnParticle(Particle.EXPLOSION, explodeLoc, 10, 0.5, 0.5, 0.5, 0.1);
                        explodeLoc.getWorld().playSound(explodeLoc, Sound.ENTITY_GENERIC_EXPLODE, 0.8f, 1.0f);

                        // 移除子弹
                        bullet.remove();
                    }
                    this.cancel();
                    return;
                }

                // 更新子弹位置
                Location currentLoc = bullet.getLocation();
                Location newLoc = currentLoc.clone().add(velocity);
                bullet.teleport(newLoc);

                // 显示粒子效果
                currentLoc.getWorld().spawnParticle(Particle.END_ROD, currentLoc, 5, 0.1, 0.1, 0.1, 0.01);

                // 检查是否击中玩家
                for (Entity entity : currentLoc.getWorld().getNearbyEntities(currentLoc, 1.5, 1.5, 1.5)) {
                    if (entity instanceof Player) {
                        Player player = (Player) entity;

                        // 造成伤害
                        player.damage(25.0, dragon); // 增加伤害

                        // 击退效果
                        Vector knockback = player.getLocation().toVector().subtract(currentLoc.toVector()).normalize().multiply(1.2).setY(0.5);
                        player.setVelocity(knockback);

                        // 添加效果
                        player.addPotionEffect(new PotionEffect(PotionEffectType.BLINDNESS, 60, 0)); // 3秒失明
                        player.addPotionEffect(new PotionEffect(PotionEffectType.NAUSEA, 100, 0)); // 5秒反胃

                        // 爆炸效果
                        currentLoc.getWorld().createExplosion(currentLoc, 0.0f, false, false);
                        currentLoc.getWorld().spawnParticle(Particle.EXPLOSION, currentLoc, 20, 0.5, 0.5, 0.5, 0.1);
                        currentLoc.getWorld().playSound(currentLoc, Sound.ENTITY_GENERIC_EXPLODE, 1.0f, 0.8f);

                        // 治疗龙
                        if (dragon != null && !dragon.isDead()) {
                            double currentHealth = dragon.getHealth();
                            double maxHealth = dragon.getMaxHealth();
                            if (currentHealth < maxHealth) {
                                dragon.setHealth(Math.min(currentHealth + 100.0, maxHealth)); // 恢复100点生命值

                                // 显示治疗效果
                                particleHelper.displayParticle(Bukkit.getOnlinePlayers(), dragon.getLocation(), 1.5f, 1.5f, 1.5f, 0.1f, 20, Particle.HEART);
                            }
                        }

                        // 移除子弹
                        bullet.remove();
                        this.cancel();
                        return;
                    }
                }

                // 检查是否碰到方块
                if (newLoc.getBlock().getType().isSolid()) {
                    // 爆炸效果
                    currentLoc.getWorld().createExplosion(currentLoc, 0.0f, false, false);
                    currentLoc.getWorld().spawnParticle(Particle.EXPLOSION, currentLoc, 15, 0.5, 0.5, 0.5, 0.1);
                    currentLoc.getWorld().playSound(currentLoc, Sound.ENTITY_GENERIC_EXPLODE, 0.8f, 1.0f);

                    // 移除子弹
                    bullet.remove();
                    this.cancel();
                    return;
                }

                ticks++;
            }
        }.runTaskTimer(plugin, 1, 1); // 立即开始，每0.05秒执行一次
    }

    /**
     * 执行龙息攻击
     *
     * @param dragon 异变之王实体
     * @param target 目标玩家
     */
    private void performBreathAttack(EnderDragon dragon, Player target) {
        // 更新上次攻击时间
        lastBreathAttackTime.put(dragon, System.currentTimeMillis());

        // 获取龙的位置和方向
        Location dragonLoc = dragon.getLocation();
        Vector direction = target.getLocation().subtract(dragonLoc).toVector().normalize();

        // 播放攻击音效
        dragon.getWorld().playSound(dragonLoc, Sound.ENTITY_ENDER_DRAGON_GROWL, 1.5f, 0.5f);
        dragon.getWorld().playSound(dragonLoc, Sound.ENTITY_ENDER_DRAGON_SHOOT, 1.0f, 0.7f);

        // 创建多个龙息效果云，形成一条龙息线
        int cloudCount = 3 + random.nextInt(3); // 3-5个效果云
        double distance = 5.0; // 初始距离

        for (int i = 0; i < cloudCount; i++) {
            // 计算生成位置，每个云之间有一定距离
            Location spawnLoc = dragonLoc.clone().add(direction.clone().multiply(distance));
            // 添加一些随机偏移
            spawnLoc.add((random.nextDouble() - 0.5) * 2, (random.nextDouble() - 0.5) * 2, (random.nextDouble() - 0.5) * 2);

            // 创建龙息效果云
            AreaEffectCloud cloud = (AreaEffectCloud) dragon.getWorld().spawnEntity(spawnLoc, EntityType.AREA_EFFECT_CLOUD);
            cloud.setRadius(3.0f);
            cloud.setDuration(100); // 5秒
            cloud.setParticle(Particle.DRAGON_BREATH);
            cloud.setColor(Color.PURPLE);
            cloud.setReapplicationDelay(10); // 0.5秒重新应用效果
            cloud.addCustomEffect(new PotionEffect(PotionEffectType.INSTANT_DAMAGE, 1, 1), true); // 即时伤害II
            cloud.addCustomEffect(new PotionEffect(PotionEffectType.WITHER, 60, 1), true); // 凋零II，持续3秒

            // 添加元数据标记
            cloud.setMetadata("mutationKingBreath", new FixedMetadataValue(plugin, true));

            // 创建追踪任务
            new BukkitRunnable() {
                private int ticks = 0;
                private final int MAX_TICKS = 100; // 5秒生命周期
                private Vector moveVector = direction.clone().multiply(0.3); // 移动向量

                @Override
                public void run() {
                    if (cloud == null || cloud.isDead() || ticks >= MAX_TICKS) {
                        this.cancel();
                        if (cloud != null && !cloud.isDead()) {
                            cloud.remove();
                        }
                        return;
                    }

                    // 龙息云向前移动
                    if (ticks < 40) { // 前2秒移动
                        Location cloudLoc = cloud.getLocation();
                        cloud.teleport(cloudLoc.add(moveVector));
                    }

                    // 检查是否击中玩家
                    for (Entity entity : cloud.getNearbyEntities(3, 3, 3)) {
                        if (entity instanceof Player) {
                            Player player = (Player) entity;
                            // 造成伤害
                            player.damage(8.0, dragon);

                            // 添加效果
                            player.addPotionEffect(new PotionEffect(PotionEffectType.BLINDNESS, 40, 0)); // 2秒失明
                            player.addPotionEffect(new PotionEffect(PotionEffectType.WITHER, 60, 1)); // 凋零效果，3秒

                            // 治疗龙
                            if (dragon != null && !dragon.isDead()) {
                                double currentHealth = dragon.getHealth();
                                double maxHealth = dragon.getMaxHealth();
                                if (currentHealth < maxHealth) {
                                    dragon.setHealth(Math.min(currentHealth + 20.0, maxHealth)); // 恢复20点生命值

                                    // 显示治疗效果
                                    particleHelper.displayParticle(Bukkit.getOnlinePlayers(), dragon.getLocation(), 1.0f, 1.0f, 1.0f, 0.1f, 10, Particle.HEART);
                                }
                            }
                        }
                    }

                    // 随机播放龙息音效
                    if (random.nextInt(10) == 0) {
                        cloud.getWorld().playSound(cloud.getLocation(), Sound.BLOCK_FIRE_AMBIENT, 0.5f, 0.8f);
                    }

                    // 增加额外的粒子效果
                    if (ticks % 5 == 0) {
                        particleHelper.displayParticle(Bukkit.getOnlinePlayers(), cloud.getLocation(), 2.0f, 2.0f, 2.0f, 0.1f, 15, Particle.DRAGON_BREATH);
                    }

                    ticks++;
                }
            }.runTaskTimer(plugin, 5, 5); // 0.25秒后开始，每0.25秒执行一次

            // 增加下一个云的距离
            distance += 3.0 + random.nextDouble() * 2.0; // 3-5格的间隔
        }

        // 龙息攻击后的恢复动作
        new BukkitRunnable() {
            @Override
            public void run() {
                if (dragon != null && !dragon.isDead()) {
                    // 播放恢复音效
                    dragon.getWorld().playSound(dragon.getLocation(), Sound.ENTITY_ENDER_DRAGON_FLAP, 1.0f, 1.2f);

                    // 随机移动
                    Vector recoveryVector = new Vector(
                            (random.nextDouble() - 0.5) * 0.8,
                            0.5, // 上升
                            (random.nextDouble() - 0.5) * 0.8
                    );
                    dragon.setVelocity(recoveryVector);
                }
            }
        }.runTaskLater(plugin, 20); // 1秒后执行
    }

    /**
     * 获取上次末影水晶攻击时间
     *
     * @param dragon 异变之王实体
     * @return 上次攻击时间（毫秒）
     */
    private long getLastCrystalAttackTime(EnderDragon dragon) {
        return lastCrystalAttackTime.getOrDefault(dragon, 0L);
    }

    /**
     * 获取上次龙息攻击时间
     *
     * @param dragon 异变之王实体
     * @return 上次攻击时间（毫秒）
     */
    private long getLastBreathAttackTime(EnderDragon dragon) {
        return lastBreathAttackTime.getOrDefault(dragon, 0L);
    }

    /**
     * 创建末影粒子减速场
     *
     * @param dragon 异变之王实体
     */
    private void createEnderField(EnderDragon dragon) {
        // 获取龙的位置
        Location center = dragon.getLocation();

        // 播放末影粒子场音效
        center.getWorld().playSound(center, Sound.BLOCK_PORTAL_AMBIENT, 1.0f, 0.5f);

        // 创建末影粒子场效果
        new BukkitRunnable() {
            private int ticks = 0;
            private final int maxTicks = 200; // 10秒
            private final double radius = 10.0; // 减速场半径

            @Override
            public void run() {
                if (ticks >= maxTicks || dragon == null || !dragon.isValid() || dragon.isDead()) {
                    this.cancel();
                    return;
                }

                // 创建末影粒子效果
                for (int i = 0; i < 20; i++) {
                    double angle = Math.random() * Math.PI * 2;
                    double distance = Math.random() * radius;
                    double x = center.getX() + Math.cos(angle) * distance;
                    double y = center.getY() + Math.random() * 2;
                    double z = center.getZ() + Math.sin(angle) * distance;

                    Location particleLoc = new Location(center.getWorld(), x, y, z);
                    center.getWorld().spawnParticle(Particle.PORTAL, particleLoc, 5, 0.1, 0.1, 0.1, 0.05);
                }

                // 每5tick播放一次音效
                if (ticks % 20 == 0) {
                    center.getWorld().playSound(center, Sound.BLOCK_PORTAL_AMBIENT, 0.5f, 0.5f);
                }

                // 对范围内的玩家施加减速效果
                for (Player player : Bukkit.getOnlinePlayers()) {
                    if (player.getWorld().equals(center.getWorld())
                            && player.getLocation().distance(center) <= radius) {

                        // 施加缓慢效果
                        player.addPotionEffect(new PotionEffect(PotionEffectType.SLOWNESS, 40, 2)); // 2秒缓慢III

                        // 显示粒子效果
                        player.getWorld().spawnParticle(Particle.PORTAL, player.getLocation().add(0, 1, 0), 10, 0.5, 0.5, 0.5, 0.05);
                    }
                }

                ticks++;
            }
        }.runTaskTimer(plugin, 0L, 1L); // 每tick执行一次，创建连续的末影粒子效果
    }

    /**
     * 随机召唤变异生物
     *
     * @param dragon 异变之王实体
     */
    private void summonRandomMutant(EnderDragon dragon) {
        // 获取龙的位置
        Location center = dragon.getLocation();

        // 随机选择一个变异生物ID (idc1-idc21)
        int randomId = random.nextInt(21) + 1;
        String entityId = "idc" + randomId;

        // 计算生成位置
        Location spawnLoc = center.clone();
        spawnLoc.add(Math.random() * 10 - 5, -3, Math.random() * 10 - 5);

        // 确保生成位置是安全的
        while (spawnLoc.getBlock().getType().isSolid() && spawnLoc.getY() < center.getY() + 10) {
            spawnLoc.add(0, 1, 0);
        }

        // 播放召唤音效
        center.getWorld().playSound(spawnLoc, Sound.ENTITY_ENDERMAN_TELEPORT, 1.0f, 0.5f);

        // 创建召唤粒子效果
        center.getWorld().spawnParticle(Particle.DRAGON_BREATH, spawnLoc, 30, 0.5, 0.5, 0.5, 0.1);

        // 生成变异生物
        if (customZombie != null) {
            logger.info("异变之王正在召唤变异生物: " + entityId);

            // 使用CustomZombie生成变异生物
            try {
                // 获取附近的玩家作为目标
                Player nearestPlayer = getNearestPlayer(spawnLoc, 50);

                if (nearestPlayer != null) {
                    // 使用CustomZombie的spawnOtherEntity方法生成变异生物
                    customZombie.spawnOtherEntity(nearestPlayer, entityId);
                    logger.info("变异生物召唤命令已执行: " + entityId);
                } else {
                    logger.warning("无法召唤变异生物: 附近没有玩家");
                }
            } catch (Exception e) {
                logger.severe("召唤变异生物时发生异常: " + e.getMessage());
                e.printStackTrace();
            }
        } else {
            logger.warning("无法召唤变异生物: CustomZombie实例为null");
        }
    }

    /**
     * 获取指定范围内最近的玩家
     *
     * @param location 中心位置
     * @param radius 搜索半径
     * @return 最近的玩家，如果没有则返回null
     */
    private Player getNearestPlayer(Location location, double radius) {
        Player nearestPlayer = null;
        double nearestDistance = Double.MAX_VALUE;

        for (Player player : Bukkit.getOnlinePlayers()) {
            if (player.getWorld().equals(location.getWorld())
                    && player.getGameMode() == org.bukkit.GameMode.SURVIVAL) {
                double distance = player.getLocation().distance(location);
                if (distance < radius && distance < nearestDistance) {
                    nearestDistance = distance;
                    nearestPlayer = player;
                }
            }
        }

        return nearestPlayer;
    }

    /**
     * 获取指定位置下方的地面高度
     *
     * @param location 当前位置
     * @return 地面的Y坐标
     */
    private double getGroundLevel(Location location) {
        Location checkLoc = location.clone();

        // 从当前位置向下查找，直到找到非空气方块
        while (checkLoc.getY() > 0) {
            checkLoc.subtract(0, 1, 0);
            if (!checkLoc.getBlock().getType().isAir()) {
                return checkLoc.getY();
            }
        }

        // 如果找不到地面，返回世界的最低高度
        return 0;
    }

    /**
     * 检查位置是否接近地面
     *
     * @param location 当前位置
     * @param threshold 阈值距离
     * @return 如果位置接近地面，则返回true
     */
    private boolean isNearGround(Location location, double threshold) {
        Location checkLoc = location.clone();

        // 检查下方是否有非空气方块
        for (double y = 0; y <= threshold; y += 0.5) {
            Location testLoc = checkLoc.clone().subtract(0, y, 0);
            if (!testLoc.getBlock().getType().isAir()) {
                return true;
            }
        }

        return false;
    }
}
