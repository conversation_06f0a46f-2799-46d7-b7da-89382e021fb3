package org.Ver_zhzh.customZombie.UserMaker;

import org.bukkit.Bukkit;
import org.bukkit.Location;
import org.bukkit.Particle;
import org.bukkit.Sound;
import org.bukkit.entity.LivingEntity;
import org.bukkit.entity.Player;
import org.bukkit.metadata.FixedMetadataValue;
import org.bukkit.potion.PotionEffect;
import org.bukkit.potion.PotionEffectType;
import org.bukkit.scheduler.BukkitRunnable;
import org.bukkit.scheduler.BukkitTask;
import org.bukkit.util.Vector;
import org.Ver_zhzh.deathZombieV4.DeathZombieV4;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.logging.Logger;

/**
 * IDZ技能执行器 - 负责执行IDZ实体的技能
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class IdzSkillExecutor {
    
    private final DeathZombieV4 plugin;
    private final Logger logger;
    private final SkillTemplateManager skillTemplateManager;
    private final Map<UUID, List<BukkitTask>> entityTasks;
    private final Map<String, SkillHandler> skillHandlers;
    
    public IdzSkillExecutor(DeathZombieV4 plugin) {
        this.plugin = plugin;
        this.logger = plugin.getLogger();
        this.skillTemplateManager = new SkillTemplateManager(plugin);
        this.entityTasks = new ConcurrentHashMap<>();
        this.skillHandlers = new HashMap<>();
        
        // 注册技能处理器
        registerSkillHandlers();
        
        logger.info("IDZ技能执行器初始化完成");
    }
    
    /**
     * 技能处理器接口
     */
    public interface SkillHandler {
        void execute(LivingEntity entity, IdzEntityConfig.SkillConfig skillConfig, SkillTemplate template);
        void stop(LivingEntity entity);
    }
    
    /**
     * 启用技能
     */
    public void enableSkill(LivingEntity entity, IdzEntityConfig.SkillConfig skillConfig) {
        SkillTemplate template = skillTemplateManager.getTemplate(skillConfig.getSkillId());
        if (template == null) {
            logger.warning("未找到技能模板: " + skillConfig.getSkillId());
            return;
        }
        
        SkillHandler handler = skillHandlers.get(skillConfig.getSkillId());
        if (handler == null) {
            logger.warning("未找到技能处理器: " + skillConfig.getSkillId());
            return;
        }
        
        try {
            handler.execute(entity, skillConfig, template);
            logger.info("为实体 " + entity.getUniqueId() + " 启用技能: " + skillConfig.getSkillId());
        } catch (Exception e) {
            logger.severe("启用技能失败: " + skillConfig.getSkillId() + " - " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 停止实体的所有技能
     */
    public void stopAllSkills(LivingEntity entity) {
        UUID entityId = entity.getUniqueId();
        
        // 取消所有任务
        List<BukkitTask> tasks = entityTasks.get(entityId);
        if (tasks != null) {
            for (BukkitTask task : tasks) {
                if (task != null && !task.isCancelled()) {
                    task.cancel();
                }
            }
            entityTasks.remove(entityId);
        }
        
        // 调用技能处理器的停止方法
        for (SkillHandler handler : skillHandlers.values()) {
            try {
                handler.stop(entity);
            } catch (Exception e) {
                // 忽略停止时的错误
            }
        }
        
        logger.info("已停止实体 " + entityId + " 的所有技能");
    }
    
    /**
     * 添加任务到实体任务列表
     */
    private void addTask(UUID entityId, BukkitTask task) {
        entityTasks.computeIfAbsent(entityId, k -> new ArrayList<>()).add(task);
    }
    
    /**
     * 注册所有技能处理器
     */
    private void registerSkillHandlers() {
        // 剧毒攻击技能
        skillHandlers.put("poison_attack", new SkillHandler() {
            @Override
            public void execute(LivingEntity entity, IdzEntityConfig.SkillConfig skillConfig, SkillTemplate template) {
                int poisonLevel = skillConfig.getParameter("poison_level", 1);
                int poisonDuration = skillConfig.getParameter("poison_duration", 60);
                double poisonChance = skillConfig.getParameter("poison_chance", 1.0);
                
                // 设置元数据，供攻击监听器使用
                entity.setMetadata("poisonAttack", new FixedMetadataValue(plugin, true));
                entity.setMetadata("poisonLevel", new FixedMetadataValue(plugin, poisonLevel));
                entity.setMetadata("poisonDuration", new FixedMetadataValue(plugin, poisonDuration));
                entity.setMetadata("poisonChance", new FixedMetadataValue(plugin, poisonChance));
            }
            
            @Override
            public void stop(LivingEntity entity) {
                entity.removeMetadata("poisonAttack", plugin);
                entity.removeMetadata("poisonLevel", plugin);
                entity.removeMetadata("poisonDuration", plugin);
                entity.removeMetadata("poisonChance", plugin);
            }
        });
        
        // 召唤仆从技能
        skillHandlers.put("summon_minion", new SkillHandler() {
            @Override
            public void execute(LivingEntity entity, IdzEntityConfig.SkillConfig skillConfig, SkillTemplate template) {
                int summonInterval = skillConfig.getParameter("summon_interval", 6000);
                int maxMinions = skillConfig.getParameter("max_minions", 3);
                String minionType = skillConfig.getParameter("minion_type", "id1");
                
                BukkitTask task = new BukkitRunnable() {
                    @Override
                    public void run() {
                        if (entity == null || entity.isDead() || !entity.isValid()) {
                            this.cancel();
                            return;
                        }
                        
                        // 检查当前仆从数量
                        // TODO: 实现仆从计数逻辑
                        
                        // 召唤仆从
                        // TODO: 实现召唤逻辑
                        
                        // 播放召唤效果
                        Location loc = entity.getLocation();
                        loc.getWorld().spawnParticle(Particle.PORTAL, loc, 20, 1, 1, 1, 0.1);
                        loc.getWorld().playSound(loc, Sound.ENTITY_ENDERMAN_TELEPORT, 1.0f, 1.0f);
                    }
                }.runTaskTimer(plugin, 0, summonInterval);
                
                addTask(entity.getUniqueId(), task);
            }
            
            @Override
            public void stop(LivingEntity entity) {
                // 任务会在stopAllSkills中统一取消
            }
        });
        
        // 电击攻击技能
        skillHandlers.put("electric_attack", new SkillHandler() {
            @Override
            public void execute(LivingEntity entity, IdzEntityConfig.SkillConfig skillConfig, SkillTemplate template) {
                int attackInterval = skillConfig.getParameter("attack_interval", 60);
                double attackRange = skillConfig.getParameter("attack_range", 8.0);
                double electricDamage = skillConfig.getParameter("electric_damage", 6.0);
                
                BukkitTask task = new BukkitRunnable() {
                    @Override
                    public void run() {
                        if (entity == null || entity.isDead() || !entity.isValid()) {
                            this.cancel();
                            return;
                        }
                        
                        Location loc = entity.getLocation();
                        
                        // 查找范围内的玩家
                        for (Player player : loc.getWorld().getPlayers()) {
                            if (player.getLocation().distance(loc) <= attackRange) {
                                // 造成电击伤害
                                player.damage(electricDamage);
                                
                                // 播放电击效果
                                player.getWorld().spawnParticle(Particle.ENCHANTED_HIT, 
                                    player.getLocation().add(0, 1, 0), 10, 0.5, 0.5, 0.5, 0.1);
                                player.getWorld().playSound(player.getLocation(), 
                                    Sound.ENTITY_LIGHTNING_BOLT_THUNDER, 0.5f, 2.0f);
                            }
                        }
                        
                        // 在实体周围播放电击粒子
                        loc.getWorld().spawnParticle(Particle.ELECTRIC_SPARK, loc.add(0, 1, 0), 15, 1, 1, 1, 0.1);
                    }
                }.runTaskTimer(plugin, 0, attackInterval);
                
                addTask(entity.getUniqueId(), task);
            }
            
            @Override
            public void stop(LivingEntity entity) {
                // 任务会在stopAllSkills中统一取消
            }
        });
        
        // 冰冻攻击技能
        skillHandlers.put("freeze_attack", new SkillHandler() {
            @Override
            public void execute(LivingEntity entity, IdzEntityConfig.SkillConfig skillConfig, SkillTemplate template) {
                int attackInterval = skillConfig.getParameter("attack_interval", 80);
                int freezeDuration = skillConfig.getParameter("freeze_duration", 100);
                int slownessLevel = skillConfig.getParameter("slowness_level", 2);
                
                BukkitTask task = new BukkitRunnable() {
                    @Override
                    public void run() {
                        if (entity == null || entity.isDead() || !entity.isValid()) {
                            this.cancel();
                            return;
                        }
                        
                        Location loc = entity.getLocation();
                        
                        // 查找最近的玩家
                        Player nearestPlayer = null;
                        double nearestDistance = Double.MAX_VALUE;
                        
                        for (Player player : loc.getWorld().getPlayers()) {
                            double distance = player.getLocation().distance(loc);
                            if (distance < nearestDistance && distance <= 15.0) {
                                nearestDistance = distance;
                                nearestPlayer = player;
                            }
                        }
                        
                        if (nearestPlayer != null) {
                            // 给玩家添加缓慢效果
                            PotionEffect slowness = new PotionEffect(PotionEffectType.SLOW, 
                                freezeDuration, slownessLevel - 1, false, true, true);
                            nearestPlayer.addPotionEffect(slowness);
                            
                            // 播放冰冻效果
                            Location playerLoc = nearestPlayer.getLocation();
                            playerLoc.getWorld().spawnParticle(Particle.SNOWFLAKE, 
                                playerLoc.add(0, 1, 0), 20, 1, 1, 1, 0.1);
                            playerLoc.getWorld().spawnParticle(Particle.CLOUD, 
                                playerLoc, 10, 0.5, 0.5, 0.5, 0.05);
                            playerLoc.getWorld().playSound(playerLoc, Sound.BLOCK_GLASS_BREAK, 1.0f, 0.5f);
                        }
                    }
                }.runTaskTimer(plugin, 0, attackInterval);
                
                addTask(entity.getUniqueId(), task);
            }
            
            @Override
            public void stop(LivingEntity entity) {
                // 任务会在stopAllSkills中统一取消
            }
        });
        
        // 剧毒烟雾技能
        skillHandlers.put("poison_smoke", new SkillHandler() {
            @Override
            public void execute(LivingEntity entity, IdzEntityConfig.SkillConfig skillConfig, SkillTemplate template) {
                int particleInterval = skillConfig.getParameter("particle_interval", 5);
                int particleCount = skillConfig.getParameter("particle_count", 8);
                
                BukkitTask task = new BukkitRunnable() {
                    @Override
                    public void run() {
                        if (entity == null || entity.isDead() || !entity.isValid()) {
                            this.cancel();
                            return;
                        }
                        
                        Location loc = entity.getLocation().add(0, 1, 0);
                        
                        // 生成黑色烟雾粒子
                        for (int i = 0; i < particleCount; i++) {
                            double offsetX = (Math.random() - 0.5) * 2;
                            double offsetY = Math.random() * 2;
                            double offsetZ = (Math.random() - 0.5) * 2;
                            
                            Location particleLoc = loc.clone().add(offsetX, offsetY, offsetZ);
                            loc.getWorld().spawnParticle(Particle.LARGE_SMOKE, particleLoc, 1, 0, 0, 0, 0);
                        }
                    }
                }.runTaskTimer(plugin, 0, particleInterval);
                
                addTask(entity.getUniqueId(), task);
            }
            
            @Override
            public void stop(LivingEntity entity) {
                // 任务会在stopAllSkills中统一取消
            }
        });
        
        // 螺旋粒子技能
        skillHandlers.put("spiral_particles", new SkillHandler() {
            @Override
            public void execute(LivingEntity entity, IdzEntityConfig.SkillConfig skillConfig, SkillTemplate template) {
                double spiralSpeed = skillConfig.getParameter("spiral_speed", 0.1);
                double spiralRadius = skillConfig.getParameter("spiral_radius", 1.0);
                
                BukkitTask task = new BukkitRunnable() {
                    private double angle = 0;
                    
                    @Override
                    public void run() {
                        if (entity == null || entity.isDead() || !entity.isValid()) {
                            this.cancel();
                            return;
                        }
                        
                        Location loc = entity.getLocation();
                        
                        // 计算螺旋位置
                        double x = Math.cos(angle) * spiralRadius;
                        double z = Math.sin(angle) * spiralRadius;
                        double y = (angle % (Math.PI * 4)) / (Math.PI * 2); // 螺旋上升
                        
                        Location particleLoc = loc.clone().add(x, y + 1, z);
                        loc.getWorld().spawnParticle(Particle.SPELL_WITCH, particleLoc, 1, 0, 0, 0, 0);
                        
                        angle += spiralSpeed;
                        if (angle > Math.PI * 8) {
                            angle = 0; // 重置角度
                        }
                    }
                }.runTaskTimer(plugin, 0, 5);
                
                addTask(entity.getUniqueId(), task);
            }
            
            @Override
            public void stop(LivingEntity entity) {
                // 任务会在stopAllSkills中统一取消
            }
        });
        
        // 跳跃攻击技能
        skillHandlers.put("jump_attack", new SkillHandler() {
            @Override
            public void execute(LivingEntity entity, IdzEntityConfig.SkillConfig skillConfig, SkillTemplate template) {
                int jumpInterval = skillConfig.getParameter("jump_interval", 100);
                double jumpRange = skillConfig.getParameter("jump_range", 10.0);
                double jumpDamage = skillConfig.getParameter("jump_damage", 4.0);

                BukkitTask task = new BukkitRunnable() {
                    @Override
                    public void run() {
                        if (entity == null || entity.isDead() || !entity.isValid()) {
                            this.cancel();
                            return;
                        }

                        // 查找最近的玩家
                        Player nearestPlayer = null;
                        double nearestDistance = Double.MAX_VALUE;

                        for (Player player : entity.getWorld().getPlayers()) {
                            double distance = player.getLocation().distance(entity.getLocation());
                            if (distance < nearestDistance && distance <= jumpRange) {
                                nearestDistance = distance;
                                nearestPlayer = player;
                            }
                        }

                        if (nearestPlayer != null) {
                            // 跳向玩家
                            Location playerLoc = nearestPlayer.getLocation();
                            Location entityLoc = entity.getLocation();

                            double deltaX = playerLoc.getX() - entityLoc.getX();
                            double deltaZ = playerLoc.getZ() - entityLoc.getZ();
                            double deltaY = 0.5; // 向上跳跃

                            entity.setVelocity(entity.getVelocity().add(
                                new Vector(deltaX * 0.3, deltaY, deltaZ * 0.3)));

                            // 播放跳跃效果
                            entity.getWorld().spawnParticle(Particle.CLOUD,
                                entityLoc, 10, 1, 0.5, 1, 0.1);
                            entity.getWorld().playSound(entityLoc, Sound.ENTITY_SLIME_JUMP, 1.0f, 1.0f);

                            // 延迟造成伤害（落地时）
                            Bukkit.getScheduler().runTaskLater(plugin, () -> {
                                if (nearestPlayer.getLocation().distance(entity.getLocation()) <= 3.0) {
                                    nearestPlayer.damage(jumpDamage);
                                }
                            }, 20L);
                        }
                    }
                }.runTaskTimer(plugin, 0, jumpInterval);

                addTask(entity.getUniqueId(), task);
            }

            @Override
            public void stop(LivingEntity entity) {
                // 任务会在stopAllSkills中统一取消
            }
        });

        // 传送攻击技能
        skillHandlers.put("teleport_attack", new SkillHandler() {
            @Override
            public void execute(LivingEntity entity, IdzEntityConfig.SkillConfig skillConfig, SkillTemplate template) {
                int teleportInterval = skillConfig.getParameter("teleport_interval", 120);
                double teleportRange = skillConfig.getParameter("teleport_range", 15.0);

                BukkitTask task = new BukkitRunnable() {
                    @Override
                    public void run() {
                        if (entity == null || entity.isDead() || !entity.isValid()) {
                            this.cancel();
                            return;
                        }

                        // 查找最近的玩家
                        Player nearestPlayer = null;
                        double nearestDistance = Double.MAX_VALUE;

                        for (Player player : entity.getWorld().getPlayers()) {
                            double distance = player.getLocation().distance(entity.getLocation());
                            if (distance < nearestDistance && distance <= teleportRange) {
                                nearestDistance = distance;
                                nearestPlayer = player;
                            }
                        }

                        if (nearestPlayer != null) {
                            // 传送到玩家身后
                            Location playerLoc = nearestPlayer.getLocation();
                            Vector direction = playerLoc.getDirection().multiply(-2);
                            Location teleportLoc = playerLoc.add(direction);
                            teleportLoc.setY(playerLoc.getY());

                            // 播放传送效果
                            entity.getWorld().spawnParticle(Particle.PORTAL,
                                entity.getLocation(), 20, 1, 1, 1, 0.1);
                            entity.getWorld().playSound(entity.getLocation(),
                                Sound.ENTITY_ENDERMAN_TELEPORT, 1.0f, 1.0f);

                            entity.teleport(teleportLoc);

                            entity.getWorld().spawnParticle(Particle.PORTAL,
                                teleportLoc, 20, 1, 1, 1, 0.1);
                            entity.getWorld().playSound(teleportLoc,
                                Sound.ENTITY_ENDERMAN_TELEPORT, 1.0f, 1.0f);
                        }
                    }
                }.runTaskTimer(plugin, 0, teleportInterval);

                addTask(entity.getUniqueId(), task);
            }

            @Override
            public void stop(LivingEntity entity) {
                // 任务会在stopAllSkills中统一取消
            }
        });

        // 生命汲取技能（被动技能，通过元数据实现）
        skillHandlers.put("life_drain", new SkillHandler() {
            @Override
            public void execute(LivingEntity entity, IdzEntityConfig.SkillConfig skillConfig, SkillTemplate template) {
                double drainAmount = skillConfig.getParameter("drain_amount", 2.0);
                double drainChance = skillConfig.getParameter("drain_chance", 0.5);

                // 设置元数据，供事件监听器使用
                entity.setMetadata("lifeDrain", new FixedMetadataValue(plugin, true));
                entity.setMetadata("drainAmount", new FixedMetadataValue(plugin, drainAmount));
                entity.setMetadata("drainChance", new FixedMetadataValue(plugin, drainChance));
            }

            @Override
            public void stop(LivingEntity entity) {
                entity.removeMetadata("lifeDrain", plugin);
                entity.removeMetadata("drainAmount", plugin);
                entity.removeMetadata("drainChance", plugin);
            }
        });

        // 护盾反射技能（被动技能，通过元数据实现）
        skillHandlers.put("shield_reflect", new SkillHandler() {
            @Override
            public void execute(LivingEntity entity, IdzEntityConfig.SkillConfig skillConfig, SkillTemplate template) {
                double reflectChance = skillConfig.getParameter("reflect_chance", 0.3);
                double reflectMultiplier = skillConfig.getParameter("reflect_multiplier", 0.5);

                // 设置元数据，供事件监听器使用
                entity.setMetadata("shieldReflect", new FixedMetadataValue(plugin, true));
                entity.setMetadata("reflectChance", new FixedMetadataValue(plugin, reflectChance));
                entity.setMetadata("reflectMultiplier", new FixedMetadataValue(plugin, reflectMultiplier));
            }

            @Override
            public void stop(LivingEntity entity) {
                entity.removeMetadata("shieldReflect", plugin);
                entity.removeMetadata("reflectChance", plugin);
                entity.removeMetadata("reflectMultiplier", plugin);
            }
        });

        // 狂暴模式技能（被动技能，通过元数据实现）
        skillHandlers.put("berserk_mode", new SkillHandler() {
            @Override
            public void execute(LivingEntity entity, IdzEntityConfig.SkillConfig skillConfig, SkillTemplate template) {
                double berserkThreshold = skillConfig.getParameter("berserk_threshold", 0.3);
                double damageBoost = skillConfig.getParameter("damage_boost", 1.5);
                int speedBoost = skillConfig.getParameter("speed_boost", 2);

                // 设置元数据，供事件监听器使用
                entity.setMetadata("berserkMode", new FixedMetadataValue(plugin, true));
                entity.setMetadata("berserkThreshold", new FixedMetadataValue(plugin, berserkThreshold));
                entity.setMetadata("damageBoost", new FixedMetadataValue(plugin, damageBoost));
                entity.setMetadata("speedBoost", new FixedMetadataValue(plugin, speedBoost));
            }

            @Override
            public void stop(LivingEntity entity) {
                entity.removeMetadata("berserkMode", plugin);
                entity.removeMetadata("berserkThreshold", plugin);
                entity.removeMetadata("damageBoost", plugin);
                entity.removeMetadata("speedBoost", plugin);
                entity.removeMetadata("berserkActive", plugin);
            }
        });

        logger.info("已注册 " + skillHandlers.size() + " 个技能处理器");
    }
    
    /**
     * 获取技能模板管理器
     */
    public SkillTemplateManager getSkillTemplateManager() {
        return skillTemplateManager;
    }
    
    /**
     * 清理所有任务
     */
    public void cleanup() {
        for (List<BukkitTask> tasks : entityTasks.values()) {
            for (BukkitTask task : tasks) {
                if (task != null && !task.isCancelled()) {
                    task.cancel();
                }
            }
        }
        entityTasks.clear();
        logger.info("IDZ技能执行器已清理所有任务");
    }
}
