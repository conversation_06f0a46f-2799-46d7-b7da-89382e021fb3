package org.Ver_zhzh.customZombie.UserMaker;

import org.bukkit.Particle;
import org.bukkit.Sound;
import org.bukkit.potion.PotionEffectType;
import java.util.*;

/**
 * 技能模板类 - 定义所有可用的技能模板
 * 从ID和IDC系列怪物中提取的完整技能库
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class SkillTemplate {
    
    // 技能基础信息
    private String skillId;              // 技能唯一ID
    private String skillName;            // 技能显示名称
    private String description;          // 技能描述
    private SkillCategory category;      // 技能分类
    private SkillType type;              // 技能类型
    private String sourceEntity;         // 来源实体（如"IDC1", "ID5"等）
    
    // 技能参数
    private Map<String, Object> defaultParameters;  // 默认参数
    private Map<String, ParameterConfig> parameterConfigs;  // 参数配置信息
    
    // 技能兼容性
    private List<String> incompatibleSkills;  // 不兼容的技能列表
    private List<String> requiredSkills;      // 前置技能列表
    private int maxCombinations;              // 最大组合数量
    
    // 技能效果配置
    private EffectConfig effectConfig;
    
    /**
     * 技能分类枚举
     */
    public enum SkillCategory {
        ATTACK("攻击类", "直接造成伤害的技能"),
        SUMMON("召唤类", "召唤仆从或分裂的技能"),
        MOVEMENT("移动类", "传送、跳跃、追踪等移动技能"),
        EFFECT("特效类", "粒子效果、音效、视觉效果"),
        DEFENSE("防御类", "护盾、减伤、反击技能"),
        DEBUFF("负面类", "给玩家添加负面效果的技能"),
        BUFF("增益类", "给自身或队友添加增益效果"),
        ENVIRONMENTAL("环境类", "改变环境的技能（如生成方块）");
        
        private final String displayName;
        private final String description;
        
        SkillCategory(String displayName, String description) {
            this.displayName = displayName;
            this.description = description;
        }
        
        public String getDisplayName() { return displayName; }
        public String getDescription() { return description; }
    }
    
    /**
     * 技能类型枚举
     */
    public enum SkillType {
        PASSIVE("被动技能", "持续生效的技能"),
        ACTIVE_TIMED("定时主动", "按时间间隔触发的技能"),
        ACTIVE_TRIGGERED("触发主动", "特定条件触发的技能"),
        DEATH_SKILL("死亡技能", "实体死亡时触发的技能"),
        DAMAGE_TRIGGERED("受伤触发", "受到伤害时触发的技能");
        
        private final String displayName;
        private final String description;
        
        SkillType(String displayName, String description) {
            this.displayName = displayName;
            this.description = description;
        }
        
        public String getDisplayName() { return displayName; }
        public String getDescription() { return description; }
    }
    
    /**
     * 参数配置类
     */
    public static class ParameterConfig {
        private String parameterName;     // 参数名称
        private String displayName;       // 显示名称
        private String description;       // 参数描述
        private Class<?> dataType;        // 数据类型
        private Object minValue;          // 最小值
        private Object maxValue;          // 最大值
        private Object defaultValue;      // 默认值
        private String unit;              // 单位（如"秒", "格", "点"）
        private boolean required;         // 是否必需
        
        // 构造函数和getter/setter方法
        public ParameterConfig(String parameterName, String displayName, String description, 
                             Class<?> dataType, Object defaultValue, String unit) {
            this.parameterName = parameterName;
            this.displayName = displayName;
            this.description = description;
            this.dataType = dataType;
            this.defaultValue = defaultValue;
            this.unit = unit;
            this.required = false;
        }
        
        // Getter方法
        public String getParameterName() { return parameterName; }
        public String getDisplayName() { return displayName; }
        public String getDescription() { return description; }
        public Class<?> getDataType() { return dataType; }
        public Object getMinValue() { return minValue; }
        public Object getMaxValue() { return maxValue; }
        public Object getDefaultValue() { return defaultValue; }
        public String getUnit() { return unit; }
        public boolean isRequired() { return required; }
        
        // Setter方法
        public ParameterConfig setMinValue(Object minValue) { this.minValue = minValue; return this; }
        public ParameterConfig setMaxValue(Object maxValue) { this.maxValue = maxValue; return this; }
        public ParameterConfig setRequired(boolean required) { this.required = required; return this; }
    }
    
    /**
     * 效果配置类
     */
    public static class EffectConfig {
        private List<Particle> particles;        // 粒子效果
        private List<Sound> sounds;              // 音效
        private List<PotionEffectType> potionEffects;  // 药水效果
        private boolean hasVisualEffect;         // 是否有视觉效果
        private boolean hasAudioEffect;          // 是否有音效
        
        public EffectConfig() {
            this.particles = new ArrayList<>();
            this.sounds = new ArrayList<>();
            this.potionEffects = new ArrayList<>();
            this.hasVisualEffect = false;
            this.hasAudioEffect = false;
        }
        
        // Getter和Setter方法
        public List<Particle> getParticles() { return particles; }
        public List<Sound> getSounds() { return sounds; }
        public List<PotionEffectType> getPotionEffects() { return potionEffects; }
        public boolean hasVisualEffect() { return hasVisualEffect; }
        public boolean hasAudioEffect() { return hasAudioEffect; }
        
        public EffectConfig addParticle(Particle particle) { 
            this.particles.add(particle); 
            this.hasVisualEffect = true;
            return this; 
        }
        public EffectConfig addSound(Sound sound) { 
            this.sounds.add(sound); 
            this.hasAudioEffect = true;
            return this; 
        }
        public EffectConfig addPotionEffect(PotionEffectType effect) { 
            this.potionEffects.add(effect); 
            return this; 
        }
    }
    
    /**
     * 构造函数
     */
    public SkillTemplate(String skillId, String skillName, String description, 
                        SkillCategory category, SkillType type, String sourceEntity) {
        this.skillId = skillId;
        this.skillName = skillName;
        this.description = description;
        this.category = category;
        this.type = type;
        this.sourceEntity = sourceEntity;
        this.defaultParameters = new HashMap<>();
        this.parameterConfigs = new HashMap<>();
        this.incompatibleSkills = new ArrayList<>();
        this.requiredSkills = new ArrayList<>();
        this.maxCombinations = -1; // -1表示无限制
        this.effectConfig = new EffectConfig();
    }
    
    // Getter方法
    public String getSkillId() { return skillId; }
    public String getSkillName() { return skillName; }
    public String getDescription() { return description; }
    public SkillCategory getCategory() { return category; }
    public SkillType getType() { return type; }
    public String getSourceEntity() { return sourceEntity; }
    public Map<String, Object> getDefaultParameters() { return defaultParameters; }
    public Map<String, ParameterConfig> getParameterConfigs() { return parameterConfigs; }
    public List<String> getIncompatibleSkills() { return incompatibleSkills; }
    public List<String> getRequiredSkills() { return requiredSkills; }
    public int getMaxCombinations() { return maxCombinations; }
    public EffectConfig getEffectConfig() { return effectConfig; }
    
    // 流式API方法
    public SkillTemplate addParameter(String name, ParameterConfig config) {
        this.parameterConfigs.put(name, config);
        this.defaultParameters.put(name, config.getDefaultValue());
        return this;
    }
    
    public SkillTemplate addIncompatibleSkill(String skillId) {
        this.incompatibleSkills.add(skillId);
        return this;
    }
    
    public SkillTemplate addRequiredSkill(String skillId) {
        this.requiredSkills.add(skillId);
        return this;
    }
    
    public SkillTemplate setMaxCombinations(int max) {
        this.maxCombinations = max;
        return this;
    }
    
    public SkillTemplate setEffectConfig(EffectConfig config) {
        this.effectConfig = config;
        return this;
    }
    
    /**
     * 检查技能兼容性
     */
    public boolean isCompatibleWith(SkillTemplate other) {
        return !this.incompatibleSkills.contains(other.getSkillId()) &&
               !other.getIncompatibleSkills().contains(this.skillId);
    }
    
    /**
     * 获取技能的完整信息
     */
    public Map<String, Object> getFullInfo() {
        Map<String, Object> info = new HashMap<>();
        info.put("skillId", skillId);
        info.put("skillName", skillName);
        info.put("description", description);
        info.put("category", category.getDisplayName());
        info.put("type", type.getDisplayName());
        info.put("sourceEntity", sourceEntity);
        info.put("parameters", parameterConfigs);
        info.put("defaultParameters", defaultParameters);
        info.put("incompatibleSkills", incompatibleSkills);
        info.put("requiredSkills", requiredSkills);
        info.put("effectConfig", effectConfig);
        return info;
    }
    
    @Override
    public String toString() {
        return String.format("SkillTemplate{id='%s', name='%s', category=%s, type=%s, source='%s'}", 
                           skillId, skillName, category, type, sourceEntity);
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        SkillTemplate that = (SkillTemplate) obj;
        return Objects.equals(skillId, that.skillId);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(skillId);
    }
}
