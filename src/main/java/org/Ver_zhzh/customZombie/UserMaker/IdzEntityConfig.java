package org.Ver_zhzh.customZombie.UserMaker;

import org.bukkit.EntityType;
import org.bukkit.Material;
import org.bukkit.enchantments.Enchantment;
import org.bukkit.potion.PotionEffectType;

import java.util.*;

/**
 * IDZ实体配置类 - 用于配置自定义IDZ系列怪物
 * 支持完全自定义的属性、技能、装备和效果
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class IdzEntityConfig {
    
    // 基础信息
    private String entityId;                    // 实体ID (如 "idz1", "idz2")
    private String displayName;                 // 显示名称
    private String description;                 // 描述
    private String createdBy;                   // 创建者
    private long createdTime;                   // 创建时间
    private long lastModified;                  // 最后修改时间
    
    // 基础属性
    private EntityType entityType;              // 实体类型
    private double health;                      // 生命值
    private double damage;                      // 攻击伤害
    private double speed;                       // 移动速度倍数
    private boolean canPickupItems;             // 是否可以拾取物品
    private boolean canDespawn;                 // 是否会自然消失
    private boolean hasAI;                      // 是否有AI
    
    // 装备配置
    private EquipmentConfig equipment;
    
    // 药水效果配置
    private List<PotionEffectConfig> potionEffects;
    
    // 技能配置
    private List<SkillConfig> skills;
    
    // 外观配置
    private AppearanceConfig appearance;
    
    // 高级配置
    private AdvancedConfig advanced;
    
    /**
     * 装备配置类
     */
    public static class EquipmentConfig {
        private ItemConfig mainHand;            // 主手武器
        private ItemConfig offHand;             // 副手物品
        private ItemConfig helmet;              // 头盔
        private ItemConfig chestplate;          // 胸甲
        private ItemConfig leggings;            // 护腿
        private ItemConfig boots;               // 靴子
        
        public EquipmentConfig() {
            this.mainHand = new ItemConfig();
            this.offHand = new ItemConfig();
            this.helmet = new ItemConfig();
            this.chestplate = new ItemConfig();
            this.leggings = new ItemConfig();
            this.boots = new ItemConfig();
        }
        
        // Getter和Setter方法
        public ItemConfig getMainHand() { return mainHand; }
        public ItemConfig getOffHand() { return offHand; }
        public ItemConfig getHelmet() { return helmet; }
        public ItemConfig getChestplate() { return chestplate; }
        public ItemConfig getLeggings() { return leggings; }
        public ItemConfig getBoots() { return boots; }
        
        public void setMainHand(ItemConfig mainHand) { this.mainHand = mainHand; }
        public void setOffHand(ItemConfig offHand) { this.offHand = offHand; }
        public void setHelmet(ItemConfig helmet) { this.helmet = helmet; }
        public void setChestplate(ItemConfig chestplate) { this.chestplate = chestplate; }
        public void setLeggings(ItemConfig leggings) { this.leggings = leggings; }
        public void setBoots(ItemConfig boots) { this.boots = boots; }
    }
    
    /**
     * 物品配置类
     */
    public static class ItemConfig {
        private Material material;              // 物品材质
        private String displayName;             // 显示名称
        private List<String> lore;              // 物品描述
        private Map<Enchantment, Integer> enchantments;  // 附魔
        private double dropChance;              // 掉落概率
        private boolean unbreakable;            // 是否无法破坏
        
        public ItemConfig() {
            this.material = Material.AIR;
            this.displayName = "";
            this.lore = new ArrayList<>();
            this.enchantments = new HashMap<>();
            this.dropChance = 0.0;
            this.unbreakable = false;
        }
        
        // Getter和Setter方法
        public Material getMaterial() { return material; }
        public String getDisplayName() { return displayName; }
        public List<String> getLore() { return lore; }
        public Map<Enchantment, Integer> getEnchantments() { return enchantments; }
        public double getDropChance() { return dropChance; }
        public boolean isUnbreakable() { return unbreakable; }
        
        public void setMaterial(Material material) { this.material = material; }
        public void setDisplayName(String displayName) { this.displayName = displayName; }
        public void setLore(List<String> lore) { this.lore = lore; }
        public void setEnchantments(Map<Enchantment, Integer> enchantments) { this.enchantments = enchantments; }
        public void setDropChance(double dropChance) { this.dropChance = dropChance; }
        public void setUnbreakable(boolean unbreakable) { this.unbreakable = unbreakable; }
        
        public void addEnchantment(Enchantment enchantment, int level) {
            this.enchantments.put(enchantment, level);
        }
        
        public void addLore(String line) {
            this.lore.add(line);
        }
    }
    
    /**
     * 药水效果配置类
     */
    public static class PotionEffectConfig {
        private PotionEffectType effectType;    // 效果类型
        private int amplifier;                  // 效果等级 (0-based)
        private int duration;                   // 持续时间 (tick, -1为永久)
        private boolean ambient;                // 是否为环境效果
        private boolean particles;              // 是否显示粒子
        private boolean icon;                   // 是否显示图标
        
        public PotionEffectConfig(PotionEffectType effectType, int amplifier, int duration) {
            this.effectType = effectType;
            this.amplifier = amplifier;
            this.duration = duration;
            this.ambient = false;
            this.particles = true;
            this.icon = true;
        }
        
        // Getter和Setter方法
        public PotionEffectType getEffectType() { return effectType; }
        public int getAmplifier() { return amplifier; }
        public int getDuration() { return duration; }
        public boolean isAmbient() { return ambient; }
        public boolean hasParticles() { return particles; }
        public boolean hasIcon() { return icon; }
        
        public void setEffectType(PotionEffectType effectType) { this.effectType = effectType; }
        public void setAmplifier(int amplifier) { this.amplifier = amplifier; }
        public void setDuration(int duration) { this.duration = duration; }
        public void setAmbient(boolean ambient) { this.ambient = ambient; }
        public void setParticles(boolean particles) { this.particles = particles; }
        public void setIcon(boolean icon) { this.icon = icon; }
    }
    
    /**
     * 技能配置类
     */
    public static class SkillConfig {
        private String skillId;                 // 技能ID
        private boolean enabled;                // 是否启用
        private Map<String, Object> parameters; // 技能参数
        private int priority;                   // 优先级
        
        public SkillConfig(String skillId) {
            this.skillId = skillId;
            this.enabled = true;
            this.parameters = new HashMap<>();
            this.priority = 0;
        }
        
        // Getter和Setter方法
        public String getSkillId() { return skillId; }
        public boolean isEnabled() { return enabled; }
        public Map<String, Object> getParameters() { return parameters; }
        public int getPriority() { return priority; }
        
        public void setSkillId(String skillId) { this.skillId = skillId; }
        public void setEnabled(boolean enabled) { this.enabled = enabled; }
        public void setParameters(Map<String, Object> parameters) { this.parameters = parameters; }
        public void setPriority(int priority) { this.priority = priority; }
        
        public void setParameter(String key, Object value) {
            this.parameters.put(key, value);
        }
        
        public Object getParameter(String key) {
            return this.parameters.get(key);
        }
        
        public <T> T getParameter(String key, T defaultValue) {
            Object value = this.parameters.get(key);
            if (value == null) {
                return defaultValue;
            }
            try {
                return (T) value;
            } catch (ClassCastException e) {
                return defaultValue;
            }
        }
    }
    
    /**
     * 外观配置类
     */
    public static class AppearanceConfig {
        private boolean customNameVisible;      // 名称是否可见
        private boolean glowing;                // 是否发光
        private boolean invisible;              // 是否隐身
        private boolean silent;                 // 是否静音
        private float scale;                    // 缩放比例 (1.0为正常大小)
        
        public AppearanceConfig() {
            this.customNameVisible = false;
            this.glowing = false;
            this.invisible = false;
            this.silent = false;
            this.scale = 1.0f;
        }
        
        // Getter和Setter方法
        public boolean isCustomNameVisible() { return customNameVisible; }
        public boolean isGlowing() { return glowing; }
        public boolean isInvisible() { return invisible; }
        public boolean isSilent() { return silent; }
        public float getScale() { return scale; }
        
        public void setCustomNameVisible(boolean customNameVisible) { this.customNameVisible = customNameVisible; }
        public void setGlowing(boolean glowing) { this.glowing = glowing; }
        public void setInvisible(boolean invisible) { this.invisible = invisible; }
        public void setSilent(boolean silent) { this.silent = silent; }
        public void setScale(float scale) { this.scale = scale; }
    }
    
    /**
     * 高级配置类
     */
    public static class AdvancedConfig {
        private boolean persistAcrossRestart;   // 是否在重启后保持
        private int maxInstanceCount;           // 最大实例数量
        private List<String> spawnConditions;   // 生成条件
        private Map<String, Object> customData; // 自定义数据
        
        public AdvancedConfig() {
            this.persistAcrossRestart = false;
            this.maxInstanceCount = -1; // -1表示无限制
            this.spawnConditions = new ArrayList<>();
            this.customData = new HashMap<>();
        }
        
        // Getter和Setter方法
        public boolean isPersistAcrossRestart() { return persistAcrossRestart; }
        public int getMaxInstanceCount() { return maxInstanceCount; }
        public List<String> getSpawnConditions() { return spawnConditions; }
        public Map<String, Object> getCustomData() { return customData; }
        
        public void setPersistAcrossRestart(boolean persistAcrossRestart) { this.persistAcrossRestart = persistAcrossRestart; }
        public void setMaxInstanceCount(int maxInstanceCount) { this.maxInstanceCount = maxInstanceCount; }
        public void setSpawnConditions(List<String> spawnConditions) { this.spawnConditions = spawnConditions; }
        public void setCustomData(Map<String, Object> customData) { this.customData = customData; }
        
        public void addSpawnCondition(String condition) {
            this.spawnConditions.add(condition);
        }
        
        public void setCustomData(String key, Object value) {
            this.customData.put(key, value);
        }
    }
    
    /**
     * 构造函数
     */
    public IdzEntityConfig(String entityId) {
        this.entityId = entityId;
        this.displayName = entityId;
        this.description = "";
        this.createdBy = "Unknown";
        this.createdTime = System.currentTimeMillis();
        this.lastModified = this.createdTime;
        
        // 默认属性
        this.entityType = EntityType.ZOMBIE;
        this.health = 20.0;
        this.damage = 2.0;
        this.speed = 1.0;
        this.canPickupItems = false;
        this.canDespawn = false;
        this.hasAI = true;
        
        // 初始化配置对象
        this.equipment = new EquipmentConfig();
        this.potionEffects = new ArrayList<>();
        this.skills = new ArrayList<>();
        this.appearance = new AppearanceConfig();
        this.advanced = new AdvancedConfig();
    }
    
    // 基础属性的Getter和Setter方法
    public String getEntityId() { return entityId; }
    public String getDisplayName() { return displayName; }
    public String getDescription() { return description; }
    public String getCreatedBy() { return createdBy; }
    public long getCreatedTime() { return createdTime; }
    public long getLastModified() { return lastModified; }
    public EntityType getEntityType() { return entityType; }
    public double getHealth() { return health; }
    public double getDamage() { return damage; }
    public double getSpeed() { return speed; }
    public boolean canPickupItems() { return canPickupItems; }
    public boolean canDespawn() { return canDespawn; }
    public boolean hasAI() { return hasAI; }
    public EquipmentConfig getEquipment() { return equipment; }
    public List<PotionEffectConfig> getPotionEffects() { return potionEffects; }
    public List<SkillConfig> getSkills() { return skills; }
    public AppearanceConfig getAppearance() { return appearance; }
    public AdvancedConfig getAdvanced() { return advanced; }
    
    public void setEntityId(String entityId) { this.entityId = entityId; updateModified(); }
    public void setDisplayName(String displayName) { this.displayName = displayName; updateModified(); }
    public void setDescription(String description) { this.description = description; updateModified(); }
    public void setCreatedBy(String createdBy) { this.createdBy = createdBy; updateModified(); }
    public void setEntityType(EntityType entityType) { this.entityType = entityType; updateModified(); }
    public void setHealth(double health) { this.health = health; updateModified(); }
    public void setDamage(double damage) { this.damage = damage; updateModified(); }
    public void setSpeed(double speed) { this.speed = speed; updateModified(); }
    public void setCanPickupItems(boolean canPickupItems) { this.canPickupItems = canPickupItems; updateModified(); }
    public void setCanDespawn(boolean canDespawn) { this.canDespawn = canDespawn; updateModified(); }
    public void setHasAI(boolean hasAI) { this.hasAI = hasAI; updateModified(); }
    
    /**
     * 更新最后修改时间
     */
    private void updateModified() {
        this.lastModified = System.currentTimeMillis();
    }
    
    /**
     * 添加技能
     */
    public void addSkill(String skillId) {
        this.skills.add(new SkillConfig(skillId));
        updateModified();
    }
    
    /**
     * 添加技能（带参数）
     */
    public void addSkill(String skillId, Map<String, Object> parameters) {
        SkillConfig skillConfig = new SkillConfig(skillId);
        skillConfig.setParameters(parameters);
        this.skills.add(skillConfig);
        updateModified();
    }
    
    /**
     * 移除技能
     */
    public void removeSkill(String skillId) {
        this.skills.removeIf(skill -> skill.getSkillId().equals(skillId));
        updateModified();
    }
    
    /**
     * 获取技能配置
     */
    public SkillConfig getSkillConfig(String skillId) {
        return this.skills.stream()
            .filter(skill -> skill.getSkillId().equals(skillId))
            .findFirst()
            .orElse(null);
    }
    
    /**
     * 添加药水效果
     */
    public void addPotionEffect(PotionEffectType effectType, int amplifier, int duration) {
        this.potionEffects.add(new PotionEffectConfig(effectType, amplifier, duration));
        updateModified();
    }
    
    /**
     * 移除药水效果
     */
    public void removePotionEffect(PotionEffectType effectType) {
        this.potionEffects.removeIf(effect -> effect.getEffectType().equals(effectType));
        updateModified();
    }
    
    /**
     * 验证配置有效性
     */
    public List<String> validate() {
        List<String> errors = new ArrayList<>();
        
        if (entityId == null || entityId.trim().isEmpty()) {
            errors.add("实体ID不能为空");
        }
        
        if (health <= 0) {
            errors.add("生命值必须大于0");
        }
        
        if (damage < 0) {
            errors.add("攻击伤害不能为负数");
        }
        
        if (speed <= 0) {
            errors.add("移动速度必须大于0");
        }
        
        // 验证技能配置
        for (SkillConfig skill : skills) {
            if (skill.getSkillId() == null || skill.getSkillId().trim().isEmpty()) {
                errors.add("技能ID不能为空");
            }
        }
        
        return errors;
    }
    
    /**
     * 克隆配置
     */
    public IdzEntityConfig clone() {
        // TODO: 实现深拷贝
        return this;
    }
    
    @Override
    public String toString() {
        return String.format("IdzEntityConfig{id='%s', name='%s', type=%s, health=%.1f, skills=%d}", 
                           entityId, displayName, entityType, health, skills.size());
    }
}
