package org.Ver_zhzh.customZombie.UserMaker;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.sun.net.httpserver.HttpExchange;
import com.sun.net.httpserver.HttpHandler;
import org.bukkit.Bukkit;
import org.bukkit.Location;
import org.bukkit.World;
import org.bukkit.entity.LivingEntity;
import org.bukkit.entity.Player;
import org.Ver_zhzh.deathZombieV4.DeathZombieV4;

import java.io.*;
import java.net.URI;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.logging.Logger;

/**
 * IDZ API控制器 - 处理IDZ相关的HTTP请求
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class IdzApiController implements HttpHandler {
    
    private final DeathZombieV4 plugin;
    private final Logger logger;
    private final UserMaker userMaker;
    private final Gson gson;
    
    public IdzApiController(DeathZombieV4 plugin, UserMaker userMaker) {
        this.plugin = plugin;
        this.logger = plugin.getLogger();
        this.userMaker = userMaker;
        this.gson = new GsonBuilder().setPrettyPrinting().create();
    }
    
    @Override
    public void handle(HttpExchange exchange) throws IOException {
        String method = exchange.getRequestMethod();
        URI uri = exchange.getRequestURI();
        String path = uri.getPath();
        
        try {
            // 设置CORS头
            exchange.getResponseHeaders().add("Access-Control-Allow-Origin", "*");
            exchange.getResponseHeaders().add("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS");
            exchange.getResponseHeaders().add("Access-Control-Allow-Headers", "Content-Type, Authorization");
            
            if ("OPTIONS".equals(method)) {
                sendResponse(exchange, 200, "");
                return;
            }
            
            // 路由处理
            if (path.startsWith("/api/idz/entities")) {
                handleEntitiesApi(exchange, method, path);
            } else if (path.startsWith("/api/idz/skill-templates")) {
                handleSkillTemplatesApi(exchange, method);
            } else if (path.startsWith("/api/idz/spawn")) {
                handleSpawnApi(exchange, method);
            } else if (path.startsWith("/api/idz/validate")) {
                handleValidateApi(exchange, method);
            } else if (path.startsWith("/api/idz/statistics")) {
                handleStatisticsApi(exchange, method);
            } else {
                sendErrorResponse(exchange, 404, "API endpoint not found");
            }
            
        } catch (Exception e) {
            logger.severe("IDZ API处理出错: " + e.getMessage());
            e.printStackTrace();
            sendErrorResponse(exchange, 500, "Internal server error: " + e.getMessage());
        }
    }
    
    /**
     * 处理实体相关API
     */
    private void handleEntitiesApi(HttpExchange exchange, String method, String path) throws IOException {
        switch (method) {
            case "GET":
                if (path.equals("/api/idz/entities")) {
                    // 获取所有IDZ实体
                    List<IdzEntityConfig> entities = userMaker.getAllIdzConfigs();
                    Map<String, Object> response = new HashMap<>();
                    response.put("success", true);
                    response.put("entities", entities);
                    sendJsonResponse(exchange, 200, response);
                } else {
                    // 获取特定IDZ实体
                    String entityId = extractEntityIdFromPath(path);
                    if (entityId != null) {
                        IdzEntityConfig entity = userMaker.getIdzConfig(entityId);
                        if (entity != null) {
                            Map<String, Object> response = new HashMap<>();
                            response.put("success", true);
                            response.put("entity", entity);
                            sendJsonResponse(exchange, 200, response);
                        } else {
                            sendErrorResponse(exchange, 404, "Entity not found");
                        }
                    } else {
                        sendErrorResponse(exchange, 400, "Invalid entity ID");
                    }
                }
                break;
                
            case "POST":
                // 创建或更新IDZ实体
                String requestBody = readRequestBody(exchange);
                try {
                    IdzEntityConfig entity = gson.fromJson(requestBody, IdzEntityConfig.class);
                    if (entity != null) {
                        boolean success = userMaker.saveIdzConfig(entity);
                        if (success) {
                            Map<String, Object> response = new HashMap<>();
                            response.put("success", true);
                            response.put("message", "Entity saved successfully");
                            sendJsonResponse(exchange, 200, response);
                        } else {
                            sendErrorResponse(exchange, 400, "Failed to save entity");
                        }
                    } else {
                        sendErrorResponse(exchange, 400, "Invalid entity data");
                    }
                } catch (Exception e) {
                    sendErrorResponse(exchange, 400, "Invalid JSON format: " + e.getMessage());
                }
                break;
                
            case "DELETE":
                String entityId = extractEntityIdFromPath(path);
                if (entityId != null) {
                    boolean success = userMaker.deleteIdzConfig(entityId);
                    if (success) {
                        Map<String, Object> response = new HashMap<>();
                        response.put("success", true);
                        response.put("message", "Entity deleted successfully");
                        sendJsonResponse(exchange, 200, response);
                    } else {
                        sendErrorResponse(exchange, 404, "Entity not found or failed to delete");
                    }
                } else {
                    sendErrorResponse(exchange, 400, "Invalid entity ID");
                }
                break;
                
            default:
                sendErrorResponse(exchange, 405, "Method not allowed");
                break;
        }
    }
    
    /**
     * 处理技能模板API
     */
    private void handleSkillTemplatesApi(HttpExchange exchange, String method) throws IOException {
        if ("GET".equals(method)) {
            List<SkillTemplate> templates = userMaker.getAllSkillTemplates();
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("templates", templates);
            sendJsonResponse(exchange, 200, response);
        } else {
            sendErrorResponse(exchange, 405, "Method not allowed");
        }
    }
    
    /**
     * 处理生成实体API
     */
    private void handleSpawnApi(HttpExchange exchange, String method) throws IOException {
        if ("POST".equals(method)) {
            String requestBody = readRequestBody(exchange);
            try {
                Map<String, Object> request = gson.fromJson(requestBody, Map.class);
                String entityId = (String) request.get("entityId");
                String locationStr = (String) request.get("location");
                
                if (entityId == null) {
                    sendErrorResponse(exchange, 400, "Missing entityId");
                    return;
                }
                
                // 在主线程中执行生成操作
                Bukkit.getScheduler().runTask(plugin, () -> {
                    try {
                        Location spawnLocation = getSpawnLocation(locationStr);
                        if (spawnLocation != null) {
                            LivingEntity entity = userMaker.spawnIdzEntity(entityId, spawnLocation);
                            if (entity != null) {
                                logger.info("成功生成IDZ实体: " + entityId + " 在位置: " + spawnLocation);
                            } else {
                                logger.warning("生成IDZ实体失败: " + entityId);
                            }
                        } else {
                            logger.warning("无效的生成位置: " + locationStr);
                        }
                    } catch (Exception e) {
                        logger.severe("生成IDZ实体时出错: " + e.getMessage());
                        e.printStackTrace();
                    }
                });
                
                Map<String, Object> response = new HashMap<>();
                response.put("success", true);
                response.put("message", "Spawn request submitted");
                sendJsonResponse(exchange, 200, response);
                
            } catch (Exception e) {
                sendErrorResponse(exchange, 400, "Invalid request format: " + e.getMessage());
            }
        } else {
            sendErrorResponse(exchange, 405, "Method not allowed");
        }
    }
    
    /**
     * 处理验证API
     */
    private void handleValidateApi(HttpExchange exchange, String method) throws IOException {
        if ("POST".equals(method)) {
            String requestBody = readRequestBody(exchange);
            try {
                IdzEntityConfig entity = gson.fromJson(requestBody, IdzEntityConfig.class);
                if (entity != null) {
                    List<String> errors = entity.validate();
                    Map<String, Object> response = new HashMap<>();
                    response.put("valid", errors.isEmpty());
                    response.put("errors", errors);
                    sendJsonResponse(exchange, 200, response);
                } else {
                    sendErrorResponse(exchange, 400, "Invalid entity data");
                }
            } catch (Exception e) {
                sendErrorResponse(exchange, 400, "Invalid JSON format: " + e.getMessage());
            }
        } else {
            sendErrorResponse(exchange, 405, "Method not allowed");
        }
    }
    
    /**
     * 处理统计信息API
     */
    private void handleStatisticsApi(HttpExchange exchange, String method) throws IOException {
        if ("GET".equals(method)) {
            Map<String, Object> statistics = userMaker.getSystemStatistics();
            sendJsonResponse(exchange, 200, statistics);
        } else {
            sendErrorResponse(exchange, 405, "Method not allowed");
        }
    }
    
    /**
     * 从路径中提取实体ID
     */
    private String extractEntityIdFromPath(String path) {
        String[] parts = path.split("/");
        if (parts.length >= 4 && "entities".equals(parts[3])) {
            return parts.length > 4 ? parts[4] : null;
        }
        return null;
    }
    
    /**
     * 获取生成位置
     */
    private Location getSpawnLocation(String locationStr) {
        if ("player".equals(locationStr)) {
            // 在第一个在线玩家位置生成
            Collection<? extends Player> players = Bukkit.getOnlinePlayers();
            if (!players.isEmpty()) {
                Player player = players.iterator().next();
                return player.getLocation().add(2, 0, 2); // 在玩家旁边生成
            }
        }
        
        // 默认在主世界出生点生成
        World world = Bukkit.getWorlds().get(0);
        return world.getSpawnLocation();
    }
    
    /**
     * 读取请求体
     */
    private String readRequestBody(HttpExchange exchange) throws IOException {
        try (BufferedReader reader = new BufferedReader(
                new InputStreamReader(exchange.getRequestBody(), StandardCharsets.UTF_8))) {
            StringBuilder body = new StringBuilder();
            String line;
            while ((line = reader.readLine()) != null) {
                body.append(line);
            }
            return body.toString();
        }
    }
    
    /**
     * 发送JSON响应
     */
    private void sendJsonResponse(HttpExchange exchange, int statusCode, Object data) throws IOException {
        String jsonResponse = gson.toJson(data);
        byte[] responseBytes = jsonResponse.getBytes(StandardCharsets.UTF_8);
        
        exchange.getResponseHeaders().set("Content-Type", "application/json; charset=utf-8");
        exchange.sendResponseHeaders(statusCode, responseBytes.length);
        
        try (OutputStream os = exchange.getResponseBody()) {
            os.write(responseBytes);
        }
    }
    
    /**
     * 发送错误响应
     */
    private void sendErrorResponse(HttpExchange exchange, int statusCode, String message) throws IOException {
        Map<String, Object> error = new HashMap<>();
        error.put("success", false);
        error.put("error", message);
        sendJsonResponse(exchange, statusCode, error);
    }
    
    /**
     * 发送普通响应
     */
    private void sendResponse(HttpExchange exchange, int statusCode, String response) throws IOException {
        byte[] responseBytes = response.getBytes(StandardCharsets.UTF_8);
        exchange.sendResponseHeaders(statusCode, responseBytes.length);
        
        try (OutputStream os = exchange.getResponseBody()) {
            os.write(responseBytes);
        }
    }
}
