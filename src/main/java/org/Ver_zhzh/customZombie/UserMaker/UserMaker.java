package org.Ver_zhzh.customZombie.UserMaker;

import org.bukkit.plugin.java.JavaPlugin;
import java.util.List;
import java.util.Map;
import java.util.logging.Logger;

/**
 * 用户自定义怪物制作器主类
 * 提供自定义怪物创建和管理功能
 *
 * <AUTHOR>
 * @version 1.0
 */
public class UserMaker {

    private final JavaPlugin plugin;
    private final Logger logger;
    private final SkillTemplateManager skillTemplateManager;

    /**
     * 构造函数
     * @param plugin 插件实例
     */
    public UserMaker(JavaPlugin plugin) {
        this.plugin = plugin;
        this.logger = plugin.getLogger();
        this.skillTemplateManager = new SkillTemplateManager();

        logger.info("UserMaker系统已初始化，加载了 " +
                   skillTemplateManager.getAllTemplates().size() + " 个技能模板");
    }

    /**
     * 获取技能模板管理器
     * @return 技能模板管理器
     */
    public SkillTemplateManager getSkillTemplateManager() {
        return skillTemplateManager;
    }

    /**
     * 获取所有可用的技能模板
     * @return 技能模板列表
     */
    public List<SkillTemplate> getAllSkillTemplates() {
        return skillTemplateManager.getAllTemplates();
    }

    /**
     * 根据分类获取技能模板
     * @param category 技能分类
     * @return 技能模板列表
     */
    public List<SkillTemplate> getSkillTemplatesByCategory(SkillCategory category) {
        return skillTemplateManager.getTemplatesByCategory(category);
    }

    /**
     * 搜索技能模板
     * @param keyword 关键词
     * @return 匹配的技能模板列表
     */
    public List<SkillTemplate> searchSkillTemplates(String keyword) {
        return skillTemplateManager.searchTemplates(keyword);
    }

    /**
     * 验证技能组合
     * @param skillIds 技能ID列表
     * @return 验证结果
     */
    public SkillCombinationResult validateSkillCombination(List<String> skillIds) {
        return skillTemplateManager.checkCombinationCompatibility(skillIds);
    }

    /**
     * 生成怪物配置
     * @param entityType 实体类型
     * @param skillConfigs 技能配置列表
     * @param basicAttributes 基础属性
     * @return 生成的配置
     */
    public Map<String, Object> generateMonsterConfig(String entityType,
                                                   List<Map<String, Object>> skillConfigs,
                                                   Map<String, Object> basicAttributes) {
        // TODO: 实现配置生成逻辑
        return null;
    }

    /**
     * 获取系统统计信息
     * @return 统计信息
     */
    public Map<String, Object> getSystemStatistics() {
        return skillTemplateManager.getStatistics();
    }

    /**
     * 获取推荐的技能组合
     * @param baseSkillId 基础技能ID
     * @return 推荐组合列表
     */
    public List<List<String>> getRecommendedCombinations(String baseSkillId) {
        return skillTemplateManager.getRecommendedCombinations(baseSkillId);
    }
}
