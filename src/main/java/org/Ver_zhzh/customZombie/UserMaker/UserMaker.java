package org.Ver_zhzh.customZombie.UserMaker;

import org.bukkit.EntityType;
import org.bukkit.entity.LivingEntity;
import org.bukkit.Location;
import org.Ver_zhzh.deathZombieV4.DeathZombieV4;
import org.Ver_zhzh.customZombie.UserCustomEntity.UserCustomEntity;
import java.util.*;
import java.util.logging.Logger;
import java.io.*;
import java.nio.file.*;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;

/**
 * 用户自定义怪物制作器 - 核心管理类
 * 负责管理技能模板、IDZ实体配置和用户交互
 *
 * <AUTHOR>
 * @version 1.0
 */
public class UserMaker {

    private final DeathZombieV4 plugin;
    private final Logger logger;
    private final IdzEntityManager idzEntityManager;
    private final Map<String, IdzEntityConfig> idzConfigs;
    private final Gson gson;
    private final File configDirectory;

    public UserMaker(DeathZombieV4 plugin) {
        this.plugin = plugin;
        this.logger = plugin.getLogger();
        this.skillTemplateManager = new SkillTemplateManager(plugin);
        this.idzEntityManager = new IdzEntityManager(plugin);
        this.idzConfigs = new HashMap<>();
        this.gson = new GsonBuilder().setPrettyPrinting().create();

        // 创建配置目录
        this.configDirectory = new File(plugin.getDataFolder(), "idz_configs");
        if (!configDirectory.exists()) {
            configDirectory.mkdirs();
        }

        // 加载现有配置
        loadAllIdzConfigs();

        logger.info("UserMaker系统初始化完成，已加载 " + idzConfigs.size() + " 个IDZ配置");
    }

    /**
     * 获取所有可用的技能模板
     * @return 技能模板列表
     */
    public List<SkillTemplate> getAllSkillTemplates() {
        return new ArrayList<>(idzEntityManager.getSkillExecutor().getAllSkillTemplates().values());
    }

    /**
     * 根据分类获取技能模板
     * @param category 技能分类
     * @return 技能模板列表
     */
    public List<SkillTemplate> getSkillTemplatesByCategory(SkillTemplate.SkillCategory category) {
        return getAllSkillTemplates().stream()
            .filter(template -> template.getCategory() == category)
            .collect(java.util.stream.Collectors.toList());
    }

    /**
     * 搜索技能模板
     * @param keyword 关键词
     * @return 匹配的技能模板列表
     */
    public List<SkillTemplate> searchSkillTemplates(String keyword) {
        String lowerKeyword = keyword.toLowerCase();
        return getAllSkillTemplates().stream()
            .filter(template ->
                template.getSkillName().toLowerCase().contains(lowerKeyword) ||
                template.getDescription().toLowerCase().contains(lowerKeyword) ||
                template.getSourceEntity().toLowerCase().contains(lowerKeyword))
            .collect(java.util.stream.Collectors.toList());
    }

    /**
     * 验证技能组合
     * @param skillIds 技能ID列表
     * @return 验证结果
     */
    public SkillCombinationResult validateSkillCombination(List<String> skillIds) {
        // 简化的验证逻辑
        List<String> errors = new ArrayList<>();
        List<String> warnings = new ArrayList<>();

        for (String skillId : skillIds) {
            if (!idzEntityManager.getSkillExecutor().hasSkill(skillId)) {
                errors.add("未知技能: " + skillId);
            }
        }

        boolean compatible = errors.isEmpty();
        return new SkillCombinationResult(compatible, errors, warnings);
    }

    /**
     * 创建新的IDZ实体配置
     * @param entityId 实体ID
     * @param createdBy 创建者
     * @return 新的IDZ配置
     */
    public IdzEntityConfig createIdzConfig(String entityId, String createdBy) {
        if (idzConfigs.containsKey(entityId)) {
            throw new IllegalArgumentException("IDZ实体 " + entityId + " 已存在");
        }

        IdzEntityConfig config = new IdzEntityConfig(entityId);
        config.setCreatedBy(createdBy);
        idzConfigs.put(entityId, config);

        logger.info("创建新的IDZ配置: " + entityId + " (创建者: " + createdBy + ")");
        return config;
    }

    /**
     * 获取IDZ实体配置
     * @param entityId 实体ID
     * @return IDZ配置，如果不存在则返回null
     */
    public IdzEntityConfig getIdzConfig(String entityId) {
        return idzConfigs.get(entityId);
    }

    /**
     * 获取所有IDZ实体配置
     * @return 所有IDZ配置的列表
     */
    public List<IdzEntityConfig> getAllIdzConfigs() {
        return new ArrayList<>(idzConfigs.values());
    }

    /**
     * 保存IDZ实体配置
     * @param config IDZ配置
     * @return 是否保存成功
     */
    public boolean saveIdzConfig(IdzEntityConfig config) {
        try {
            // 验证配置
            List<String> errors = config.validate();
            if (!errors.isEmpty()) {
                logger.warning("IDZ配置验证失败: " + String.join(", ", errors));
                return false;
            }

            // 保存到内存
            idzConfigs.put(config.getEntityId(), config);

            // 保存到文件
            File configFile = new File(configDirectory, config.getEntityId() + ".json");
            try (FileWriter writer = new FileWriter(configFile)) {
                gson.toJson(config, writer);
            }

            logger.info("IDZ配置已保存: " + config.getEntityId());
            return true;

        } catch (Exception e) {
            logger.severe("保存IDZ配置失败: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 删除IDZ实体配置
     * @param entityId 实体ID
     * @return 是否删除成功
     */
    public boolean deleteIdzConfig(String entityId) {
        try {
            // 从内存中移除
            IdzEntityConfig removed = idzConfigs.remove(entityId);
            if (removed == null) {
                return false;
            }

            // 删除配置文件
            File configFile = new File(configDirectory, entityId + ".json");
            if (configFile.exists()) {
                configFile.delete();
            }

            logger.info("IDZ配置已删除: " + entityId);
            return true;

        } catch (Exception e) {
            logger.severe("删除IDZ配置失败: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 生成IDZ实体
     * @param entityId 实体ID
     * @param location 生成位置
     * @return 生成的实体，如果失败则返回null
     */
    public LivingEntity spawnIdzEntity(String entityId, Location location) {
        IdzEntityConfig config = idzConfigs.get(entityId);
        if (config == null) {
            logger.warning("未找到IDZ配置: " + entityId);
            return null;
        }

        return idzEntityManager.spawnEntity(config, location);
    }

    /**
     * 加载所有IDZ配置
     */
    private void loadAllIdzConfigs() {
        if (!configDirectory.exists()) {
            return;
        }

        File[] configFiles = configDirectory.listFiles((dir, name) -> name.endsWith(".json"));
        if (configFiles == null) {
            return;
        }

        for (File configFile : configFiles) {
            try {
                String entityId = configFile.getName().replace(".json", "");
                try (FileReader reader = new FileReader(configFile)) {
                    IdzEntityConfig config = gson.fromJson(reader, IdzEntityConfig.class);
                    if (config != null) {
                        idzConfigs.put(entityId, config);
                    }
                }
            } catch (Exception e) {
                logger.warning("加载IDZ配置失败: " + configFile.getName() + " - " + e.getMessage());
            }
        }

        logger.info("已加载 " + idzConfigs.size() + " 个IDZ配置");
    }

    /**
     * 获取系统统计信息
     * @return 统计信息
     */
    public Map<String, Object> getSystemStatistics() {
        Map<String, Object> stats = idzEntityManager.getSkillExecutor().getStatistics();
        stats.put("idzConfigCount", idzConfigs.size());

        // 统计IDZ配置的实体类型分布
        Map<String, Integer> entityTypeStats = new HashMap<>();
        for (IdzEntityConfig config : idzConfigs.values()) {
            String typeName = config.getEntityType().name();
            entityTypeStats.put(typeName, entityTypeStats.getOrDefault(typeName, 0) + 1);
        }
        stats.put("idzEntityTypes", entityTypeStats);

        return stats;
    }

    /**
     * 获取推荐的技能组合
     * @param baseSkillId 基础技能ID
     * @return 推荐组合列表
     */
    public List<List<String>> getRecommendedCombinations(String baseSkillId) {
        // 简化的推荐逻辑
        List<List<String>> recommendations = new ArrayList<>();

        // 基于技能分类的推荐
        SkillTemplate baseTemplate = idzEntityManager.getSkillExecutor().getSkillTemplate(baseSkillId);
        if (baseTemplate != null) {
            List<String> sameCategorySkills = getSkillTemplatesByCategory(baseTemplate.getCategory())
                .stream()
                .map(SkillTemplate::getSkillId)
                .filter(id -> !id.equals(baseSkillId))
                .limit(3)
                .collect(java.util.stream.Collectors.toList());

            if (!sameCategorySkills.isEmpty()) {
                List<String> combination = new ArrayList<>();
                combination.add(baseSkillId);
                combination.addAll(sameCategorySkills);
                recommendations.add(combination);
            }
        }

        return recommendations;
    }

    /**
     * 导出IDZ配置
     * @param entityId 实体ID
     * @return JSON格式的配置字符串
     */
    public String exportIdzConfig(String entityId) {
        IdzEntityConfig config = idzConfigs.get(entityId);
        if (config == null) {
            return null;
        }
        return gson.toJson(config);
    }

    /**
     * 导入IDZ配置
     * @param configJson JSON格式的配置字符串
     * @return 是否导入成功
     */
    public boolean importIdzConfig(String configJson) {
        try {
            IdzEntityConfig config = gson.fromJson(configJson, IdzEntityConfig.class);
            if (config == null) {
                return false;
            }

            return saveIdzConfig(config);
        } catch (Exception e) {
            logger.warning("导入IDZ配置失败: " + e.getMessage());
            return false;
        }
    }

    /**
     * 获取技能模板管理器
     */
    public SkillTemplateManager getSkillTemplateManager() {
        return skillTemplateManager;
    }

    /**
     * 获取IDZ实体管理器
     */
    public IdzEntityManager getIdzEntityManager() {
        return idzEntityManager;
    }
}
