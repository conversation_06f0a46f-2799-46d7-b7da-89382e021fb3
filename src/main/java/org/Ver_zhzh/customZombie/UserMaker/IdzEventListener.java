package org.Ver_zhzh.customZombie.UserMaker;

import org.bukkit.entity.LivingEntity;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.entity.*;
import org.bukkit.event.player.PlayerInteractEntityEvent;
import org.bukkit.metadata.MetadataValue;
import org.bukkit.potion.PotionEffect;
import org.bukkit.potion.PotionEffectType;
import org.Ver_zhzh.deathZombieV4.DeathZombieV4;

import java.util.Random;
import java.util.logging.Logger;

/**
 * IDZ事件监听器 - 处理IDZ实体的各种游戏事件
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class IdzEventListener implements Listener {
    
    private final DeathZombieV4 plugin;
    private final Logger logger;
    private final UserMaker userMaker;
    private final Random random;
    
    public IdzEventListener(DeathZombieV4 plugin, UserMaker userMaker) {
        this.plugin = plugin;
        this.logger = plugin.getLogger();
        this.userMaker = userMaker;
        this.random = new Random();
    }
    
    /**
     * 处理IDZ实体攻击玩家事件
     */
    @EventHandler(priority = EventPriority.HIGH)
    public void onIdzEntityDamagePlayer(EntityDamageByEntityEvent event) {
        if (!(event.getDamager() instanceof LivingEntity)) {
            return;
        }
        
        LivingEntity damager = (LivingEntity) event.getDamager();
        
        // 检查是否为IDZ实体
        if (!isIdzEntity(damager)) {
            return;
        }
        
        if (!(event.getEntity() instanceof Player)) {
            return;
        }
        
        Player player = (Player) event.getEntity();
        
        // 处理剧毒攻击技能
        handlePoisonAttack(damager, player);
        
        // 处理生命汲取技能
        handleLifeDrain(damager, player, event.getDamage());
        
        // 处理其他被动攻击技能
        handlePassiveAttackSkills(damager, player);
    }
    
    /**
     * 处理IDZ实体受到伤害事件
     */
    @EventHandler(priority = EventPriority.HIGH)
    public void onIdzEntityDamaged(EntityDamageEvent event) {
        if (!(event.getEntity() instanceof LivingEntity)) {
            return;
        }
        
        LivingEntity entity = (LivingEntity) event.getEntity();
        
        // 检查是否为IDZ实体
        if (!isIdzEntity(entity)) {
            return;
        }
        
        // 处理护盾反射技能
        if (event instanceof EntityDamageByEntityEvent) {
            EntityDamageByEntityEvent damageEvent = (EntityDamageByEntityEvent) event;
            handleShieldReflect(entity, damageEvent);
        }
        
        // 处理狂暴模式技能
        handleBerserkMode(entity, event.getDamage());
    }
    
    /**
     * 处理IDZ实体死亡事件
     */
    @EventHandler(priority = EventPriority.HIGH)
    public void onIdzEntityDeath(EntityDeathEvent event) {
        LivingEntity entity = event.getEntity();
        
        // 检查是否为IDZ实体
        if (!isIdzEntity(entity)) {
            return;
        }
        
        // 处理死亡技能
        handleDeathSkills(entity);
        
        // 清理IDZ实体的技能任务
        if (userMaker.getIdzEntityManager() != null) {
            userMaker.getIdzEntityManager().stopEntitySkills(entity);
            userMaker.getIdzEntityManager().removeActiveEntity(entity.getUniqueId());
        }
        
        logger.info("IDZ实体已死亡并清理: " + getIdzEntityId(entity));
    }
    
    /**
     * 处理玩家与IDZ实体交互事件
     */
    @EventHandler(priority = EventPriority.NORMAL)
    public void onPlayerInteractIdzEntity(PlayerInteractEntityEvent event) {
        if (!(event.getRightClicked() instanceof LivingEntity)) {
            return;
        }
        
        LivingEntity entity = (LivingEntity) event.getRightClicked();
        
        // 检查是否为IDZ实体
        if (!isIdzEntity(entity)) {
            return;
        }
        
        Player player = event.getPlayer();
        
        // 如果玩家有管理权限，显示实体信息
        if (player.hasPermission("deathzombie.idz.info")) {
            String entityId = getIdzEntityId(entity);
            IdzEntityConfig config = userMaker.getIdzConfig(entityId);
            
            if (config != null) {
                player.sendMessage("§6=== IDZ实体信息 ===");
                player.sendMessage("§aID: §f" + config.getEntityId());
                player.sendMessage("§a名称: §f" + config.getDisplayName());
                player.sendMessage("§a类型: §f" + config.getEntityType());
                player.sendMessage("§a生命值: §f" + entity.getHealth() + "/" + entity.getMaxHealth());
                player.sendMessage("§a技能数量: §f" + config.getSkills().size());
                player.sendMessage("§a创建者: §f" + config.getCreatedBy());
            }
        }
    }
    
    /**
     * 处理剧毒攻击技能
     */
    private void handlePoisonAttack(LivingEntity damager, Player player) {
        if (!damager.hasMetadata("poisonAttack")) {
            return;
        }
        
        double chance = getMetadataValue(damager, "poisonChance", 1.0);
        if (random.nextDouble() > chance) {
            return;
        }
        
        int level = getMetadataValue(damager, "poisonLevel", 1);
        int duration = getMetadataValue(damager, "poisonDuration", 60);
        
        PotionEffect poison = new PotionEffect(PotionEffectType.POISON, duration, level - 1);
        player.addPotionEffect(poison);
        
        player.sendMessage("§c你被剧毒攻击了！");
    }
    
    /**
     * 处理生命汲取技能
     */
    private void handleLifeDrain(LivingEntity damager, Player player, double damage) {
        if (!damager.hasMetadata("lifeDrain")) {
            return;
        }
        
        double chance = getMetadataValue(damager, "drainChance", 0.5);
        if (random.nextDouble() > chance) {
            return;
        }
        
        double drainAmount = getMetadataValue(damager, "drainAmount", 2.0);
        
        // 汲取生命值
        double newHealth = Math.min(damager.getMaxHealth(), damager.getHealth() + drainAmount);
        damager.setHealth(newHealth);
        
        player.sendMessage("§c你的生命力被汲取了！");
    }
    
    /**
     * 处理护盾反射技能
     */
    private void handleShieldReflect(LivingEntity entity, EntityDamageByEntityEvent event) {
        if (!entity.hasMetadata("shieldReflect")) {
            return;
        }
        
        double chance = getMetadataValue(entity, "reflectChance", 0.3);
        if (random.nextDouble() > chance) {
            return;
        }
        
        double multiplier = getMetadataValue(entity, "reflectMultiplier", 0.5);
        double reflectDamage = event.getDamage() * multiplier;
        
        if (event.getDamager() instanceof LivingEntity) {
            LivingEntity attacker = (LivingEntity) event.getDamager();
            attacker.damage(reflectDamage);
            
            if (attacker instanceof Player) {
                ((Player) attacker).sendMessage("§c你的攻击被反射了！");
            }
        }
    }
    
    /**
     * 处理狂暴模式技能
     */
    private void handleBerserkMode(LivingEntity entity, double damage) {
        if (!entity.hasMetadata("berserkMode")) {
            return;
        }
        
        double healthPercent = entity.getHealth() / entity.getMaxHealth();
        double threshold = getMetadataValue(entity, "berserkThreshold", 0.3);
        
        if (healthPercent > threshold) {
            return;
        }
        
        // 检查是否已经在狂暴状态
        if (entity.hasMetadata("berserkActive")) {
            return;
        }
        
        // 激活狂暴模式
        entity.setMetadata("berserkActive", new org.bukkit.metadata.FixedMetadataValue(plugin, true));
        
        int speedBoost = getMetadataValue(entity, "speedBoost", 2);
        PotionEffect speed = new PotionEffect(PotionEffectType.SPEED, Integer.MAX_VALUE, speedBoost - 1);
        entity.addPotionEffect(speed);
        
        // 播放狂暴效果
        entity.getWorld().spawnParticle(org.bukkit.Particle.ANGRY_VILLAGER, 
            entity.getLocation().add(0, 1, 0), 20, 1, 1, 1, 0.1);
        entity.getWorld().playSound(entity.getLocation(), 
            org.bukkit.Sound.ENTITY_ZOMBIE_ANGRY, 1.0f, 0.8f);
    }
    
    /**
     * 处理死亡技能
     */
    private void handleDeathSkills(LivingEntity entity) {
        String entityId = getIdzEntityId(entity);
        IdzEntityConfig config = userMaker.getIdzConfig(entityId);
        
        if (config == null) {
            return;
        }
        
        // 处理分裂召唤技能
        for (IdzEntityConfig.SkillConfig skill : config.getSkills()) {
            if (!skill.isEnabled()) {
                continue;
            }
            
            if ("split_summon".equals(skill.getSkillId())) {
                handleSplitSummon(entity, skill);
            } else if ("explosion_attack".equals(skill.getSkillId())) {
                handleExplosionAttack(entity, skill);
            } else if ("magma_cube_split".equals(skill.getSkillId())) {
                handleMagmaCubeSplit(entity, skill);
            }
        }
    }
    
    /**
     * 处理分裂召唤
     */
    private void handleSplitSummon(LivingEntity entity, IdzEntityConfig.SkillConfig skill) {
        int count = skill.getParameter("split_count", 3);
        double health = skill.getParameter("split_health", 10.0);
        
        for (int i = 0; i < count; i++) {
            // TODO: 实现分裂召唤逻辑
            // 这里需要生成小型版本的实体
        }
        
        // 播放分裂效果
        entity.getWorld().spawnParticle(org.bukkit.Particle.EXPLOSION_LARGE, 
            entity.getLocation(), 5, 1, 1, 1, 0.1);
    }
    
    /**
     * 处理爆炸攻击
     */
    private void handleExplosionAttack(LivingEntity entity, IdzEntityConfig.SkillConfig skill) {
        float power = skill.getParameter("explosion_power", 2.0f);
        
        entity.getWorld().createExplosion(entity.getLocation(), power, false, false);
    }
    
    /**
     * 处理岩浆怪分裂
     */
    private void handleMagmaCubeSplit(LivingEntity entity, IdzEntityConfig.SkillConfig skill) {
        int count = skill.getParameter("split_count", 4);
        double fireDamage = skill.getParameter("fire_damage", 4.0);
        
        // 对周围玩家造成火焰伤害
        entity.getNearbyEntities(3, 3, 3).stream()
            .filter(e -> e instanceof Player)
            .forEach(e -> {
                Player player = (Player) e;
                player.damage(fireDamage);
                player.setFireTicks(100);
            });
        
        // 播放岩浆分裂效果
        entity.getWorld().spawnParticle(org.bukkit.Particle.LAVA, 
            entity.getLocation(), 20, 2, 1, 2, 0.1);
    }
    
    /**
     * 处理其他被动攻击技能
     */
    private void handlePassiveAttackSkills(LivingEntity damager, Player player) {
        // 可以在这里添加更多被动攻击技能的处理
    }
    
    /**
     * 检查是否为IDZ实体
     */
    private boolean isIdzEntity(LivingEntity entity) {
        return entity.hasMetadata("idzEntity");
    }
    
    /**
     * 获取IDZ实体ID
     */
    private String getIdzEntityId(LivingEntity entity) {
        if (entity.hasMetadata("idzEntityId")) {
            MetadataValue value = entity.getMetadata("idzEntityId").get(0);
            return value.asString();
        }
        return null;
    }
    
    /**
     * 获取元数据值
     */
    @SuppressWarnings("unchecked")
    private <T> T getMetadataValue(LivingEntity entity, String key, T defaultValue) {
        if (entity.hasMetadata(key)) {
            MetadataValue value = entity.getMetadata(key).get(0);
            try {
                return (T) value.value();
            } catch (ClassCastException e) {
                return defaultValue;
            }
        }
        return defaultValue;
    }
}
