package org.Ver_zhzh.customZombie.UserMaker;

import java.util.*;

/**
 * 技能组合结果类 - 用于验证技能组合的兼容性
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class SkillCombinationResult {
    
    private boolean valid;                          // 组合是否有效
    private List<ConflictInfo> conflicts;           // 冲突信息列表
    private List<String> warnings;                  // 警告信息列表
    private List<String> suggestions;               // 建议信息列表
    private Map<String, Object> metadata;           // 额外元数据
    
    /**
     * 冲突信息类
     */
    public static class ConflictInfo {
        private String skill1Id;        // 冲突技能1的ID
        private String skill2Id;        // 冲突技能2的ID
        private String reason;          // 冲突原因
        private ConflictType type;      // 冲突类型
        
        public enum ConflictType {
            INCOMPATIBLE("不兼容"),
            RESOURCE_CONFLICT("资源冲突"),
            PARAMETER_CONFLICT("参数冲突"),
            PERFORMANCE_ISSUE("性能问题");
            
            private final String displayName;
            
            ConflictType(String displayName) {
                this.displayName = displayName;
            }
            
            public String getDisplayName() {
                return displayName;
            }
        }
        
        public ConflictInfo(String skill1Id, String skill2Id, String reason) {
            this.skill1Id = skill1Id;
            this.skill2Id = skill2Id;
            this.reason = reason;
            this.type = ConflictType.INCOMPATIBLE;
        }
        
        public ConflictInfo(String skill1Id, String skill2Id, String reason, ConflictType type) {
            this.skill1Id = skill1Id;
            this.skill2Id = skill2Id;
            this.reason = reason;
            this.type = type;
        }
        
        // Getter方法
        public String getSkill1Id() { return skill1Id; }
        public String getSkill2Id() { return skill2Id; }
        public String getReason() { return reason; }
        public ConflictType getType() { return type; }
        
        @Override
        public String toString() {
            return String.format("[%s] %s 与 %s: %s", 
                type.getDisplayName(), skill1Id, skill2Id, reason);
        }
    }
    
    /**
     * 构造函数
     */
    public SkillCombinationResult() {
        this.valid = true;
        this.conflicts = new ArrayList<>();
        this.warnings = new ArrayList<>();
        this.suggestions = new ArrayList<>();
        this.metadata = new HashMap<>();
    }
    
    // Getter和Setter方法
    public boolean isValid() {
        return valid;
    }
    
    public void setValid(boolean valid) {
        this.valid = valid;
    }
    
    public List<ConflictInfo> getConflicts() {
        return conflicts;
    }
    
    public List<String> getWarnings() {
        return warnings;
    }
    
    public List<String> getSuggestions() {
        return suggestions;
    }
    
    public Map<String, Object> getMetadata() {
        return metadata;
    }
    
    /**
     * 添加冲突信息
     */
    public void addConflict(String skill1Id, String skill2Id, String reason) {
        conflicts.add(new ConflictInfo(skill1Id, skill2Id, reason));
        this.valid = false;
    }
    
    /**
     * 添加冲突信息（带类型）
     */
    public void addConflict(String skill1Id, String skill2Id, String reason, ConflictInfo.ConflictType type) {
        conflicts.add(new ConflictInfo(skill1Id, skill2Id, reason, type));
        if (type == ConflictInfo.ConflictType.INCOMPATIBLE) {
            this.valid = false;
        }
    }
    
    /**
     * 添加警告信息
     */
    public void addWarning(String warning) {
        warnings.add(warning);
    }
    
    /**
     * 添加建议信息
     */
    public void addSuggestion(String suggestion) {
        suggestions.add(suggestion);
    }
    
    /**
     * 添加元数据
     */
    public void addMetadata(String key, Object value) {
        metadata.put(key, value);
    }
    
    /**
     * 检查是否有冲突
     */
    public boolean hasConflicts() {
        return !conflicts.isEmpty();
    }
    
    /**
     * 检查是否有警告
     */
    public boolean hasWarnings() {
        return !warnings.isEmpty();
    }
    
    /**
     * 检查是否有建议
     */
    public boolean hasSuggestions() {
        return !suggestions.isEmpty();
    }
    
    /**
     * 获取冲突数量
     */
    public int getConflictCount() {
        return conflicts.size();
    }
    
    /**
     * 获取特定类型的冲突
     */
    public List<ConflictInfo> getConflictsByType(ConflictInfo.ConflictType type) {
        return conflicts.stream()
            .filter(conflict -> conflict.getType() == type)
            .collect(ArrayList::new, ArrayList::add, ArrayList::addAll);
    }
    
    /**
     * 获取涉及特定技能的冲突
     */
    public List<ConflictInfo> getConflictsForSkill(String skillId) {
        return conflicts.stream()
            .filter(conflict -> conflict.getSkill1Id().equals(skillId) || 
                              conflict.getSkill2Id().equals(skillId))
            .collect(ArrayList::new, ArrayList::add, ArrayList::addAll);
    }
    
    /**
     * 生成详细报告
     */
    public String generateDetailedReport() {
        StringBuilder report = new StringBuilder();
        
        report.append("=== 技能组合验证报告 ===\n");
        report.append("验证结果: ").append(valid ? "✅ 通过" : "❌ 失败").append("\n\n");
        
        if (hasConflicts()) {
            report.append("🚫 冲突信息 (").append(conflicts.size()).append("个):\n");
            for (int i = 0; i < conflicts.size(); i++) {
                report.append("  ").append(i + 1).append(". ").append(conflicts.get(i)).append("\n");
            }
            report.append("\n");
        }
        
        if (hasWarnings()) {
            report.append("⚠️ 警告信息 (").append(warnings.size()).append("个):\n");
            for (int i = 0; i < warnings.size(); i++) {
                report.append("  ").append(i + 1).append(". ").append(warnings.get(i)).append("\n");
            }
            report.append("\n");
        }
        
        if (hasSuggestions()) {
            report.append("💡 建议信息 (").append(suggestions.size()).append("个):\n");
            for (int i = 0; i < suggestions.size(); i++) {
                report.append("  ").append(i + 1).append(". ").append(suggestions.get(i)).append("\n");
            }
            report.append("\n");
        }
        
        if (!metadata.isEmpty()) {
            report.append("📊 额外信息:\n");
            metadata.forEach((key, value) -> 
                report.append("  ").append(key).append(": ").append(value).append("\n"));
        }
        
        return report.toString();
    }
    
    /**
     * 生成简要报告
     */
    public String generateSummaryReport() {
        if (valid && !hasWarnings()) {
            return "✅ 技能组合验证通过，无冲突或警告";
        }
        
        StringBuilder summary = new StringBuilder();
        if (!valid) {
            summary.append("❌ 验证失败: ").append(conflicts.size()).append("个冲突");
        } else {
            summary.append("✅ 验证通过");
        }
        
        if (hasWarnings()) {
            summary.append(", ").append(warnings.size()).append("个警告");
        }
        
        if (hasSuggestions()) {
            summary.append(", ").append(suggestions.size()).append("个建议");
        }
        
        return summary.toString();
    }
    
    /**
     * 转换为JSON格式的Map
     */
    public Map<String, Object> toMap() {
        Map<String, Object> result = new HashMap<>();
        result.put("valid", valid);
        result.put("conflictCount", conflicts.size());
        result.put("warningCount", warnings.size());
        result.put("suggestionCount", suggestions.size());
        
        // 转换冲突信息
        List<Map<String, Object>> conflictMaps = new ArrayList<>();
        for (ConflictInfo conflict : conflicts) {
            Map<String, Object> conflictMap = new HashMap<>();
            conflictMap.put("skill1Id", conflict.getSkill1Id());
            conflictMap.put("skill2Id", conflict.getSkill2Id());
            conflictMap.put("reason", conflict.getReason());
            conflictMap.put("type", conflict.getType().name());
            conflictMap.put("typeDisplayName", conflict.getType().getDisplayName());
            conflictMaps.add(conflictMap);
        }
        result.put("conflicts", conflictMaps);
        
        result.put("warnings", new ArrayList<>(warnings));
        result.put("suggestions", new ArrayList<>(suggestions));
        result.put("metadata", new HashMap<>(metadata));
        
        return result;
    }
    
    @Override
    public String toString() {
        return generateSummaryReport();
    }
}
