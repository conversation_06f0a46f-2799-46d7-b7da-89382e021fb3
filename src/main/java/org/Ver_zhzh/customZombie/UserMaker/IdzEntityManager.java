package org.Ver_zhzh.customZombie.UserMaker;

import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.enchantments.Enchantment;
import org.bukkit.entity.LivingEntity;
import org.bukkit.inventory.EntityEquipment;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.metadata.FixedMetadataValue;
import org.bukkit.potion.PotionEffect;
import org.bukkit.attribute.Attribute;
import org.Ver_zhzh.deathZombieV4.DeathZombieV4;
import org.Ver_zhzh.customZombie.UserMaker.skill.SkillExecutor;

import java.util.*;
import java.util.logging.Logger;

/**
 * IDZ实体管理器 - 负责IDZ实体的生成、配置和管理
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class IdzEntityManager {
    
    private final DeathZombieV4 plugin;
    private final Logger logger;
    private final SkillExecutor skillExecutor;
    private final Map<UUID, IdzEntityConfig> activeEntities;
    
    public IdzEntityManager(DeathZombieV4 plugin) {
        this.plugin = plugin;
        this.logger = plugin.getLogger();
        this.skillExecutor = new SkillExecutor(plugin);
        this.activeEntities = new HashMap<>();

        logger.info("IDZ实体管理器初始化完成");
    }
    
    /**
     * 生成IDZ实体
     * @param config IDZ实体配置
     * @param location 生成位置
     * @return 生成的实体，如果失败则返回null
     */
    public LivingEntity spawnEntity(IdzEntityConfig config, Location location) {
        try {
            // 生成基础实体
            LivingEntity entity = (LivingEntity) location.getWorld().spawnEntity(location, config.getEntityType());
            
            // 配置基础属性
            configureBasicAttributes(entity, config);
            
            // 配置装备
            configureEquipment(entity, config);
            
            // 配置药水效果
            configurePotionEffects(entity, config);
            
            // 配置外观
            configureAppearance(entity, config);
            
            // 设置元数据
            setEntityMetadata(entity, config);
            
            // 启用技能
            enableSkills(entity, config);
            
            // 记录活跃实体
            activeEntities.put(entity.getUniqueId(), config);
            
            logger.info("成功生成IDZ实体: " + config.getEntityId() + " (" + config.getDisplayName() + ")");
            return entity;
            
        } catch (Exception e) {
            logger.severe("生成IDZ实体失败: " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }
    
    /**
     * 配置基础属性
     */
    private void configureBasicAttributes(LivingEntity entity, IdzEntityConfig config) {
        // 设置生命值
        if (entity.getAttribute(Attribute.GENERIC_MAX_HEALTH) != null) {
            entity.getAttribute(Attribute.GENERIC_MAX_HEALTH).setBaseValue(config.getHealth());
            entity.setHealth(config.getHealth());
        }
        
        // 设置攻击伤害
        if (entity.getAttribute(Attribute.GENERIC_ATTACK_DAMAGE) != null) {
            entity.getAttribute(Attribute.GENERIC_ATTACK_DAMAGE).setBaseValue(config.getDamage());
        }
        
        // 设置移动速度
        if (entity.getAttribute(Attribute.GENERIC_MOVEMENT_SPEED) != null) {
            double baseSpeed = entity.getAttribute(Attribute.GENERIC_MOVEMENT_SPEED).getBaseValue();
            entity.getAttribute(Attribute.GENERIC_MOVEMENT_SPEED).setBaseValue(baseSpeed * config.getSpeed());
        }
        
        // 设置名称
        entity.setCustomName(config.getDisplayName());
        entity.setCustomNameVisible(config.getAppearance().isCustomNameVisible());
        
        // 设置其他属性
        entity.setCanPickupItems(config.canPickupItems());
        entity.setPersistent(!config.canDespawn());
        entity.setRemoveWhenFarAway(config.canDespawn());
        
        // 设置AI
        if (!config.hasAI()) {
            entity.setAI(false);
        }
    }
    
    /**
     * 配置装备
     */
    private void configureEquipment(LivingEntity entity, IdzEntityConfig config) {
        EntityEquipment equipment = entity.getEquipment();
        if (equipment == null) {
            return;
        }
        
        IdzEntityConfig.EquipmentConfig equipConfig = config.getEquipment();
        
        // 配置主手武器
        ItemStack mainHand = createItemStack(equipConfig.getMainHand());
        if (mainHand != null) {
            equipment.setItemInMainHand(mainHand);
            equipment.setItemInMainHandDropChance((float) equipConfig.getMainHand().getDropChance());
        }
        
        // 配置副手物品
        ItemStack offHand = createItemStack(equipConfig.getOffHand());
        if (offHand != null) {
            equipment.setItemInOffHand(offHand);
            equipment.setItemInOffHandDropChance((float) equipConfig.getOffHand().getDropChance());
        }
        
        // 配置护甲
        ItemStack helmet = createItemStack(equipConfig.getHelmet());
        if (helmet != null) {
            equipment.setHelmet(helmet);
            equipment.setHelmetDropChance((float) equipConfig.getHelmet().getDropChance());
        }
        
        ItemStack chestplate = createItemStack(equipConfig.getChestplate());
        if (chestplate != null) {
            equipment.setChestplate(chestplate);
            equipment.setChestplateDropChance((float) equipConfig.getChestplate().getDropChance());
        }
        
        ItemStack leggings = createItemStack(equipConfig.getLeggings());
        if (leggings != null) {
            equipment.setLeggings(leggings);
            equipment.setLeggingsDropChance((float) equipConfig.getLeggings().getDropChance());
        }
        
        ItemStack boots = createItemStack(equipConfig.getBoots());
        if (boots != null) {
            equipment.setBoots(boots);
            equipment.setBootsDropChance((float) equipConfig.getBoots().getDropChance());
        }
    }
    
    /**
     * 创建物品堆
     */
    private ItemStack createItemStack(IdzEntityConfig.ItemConfig itemConfig) {
        if (itemConfig.getMaterial() == Material.AIR) {
            return null;
        }
        
        ItemStack item = new ItemStack(itemConfig.getMaterial());
        ItemMeta meta = item.getItemMeta();
        
        if (meta != null) {
            // 设置显示名称
            if (!itemConfig.getDisplayName().isEmpty()) {
                meta.setDisplayName(itemConfig.getDisplayName());
            }
            
            // 设置描述
            if (!itemConfig.getLore().isEmpty()) {
                meta.setLore(itemConfig.getLore());
            }
            
            // 设置无法破坏
            meta.setUnbreakable(itemConfig.isUnbreakable());
            
            item.setItemMeta(meta);
        }
        
        // 添加附魔
        for (Map.Entry<Enchantment, Integer> entry : itemConfig.getEnchantments().entrySet()) {
            item.addUnsafeEnchantment(entry.getKey(), entry.getValue());
        }
        
        return item;
    }
    
    /**
     * 配置药水效果
     */
    private void configurePotionEffects(LivingEntity entity, IdzEntityConfig config) {
        for (IdzEntityConfig.PotionEffectConfig effectConfig : config.getPotionEffects()) {
            PotionEffect effect = new PotionEffect(
                effectConfig.getEffectType(),
                effectConfig.getDuration(),
                effectConfig.getAmplifier(),
                effectConfig.isAmbient(),
                effectConfig.hasParticles(),
                effectConfig.hasIcon()
            );
            entity.addPotionEffect(effect);
        }
    }
    
    /**
     * 配置外观
     */
    private void configureAppearance(LivingEntity entity, IdzEntityConfig config) {
        IdzEntityConfig.AppearanceConfig appearance = config.getAppearance();
        
        // 设置发光效果
        entity.setGlowing(appearance.isGlowing());
        
        // 设置隐身效果
        entity.setInvisible(appearance.isInvisible());
        
        // 设置静音效果
        entity.setSilent(appearance.isSilent());
        
        // TODO: 设置缩放比例（需要特殊处理，可能需要使用NBT或其他方法）
    }
    
    /**
     * 设置实体元数据
     */
    private void setEntityMetadata(LivingEntity entity, IdzEntityConfig config) {
        // 设置IDZ标记
        entity.setMetadata("idzEntity", new FixedMetadataValue(plugin, true));
        entity.setMetadata("idzEntityId", new FixedMetadataValue(plugin, config.getEntityId()));
        entity.setMetadata("idzDisplayName", new FixedMetadataValue(plugin, config.getDisplayName()));
        entity.setMetadata("idzCreatedBy", new FixedMetadataValue(plugin, config.getCreatedBy()));
        entity.setMetadata("idzCreatedTime", new FixedMetadataValue(plugin, config.getCreatedTime()));
        
        // 设置游戏实体标记（兼容现有系统）
        entity.setMetadata("gameEntity", new FixedMetadataValue(plugin, true));
        entity.setMetadata("userCustomEntity", new FixedMetadataValue(plugin, true));
        
        // 设置技能相关元数据
        List<String> skillIds = new ArrayList<>();
        for (IdzEntityConfig.SkillConfig skillConfig : config.getSkills()) {
            if (skillConfig.isEnabled()) {
                skillIds.add(skillConfig.getSkillId());
            }
        }
        entity.setMetadata("idzSkills", new FixedMetadataValue(plugin, skillIds));
    }
    
    /**
     * 启用技能
     */
    private void enableSkills(LivingEntity entity, IdzEntityConfig config) {
        skillExecutor.activateAllSkills(entity, config.getSkills());
    }
    
    /**
     * 获取活跃实体配置
     */
    public IdzEntityConfig getEntityConfig(UUID entityId) {
        return activeEntities.get(entityId);
    }
    
    /**
     * 移除活跃实体记录
     */
    public void removeActiveEntity(UUID entityId) {
        activeEntities.remove(entityId);
    }
    
    /**
     * 获取所有活跃实体
     */
    public Map<UUID, IdzEntityConfig> getActiveEntities() {
        return new HashMap<>(activeEntities);
    }
    
    /**
     * 清理所有活跃实体记录
     */
    public void clearActiveEntities() {
        activeEntities.clear();
    }
    
    /**
     * 获取技能执行器
     */
    public SkillExecutor getSkillExecutor() {
        return skillExecutor;
    }

    /**
     * 停用实体的所有技能
     */
    public void stopEntitySkills(LivingEntity entity) {
        skillExecutor.deactivateAllSkills(entity);
    }
}
