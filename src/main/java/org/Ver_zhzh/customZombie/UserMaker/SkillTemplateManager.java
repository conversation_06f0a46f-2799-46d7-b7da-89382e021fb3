package org.Ver_zhzh.customZombie.UserMaker;

import org.bukkit.Particle;
import org.bukkit.Sound;
import org.bukkit.potion.PotionEffectType;
import org.Ver_zhzh.deathZombieV4.DeathZombieV4;

import java.util.*;
import java.util.logging.Logger;
import java.util.stream.Collectors;

/**
 * 技能模板管理器 - 管理所有技能模板的核心类
 * 负责技能模板的注册、查询、验证和组合检查
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class SkillTemplateManager {
    
    private final DeathZombieV4 plugin;
    private final Logger logger;
    private final Map<String, SkillTemplate> skillTemplates;
    private final Map<SkillTemplate.SkillCategory, List<SkillTemplate>> skillsByCategory;
    private final Map<String, List<SkillTemplate>> skillsBySource;
    
    public SkillTemplateManager(DeathZombieV4 plugin) {
        this.plugin = plugin;
        this.logger = plugin.getLogger();
        this.skillTemplates = new HashMap<>();
        this.skillsByCategory = new HashMap<>();
        this.skillsBySource = new HashMap<>();
        
        // 初始化分类映射
        for (SkillTemplate.SkillCategory category : SkillTemplate.SkillCategory.values()) {
            skillsByCategory.put(category, new ArrayList<>());
        }
        
        // 注册所有技能模板
        registerAllSkillTemplates();
        
        logger.info("技能模板管理器初始化完成，共注册 " + skillTemplates.size() + " 个技能模板");
    }
    
    /**
     * 注册所有技能模板
     */
    private void registerAllSkillTemplates() {
        // 注册ID系列技能
        registerIdSeriesSkills();
        
        // 注册IDC系列技能
        registerIdcSeriesSkills();

        // 注册更多IDC系列技能
        registerMoreIdcSeriesSkills();
        
        logger.info("所有技能模板注册完成");
    }
    
    /**
     * 注册ID系列技能模板
     */
    private void registerIdSeriesSkills() {
        // ID1 - 基础僵尸（无特殊技能，仅作为基础模板）
        registerSkill(new SkillTemplate("basic_zombie", "基础僵尸",
                "标准的僵尸实体，无特殊技能",
                SkillTemplate.SkillCategory.ATTACK, SkillTemplate.SkillType.PASSIVE, "ID1")
            .setEffectConfig(new SkillTemplate.EffectConfig()));

        // ID2 - 基础僵尸变种（无特殊技能）
        registerSkill(new SkillTemplate("basic_zombie_variant", "基础僵尸变种",
                "基础僵尸的变种，无特殊技能",
                SkillTemplate.SkillCategory.ATTACK, SkillTemplate.SkillType.PASSIVE, "ID2")
            .setEffectConfig(new SkillTemplate.EffectConfig()));

        // ID3 - 强化僵尸（无特殊技能，但属性更强）
        registerSkill(new SkillTemplate("enhanced_zombie", "强化僵尸",
                "属性增强的僵尸，无特殊技能",
                SkillTemplate.SkillCategory.ATTACK, SkillTemplate.SkillType.PASSIVE, "ID3")
            .setEffectConfig(new SkillTemplate.EffectConfig()));

        // ID4 - 精英僵尸（无特殊技能，但装备更好）
        registerSkill(new SkillTemplate("elite_zombie", "精英僵尸",
                "装备精良的僵尸，无特殊技能",
                SkillTemplate.SkillCategory.ATTACK, SkillTemplate.SkillType.PASSIVE, "ID4")
            .setEffectConfig(new SkillTemplate.EffectConfig()));

        // ID5 - 剧毒攻击
        registerSkill(new SkillTemplate("poison_attack", "剧毒攻击",
                "攻击玩家时有概率造成中毒效果",
                SkillTemplate.SkillCategory.ATTACK, SkillTemplate.SkillType.PASSIVE, "ID5")
            .addParameter("poison_level", new SkillTemplate.ParameterConfig(
                "poison_level", "中毒等级", "中毒效果的等级",
                Integer.class, 1, "级").setMinValue(1).setMaxValue(10))
            .addParameter("poison_duration", new SkillTemplate.ParameterConfig(
                "poison_duration", "持续时间", "中毒效果持续时间",
                Integer.class, 60, "tick").setMinValue(20).setMaxValue(1200))
            .addParameter("poison_chance", new SkillTemplate.ParameterConfig(
                "poison_chance", "触发概率", "攻击时触发中毒的概率",
                Double.class, 1.0, "%").setMinValue(0.1).setMaxValue(1.0))
            .setEffectConfig(new SkillTemplate.EffectConfig()
                .addParticle(Particle.VILLAGER_ANGRY)
                .addPotionEffect(PotionEffectType.POISON)));
        
        // ID6 - 召唤仆从
        registerSkill(new SkillTemplate("summon_minion", "召唤仆从", 
                "定时召唤普通僵尸作为仆从", 
                SkillTemplate.SkillCategory.SUMMON, SkillTemplate.SkillType.ACTIVE_TIMED, "ID6")
            .addParameter("summon_interval", new SkillTemplate.ParameterConfig(
                "summon_interval", "召唤间隔", "召唤仆从的时间间隔", 
                Integer.class, 6000, "tick").setMinValue(1200).setMaxValue(12000))
            .addParameter("max_minions", new SkillTemplate.ParameterConfig(
                "max_minions", "最大数量", "同时存在的仆从最大数量", 
                Integer.class, 3, "个").setMinValue(1).setMaxValue(10))
            .addParameter("minion_type", new SkillTemplate.ParameterConfig(
                "minion_type", "仆从类型", "召唤的仆从类型", 
                String.class, "id1", ""))
            .setEffectConfig(new SkillTemplate.EffectConfig()
                .addParticle(Particle.PORTAL)
                .addSound(Sound.ENTITY_ENDERMAN_TELEPORT)));
        
        // ID7 - 远程射击
        registerSkill(new SkillTemplate("ranged_attack", "远程射击", 
                "向玩家发射箭矢进行远程攻击", 
                SkillTemplate.SkillCategory.ATTACK, SkillTemplate.SkillType.ACTIVE_TIMED, "ID7")
            .addParameter("attack_interval", new SkillTemplate.ParameterConfig(
                "attack_interval", "攻击间隔", "发射箭矢的时间间隔", 
                Integer.class, 40, "tick").setMinValue(10).setMaxValue(200))
            .addParameter("attack_range", new SkillTemplate.ParameterConfig(
                "attack_range", "攻击范围", "射击的最大距离", 
                Double.class, 20.0, "格").setMinValue(5.0).setMaxValue(50.0))
            .addParameter("arrow_damage", new SkillTemplate.ParameterConfig(
                "arrow_damage", "箭矢伤害", "每支箭矢造成的伤害", 
                Double.class, 4.0, "点").setMinValue(1.0).setMaxValue(20.0))
            .setEffectConfig(new SkillTemplate.EffectConfig()
                .addSound(Sound.ENTITY_SKELETON_SHOOT)));
        
        // ID10 - 法师召唤
        registerSkill(new SkillTemplate("mage_summon", "法师召唤", 
                "召唤双生僵尸作为法师仆从", 
                SkillTemplate.SkillCategory.SUMMON, SkillTemplate.SkillType.ACTIVE_TIMED, "ID10")
            .addParameter("summon_interval", new SkillTemplate.ParameterConfig(
                "summon_interval", "召唤间隔", "召唤仆从的时间间隔", 
                Integer.class, 200, "tick").setMinValue(100).setMaxValue(600))
            .addParameter("summon_count", new SkillTemplate.ParameterConfig(
                "summon_count", "召唤数量", "每次召唤的仆从数量", 
                Integer.class, 2, "个").setMinValue(1).setMaxValue(5))
            .addParameter("max_minions", new SkillTemplate.ParameterConfig(
                "max_minions", "最大数量", "同时存在的仆从最大数量", 
                Integer.class, 6, "个").setMinValue(2).setMaxValue(15))
            .setEffectConfig(new SkillTemplate.EffectConfig()
                .addParticle(Particle.ENCHANTMENT_TABLE)
                .addSound(Sound.ENTITY_EVOKER_CAST_SPELL)));
        
        // ID13 - 电击攻击
        registerSkill(new SkillTemplate("electric_attack", "电击攻击", 
                "对范围内玩家造成电击伤害", 
                SkillTemplate.SkillCategory.ATTACK, SkillTemplate.SkillType.ACTIVE_TIMED, "ID13")
            .addParameter("attack_interval", new SkillTemplate.ParameterConfig(
                "attack_interval", "攻击间隔", "电击攻击的时间间隔", 
                Integer.class, 60, "tick").setMinValue(20).setMaxValue(200))
            .addParameter("attack_range", new SkillTemplate.ParameterConfig(
                "attack_range", "攻击范围", "电击的影响范围", 
                Double.class, 8.0, "格").setMinValue(3.0).setMaxValue(20.0))
            .addParameter("electric_damage", new SkillTemplate.ParameterConfig(
                "electric_damage", "电击伤害", "每次电击造成的伤害", 
                Double.class, 6.0, "点").setMinValue(1.0).setMaxValue(15.0))
            .setEffectConfig(new SkillTemplate.EffectConfig()
                .addParticle(Particle.ENCHANTED_HIT)
                .addSound(Sound.ENTITY_LIGHTNING_BOLT_THUNDER)));
        
        // ID14 - 冰冻攻击
        registerSkill(new SkillTemplate("freeze_attack", "冰冻攻击", 
                "对玩家造成缓慢效果的冰冻攻击", 
                SkillTemplate.SkillCategory.DEBUFF, SkillTemplate.SkillType.ACTIVE_TIMED, "ID14")
            .addParameter("attack_interval", new SkillTemplate.ParameterConfig(
                "attack_interval", "攻击间隔", "冰冻攻击的时间间隔", 
                Integer.class, 80, "tick").setMinValue(40).setMaxValue(300))
            .addParameter("freeze_duration", new SkillTemplate.ParameterConfig(
                "freeze_duration", "冰冻时长", "缓慢效果持续时间", 
                Integer.class, 100, "tick").setMinValue(40).setMaxValue(400))
            .addParameter("slowness_level", new SkillTemplate.ParameterConfig(
                "slowness_level", "缓慢等级", "缓慢效果的等级", 
                Integer.class, 2, "级").setMinValue(1).setMaxValue(5))
            .setEffectConfig(new SkillTemplate.EffectConfig()
                .addParticle(Particle.SNOWFLAKE)
                .addParticle(Particle.CLOUD)
                .addSound(Sound.BLOCK_GLASS_BREAK)
                .addPotionEffect(PotionEffectType.SLOW)));
        
        // ID8 - 跳跃攻击
        registerSkill(new SkillTemplate("jump_attack", "跳跃攻击",
                "跳向玩家进行攻击",
                SkillTemplate.SkillCategory.MOVEMENT, SkillTemplate.SkillType.ACTIVE_TIMED, "ID8")
            .addParameter("jump_interval", new SkillTemplate.ParameterConfig(
                "jump_interval", "跳跃间隔", "跳跃攻击的时间间隔",
                Integer.class, 100, "tick").setMinValue(40).setMaxValue(300))
            .addParameter("jump_range", new SkillTemplate.ParameterConfig(
                "jump_range", "跳跃范围", "跳跃的最大距离",
                Double.class, 10.0, "格").setMinValue(3.0).setMaxValue(20.0))
            .addParameter("jump_damage", new SkillTemplate.ParameterConfig(
                "jump_damage", "跳跃伤害", "落地时造成的伤害",
                Double.class, 4.0, "点").setMinValue(1.0).setMaxValue(15.0))
            .setEffectConfig(new SkillTemplate.EffectConfig()
                .addParticle(Particle.CLOUD)
                .addSound(Sound.ENTITY_SLIME_JUMP)));

        // ID9 - 分裂召唤
        registerSkill(new SkillTemplate("split_summon", "分裂召唤",
                "死亡时分裂成多个小怪物",
                SkillTemplate.SkillCategory.SUMMON, SkillTemplate.SkillType.DEATH_SKILL, "ID9")
            .addParameter("split_count", new SkillTemplate.ParameterConfig(
                "split_count", "分裂数量", "分裂出的怪物数量",
                Integer.class, 3, "个").setMinValue(1).setMaxValue(8))
            .addParameter("split_health", new SkillTemplate.ParameterConfig(
                "split_health", "分裂体血量", "分裂出的怪物血量",
                Double.class, 10.0, "点").setMinValue(5.0).setMaxValue(50.0))
            .setEffectConfig(new SkillTemplate.EffectConfig()
                .addParticle(Particle.EXPLOSION_LARGE)
                .addSound(Sound.ENTITY_GENERIC_EXPLODE)));

        // ID11 - 传送攻击
        registerSkill(new SkillTemplate("teleport_attack", "传送攻击",
                "传送到玩家身后进行攻击",
                SkillTemplate.SkillCategory.MOVEMENT, SkillTemplate.SkillType.ACTIVE_TIMED, "ID11")
            .addParameter("teleport_interval", new SkillTemplate.ParameterConfig(
                "teleport_interval", "传送间隔", "传送攻击的时间间隔",
                Integer.class, 120, "tick").setMinValue(60).setMaxValue(400))
            .addParameter("teleport_range", new SkillTemplate.ParameterConfig(
                "teleport_range", "传送范围", "传送的最大距离",
                Double.class, 15.0, "格").setMinValue(5.0).setMaxValue(30.0))
            .setEffectConfig(new SkillTemplate.EffectConfig()
                .addParticle(Particle.PORTAL)
                .addSound(Sound.ENTITY_ENDERMAN_TELEPORT)));

        // ID12 - 爆炸攻击
        registerSkill(new SkillTemplate("explosion_attack", "爆炸攻击",
                "死亡时产生爆炸",
                SkillTemplate.SkillCategory.ATTACK, SkillTemplate.SkillType.DEATH_SKILL, "ID12")
            .addParameter("explosion_power", new SkillTemplate.ParameterConfig(
                "explosion_power", "爆炸威力", "爆炸的威力大小",
                Float.class, 2.0f, "").setMinValue(0.5f).setMaxValue(6.0f))
            .addParameter("explosion_damage", new SkillTemplate.ParameterConfig(
                "explosion_damage", "爆炸伤害", "爆炸造成的伤害",
                Double.class, 8.0, "点").setMinValue(2.0).setMaxValue(20.0))
            .setEffectConfig(new SkillTemplate.EffectConfig()
                .addParticle(Particle.EXPLOSION_HUGE)
                .addSound(Sound.ENTITY_GENERIC_EXPLODE)));

        // ID15 - 生命汲取
        registerSkill(new SkillTemplate("life_drain", "生命汲取",
                "攻击时汲取玩家生命值",
                SkillTemplate.SkillCategory.ATTACK, SkillTemplate.SkillType.PASSIVE, "ID15")
            .addParameter("drain_amount", new SkillTemplate.ParameterConfig(
                "drain_amount", "汲取量", "每次攻击汲取的生命值",
                Double.class, 2.0, "点").setMinValue(0.5).setMaxValue(8.0))
            .addParameter("drain_chance", new SkillTemplate.ParameterConfig(
                "drain_chance", "汲取概率", "触发生命汲取的概率",
                Double.class, 0.5, "%").setMinValue(0.1).setMaxValue(1.0))
            .setEffectConfig(new SkillTemplate.EffectConfig()
                .addParticle(Particle.HEART)
                .addSound(Sound.ENTITY_PLAYER_HURT)));

        // ID16 - 护盾反射
        registerSkill(new SkillTemplate("shield_reflect", "护盾反射",
                "反射部分受到的伤害",
                SkillTemplate.SkillCategory.DEFENSE, SkillTemplate.SkillType.DAMAGE_TRIGGERED, "ID16")
            .addParameter("reflect_chance", new SkillTemplate.ParameterConfig(
                "reflect_chance", "反射概率", "触发护盾反射的概率",
                Double.class, 0.3, "%").setMinValue(0.1).setMaxValue(0.8))
            .addParameter("reflect_multiplier", new SkillTemplate.ParameterConfig(
                "reflect_multiplier", "反射倍数", "反射伤害的倍数",
                Double.class, 0.5, "倍").setMinValue(0.2).setMaxValue(1.5))
            .setEffectConfig(new SkillTemplate.EffectConfig()
                .addParticle(Particle.ENCHANTED_HIT)
                .addSound(Sound.ITEM_SHIELD_BLOCK)));

        // ID17 - 雷霆攻击
        registerSkill(new SkillTemplate("thunder_attack", "雷霆攻击",
                "召唤闪电攻击玩家",
                SkillTemplate.SkillCategory.ATTACK, SkillTemplate.SkillType.ACTIVE_TIMED, "ID17")
            .addParameter("lightning_interval", new SkillTemplate.ParameterConfig(
                "lightning_interval", "闪电间隔", "召唤闪电的时间间隔",
                Integer.class, 100, "tick").setMinValue(40).setMaxValue(400))
            .addParameter("lightning_range", new SkillTemplate.ParameterConfig(
                "lightning_range", "闪电范围", "闪电攻击的最大距离",
                Double.class, 15.0, "格").setMinValue(5.0).setMaxValue(30.0))
            .addParameter("lightning_damage", new SkillTemplate.ParameterConfig(
                "lightning_damage", "闪电伤害", "闪电造成的伤害",
                Double.class, 8.0, "点").setMinValue(2.0).setMaxValue(20.0))
            .setEffectConfig(new SkillTemplate.EffectConfig()
                .addParticle(Particle.ELECTRIC_SPARK)
                .addSound(Sound.ENTITY_LIGHTNING_BOLT_THUNDER)));

        // ID18 - 虚弱光环
        registerSkill(new SkillTemplate("weakness_aura", "虚弱光环",
                "持续对周围玩家施加虚弱效果",
                SkillTemplate.SkillCategory.DEBUFF, SkillTemplate.SkillType.PASSIVE, "ID18")
            .addParameter("aura_range", new SkillTemplate.ParameterConfig(
                "aura_range", "光环范围", "虚弱光环的影响范围",
                Double.class, 8.0, "格").setMinValue(3.0).setMaxValue(15.0))
            .addParameter("weakness_level", new SkillTemplate.ParameterConfig(
                "weakness_level", "虚弱等级", "虚弱效果的等级",
                Integer.class, 1, "级").setMinValue(1).setMaxValue(5))
            .setEffectConfig(new SkillTemplate.EffectConfig()
                .addParticle(Particle.SPELL_MOB_AMBIENT)
                .addPotionEffect(PotionEffectType.WEAKNESS)));

        // ID19 - 狂暴模式
        registerSkill(new SkillTemplate("berserk_mode", "狂暴模式",
                "血量低时进入狂暴状态",
                SkillTemplate.SkillCategory.BUFF, SkillTemplate.SkillType.DAMAGE_TRIGGERED, "ID19")
            .addParameter("berserk_threshold", new SkillTemplate.ParameterConfig(
                "berserk_threshold", "触发血量", "触发狂暴的血量百分比",
                Double.class, 0.3, "%").setMinValue(0.1).setMaxValue(0.8))
            .addParameter("damage_boost", new SkillTemplate.ParameterConfig(
                "damage_boost", "伤害提升", "狂暴时的伤害提升倍数",
                Double.class, 1.5, "倍").setMinValue(1.2).setMaxValue(3.0))
            .addParameter("speed_boost", new SkillTemplate.ParameterConfig(
                "speed_boost", "速度提升", "狂暴时的速度提升等级",
                Integer.class, 2, "级").setMinValue(1).setMaxValue(5))
            .setEffectConfig(new SkillTemplate.EffectConfig()
                .addParticle(Particle.ANGRY_VILLAGER)
                .addSound(Sound.ENTITY_ZOMBIE_ANGRY)));

        // ID20 - 召唤风暴
        registerSkill(new SkillTemplate("summon_storm", "召唤风暴",
                "召唤雷电风暴攻击大范围敌人",
                SkillTemplate.SkillCategory.ATTACK, SkillTemplate.SkillType.ACTIVE_TIMED, "ID20")
            .addParameter("storm_interval", new SkillTemplate.ParameterConfig(
                "storm_interval", "风暴间隔", "召唤风暴的时间间隔",
                Integer.class, 300, "tick").setMinValue(200).setMaxValue(800))
            .addParameter("storm_duration", new SkillTemplate.ParameterConfig(
                "storm_duration", "风暴持续", "风暴持续的时间",
                Integer.class, 100, "tick").setMinValue(40).setMaxValue(300))
            .addParameter("storm_range", new SkillTemplate.ParameterConfig(
                "storm_range", "风暴范围", "风暴影响的范围",
                Double.class, 20.0, "格").setMinValue(10.0).setMaxValue(40.0))
            .addParameter("lightning_count", new SkillTemplate.ParameterConfig(
                "lightning_count", "闪电数量", "风暴期间的闪电数量",
                Integer.class, 8, "个").setMinValue(3).setMaxValue(20))
            .setEffectConfig(new SkillTemplate.EffectConfig()
                .addParticle(Particle.ELECTRIC_SPARK)
                .addParticle(Particle.CLOUD)
                .addSound(Sound.ENTITY_LIGHTNING_BOLT_THUNDER)));

        logger.info("ID系列技能模板注册完成");
    }
    
    /**
     * 注册IDC系列技能模板
     */
    private void registerIdcSeriesSkills() {
        // IDC1 - 剧毒烟雾
        registerSkill(new SkillTemplate("poison_smoke", "剧毒烟雾", 
                "持续生成黑色烟雾粒子效果", 
                SkillTemplate.SkillCategory.EFFECT, SkillTemplate.SkillType.PASSIVE, "IDC1")
            .addParameter("particle_interval", new SkillTemplate.ParameterConfig(
                "particle_interval", "粒子间隔", "生成粒子的时间间隔", 
                Integer.class, 5, "tick").setMinValue(1).setMaxValue(20))
            .addParameter("particle_count", new SkillTemplate.ParameterConfig(
                "particle_count", "粒子数量", "每次生成的粒子数量", 
                Integer.class, 8, "个").setMinValue(1).setMaxValue(20))
            .setEffectConfig(new SkillTemplate.EffectConfig()
                .addParticle(Particle.LARGE_SMOKE)));
        
        // IDC2 - 螺旋粒子
        registerSkill(new SkillTemplate("spiral_particles", "螺旋粒子", 
                "生成螺旋上升的紫色粒子效果", 
                SkillTemplate.SkillCategory.EFFECT, SkillTemplate.SkillType.PASSIVE, "IDC2")
            .addParameter("spiral_speed", new SkillTemplate.ParameterConfig(
                "spiral_speed", "螺旋速度", "粒子螺旋上升的速度", 
                Double.class, 0.1, "").setMinValue(0.05).setMaxValue(0.5))
            .addParameter("spiral_radius", new SkillTemplate.ParameterConfig(
                "spiral_radius", "螺旋半径", "粒子螺旋的半径", 
                Double.class, 1.0, "格").setMinValue(0.5).setMaxValue(3.0))
            .setEffectConfig(new SkillTemplate.EffectConfig()
                .addParticle(Particle.SPELL_WITCH)));
        
        // IDC3 - 烈焰攻击
        registerSkill(new SkillTemplate("blaze_attack", "烈焰攻击",
                "发射烈焰弹攻击玩家",
                SkillTemplate.SkillCategory.ATTACK, SkillTemplate.SkillType.ACTIVE_TIMED, "IDC3")
            .addParameter("fireball_interval", new SkillTemplate.ParameterConfig(
                "fireball_interval", "火球间隔", "发射火球的时间间隔",
                Integer.class, 60, "tick").setMinValue(20).setMaxValue(200))
            .addParameter("fireball_speed", new SkillTemplate.ParameterConfig(
                "fireball_speed", "火球速度", "火球飞行速度",
                Double.class, 2.0, "").setMinValue(0.5).setMaxValue(5.0))
            .addParameter("explosion_power", new SkillTemplate.ParameterConfig(
                "explosion_power", "爆炸威力", "火球爆炸的威力",
                Float.class, 1.0f, "").setMinValue(0.5f).setMaxValue(3.0f))
            .setEffectConfig(new SkillTemplate.EffectConfig()
                .addParticle(Particle.FLAME)
                .addSound(Sound.ENTITY_BLAZE_SHOOT)));

        // IDC4 - 爬行者爆炸
        registerSkill(new SkillTemplate("creeper_explosion", "爬行者爆炸",
                "接近玩家时自爆",
                SkillTemplate.SkillCategory.ATTACK, SkillTemplate.SkillType.ACTIVE_TRIGGERED, "IDC4")
            .addParameter("explosion_range", new SkillTemplate.ParameterConfig(
                "explosion_range", "爆炸范围", "触发爆炸的距离",
                Double.class, 3.0, "格").setMinValue(1.0).setMaxValue(8.0))
            .addParameter("explosion_power", new SkillTemplate.ParameterConfig(
                "explosion_power", "爆炸威力", "爆炸的威力大小",
                Float.class, 3.0f, "").setMinValue(1.0f).setMaxValue(8.0f))
            .addParameter("fuse_time", new SkillTemplate.ParameterConfig(
                "fuse_time", "引爆时间", "开始引爆到爆炸的时间",
                Integer.class, 30, "tick").setMinValue(10).setMaxValue(100))
            .setEffectConfig(new SkillTemplate.EffectConfig()
                .addParticle(Particle.EXPLOSION_HUGE)
                .addSound(Sound.ENTITY_CREEPER_PRIMED)));

        // IDC5 - 末影传送
        registerSkill(new SkillTemplate("ender_teleport", "末影传送",
                "随机传送并进行电流攻击",
                SkillTemplate.SkillCategory.MOVEMENT, SkillTemplate.SkillType.ACTIVE_TIMED, "IDC5")
            .addParameter("teleport_interval", new SkillTemplate.ParameterConfig(
                "teleport_interval", "传送间隔", "传送的时间间隔",
                Integer.class, 160, "tick").setMinValue(80).setMaxValue(400))
            .addParameter("teleport_range", new SkillTemplate.ParameterConfig(
                "teleport_range", "传送范围", "传送的最大距离",
                Double.class, 20.0, "格").setMinValue(5.0).setMaxValue(40.0))
            .addParameter("electric_damage", new SkillTemplate.ParameterConfig(
                "electric_damage", "电流伤害", "传送后电流攻击的伤害",
                Double.class, 4.0, "点").setMinValue(1.0).setMaxValue(12.0))
            .setEffectConfig(new SkillTemplate.EffectConfig()
                .addParticle(Particle.PORTAL)
                .addParticle(Particle.ENCHANTED_HIT)
                .addSound(Sound.ENTITY_ENDERMAN_TELEPORT)));

        // IDC6 - 蜘蛛网陷阱
        registerSkill(new SkillTemplate("web_trap", "蜘蛛网陷阱",
                "在周围生成蜘蛛网并喷射毒液",
                SkillTemplate.SkillCategory.ENVIRONMENTAL, SkillTemplate.SkillType.ACTIVE_TIMED, "IDC6")
            .addParameter("web_interval", new SkillTemplate.ParameterConfig(
                "web_interval", "蛛网间隔", "生成蜘蛛网的时间间隔",
                Integer.class, 200, "tick").setMinValue(100).setMaxValue(600))
            .addParameter("web_count", new SkillTemplate.ParameterConfig(
                "web_count", "蛛网数量", "每次生成的蜘蛛网数量",
                Integer.class, 5, "个").setMinValue(2).setMaxValue(15))
            .addParameter("poison_level", new SkillTemplate.ParameterConfig(
                "poison_level", "毒液等级", "毒液攻击的中毒等级",
                Integer.class, 5, "级").setMinValue(1).setMaxValue(10))
            .addParameter("poison_duration", new SkillTemplate.ParameterConfig(
                "poison_duration", "中毒时长", "中毒效果持续时间",
                Integer.class, 600, "tick").setMinValue(100).setMaxValue(1200))
            .setEffectConfig(new SkillTemplate.EffectConfig()
                .addParticle(Particle.SPELL_WITCH)
                .addSound(Sound.ENTITY_SPIDER_AMBIENT)
                .addPotionEffect(PotionEffectType.POISON)));

        // IDC7 - 卫道士圆圈攻击
        registerSkill(new SkillTemplate("guardian_circle_attack", "卫道士圆圈攻击",
                "脚底暴击圆圈和召唤仆从",
                SkillTemplate.SkillCategory.ATTACK, SkillTemplate.SkillType.ACTIVE_TIMED, "IDC7")
            .addParameter("circle_interval", new SkillTemplate.ParameterConfig(
                "circle_interval", "圆圈间隔", "脚底圆圈攻击的间隔",
                Integer.class, 10, "tick").setMinValue(5).setMaxValue(40))
            .addParameter("circle_radius", new SkillTemplate.ParameterConfig(
                "circle_radius", "圆圈半径", "攻击圆圈的半径",
                Double.class, 3.0, "格").setMinValue(1.5).setMaxValue(8.0))
            .addParameter("circle_damage", new SkillTemplate.ParameterConfig(
                "circle_damage", "圆圈伤害", "圆圈攻击造成的伤害",
                Double.class, 2.0, "点").setMinValue(0.5).setMaxValue(8.0))
            .addParameter("summon_interval", new SkillTemplate.ParameterConfig(
                "summon_interval", "召唤间隔", "召唤仆从的时间间隔",
                Integer.class, 400, "tick").setMinValue(200).setMaxValue(1000))
            .setEffectConfig(new SkillTemplate.EffectConfig()
                .addParticle(Particle.CRIT)
                .addParticle(Particle.PORTAL)
                .addSound(Sound.ENTITY_PLAYER_ATTACK_CRIT)));

        // IDC8 - 唤魔者魔法攻击
        registerSkill(new SkillTemplate("evoker_magic_attack", "唤魔者魔法攻击",
                "DNA螺旋魔法攻击和尖牙攻击",
                SkillTemplate.SkillCategory.ATTACK, SkillTemplate.SkillType.ACTIVE_TIMED, "IDC8")
            .addParameter("magic_interval", new SkillTemplate.ParameterConfig(
                "magic_interval", "魔法间隔", "魔法攻击的时间间隔",
                Integer.class, 80, "tick").setMinValue(40).setMaxValue(200))
            .addParameter("magic_damage", new SkillTemplate.ParameterConfig(
                "magic_damage", "魔法伤害", "魔法攻击造成的伤害",
                Double.class, 6.0, "点").setMinValue(2.0).setMaxValue(15.0))
            .addParameter("fang_count", new SkillTemplate.ParameterConfig(
                "fang_count", "尖牙数量", "尖牙攻击的数量",
                Integer.class, 8, "个").setMinValue(4).setMaxValue(20))
            .setEffectConfig(new SkillTemplate.EffectConfig()
                .addParticle(Particle.DRAGON_BREATH)
                .addParticle(Particle.SPELL_WITCH)
                .addSound(Sound.ENTITY_EVOKER_CAST_SPELL)));

        logger.info("IDC系列技能模板注册完成");
    }

    /**
     * 注册更多IDC系列技能模板
     */
    private void registerMoreIdcSeriesSkills() {
        // IDC9 - 劫掠兽冲撞
        registerSkill(new SkillTemplate("ravager_charge", "劫掠兽冲撞",
                "向玩家冲撞并造成击退效果",
                SkillTemplate.SkillCategory.ATTACK, SkillTemplate.SkillType.ACTIVE_TIMED, "IDC9")
            .addParameter("charge_interval", new SkillTemplate.ParameterConfig(
                "charge_interval", "冲撞间隔", "冲撞攻击的时间间隔",
                Integer.class, 120, "tick").setMinValue(60).setMaxValue(300))
            .addParameter("charge_speed", new SkillTemplate.ParameterConfig(
                "charge_speed", "冲撞速度", "冲撞时的移动速度倍数",
                Double.class, 2.0, "倍").setMinValue(1.2).setMaxValue(4.0))
            .addParameter("charge_damage", new SkillTemplate.ParameterConfig(
                "charge_damage", "冲撞伤害", "冲撞造成的伤害",
                Double.class, 8.0, "点").setMinValue(3.0).setMaxValue(20.0))
            .addParameter("knockback_power", new SkillTemplate.ParameterConfig(
                "knockback_power", "击退力度", "击退效果的强度",
                Double.class, 2.0, "").setMinValue(0.5).setMaxValue(5.0))
            .setEffectConfig(new SkillTemplate.EffectConfig()
                .addParticle(Particle.CLOUD)
                .addSound(Sound.ENTITY_RAVAGER_ROAR)));

        // IDC10 - 僵尸马践踏
        registerSkill(new SkillTemplate("zombie_horse_trample", "僵尸马践踏",
                "践踏攻击和毒雾效果",
                SkillTemplate.SkillCategory.ATTACK, SkillTemplate.SkillType.ACTIVE_TIMED, "IDC10")
            .addParameter("trample_interval", new SkillTemplate.ParameterConfig(
                "trample_interval", "践踏间隔", "践踏攻击的时间间隔",
                Integer.class, 100, "tick").setMinValue(50).setMaxValue(250))
            .addParameter("trample_range", new SkillTemplate.ParameterConfig(
                "trample_range", "践踏范围", "践踏攻击的影响范围",
                Double.class, 4.0, "格").setMinValue(2.0).setMaxValue(10.0))
            .addParameter("poison_duration", new SkillTemplate.ParameterConfig(
                "poison_duration", "中毒时长", "毒雾造成的中毒时间",
                Integer.class, 100, "tick").setMinValue(40).setMaxValue(300))
            .setEffectConfig(new SkillTemplate.EffectConfig()
                .addParticle(Particle.LARGE_SMOKE)
                .addParticle(Particle.SPELL_WITCH)
                .addSound(Sound.ENTITY_HORSE_ANGRY)
                .addPotionEffect(PotionEffectType.POISON)));

        // IDC11 - 岩浆怪分裂
        registerSkill(new SkillTemplate("magma_cube_split", "岩浆怪分裂",
                "死亡时分裂成小岩浆怪",
                SkillTemplate.SkillCategory.SUMMON, SkillTemplate.SkillType.DEATH_SKILL, "IDC11")
            .addParameter("split_count", new SkillTemplate.ParameterConfig(
                "split_count", "分裂数量", "分裂出的小岩浆怪数量",
                Integer.class, 4, "个").setMinValue(2).setMaxValue(8))
            .addParameter("split_size", new SkillTemplate.ParameterConfig(
                "split_size", "分裂体大小", "分裂出的岩浆怪大小",
                Integer.class, 1, "级").setMinValue(1).setMaxValue(3))
            .addParameter("fire_damage", new SkillTemplate.ParameterConfig(
                "fire_damage", "火焰伤害", "分裂时造成的火焰伤害",
                Double.class, 4.0, "点").setMinValue(1.0).setMaxValue(10.0))
            .setEffectConfig(new SkillTemplate.EffectConfig()
                .addParticle(Particle.LAVA)
                .addParticle(Particle.FLAME)
                .addSound(Sound.ENTITY_MAGMA_CUBE_DEATH)));

        // IDC12 - 骷髅射击
        registerSkill(new SkillTemplate("skeleton_shooting", "骷髅射击",
                "高频率的箭矢攻击",
                SkillTemplate.SkillCategory.ATTACK, SkillTemplate.SkillType.ACTIVE_TIMED, "IDC12")
            .addParameter("shoot_interval", new SkillTemplate.ParameterConfig(
                "shoot_interval", "射击间隔", "射击的时间间隔",
                Integer.class, 20, "tick").setMinValue(10).setMaxValue(60))
            .addParameter("arrow_speed", new SkillTemplate.ParameterConfig(
                "arrow_speed", "箭矢速度", "箭矢的飞行速度",
                Double.class, 3.0, "").setMinValue(1.0).setMaxValue(6.0))
            .addParameter("arrow_damage", new SkillTemplate.ParameterConfig(
                "arrow_damage", "箭矢伤害", "每支箭矢的伤害",
                Double.class, 4.0, "点").setMinValue(1.0).setMaxValue(12.0))
            .addParameter("accuracy", new SkillTemplate.ParameterConfig(
                "accuracy", "射击精度", "射击的准确度",
                Double.class, 0.8, "").setMinValue(0.3).setMaxValue(1.0))
            .setEffectConfig(new SkillTemplate.EffectConfig()
                .addSound(Sound.ENTITY_SKELETON_SHOOT)));

        // IDC13 - 僵尸群体召唤
        registerSkill(new SkillTemplate("zombie_horde_summon", "僵尸群体召唤",
                "召唤大量僵尸仆从",
                SkillTemplate.SkillCategory.SUMMON, SkillTemplate.SkillType.ACTIVE_TIMED, "IDC13")
            .addParameter("summon_interval", new SkillTemplate.ParameterConfig(
                "summon_interval", "召唤间隔", "群体召唤的时间间隔",
                Integer.class, 600, "tick").setMinValue(300).setMaxValue(1200))
            .addParameter("summon_count", new SkillTemplate.ParameterConfig(
                "summon_count", "召唤数量", "每次召唤的僵尸数量",
                Integer.class, 6, "个").setMinValue(3).setMaxValue(15))
            .addParameter("minion_health", new SkillTemplate.ParameterConfig(
                "minion_health", "仆从血量", "召唤僵尸的血量",
                Double.class, 15.0, "点").setMinValue(5.0).setMaxValue(40.0))
            .setEffectConfig(new SkillTemplate.EffectConfig()
                .addParticle(Particle.PORTAL)
                .addParticle(Particle.LARGE_SMOKE)
                .addSound(Sound.ENTITY_ZOMBIE_AMBIENT)));

        logger.info("更多IDC系列技能模板注册完成");
    }
    
    /**
     * 注册技能模板
     */
    private void registerSkill(SkillTemplate template) {
        skillTemplates.put(template.getSkillId(), template);
        
        // 按分类分组
        skillsByCategory.get(template.getCategory()).add(template);
        
        // 按来源分组
        skillsBySource.computeIfAbsent(template.getSourceEntity(), k -> new ArrayList<>()).add(template);
    }
    
    /**
     * 获取所有技能模板
     */
    public List<SkillTemplate> getAllTemplates() {
        return new ArrayList<>(skillTemplates.values());
    }
    
    /**
     * 根据分类获取技能模板
     */
    public List<SkillTemplate> getTemplatesByCategory(SkillTemplate.SkillCategory category) {
        return new ArrayList<>(skillsByCategory.get(category));
    }
    
    /**
     * 根据来源获取技能模板
     */
    public List<SkillTemplate> getTemplatesBySource(String source) {
        return skillsBySource.getOrDefault(source, new ArrayList<>());
    }
    
    /**
     * 根据ID获取技能模板
     */
    public SkillTemplate getTemplate(String skillId) {
        return skillTemplates.get(skillId);
    }
    
    /**
     * 搜索技能模板
     */
    public List<SkillTemplate> searchTemplates(String keyword) {
        return skillTemplates.values().stream()
            .filter(template -> 
                template.getSkillName().toLowerCase().contains(keyword.toLowerCase()) ||
                template.getDescription().toLowerCase().contains(keyword.toLowerCase()) ||
                template.getSourceEntity().toLowerCase().contains(keyword.toLowerCase()))
            .collect(Collectors.toList());
    }
    
    /**
     * 检查技能组合兼容性
     */
    public SkillCombinationResult checkCombinationCompatibility(List<String> skillIds) {
        List<SkillTemplate> skills = skillIds.stream()
            .map(this::getTemplate)
            .filter(Objects::nonNull)
            .collect(Collectors.toList());
        
        SkillCombinationResult result = new SkillCombinationResult();
        result.setValid(true);
        
        // 检查技能兼容性
        for (int i = 0; i < skills.size(); i++) {
            for (int j = i + 1; j < skills.size(); j++) {
                SkillTemplate skill1 = skills.get(i);
                SkillTemplate skill2 = skills.get(j);
                
                if (!skill1.isCompatibleWith(skill2)) {
                    result.setValid(false);
                    result.addConflict(skill1.getSkillId(), skill2.getSkillId(), 
                        "技能 " + skill1.getSkillName() + " 与 " + skill2.getSkillName() + " 不兼容");
                }
            }
        }
        
        return result;
    }
    
    /**
     * 获取推荐的技能组合
     */
    public List<List<String>> getRecommendedCombinations(String baseSkillId) {
        // TODO: 实现推荐算法
        return new ArrayList<>();
    }
    
    /**
     * 获取统计信息
     */
    public Map<String, Object> getStatistics() {
        Map<String, Object> stats = new HashMap<>();
        stats.put("totalSkills", skillTemplates.size());
        stats.put("skillsByCategory", skillsByCategory.entrySet().stream()
            .collect(Collectors.toMap(
                entry -> entry.getKey().getDisplayName(),
                entry -> entry.getValue().size())));
        stats.put("skillsBySource", skillsBySource.entrySet().stream()
            .collect(Collectors.toMap(
                Map.Entry::getKey,
                entry -> entry.getValue().size())));
        return stats;
    }
}
