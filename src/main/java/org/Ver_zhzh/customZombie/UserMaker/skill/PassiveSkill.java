package org.Ver_zhzh.customZombie.UserMaker.skill;

import org.bukkit.entity.LivingEntity;
import org.Ver_zhzh.customZombie.UserMaker.IdzEntityConfig;
import org.Ver_zhzh.customZombie.UserMaker.SkillTemplate;
import org.Ver_zhzh.deathZombieV4.DeathZombieV4;

/**
 * 被动技能抽象类 - 通过元数据实现的被动效果
 * 
 * <AUTHOR>
 * @version 1.0
 */
public abstract class PassiveSkill extends AbstractSkill {
    
    public PassiveSkill(DeathZombieV4 plugin, SkillTemplate template) {
        super(plugin, template);
    }
    
    @Override
    public void activate(LivingEntity entity, IdzEntityConfig.SkillConfig skillConfig) {
        if (!canActivate(entity, skillConfig)) {
            return;
        }
        
        // 设置被动技能标记
        setEntityMetadata(entity, getSkillMetadataKey(), true);
        
        // 设置技能参数到元数据
        setupSkillMetadata(entity, skillConfig);
        
        // 执行被动技能特定的激活逻辑
        activatePassive(entity, skillConfig);
        
        logger.info("被动技能已激活: " + template.getSkillName() + " for " + entity.getUniqueId());
    }
    
    @Override
    public void deactivate(LivingEntity entity) {
        // 移除被动技能标记
        removeEntityMetadata(entity, getSkillMetadataKey());
        
        // 清理技能参数元数据
        cleanupSkillMetadata(entity);
        
        // 执行被动技能特定的停用逻辑
        deactivatePassive(entity);
        
        logger.info("被动技能已停用: " + template.getSkillName() + " for " + entity.getUniqueId());
    }
    
    @Override
    protected boolean canActivateSpecific(LivingEntity entity, IdzEntityConfig.SkillConfig skillConfig) {
        // 检查是否已经激活
        return !getEntityMetadata(entity, getSkillMetadataKey(), false);
    }
    
    @Override
    protected SkillValidationResult validateConfigSpecific(IdzEntityConfig.SkillConfig skillConfig) {
        return SkillValidationResult.success();
    }
    
    /**
     * 获取技能元数据键
     */
    protected String getSkillMetadataKey() {
        return template.getSkillId() + "_active";
    }
    
    /**
     * 设置技能参数到元数据
     */
    protected abstract void setupSkillMetadata(LivingEntity entity, IdzEntityConfig.SkillConfig skillConfig);
    
    /**
     * 清理技能参数元数据
     */
    protected abstract void cleanupSkillMetadata(LivingEntity entity);
    
    /**
     * 激活被动技能的特定逻辑
     */
    protected abstract void activatePassive(LivingEntity entity, IdzEntityConfig.SkillConfig skillConfig);
    
    /**
     * 停用被动技能的特定逻辑
     */
    protected abstract void deactivatePassive(LivingEntity entity);
}
