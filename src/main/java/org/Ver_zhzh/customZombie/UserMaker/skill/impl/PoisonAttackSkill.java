package org.Ver_zhzh.customZombie.UserMaker.skill.impl;

import org.bukkit.entity.LivingEntity;
import org.Ver_zhzh.customZombie.UserMaker.IdzEntityConfig;
import org.Ver_zhzh.customZombie.UserMaker.SkillTemplate;
import org.Ver_zhzh.customZombie.UserMaker.skill.PassiveSkill;
import org.Ver_zhzh.deathZombieV4.DeathZombieV4;

/**
 * 剧毒攻击技能 - 攻击时有概率造成中毒效果
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class PoisonAttackSkill extends PassiveSkill {
    
    public PoisonAttackSkill(DeathZombieV4 plugin, SkillTemplate template) {
        super(plugin, template);
    }
    
    @Override
    protected void setupSkillMetadata(LivingEntity entity, IdzEntityConfig.SkillConfig skillConfig) {
        // 设置中毒等级
        int poisonLevel = getSkillParameter(skillConfig, "poison_level", 1);
        setEntityMetadata(entity, "poisonLevel", poisonLevel);
        
        // 设置中毒持续时间
        int poisonDuration = getSkillParameter(skillConfig, "poison_duration", 60);
        setEntityMetadata(entity, "poisonDuration", poisonDuration);
        
        // 设置触发概率
        double poisonChance = getSkillParameter(skillConfig, "poison_chance", 1.0);
        setEntityMetadata(entity, "poisonChance", poisonChance);
    }
    
    @Override
    protected void cleanupSkillMetadata(LivingEntity entity) {
        removeEntityMetadata(entity, "poisonLevel");
        removeEntityMetadata(entity, "poisonDuration");
        removeEntityMetadata(entity, "poisonChance");
    }
    
    @Override
    protected void activatePassive(LivingEntity entity, IdzEntityConfig.SkillConfig skillConfig) {
        // 被动技能无需额外激活逻辑，通过事件监听器处理
    }
    
    @Override
    protected void deactivatePassive(LivingEntity entity) {
        // 被动技能无需额外停用逻辑
    }
    
    @Override
    protected SkillValidationResult validateConfigSpecific(IdzEntityConfig.SkillConfig skillConfig) {
        // 验证中毒等级
        Integer poisonLevel = getSkillParameter(skillConfig, "poison_level", null);
        if (poisonLevel != null && (poisonLevel < 1 || poisonLevel > 10)) {
            return SkillValidationResult.error("中毒等级必须在1-10之间");
        }
        
        // 验证触发概率
        Double poisonChance = getSkillParameter(skillConfig, "poison_chance", null);
        if (poisonChance != null && (poisonChance < 0.0 || poisonChance > 1.0)) {
            return SkillValidationResult.error("触发概率必须在0.0-1.0之间");
        }
        
        return SkillValidationResult.success();
    }
}
