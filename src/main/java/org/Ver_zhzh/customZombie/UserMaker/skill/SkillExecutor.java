package org.Ver_zhzh.customZombie.UserMaker.skill;

import org.bukkit.entity.LivingEntity;
import org.Ver_zhzh.customZombie.UserMaker.IdzEntityConfig;
import org.Ver_zhzh.customZombie.UserMaker.SkillTemplate;
import org.Ver_zhzh.deathZombieV4.DeathZombieV4;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.logging.Logger;

/**
 * 技能执行器 - 负责管理和执行IDZ实体的技能
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class SkillExecutor {
    
    private final DeathZombieV4 plugin;
    private final Logger logger;
    private final SkillRegistry skillRegistry;
    
    // 实体技能映射 - 记录每个实体激活的技能
    private final Map<UUID, Set<String>> entityActiveSkills;
    
    public SkillExecutor(DeathZombieV4 plugin) {
        this.plugin = plugin;
        this.logger = plugin.getLogger();
        this.skillRegistry = new SkillRegistry(plugin);
        this.entityActiveSkills = new ConcurrentHashMap<>();
        
        logger.info("技能执行器初始化完成");
    }
    
    /**
     * 为实体激活技能
     */
    public boolean activateSkill(LivingEntity entity, IdzEntityConfig.SkillConfig skillConfig) {
        if (entity == null || skillConfig == null) {
            return false;
        }
        
        String skillId = skillConfig.getSkillId();
        ISkill skill = skillRegistry.getSkill(skillId);
        
        if (skill == null) {
            logger.warning("未找到技能: " + skillId);
            return false;
        }
        
        // 验证技能配置
        ISkill.SkillValidationResult validation = skill.validateConfig(skillConfig);
        if (!validation.isValid()) {
            logger.warning("技能配置验证失败: " + skillId + " - " + validation.getErrorMessage());
            return false;
        }
        
        // 检查是否可以激活
        if (!skill.canActivate(entity, skillConfig)) {
            logger.info("技能无法激活: " + skillId + " for " + entity.getUniqueId());
            return false;
        }
        
        try {
            // 激活技能
            skill.activate(entity, skillConfig);
            
            // 记录激活的技能
            UUID entityId = entity.getUniqueId();
            entityActiveSkills.computeIfAbsent(entityId, k -> ConcurrentHashMap.newKeySet()).add(skillId);
            
            logger.info("技能已激活: " + skillId + " for " + entity.getUniqueId());
            return true;
            
        } catch (Exception e) {
            logger.severe("激活技能时出错: " + skillId + " - " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }
    
    /**
     * 为实体停用技能
     */
    public boolean deactivateSkill(LivingEntity entity, String skillId) {
        if (entity == null || skillId == null) {
            return false;
        }
        
        ISkill skill = skillRegistry.getSkill(skillId);
        if (skill == null) {
            logger.warning("未找到技能: " + skillId);
            return false;
        }
        
        try {
            // 停用技能
            skill.deactivate(entity);
            
            // 移除激活记录
            UUID entityId = entity.getUniqueId();
            Set<String> activeSkills = entityActiveSkills.get(entityId);
            if (activeSkills != null) {
                activeSkills.remove(skillId);
                if (activeSkills.isEmpty()) {
                    entityActiveSkills.remove(entityId);
                }
            }
            
            logger.info("技能已停用: " + skillId + " for " + entity.getUniqueId());
            return true;
            
        } catch (Exception e) {
            logger.severe("停用技能时出错: " + skillId + " - " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }
    
    /**
     * 为实体激活所有配置的技能
     */
    public void activateAllSkills(LivingEntity entity, List<IdzEntityConfig.SkillConfig> skillConfigs) {
        if (entity == null || skillConfigs == null) {
            return;
        }
        
        // 按优先级排序
        skillConfigs.sort(Comparator.comparingInt(IdzEntityConfig.SkillConfig::getPriority));
        
        int successCount = 0;
        for (IdzEntityConfig.SkillConfig skillConfig : skillConfigs) {
            if (skillConfig.isEnabled()) {
                if (activateSkill(entity, skillConfig)) {
                    successCount++;
                }
            }
        }
        
        logger.info("为实体 " + entity.getUniqueId() + " 激活了 " + successCount + "/" + skillConfigs.size() + " 个技能");
    }
    
    /**
     * 停用实体的所有技能
     */
    public void deactivateAllSkills(LivingEntity entity) {
        if (entity == null) {
            return;
        }
        
        UUID entityId = entity.getUniqueId();
        Set<String> activeSkills = entityActiveSkills.get(entityId);
        
        if (activeSkills == null || activeSkills.isEmpty()) {
            return;
        }
        
        // 复制集合以避免并发修改
        Set<String> skillsToDeactivate = new HashSet<>(activeSkills);
        
        int deactivatedCount = 0;
        for (String skillId : skillsToDeactivate) {
            if (deactivateSkill(entity, skillId)) {
                deactivatedCount++;
            }
        }
        
        // 清理静态数据
        AbstractSkill.cleanupEntity(entityId);
        
        logger.info("为实体 " + entity.getUniqueId() + " 停用了 " + deactivatedCount + " 个技能");
    }
    
    /**
     * 获取实体的激活技能列表
     */
    public Set<String> getActiveSkills(LivingEntity entity) {
        if (entity == null) {
            return Collections.emptySet();
        }
        
        Set<String> activeSkills = entityActiveSkills.get(entity.getUniqueId());
        return activeSkills != null ? new HashSet<>(activeSkills) : Collections.emptySet();
    }
    
    /**
     * 检查实体是否有激活的技能
     */
    public boolean hasActiveSkills(LivingEntity entity) {
        if (entity == null) {
            return false;
        }
        
        Set<String> activeSkills = entityActiveSkills.get(entity.getUniqueId());
        return activeSkills != null && !activeSkills.isEmpty();
    }
    
    /**
     * 检查实体是否激活了特定技能
     */
    public boolean hasActiveSkill(LivingEntity entity, String skillId) {
        if (entity == null || skillId == null) {
            return false;
        }
        
        Set<String> activeSkills = entityActiveSkills.get(entity.getUniqueId());
        return activeSkills != null && activeSkills.contains(skillId);
    }
    
    /**
     * 获取技能注册器
     */
    public SkillRegistry getSkillRegistry() {
        return skillRegistry;
    }
    
    /**
     * 获取所有技能模板
     */
    public Map<String, SkillTemplate> getAllSkillTemplates() {
        return skillRegistry.getAllSkillTemplates();
    }
    
    /**
     * 获取技能模板
     */
    public SkillTemplate getSkillTemplate(String skillId) {
        return skillRegistry.getSkillTemplate(skillId);
    }
    
    /**
     * 检查技能是否存在
     */
    public boolean hasSkill(String skillId) {
        return skillRegistry.hasSkill(skillId);
    }
    
    /**
     * 获取统计信息
     */
    public Map<String, Object> getStatistics() {
        Map<String, Object> stats = new HashMap<>();
        
        stats.put("totalSkills", skillRegistry.getSkillCount());
        stats.put("activeEntities", entityActiveSkills.size());
        
        // 统计各类型技能数量
        Map<String, Integer> skillsByCategory = new HashMap<>();
        for (SkillTemplate template : skillRegistry.getAllSkillTemplates().values()) {
            String category = template.getCategory().name();
            skillsByCategory.put(category, skillsByCategory.getOrDefault(category, 0) + 1);
        }
        stats.put("skillsByCategory", skillsByCategory);
        
        // 统计激活的技能总数
        int totalActiveSkills = entityActiveSkills.values().stream()
            .mapToInt(Set::size)
            .sum();
        stats.put("totalActiveSkills", totalActiveSkills);
        
        return stats;
    }
    
    /**
     * 清理所有数据
     */
    public void cleanup() {
        // 停用所有实体的技能
        for (UUID entityId : new HashSet<>(entityActiveSkills.keySet())) {
            // 这里无法获取到LivingEntity，只能清理数据
            AbstractSkill.cleanupEntity(entityId);
        }
        
        entityActiveSkills.clear();
        skillRegistry.cleanup();
        
        logger.info("技能执行器已清理");
    }
}
