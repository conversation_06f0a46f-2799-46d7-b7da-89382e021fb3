package org.Ver_zhzh.customZombie.UserMaker.skill;

import org.bukkit.entity.LivingEntity;
import org.Ver_zhzh.customZombie.UserMaker.IdzEntityConfig;
import org.Ver_zhzh.customZombie.UserMaker.SkillTemplate;

/**
 * 技能接口 - 定义所有技能的基本行为
 * 
 * <AUTHOR>
 * @version 1.0
 */
public interface ISkill {
    
    /**
     * 获取技能模板
     * @return 技能模板
     */
    SkillTemplate getTemplate();
    
    /**
     * 激活技能
     * @param entity 实体
     * @param skillConfig 技能配置
     */
    void activate(LivingEntity entity, IdzEntityConfig.SkillConfig skillConfig);
    
    /**
     * 停用技能
     * @param entity 实体
     */
    void deactivate(LivingEntity entity);
    
    /**
     * 检查技能是否可以激活
     * @param entity 实体
     * @param skillConfig 技能配置
     * @return 是否可以激活
     */
    boolean canActivate(LivingEntity entity, IdzEntityConfig.SkillConfig skillConfig);
    
    /**
     * 获取技能冷却时间
     * @param skillConfig 技能配置
     * @return 冷却时间（毫秒）
     */
    long getCooldown(IdzEntityConfig.SkillConfig skillConfig);
    
    /**
     * 检查技能是否在冷却中
     * @param entity 实体
     * @return 是否在冷却中
     */
    boolean isOnCooldown(LivingEntity entity);
    
    /**
     * 设置技能冷却
     * @param entity 实体
     * @param cooldown 冷却时间（毫秒）
     */
    void setCooldown(LivingEntity entity, long cooldown);
    
    /**
     * 验证技能配置
     * @param skillConfig 技能配置
     * @return 验证结果
     */
    SkillValidationResult validateConfig(IdzEntityConfig.SkillConfig skillConfig);
    
    /**
     * 技能验证结果
     */
    class SkillValidationResult {
        private final boolean valid;
        private final String errorMessage;
        
        public SkillValidationResult(boolean valid, String errorMessage) {
            this.valid = valid;
            this.errorMessage = errorMessage;
        }
        
        public static SkillValidationResult success() {
            return new SkillValidationResult(true, null);
        }
        
        public static SkillValidationResult error(String message) {
            return new SkillValidationResult(false, message);
        }
        
        public boolean isValid() {
            return valid;
        }
        
        public String getErrorMessage() {
            return errorMessage;
        }
    }
}
