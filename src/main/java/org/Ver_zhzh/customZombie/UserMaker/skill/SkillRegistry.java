package org.Ver_zhzh.customZombie.UserMaker.skill;

import org.bukkit.Particle;
import org.bukkit.Sound;
import org.bukkit.potion.PotionEffectType;
import org.Ver_zhzh.customZombie.UserMaker.SkillTemplate;
import org.Ver_zhzh.customZombie.UserMaker.skill.impl.*;
import org.Ver_zhzh.deathZombieV4.DeathZombieV4;

import java.util.HashMap;
import java.util.Map;
import java.util.logging.Logger;

/**
 * 技能注册器 - 管理所有技能的注册和创建
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class SkillRegistry {
    
    private final DeathZombieV4 plugin;
    private final Logger logger;
    private final Map<String, ISkill> registeredSkills;
    private final Map<String, SkillTemplate> skillTemplates;
    
    public SkillRegistry(DeathZombieV4 plugin) {
        this.plugin = plugin;
        this.logger = plugin.getLogger();
        this.registeredSkills = new HashMap<>();
        this.skillTemplates = new HashMap<>();
        
        // 注册所有技能
        registerAllSkills();
        
        logger.info("技能注册器初始化完成，共注册 " + registeredSkills.size() + " 个技能");
    }
    
    /**
     * 注册所有技能
     */
    private void registerAllSkills() {
        // 注册被动技能
        registerPassiveSkills();
        
        // 注册主动技能
        registerActiveSkills();
        
        // 注册特殊技能
        registerSpecialSkills();
    }
    
    /**
     * 注册被动技能
     */
    private void registerPassiveSkills() {
        // 剧毒攻击技能
        SkillTemplate poisonAttackTemplate = new SkillTemplate("poison_attack", "剧毒攻击", 
                "攻击玩家时有概率造成中毒效果", 
                SkillTemplate.SkillCategory.ATTACK, SkillTemplate.SkillType.PASSIVE, "ID5")
            .addParameter("poison_level", new SkillTemplate.ParameterConfig(
                "poison_level", "中毒等级", "中毒效果的等级", 
                Integer.class, 1, "级").setMinValue(1).setMaxValue(10))
            .addParameter("poison_duration", new SkillTemplate.ParameterConfig(
                "poison_duration", "持续时间", "中毒效果持续时间", 
                Integer.class, 60, "tick").setMinValue(20).setMaxValue(1200))
            .addParameter("poison_chance", new SkillTemplate.ParameterConfig(
                "poison_chance", "触发概率", "攻击时触发中毒的概率", 
                Double.class, 1.0, "%").setMinValue(0.1).setMaxValue(1.0))
            .setEffectConfig(new SkillTemplate.EffectConfig()
                .addParticle(Particle.VILLAGER_ANGRY)
                .addPotionEffect(PotionEffectType.POISON));
        
        registerSkill("poison_attack", poisonAttackTemplate, new PoisonAttackSkill(plugin, poisonAttackTemplate));
        
        // TODO: 注册更多被动技能
        // - 生命汲取
        // - 护盾反射
        // - 狂暴模式
        // - 虚弱光环
    }
    
    /**
     * 注册主动技能
     */
    private void registerActiveSkills() {
        // 电击攻击技能
        SkillTemplate electricAttackTemplate = new SkillTemplate("electric_attack", "电击攻击", 
                "对范围内玩家造成电击伤害", 
                SkillTemplate.SkillCategory.ATTACK, SkillTemplate.SkillType.ACTIVE_TIMED, "ID13")
            .addParameter("attack_interval", new SkillTemplate.ParameterConfig(
                "attack_interval", "攻击间隔", "电击攻击的时间间隔", 
                Integer.class, 60, "tick").setMinValue(20).setMaxValue(200))
            .addParameter("attack_range", new SkillTemplate.ParameterConfig(
                "attack_range", "攻击范围", "电击的影响范围", 
                Double.class, 8.0, "格").setMinValue(3.0).setMaxValue(20.0))
            .addParameter("electric_damage", new SkillTemplate.ParameterConfig(
                "electric_damage", "电击伤害", "每次电击造成的伤害", 
                Double.class, 6.0, "点").setMinValue(1.0).setMaxValue(15.0))
            .setEffectConfig(new SkillTemplate.EffectConfig()
                .addParticle(Particle.ENCHANTED_HIT)
                .addSound(Sound.ENTITY_LIGHTNING_BOLT_THUNDER));
        
        registerSkill("electric_attack", electricAttackTemplate, new ElectricAttackSkill(plugin, electricAttackTemplate));
        
        // 跳跃攻击技能
        SkillTemplate jumpAttackTemplate = new SkillTemplate("jump_attack", "跳跃攻击", 
                "跳向玩家进行攻击", 
                SkillTemplate.SkillCategory.MOVEMENT, SkillTemplate.SkillType.ACTIVE_TIMED, "ID8")
            .addParameter("jump_interval", new SkillTemplate.ParameterConfig(
                "jump_interval", "跳跃间隔", "跳跃攻击的时间间隔", 
                Integer.class, 100, "tick").setMinValue(40).setMaxValue(300))
            .addParameter("jump_range", new SkillTemplate.ParameterConfig(
                "jump_range", "跳跃范围", "跳跃的最大距离", 
                Double.class, 10.0, "格").setMinValue(3.0).setMaxValue(20.0))
            .addParameter("jump_damage", new SkillTemplate.ParameterConfig(
                "jump_damage", "跳跃伤害", "落地时造成的伤害", 
                Double.class, 4.0, "点").setMinValue(1.0).setMaxValue(15.0))
            .setEffectConfig(new SkillTemplate.EffectConfig()
                .addParticle(Particle.CLOUD)
                .addSound(Sound.ENTITY_SLIME_JUMP));
        
        registerSkill("jump_attack", jumpAttackTemplate, new JumpAttackSkill(plugin, jumpAttackTemplate));
        
        // TODO: 注册更多主动技能
        // - 传送攻击
        // - 冰冻攻击
        // - 雷霆攻击
        // - 召唤仆从
        // - 烈焰攻击
    }
    
    /**
     * 注册特殊技能
     */
    private void registerSpecialSkills() {
        // TODO: 注册特殊技能
        // - 死亡技能（爆炸攻击、分裂召唤）
        // - 触发技能（受伤时触发）
        // - 环境技能（蜘蛛网陷阱）
    }
    
    /**
     * 注册单个技能
     */
    private void registerSkill(String skillId, SkillTemplate template, ISkill skill) {
        registeredSkills.put(skillId, skill);
        skillTemplates.put(skillId, template);
        logger.info("已注册技能: " + template.getSkillName() + " (" + skillId + ")");
    }
    
    /**
     * 获取技能实例
     */
    public ISkill getSkill(String skillId) {
        return registeredSkills.get(skillId);
    }
    
    /**
     * 获取技能模板
     */
    public SkillTemplate getSkillTemplate(String skillId) {
        return skillTemplates.get(skillId);
    }
    
    /**
     * 获取所有技能模板
     */
    public Map<String, SkillTemplate> getAllSkillTemplates() {
        return new HashMap<>(skillTemplates);
    }
    
    /**
     * 获取所有技能实例
     */
    public Map<String, ISkill> getAllSkills() {
        return new HashMap<>(registeredSkills);
    }
    
    /**
     * 检查技能是否存在
     */
    public boolean hasSkill(String skillId) {
        return registeredSkills.containsKey(skillId);
    }
    
    /**
     * 获取技能数量
     */
    public int getSkillCount() {
        return registeredSkills.size();
    }
    
    /**
     * 清理所有技能
     */
    public void cleanup() {
        // 清理所有技能的静态数据
        AbstractSkill.cleanupAll();
        
        registeredSkills.clear();
        skillTemplates.clear();
        
        logger.info("技能注册器已清理");
    }
}
