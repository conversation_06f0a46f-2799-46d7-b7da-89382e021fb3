package org.Ver_zhzh.customZombie.UserMaker.skill;

import org.bukkit.entity.LivingEntity;
import org.bukkit.scheduler.BukkitRunnable;
import org.bukkit.scheduler.BukkitTask;
import org.Ver_zhzh.customZombie.UserMaker.IdzEntityConfig;
import org.Ver_zhzh.customZombie.UserMaker.SkillTemplate;
import org.Ver_zhzh.deathZombieV4.DeathZombieV4;

/**
 * 主动技能抽象类 - 定时执行的主动技能
 * 
 * <AUTHOR>
 * @version 1.0
 */
public abstract class ActiveSkill extends AbstractSkill {
    
    public ActiveSkill(DeathZombieV4 plugin, SkillTemplate template) {
        super(plugin, template);
    }
    
    @Override
    public void activate(LivingEntity entity, IdzEntityConfig.SkillConfig skillConfig) {
        if (!canActivate(entity, skillConfig)) {
            return;
        }
        
        // 获取技能间隔
        int interval = getSkillInterval(skillConfig);
        int initialDelay = getInitialDelay(skillConfig);
        
        // 创建定时任务
        BukkitTask task = new BukkitRunnable() {
            @Override
            public void run() {
                if (entity == null || entity.isDead() || !entity.isValid()) {
                    this.cancel();
                    return;
                }
                
                // 检查是否在冷却中
                if (isOnCooldown(entity)) {
                    return;
                }
                
                // 执行技能效果
                boolean success = executeSkill(entity, skillConfig);
                
                if (success) {
                    // 设置冷却
                    long cooldown = getCooldown(skillConfig);
                    if (cooldown > 0) {
                        setCooldown(entity, cooldown);
                    }
                }
            }
        }.runTaskTimer(plugin, initialDelay, interval);
        
        // 添加任务到管理器
        addTask(entity, task);
        
        logger.info("主动技能已激活: " + template.getSkillName() + " for " + entity.getUniqueId());
    }
    
    @Override
    public void deactivate(LivingEntity entity) {
        // 移除并取消任务
        removeTask(entity);
        
        // 执行技能特定的停用逻辑
        deactivateActive(entity);
        
        logger.info("主动技能已停用: " + template.getSkillName() + " for " + entity.getUniqueId());
    }
    
    @Override
    protected boolean canActivateSpecific(LivingEntity entity, IdzEntityConfig.SkillConfig skillConfig) {
        // 主动技能可以重复激活，但需要检查是否已有任务在运行
        return true;
    }
    
    @Override
    protected SkillValidationResult validateConfigSpecific(IdzEntityConfig.SkillConfig skillConfig) {
        // 验证间隔参数
        int interval = getSkillInterval(skillConfig);
        if (interval <= 0) {
            return SkillValidationResult.error("技能间隔必须大于0");
        }
        
        return SkillValidationResult.success();
    }
    
    /**
     * 获取技能执行间隔
     */
    protected int getSkillInterval(IdzEntityConfig.SkillConfig skillConfig) {
        // 默认查找常见的间隔参数名
        String[] intervalParams = {"interval", "attack_interval", "skill_interval", "execute_interval"};
        
        for (String param : intervalParams) {
            Object value = skillConfig.getParameters().get(param);
            if (value instanceof Number) {
                return ((Number) value).intValue();
            }
        }
        
        // 默认间隔
        return 100; // 5秒
    }
    
    /**
     * 获取初始延迟
     */
    protected int getInitialDelay(IdzEntityConfig.SkillConfig skillConfig) {
        Object value = skillConfig.getParameters().get("initial_delay");
        if (value instanceof Number) {
            return ((Number) value).intValue();
        }
        return 0; // 默认无延迟
    }
    
    /**
     * 执行技能效果
     * @param entity 实体
     * @param skillConfig 技能配置
     * @return 是否成功执行
     */
    protected abstract boolean executeSkill(LivingEntity entity, IdzEntityConfig.SkillConfig skillConfig);
    
    /**
     * 停用主动技能的特定逻辑
     */
    protected abstract void deactivateActive(LivingEntity entity);
}
