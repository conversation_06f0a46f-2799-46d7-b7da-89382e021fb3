package org.Ver_zhzh.customZombie.UserMaker.skill.impl;

import org.bukkit.Bukkit;
import org.bukkit.Location;
import org.bukkit.Particle;
import org.bukkit.Sound;
import org.bukkit.entity.LivingEntity;
import org.bukkit.entity.Player;
import org.bukkit.util.Vector;
import org.Ver_zhzh.customZombie.UserMaker.IdzEntityConfig;
import org.Ver_zhzh.customZombie.UserMaker.SkillTemplate;
import org.Ver_zhzh.customZombie.UserMaker.skill.ActiveSkill;
import org.Ver_zhzh.deathZombieV4.DeathZombieV4;

/**
 * 跳跃攻击技能 - 跳向玩家进行攻击
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class JumpAttackSkill extends ActiveSkill {
    
    public JumpAttackSkill(DeathZombieV4 plugin, SkillTemplate template) {
        super(plugin, template);
    }
    
    @Override
    protected boolean executeSkill(LivingEntity entity, IdzEntityConfig.SkillConfig skillConfig) {
        Location entityLocation = entity.getLocation();
        
        // 获取技能参数
        double jumpRange = getSkillParameter(skillConfig, "jump_range", 10.0);
        double jumpDamage = getSkillParameter(skillConfig, "jump_damage", 4.0);
        
        // 查找最近的玩家
        Player nearestPlayer = null;
        double nearestDistance = Double.MAX_VALUE;
        
        for (Player player : entityLocation.getWorld().getPlayers()) {
            double distance = player.getLocation().distance(entityLocation);
            if (distance < nearestDistance && distance <= jumpRange && distance > 2.0) {
                nearestDistance = distance;
                nearestPlayer = player;
            }
        }
        
        if (nearestPlayer == null) {
            return false;
        }
        
        // 计算跳跃向量
        Location playerLocation = nearestPlayer.getLocation();
        double deltaX = playerLocation.getX() - entityLocation.getX();
        double deltaZ = playerLocation.getZ() - entityLocation.getZ();
        double deltaY = 0.5; // 向上跳跃
        
        // 标准化水平方向并应用跳跃力度
        double horizontalDistance = Math.sqrt(deltaX * deltaX + deltaZ * deltaZ);
        if (horizontalDistance > 0) {
            double jumpForce = Math.min(0.8, horizontalDistance * 0.3);
            deltaX = (deltaX / horizontalDistance) * jumpForce;
            deltaZ = (deltaZ / horizontalDistance) * jumpForce;
        }
        
        // 应用跳跃速度
        Vector jumpVector = new Vector(deltaX, deltaY, deltaZ);
        entity.setVelocity(entity.getVelocity().add(jumpVector));
        
        // 播放跳跃效果
        entityLocation.getWorld().spawnParticle(Particle.CLOUD, 
            entityLocation, 10, 1, 0.5, 1, 0.1);
        entityLocation.getWorld().playSound(entityLocation, 
            Sound.ENTITY_SLIME_JUMP, 1.0f, 1.0f);
        
        // 延迟造成伤害（落地时）
        final Player targetPlayer = nearestPlayer;
        Bukkit.getScheduler().runTaskLater(plugin, () -> {
            if (entity.isValid() && !entity.isDead() && targetPlayer.isOnline()) {
                double landingDistance = targetPlayer.getLocation().distance(entity.getLocation());
                if (landingDistance <= 3.0) {
                    targetPlayer.damage(jumpDamage);
                    
                    // 播放落地效果
                    Location landingLocation = entity.getLocation();
                    landingLocation.getWorld().spawnParticle(Particle.BLOCK_CRACK, 
                        landingLocation, 20, 1, 0.1, 1, 0.1);
                    landingLocation.getWorld().playSound(landingLocation, 
                        Sound.ENTITY_GENERIC_EXPLODE, 0.5f, 1.5f);
                }
            }
        }, 20L); // 1秒后检查落地伤害
        
        return true;
    }
    
    @Override
    protected void deactivateActive(LivingEntity entity) {
        // 主动技能无需额外停用逻辑
    }
    
    @Override
    protected SkillValidationResult validateConfigSpecific(IdzEntityConfig.SkillConfig skillConfig) {
        // 验证跳跃范围
        Double jumpRange = getSkillParameter(skillConfig, "jump_range", null);
        if (jumpRange != null && (jumpRange < 3.0 || jumpRange > 30.0)) {
            return SkillValidationResult.error("跳跃范围必须在3.0-30.0之间");
        }
        
        // 验证跳跃伤害
        Double jumpDamage = getSkillParameter(skillConfig, "jump_damage", null);
        if (jumpDamage != null && (jumpDamage < 0.5 || jumpDamage > 20.0)) {
            return SkillValidationResult.error("跳跃伤害必须在0.5-20.0之间");
        }
        
        return SkillValidationResult.success();
    }
}
