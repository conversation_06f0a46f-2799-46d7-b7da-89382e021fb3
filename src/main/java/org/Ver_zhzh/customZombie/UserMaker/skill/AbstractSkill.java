package org.Ver_zhzh.customZombie.UserMaker.skill;

import org.bukkit.entity.LivingEntity;
import org.bukkit.metadata.FixedMetadataValue;
import org.bukkit.metadata.MetadataValue;
import org.bukkit.scheduler.BukkitTask;
import org.Ver_zhzh.customZombie.UserMaker.IdzEntityConfig;
import org.Ver_zhzh.customZombie.UserMaker.SkillTemplate;
import org.Ver_zhzh.deathZombieV4.DeathZombieV4;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.logging.Logger;

/**
 * 技能抽象基类 - 提供技能的通用实现
 * 
 * <AUTHOR>
 * @version 1.0
 */
public abstract class AbstractSkill implements ISkill {
    
    protected final DeathZombieV4 plugin;
    protected final Logger logger;
    protected final SkillTemplate template;
    
    // 技能任务管理
    private static final Map<UUID, Map<String, BukkitTask>> entityTasks = new ConcurrentHashMap<>();
    
    // 技能冷却管理
    private static final Map<UUID, Map<String, Long>> entityCooldowns = new ConcurrentHashMap<>();
    
    public AbstractSkill(DeathZombieV4 plugin, SkillTemplate template) {
        this.plugin = plugin;
        this.logger = plugin.getLogger();
        this.template = template;
    }
    
    @Override
    public SkillTemplate getTemplate() {
        return template;
    }
    
    @Override
    public boolean canActivate(LivingEntity entity, IdzEntityConfig.SkillConfig skillConfig) {
        if (entity == null || entity.isDead() || !entity.isValid()) {
            return false;
        }
        
        if (!skillConfig.isEnabled()) {
            return false;
        }
        
        if (isOnCooldown(entity)) {
            return false;
        }
        
        return canActivateSpecific(entity, skillConfig);
    }
    
    /**
     * 子类特定的激活条件检查
     */
    protected abstract boolean canActivateSpecific(LivingEntity entity, IdzEntityConfig.SkillConfig skillConfig);
    
    @Override
    public long getCooldown(IdzEntityConfig.SkillConfig skillConfig) {
        // 默认从技能配置中获取冷却时间
        Object cooldownObj = skillConfig.getParameters().get("cooldown");
        if (cooldownObj instanceof Number) {
            return ((Number) cooldownObj).longValue() * 50; // tick转毫秒
        }
        return 0;
    }
    
    @Override
    public boolean isOnCooldown(LivingEntity entity) {
        UUID entityId = entity.getUniqueId();
        String skillId = template.getSkillId();
        
        Map<String, Long> cooldowns = entityCooldowns.get(entityId);
        if (cooldowns == null) {
            return false;
        }
        
        Long cooldownEnd = cooldowns.get(skillId);
        if (cooldownEnd == null) {
            return false;
        }
        
        return System.currentTimeMillis() < cooldownEnd;
    }
    
    @Override
    public void setCooldown(LivingEntity entity, long cooldown) {
        if (cooldown <= 0) {
            return;
        }
        
        UUID entityId = entity.getUniqueId();
        String skillId = template.getSkillId();
        
        entityCooldowns.computeIfAbsent(entityId, k -> new ConcurrentHashMap<>())
                      .put(skillId, System.currentTimeMillis() + cooldown);
    }
    
    @Override
    public SkillValidationResult validateConfig(IdzEntityConfig.SkillConfig skillConfig) {
        if (skillConfig == null) {
            return SkillValidationResult.error("技能配置不能为空");
        }
        
        if (!template.getSkillId().equals(skillConfig.getSkillId())) {
            return SkillValidationResult.error("技能ID不匹配");
        }
        
        // 验证必需参数
        for (Map.Entry<String, SkillTemplate.ParameterConfig> entry : template.getParameterConfigs().entrySet()) {
            String paramName = entry.getKey();
            SkillTemplate.ParameterConfig paramConfig = entry.getValue();
            
            if (paramConfig.isRequired() && !skillConfig.getParameters().containsKey(paramName)) {
                return SkillValidationResult.error("缺少必需参数: " + paramName);
            }
            
            Object value = skillConfig.getParameters().get(paramName);
            if (value != null) {
                SkillValidationResult result = validateParameter(paramName, value, paramConfig);
                if (!result.isValid()) {
                    return result;
                }
            }
        }
        
        return validateConfigSpecific(skillConfig);
    }
    
    /**
     * 子类特定的配置验证
     */
    protected abstract SkillValidationResult validateConfigSpecific(IdzEntityConfig.SkillConfig skillConfig);
    
    /**
     * 验证单个参数
     */
    private SkillValidationResult validateParameter(String paramName, Object value, SkillTemplate.ParameterConfig config) {
        Class<?> expectedType = config.getDataType();
        
        // 类型检查
        if (!expectedType.isInstance(value)) {
            return SkillValidationResult.error(
                String.format("参数 %s 类型错误，期望 %s，实际 %s", 
                    paramName, expectedType.getSimpleName(), value.getClass().getSimpleName()));
        }
        
        // 范围检查
        if (value instanceof Number) {
            Number numValue = (Number) value;
            
            if (config.getMinValue() instanceof Number) {
                Number minValue = (Number) config.getMinValue();
                if (numValue.doubleValue() < minValue.doubleValue()) {
                    return SkillValidationResult.error(
                        String.format("参数 %s 值 %s 小于最小值 %s", paramName, numValue, minValue));
                }
            }
            
            if (config.getMaxValue() instanceof Number) {
                Number maxValue = (Number) config.getMaxValue();
                if (numValue.doubleValue() > maxValue.doubleValue()) {
                    return SkillValidationResult.error(
                        String.format("参数 %s 值 %s 大于最大值 %s", paramName, numValue, maxValue));
                }
            }
        }
        
        return SkillValidationResult.success();
    }
    
    /**
     * 添加任务到实体任务列表
     */
    protected void addTask(LivingEntity entity, BukkitTask task) {
        UUID entityId = entity.getUniqueId();
        String skillId = template.getSkillId();
        
        entityTasks.computeIfAbsent(entityId, k -> new ConcurrentHashMap<>())
                   .put(skillId, task);
    }
    
    /**
     * 移除实体的技能任务
     */
    protected void removeTask(LivingEntity entity) {
        UUID entityId = entity.getUniqueId();
        String skillId = template.getSkillId();
        
        Map<String, BukkitTask> tasks = entityTasks.get(entityId);
        if (tasks != null) {
            BukkitTask task = tasks.remove(skillId);
            if (task != null && !task.isCancelled()) {
                task.cancel();
            }
            
            if (tasks.isEmpty()) {
                entityTasks.remove(entityId);
            }
        }
    }
    
    /**
     * 设置实体元数据
     */
    protected void setEntityMetadata(LivingEntity entity, String key, Object value) {
        entity.setMetadata(key, new FixedMetadataValue(plugin, value));
    }
    
    /**
     * 获取实体元数据
     */
    @SuppressWarnings("unchecked")
    protected <T> T getEntityMetadata(LivingEntity entity, String key, T defaultValue) {
        if (entity.hasMetadata(key)) {
            MetadataValue metadataValue = entity.getMetadata(key).get(0);
            try {
                return (T) metadataValue.value();
            } catch (ClassCastException e) {
                logger.warning("元数据类型转换失败: " + key + " -> " + e.getMessage());
                return defaultValue;
            }
        }
        return defaultValue;
    }
    
    /**
     * 移除实体元数据
     */
    protected void removeEntityMetadata(LivingEntity entity, String key) {
        entity.removeMetadata(key, plugin);
    }
    
    /**
     * 获取技能参数
     */
    @SuppressWarnings("unchecked")
    protected <T> T getSkillParameter(IdzEntityConfig.SkillConfig skillConfig, String paramName, T defaultValue) {
        Object value = skillConfig.getParameters().get(paramName);
        if (value == null) {
            return defaultValue;
        }
        
        try {
            return (T) value;
        } catch (ClassCastException e) {
            logger.warning("技能参数类型转换失败: " + paramName + " -> " + e.getMessage());
            return defaultValue;
        }
    }
    
    /**
     * 清理所有实体的技能数据
     */
    public static void cleanupAll() {
        // 取消所有任务
        for (Map<String, BukkitTask> tasks : entityTasks.values()) {
            for (BukkitTask task : tasks.values()) {
                if (task != null && !task.isCancelled()) {
                    task.cancel();
                }
            }
        }
        entityTasks.clear();
        
        // 清理冷却数据
        entityCooldowns.clear();
    }
    
    /**
     * 清理特定实体的技能数据
     */
    public static void cleanupEntity(UUID entityId) {
        // 取消实体的所有任务
        Map<String, BukkitTask> tasks = entityTasks.remove(entityId);
        if (tasks != null) {
            for (BukkitTask task : tasks.values()) {
                if (task != null && !task.isCancelled()) {
                    task.cancel();
                }
            }
        }
        
        // 清理冷却数据
        entityCooldowns.remove(entityId);
    }
}
