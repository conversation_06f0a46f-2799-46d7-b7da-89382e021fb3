package org.Ver_zhzh.customZombie.UserMaker.skill.impl;

import org.bukkit.Location;
import org.bukkit.Particle;
import org.bukkit.Sound;
import org.bukkit.entity.LivingEntity;
import org.bukkit.entity.Player;
import org.Ver_zhzh.customZombie.UserMaker.IdzEntityConfig;
import org.Ver_zhzh.customZombie.UserMaker.SkillTemplate;
import org.Ver_zhzh.customZombie.UserMaker.skill.ActiveSkill;
import org.Ver_zhzh.deathZombieV4.DeathZombieV4;

/**
 * 电击攻击技能 - 对范围内玩家造成电击伤害
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class ElectricAttackSkill extends ActiveSkill {
    
    public ElectricAttackSkill(DeathZombieV4 plugin, SkillTemplate template) {
        super(plugin, template);
    }
    
    @Override
    protected boolean executeSkill(LivingEntity entity, IdzEntityConfig.SkillConfig skillConfig) {
        Location entityLocation = entity.getLocation();
        
        // 获取技能参数
        double attackRange = getSkillParameter(skillConfig, "attack_range", 8.0);
        double electricDamage = getSkillParameter(skillConfig, "electric_damage", 6.0);
        
        boolean hitAnyTarget = false;
        
        // 查找范围内的玩家
        for (Player player : entityLocation.getWorld().getPlayers()) {
            double distance = player.getLocation().distance(entityLocation);
            if (distance <= attackRange) {
                // 造成电击伤害
                player.damage(electricDamage);
                
                // 播放电击效果
                Location playerLocation = player.getLocation().add(0, 1, 0);
                player.getWorld().spawnParticle(Particle.ENCHANTED_HIT, 
                    playerLocation, 10, 0.5, 0.5, 0.5, 0.1);
                player.getWorld().playSound(playerLocation, 
                    Sound.ENTITY_LIGHTNING_BOLT_THUNDER, 0.5f, 2.0f);
                
                hitAnyTarget = true;
            }
        }
        
        if (hitAnyTarget) {
            // 在实体周围播放电击粒子
            Location effectLocation = entityLocation.add(0, 1, 0);
            entityLocation.getWorld().spawnParticle(Particle.ELECTRIC_SPARK, 
                effectLocation, 15, 1, 1, 1, 0.1);
        }
        
        return hitAnyTarget;
    }
    
    @Override
    protected void deactivateActive(LivingEntity entity) {
        // 主动技能无需额外停用逻辑
    }
    
    @Override
    protected SkillValidationResult validateConfigSpecific(IdzEntityConfig.SkillConfig skillConfig) {
        // 验证攻击范围
        Double attackRange = getSkillParameter(skillConfig, "attack_range", null);
        if (attackRange != null && (attackRange < 1.0 || attackRange > 50.0)) {
            return SkillValidationResult.error("攻击范围必须在1.0-50.0之间");
        }
        
        // 验证电击伤害
        Double electricDamage = getSkillParameter(skillConfig, "electric_damage", null);
        if (electricDamage != null && (electricDamage < 0.5 || electricDamage > 50.0)) {
            return SkillValidationResult.error("电击伤害必须在0.5-50.0之间");
        }
        
        return SkillValidationResult.success();
    }
}
