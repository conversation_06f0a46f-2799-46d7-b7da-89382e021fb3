package org.Ver_zhzh.customZombie.UserMaker;

import org.bukkit.ChatColor;
import org.bukkit.Location;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.command.TabCompleter;
import org.bukkit.entity.LivingEntity;
import org.bukkit.entity.Player;
import org.Ver_zhzh.deathZombieV4.DeathZombieV4;

import java.util.*;
import java.util.stream.Collectors;

/**
 * IDZ命令处理器 - 处理IDZ相关的游戏内命令
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class IdzCommand implements CommandExecutor, TabCompleter {
    
    private final DeathZombieV4 plugin;
    private final UserMaker userMaker;
    
    public IdzCommand(DeathZombieV4 plugin, UserMaker userMaker) {
        this.plugin = plugin;
        this.userMaker = userMaker;
    }
    
    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        if (!sender.hasPermission("deathzombie.idz")) {
            sender.sendMessage(ChatColor.RED + "你没有权限使用IDZ命令");
            return true;
        }
        
        if (args.length == 0) {
            sendHelpMessage(sender);
            return true;
        }
        
        String subCommand = args[0].toLowerCase();
        
        switch (subCommand) {
            case "spawn":
                return handleSpawnCommand(sender, args);
            case "list":
                return handleListCommand(sender, args);
            case "info":
                return handleInfoCommand(sender, args);
            case "create":
                return handleCreateCommand(sender, args);
            case "delete":
                return handleDeleteCommand(sender, args);
            case "reload":
                return handleReloadCommand(sender, args);
            case "stats":
                return handleStatsCommand(sender, args);
            case "gui":
                return handleGuiCommand(sender, args);
            default:
                sender.sendMessage(ChatColor.RED + "未知的子命令: " + subCommand);
                sendHelpMessage(sender);
                return true;
        }
    }
    
    /**
     * 处理生成命令
     */
    private boolean handleSpawnCommand(CommandSender sender, String[] args) {
        if (args.length < 2) {
            sender.sendMessage(ChatColor.RED + "用法: /idz spawn <实体ID> [数量]");
            return true;
        }
        
        if (!(sender instanceof Player)) {
            sender.sendMessage(ChatColor.RED + "只有玩家可以使用此命令");
            return true;
        }
        
        Player player = (Player) sender;
        String entityId = args[1];
        int count = 1;
        
        if (args.length > 2) {
            try {
                count = Integer.parseInt(args[2]);
                if (count <= 0 || count > 10) {
                    sender.sendMessage(ChatColor.RED + "数量必须在1-10之间");
                    return true;
                }
            } catch (NumberFormatException e) {
                sender.sendMessage(ChatColor.RED + "无效的数量: " + args[2]);
                return true;
            }
        }
        
        IdzEntityConfig config = userMaker.getIdzConfig(entityId);
        if (config == null) {
            sender.sendMessage(ChatColor.RED + "未找到IDZ实体: " + entityId);
            return true;
        }
        
        Location spawnLocation = player.getLocation().add(2, 0, 2);
        int successCount = 0;
        
        for (int i = 0; i < count; i++) {
            Location loc = spawnLocation.clone().add(i * 2, 0, 0);
            LivingEntity entity = userMaker.spawnIdzEntity(entityId, loc);
            if (entity != null) {
                successCount++;
            }
        }
        
        if (successCount > 0) {
            sender.sendMessage(ChatColor.GREEN + "成功生成 " + successCount + " 个 " + config.getDisplayName());
        } else {
            sender.sendMessage(ChatColor.RED + "生成失败");
        }
        
        return true;
    }
    
    /**
     * 处理列表命令
     */
    private boolean handleListCommand(CommandSender sender, String[] args) {
        List<IdzEntityConfig> entities = userMaker.getAllIdzConfigs();
        
        if (entities.isEmpty()) {
            sender.sendMessage(ChatColor.YELLOW + "暂无IDZ实体配置");
            return true;
        }
        
        sender.sendMessage(ChatColor.GOLD + "=== IDZ实体列表 ===");
        for (IdzEntityConfig entity : entities) {
            String message = ChatColor.AQUA + entity.getEntityId() + 
                           ChatColor.WHITE + " - " + 
                           ChatColor.GREEN + entity.getDisplayName() + 
                           ChatColor.GRAY + " (" + entity.getEntityType() + ")";
            sender.sendMessage(message);
        }
        sender.sendMessage(ChatColor.GOLD + "总计: " + entities.size() + " 个实体");
        
        return true;
    }
    
    /**
     * 处理信息命令
     */
    private boolean handleInfoCommand(CommandSender sender, String[] args) {
        if (args.length < 2) {
            sender.sendMessage(ChatColor.RED + "用法: /idz info <实体ID>");
            return true;
        }
        
        String entityId = args[1];
        IdzEntityConfig config = userMaker.getIdzConfig(entityId);
        
        if (config == null) {
            sender.sendMessage(ChatColor.RED + "未找到IDZ实体: " + entityId);
            return true;
        }
        
        sender.sendMessage(ChatColor.GOLD + "=== " + config.getDisplayName() + " 信息 ===");
        sender.sendMessage(ChatColor.AQUA + "ID: " + ChatColor.WHITE + config.getEntityId());
        sender.sendMessage(ChatColor.AQUA + "类型: " + ChatColor.WHITE + config.getEntityType());
        sender.sendMessage(ChatColor.AQUA + "生命值: " + ChatColor.WHITE + config.getHealth());
        sender.sendMessage(ChatColor.AQUA + "攻击力: " + ChatColor.WHITE + config.getDamage());
        sender.sendMessage(ChatColor.AQUA + "速度: " + ChatColor.WHITE + config.getSpeed() + "x");
        sender.sendMessage(ChatColor.AQUA + "技能数量: " + ChatColor.WHITE + config.getSkills().size());
        sender.sendMessage(ChatColor.AQUA + "创建者: " + ChatColor.WHITE + config.getCreatedBy());
        
        if (!config.getDescription().isEmpty()) {
            sender.sendMessage(ChatColor.AQUA + "描述: " + ChatColor.WHITE + config.getDescription());
        }
        
        return true;
    }
    
    /**
     * 处理创建命令
     */
    private boolean handleCreateCommand(CommandSender sender, String[] args) {
        if (args.length < 3) {
            sender.sendMessage(ChatColor.RED + "用法: /idz create <实体ID> <显示名称>");
            return true;
        }
        
        String entityId = args[1];
        String displayName = String.join(" ", Arrays.copyOfRange(args, 2, args.length));
        
        try {
            IdzEntityConfig config = userMaker.createIdzConfig(entityId, sender.getName());
            config.setDisplayName(displayName);
            
            boolean success = userMaker.saveIdzConfig(config);
            if (success) {
                sender.sendMessage(ChatColor.GREEN + "成功创建IDZ实体: " + displayName + " (ID: " + entityId + ")");
                sender.sendMessage(ChatColor.YELLOW + "使用 /idz gui 打开网页编辑器进行详细配置");
            } else {
                sender.sendMessage(ChatColor.RED + "保存IDZ实体失败");
            }
        } catch (IllegalArgumentException e) {
            sender.sendMessage(ChatColor.RED + e.getMessage());
        }
        
        return true;
    }
    
    /**
     * 处理删除命令
     */
    private boolean handleDeleteCommand(CommandSender sender, String[] args) {
        if (args.length < 2) {
            sender.sendMessage(ChatColor.RED + "用法: /idz delete <实体ID>");
            return true;
        }
        
        String entityId = args[1];
        IdzEntityConfig config = userMaker.getIdzConfig(entityId);
        
        if (config == null) {
            sender.sendMessage(ChatColor.RED + "未找到IDZ实体: " + entityId);
            return true;
        }
        
        boolean success = userMaker.deleteIdzConfig(entityId);
        if (success) {
            sender.sendMessage(ChatColor.GREEN + "成功删除IDZ实体: " + config.getDisplayName());
        } else {
            sender.sendMessage(ChatColor.RED + "删除IDZ实体失败");
        }
        
        return true;
    }
    
    /**
     * 处理重载命令
     */
    private boolean handleReloadCommand(CommandSender sender, String[] args) {
        // TODO: 实现重载功能
        sender.sendMessage(ChatColor.GREEN + "IDZ系统已重载");
        return true;
    }
    
    /**
     * 处理统计命令
     */
    private boolean handleStatsCommand(CommandSender sender, String[] args) {
        Map<String, Object> stats = userMaker.getSystemStatistics();
        
        sender.sendMessage(ChatColor.GOLD + "=== IDZ系统统计 ===");
        sender.sendMessage(ChatColor.AQUA + "技能模板总数: " + ChatColor.WHITE + stats.get("totalSkills"));
        sender.sendMessage(ChatColor.AQUA + "IDZ实体数量: " + ChatColor.WHITE + stats.get("idzConfigCount"));
        
        @SuppressWarnings("unchecked")
        Map<String, Integer> skillsByCategory = (Map<String, Integer>) stats.get("skillsByCategory");
        if (skillsByCategory != null && !skillsByCategory.isEmpty()) {
            sender.sendMessage(ChatColor.AQUA + "技能分类统计:");
            for (Map.Entry<String, Integer> entry : skillsByCategory.entrySet()) {
                sender.sendMessage(ChatColor.GRAY + "  " + entry.getKey() + ": " + entry.getValue() + "个");
            }
        }
        
        return true;
    }
    
    /**
     * 处理GUI命令
     */
    private boolean handleGuiCommand(CommandSender sender, String[] args) {
        if (!(sender instanceof Player)) {
            sender.sendMessage(ChatColor.RED + "只有玩家可以使用此命令");
            return true;
        }
        
        Player player = (Player) sender;
        
        // 发送网页编辑器链接
        player.sendMessage(ChatColor.GOLD + "=== IDZ自定义怪物创建器 ===");
        player.sendMessage(ChatColor.GREEN + "请在浏览器中打开以下链接:");
        player.sendMessage(ChatColor.AQUA + "http://localhost:8080/idz-creator-vue.html");
        player.sendMessage(ChatColor.YELLOW + "提示: 确保服务器的Web服务已启动");
        
        return true;
    }
    
    /**
     * 发送帮助信息
     */
    private void sendHelpMessage(CommandSender sender) {
        sender.sendMessage(ChatColor.GOLD + "=== IDZ命令帮助 ===");
        sender.sendMessage(ChatColor.AQUA + "/idz spawn <实体ID> [数量]" + ChatColor.WHITE + " - 生成IDZ实体");
        sender.sendMessage(ChatColor.AQUA + "/idz list" + ChatColor.WHITE + " - 列出所有IDZ实体");
        sender.sendMessage(ChatColor.AQUA + "/idz info <实体ID>" + ChatColor.WHITE + " - 查看实体信息");
        sender.sendMessage(ChatColor.AQUA + "/idz create <实体ID> <名称>" + ChatColor.WHITE + " - 创建新实体");
        sender.sendMessage(ChatColor.AQUA + "/idz delete <实体ID>" + ChatColor.WHITE + " - 删除实体");
        sender.sendMessage(ChatColor.AQUA + "/idz gui" + ChatColor.WHITE + " - 打开网页编辑器");
        sender.sendMessage(ChatColor.AQUA + "/idz stats" + ChatColor.WHITE + " - 查看系统统计");
        sender.sendMessage(ChatColor.AQUA + "/idz reload" + ChatColor.WHITE + " - 重载配置");
    }
    
    @Override
    public List<String> onTabComplete(CommandSender sender, Command command, String alias, String[] args) {
        if (!sender.hasPermission("deathzombie.idz")) {
            return new ArrayList<>();
        }
        
        if (args.length == 1) {
            // 第一个参数：子命令
            List<String> subCommands = Arrays.asList("spawn", "list", "info", "create", "delete", "gui", "stats", "reload");
            return subCommands.stream()
                .filter(cmd -> cmd.toLowerCase().startsWith(args[0].toLowerCase()))
                .collect(Collectors.toList());
        } else if (args.length == 2) {
            String subCommand = args[0].toLowerCase();
            if ("spawn".equals(subCommand) || "info".equals(subCommand) || "delete".equals(subCommand)) {
                // 第二个参数：实体ID
                List<IdzEntityConfig> entities = userMaker.getAllIdzConfigs();
                return entities.stream()
                    .map(IdzEntityConfig::getEntityId)
                    .filter(id -> id.toLowerCase().startsWith(args[1].toLowerCase()))
                    .collect(Collectors.toList());
            }
        } else if (args.length == 3 && "spawn".equals(args[0].toLowerCase())) {
            // 第三个参数：数量
            return Arrays.asList("1", "2", "3", "5", "10");
        }
        
        return new ArrayList<>();
    }
}
