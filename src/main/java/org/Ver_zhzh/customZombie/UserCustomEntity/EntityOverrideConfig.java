package org.Ver_zhzh.customZombie.UserCustomEntity;

import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.entity.EntityType;

import java.util.HashMap;
import java.util.Map;

/**
 * 实体覆盖配置类
 * 存储单个IDC实体的覆盖设置
 *
 * <AUTHOR>
 * @version 1.0
 * 符合阿里巴巴编码规范
 */
public class EntityOverrideConfig {

    // 基础配置
    public boolean enabled = false;
    public double healthOverride = -1;
    public double damageOverride = -1;
    public double speedMultiplier = -1;
    public String customNameOverride = null;
    public String entityTypeOverride = null;

    // 装备覆盖
    public String weaponOverride = null;
    public String helmetOverride = null;
    public String chestplateOverride = null;
    public String leggingsOverride = null;
    public String bootsOverride = null;

    // 附魔配置
    public Map<String, Integer> weaponEnchantments = new HashMap<>();
    public Map<String, Integer> helmetEnchantments = new HashMap<>();
    public Map<String, Integer> chestplateEnchantments = new HashMap<>();
    public Map<String, Integer> leggingsEnchantments = new HashMap<>();
    public Map<String, Integer> bootsEnchantments = new HashMap<>();

    // 药水效果配置
    public Map<String, PotionEffectConfig> potionEffects = new HashMap<>();

    // 技能冷却时间覆盖
    public Map<String, Integer> skillCooldownOverrides = new HashMap<>();

    // 特殊能力配置
    public Map<String, Object> specialAbilities = new HashMap<>();

    // 护甲覆盖配置（支持复杂的护甲配置，如颜色等）
    public Map<String, Object> armorOverrides = new HashMap<>();

    /**
     * 从配置节加载设置
     *
     * @param section 配置节
     */
    public void loadFromConfig(ConfigurationSection section) {
        enabled = section.getBoolean("enabled", false);
        healthOverride = section.getDouble("health_override", -1);
        damageOverride = section.getDouble("damage_override", -1);
        speedMultiplier = section.getDouble("speed_multiplier", -1);
        customNameOverride = section.getString("custom_name_override");
        entityTypeOverride = section.getString("entity_type_override");

        // 装备覆盖
        weaponOverride = section.getString("weapon_override");
        helmetOverride = section.getString("helmet_override");
        chestplateOverride = section.getString("chestplate_override");
        leggingsOverride = section.getString("leggings_override");
        bootsOverride = section.getString("boots_override");

        // 加载附魔配置
        loadEnchantments(section, "weapon_enchantments", weaponEnchantments);
        loadEnchantments(section, "helmet_enchantments", helmetEnchantments);
        loadEnchantments(section, "chestplate_enchantments", chestplateEnchantments);
        loadEnchantments(section, "leggings_enchantments", leggingsEnchantments);
        loadEnchantments(section, "boots_enchantments", bootsEnchantments);

        // 加载药水效果
        loadPotionEffects(section);

        // 加载技能冷却时间覆盖
        loadSkillCooldownOverrides(section);

        // 加载特殊能力
        loadSpecialAbilities(section);

        // 加载护甲覆盖配置
        loadArmorOverrides(section);
    }

    /**
     * 加载附魔配置
     */
    private void loadEnchantments(ConfigurationSection section, String path, Map<String, Integer> enchantments) {
        ConfigurationSection enchantSection = section.getConfigurationSection(path);
        if (enchantSection != null) {
            for (String enchantName : enchantSection.getKeys(false)) {
                enchantments.put(enchantName, enchantSection.getInt(enchantName));
            }
        }
    }

    /**
     * 加载药水效果配置
     */
    private void loadPotionEffects(ConfigurationSection section) {
        ConfigurationSection potionSection = section.getConfigurationSection("potion_effects");
        if (potionSection != null) {
            for (String effectName : potionSection.getKeys(false)) {
                ConfigurationSection effectSection = potionSection.getConfigurationSection(effectName);
                if (effectSection != null) {
                    PotionEffectConfig effectConfig = new PotionEffectConfig();
                    effectConfig.level = effectSection.getInt("level", 0);
                    effectConfig.duration = effectSection.getInt("duration", -1);
                    potionEffects.put(effectName, effectConfig);
                }
            }
        }
    }

    /**
     * 加载技能冷却时间覆盖
     */
    private void loadSkillCooldownOverrides(ConfigurationSection section) {
        ConfigurationSection cooldownSection = section.getConfigurationSection("skill_cooldown_overrides");
        if (cooldownSection != null) {
            for (String skillName : cooldownSection.getKeys(false)) {
                skillCooldownOverrides.put(skillName, cooldownSection.getInt(skillName));
            }
        }
    }

    /**
     * 加载特殊能力配置
     */
    private void loadSpecialAbilities(ConfigurationSection section) {
        ConfigurationSection abilitiesSection = section.getConfigurationSection("special_abilities");
        if (abilitiesSection != null) {
            for (String abilityName : abilitiesSection.getKeys(false)) {
                Object value = abilitiesSection.get(abilityName);
                specialAbilities.put(abilityName, value);
            }
        }
    }

    /**
     * 加载护甲覆盖配置
     */
    private void loadArmorOverrides(ConfigurationSection section) {
        ConfigurationSection armorSection = section.getConfigurationSection("armor_overrides");
        if (armorSection != null) {
            for (String armorKey : armorSection.getKeys(false)) {
                Object value = armorSection.get(armorKey);
                armorOverrides.put(armorKey, value);
            }
        }
    }

    /**
     * 获取实体类型枚举
     */
    public EntityType getEntityType() {
        if (entityTypeOverride == null || entityTypeOverride.isEmpty()) {
            return null;
        }

        try {
            return EntityType.valueOf(entityTypeOverride.toUpperCase());
        } catch (IllegalArgumentException e) {
            return null;
        }
    }

    /**
     * 药水效果配置类
     */
    public static class PotionEffectConfig {
        public int level = 0;
        public int duration = -1;
    }
}
