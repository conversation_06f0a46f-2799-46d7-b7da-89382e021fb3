package org.Ver_zhzh.customZombie.UserCustomEntity;

import org.bukkit.Location;
import org.bukkit.entity.LivingEntity;
import org.bukkit.plugin.Plugin;

import java.util.logging.Logger;

/**
 * 双实体系统管理器
 * 管理原有IDC系统和新的UserCustomEntity系统之间的协调
 *
 * <AUTHOR>
 * @version 1.0
 * 符合阿里巴巴编码规范
 */
public class DualEntitySystemManager {

    private final Plugin plugin;
    private final Logger logger;
    private final UserCustomEntity userCustomEntity;
    private final Object originalEntitySpawner;
    private boolean initialized = false;

    /**
     * 构造函数
     *
     * @param plugin 插件实例
     * @param userCustomEntity UserCustomEntity实例
     * @param originalEntitySpawner 原有的实体生成器
     */
    public DualEntitySystemManager(Plugin plugin, UserCustomEntity userCustomEntity, Object originalEntitySpawner) {
        this.plugin = plugin;
        this.logger = plugin.getLogger();
        this.userCustomEntity = userCustomEntity;
        this.originalEntitySpawner = originalEntitySpawner;
        this.initialized = true;

        logger.info("双实体系统管理器初始化完成");
    }

    /**
     * 直接在指定位置生成自定义实体
     *
     * @param location 生成位置
     * @param entityId 实体ID
     * @return 生成的实体，失败时返回null
     */
    public LivingEntity spawnCustomEntityDirect(Location location, String entityId) {
        if (!initialized) {
            logger.severe("双实体系统未初始化");
            return null;
        }

        // 检查是否为支持的ID（目前支持IDC1-IDC25）
        if (!isSupportedEntityId(entityId)) {
            if (userCustomEntity.isDebugMode()) {
                logger.info("实体ID " + entityId + " 不在双系统支持范围内，使用原有系统");
            }
            return spawnWithOriginalSystem(location, entityId);
        }

        if (userCustomEntity.isDebugMode()) {
            logger.info("尝试使用双系统生成实体: " + entityId);
        }

        // 简化的生成策略：只使用启用的系统，不再有fallback逻辑
        boolean userCustomEnabled = userCustomEntity.isUserCustomEnabled();
        boolean defaultEnabled = userCustomEntity.isDefaultEnabled();

        // 优先使用用户自定义系统（如果启用）
        if (userCustomEnabled) {
            LivingEntity userEntity = userCustomEntity.spawnUserCustomEntityDirect(location, entityId);
            if (userEntity != null) {
                if (userCustomEntity.isDebugMode()) {
                    logger.info("使用用户自定义系统成功生成: " + entityId);
                }
                return userEntity;
            }
            // 用户自定义系统生成失败，直接返回null，不再尝试默认系统
            if (userCustomEntity.isDebugMode()) {
                logger.info("用户自定义系统生成失败: " + entityId);
            }
            return null;
        }

        // 如果用户自定义系统未启用，使用默认系统
        if (defaultEnabled) {
            LivingEntity entity = spawnWithOriginalSystem(location, entityId);
            if (entity != null) {
                if (userCustomEntity.isDebugMode()) {
                    logger.info("使用默认系统成功生成: " + entityId);
                }
                return entity;
            }
            // 默认系统生成失败，直接返回null
            if (userCustomEntity.isDebugMode()) {
                logger.info("默认系统生成失败: " + entityId);
            }
            return null;
        }

        if (userCustomEntity.isDebugMode()) {
            logger.warning("所有系统都未启用，无法生成实体: " + entityId);
        }
        return null;
    }

    /**
     * 使用原有系统生成实体
     */
    private LivingEntity spawnWithOriginalSystem(Location location, String entityId) {
        try {
            if (originalEntitySpawner != null) {
                // 通过反射调用AdvancedEntitySpawner的生成方法
                if (userCustomEntity.isDebugMode()) {
                    logger.info("调用原有系统生成实体: " + entityId);
                }

                // 尝试调用AdvancedEntitySpawner的spawnAdvancedEntity方法
                try {
                    java.lang.reflect.Method spawnMethod = originalEntitySpawner.getClass().getMethod("spawnAdvancedEntity", Location.class, String.class);
                    boolean success = (Boolean) spawnMethod.invoke(originalEntitySpawner, location, entityId);

                    if (success) {
                        // 查找刚生成的实体
                        for (org.bukkit.entity.Entity entity : location.getWorld().getEntities()) {
                            if (entity instanceof LivingEntity && entity.getLocation().distanceSquared(location) < 4) {
                                // 检查是否有正确的标记
                                if (entity.hasMetadata("idcZombieEntity") || entity.hasMetadata("gameEntity")) {
                                    if (userCustomEntity.isDebugMode()) {
                                        logger.info("原有系统成功生成实体: " + entityId + ", 类型: " + entity.getType());
                                    }
                                    return (LivingEntity) entity;
                                }
                            }
                        }
                    }
                } catch (NoSuchMethodException e) {
                    // 如果没有spawnAdvancedEntity方法，尝试其他方法
                    if (userCustomEntity.isDebugMode()) {
                        logger.info("未找到spawnAdvancedEntity方法，尝试其他生成方式");
                    }
                }

                // 尝试直接调用特定的生成方法
                if ("idc22".equals(entityId)) {
                    try {
                        java.lang.reflect.Method spawnMutationKingMethod = originalEntitySpawner.getClass().getMethod("spawnMutationKing", Location.class);
                        boolean success = (Boolean) spawnMutationKingMethod.invoke(originalEntitySpawner, location);

                        if (success) {
                            // 查找刚生成的异变之王
                            for (org.bukkit.entity.Entity entity : location.getWorld().getEntities()) {
                                if (entity instanceof org.bukkit.entity.EnderDragon && entity.getLocation().distanceSquared(location) < 16) {
                                    if (userCustomEntity.isDebugMode()) {
                                        logger.info("原有系统成功生成IDC22异变之王");
                                    }
                                    return (LivingEntity) entity;
                                }
                            }
                        }
                    } catch (NoSuchMethodException e) {
                        if (userCustomEntity.isDebugMode()) {
                            logger.info("未找到spawnMutationKing方法");
                        }
                    }
                }
            }

            if (userCustomEntity.isDebugMode()) {
                logger.info("原有系统无法生成实体: " + entityId);
            }
            return null;
        } catch (Exception e) {
            logger.warning("原有系统生成实体失败: " + e.getMessage());
            return null;
        }
    }

    /**
     * 检查实体ID是否被支持
     */
    private boolean isSupportedEntityId(String entityId) {
        return userCustomEntity.isSupportedEntityId(entityId);
    }

    /**
     * 获取系统状态信息
     */
    public String getSystemStatus() {
        StringBuilder status = new StringBuilder();
        status.append("双实体系统状态:\n");
        status.append("- 初始化状态: ").append(initialized).append("\n");
        status.append("- 用户自定义系统启用: ").append(userCustomEntity.isUserCustomEnabled()).append("\n");
        status.append("- 默认系统启用: ").append(userCustomEntity.isDefaultEnabled()).append("\n");

        status.append("- 调试模式: ").append(userCustomEntity.isDebugMode()).append("\n");
        status.append("- 已加载配置数量: ").append(userCustomEntity.getConfigCache().size());
        return status.toString();
    }

    /**
     * 重载配置
     */
    public void reloadConfig() {
        userCustomEntity.reloadConfig();
        logger.info("双实体系统配置已重载");
    }

    /**
     * 检查系统是否已初始化
     */
    public boolean isInitialized() {
        return initialized;
    }
}
