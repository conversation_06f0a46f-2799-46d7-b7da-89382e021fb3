package org.Ver_zhzh.customZombie.UserCustomEntity;

import org.bukkit.Particle;
import org.bukkit.Sound;
import org.bukkit.entity.LivingEntity;
import org.bukkit.entity.Piglin;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.entity.EntityDamageByEntityEvent;
import org.bukkit.event.entity.ProjectileHitEvent;
import org.bukkit.plugin.java.JavaPlugin;
import org.bukkit.entity.Snowball;

import java.util.logging.Logger;

/**
 * UserCustomEntity系统的事件监听器
 * 处理自定义实体的特殊事件，如攻击回血等
 */
public class UserCustomEntityEventListener implements Listener {
    
    private final JavaPlugin plugin;
    private final Logger logger;
    private final boolean debugMode;
    
    public UserCustomEntityEventListener(JavaPlugin plugin, boolean debugMode) {
        this.plugin = plugin;
        this.logger = plugin.getLogger();
        this.debugMode = debugMode;
    }
    
    /**
     * 处理实体攻击事件
     * 主要用于鲜血猪灵的攻击回血功能
     */
    @EventHandler
    public void onEntityDamageByEntity(EntityDamageByEntityEvent event) {
        // 处理鲜血猪灵攻击玩家的情况
        if (event.getEntity() instanceof Player && event.getDamager() instanceof Piglin) {
            Piglin piglin = (Piglin) event.getDamager();
            Player player = (Player) event.getEntity();
            
            // 检查是否是鲜血猪灵（IDC15）
            if (piglin.hasMetadata("bloodPiglin") && piglin.hasMetadata("userCustomEntity")) {
                handleBloodPiglinAttackHeal(piglin, player, event);
            }
        }
    }

    /**
     * 处理投射物命中事件
     * 主要用于取消变异雪傀儡雪球的消息发送
     */
    @EventHandler
    public void onProjectileHit(ProjectileHitEvent event) {
        // 处理变异雪傀儡雪球命中
        if (event.getEntity() instanceof Snowball) {
            Snowball snowball = (Snowball) event.getEntity();

            // 检查是否是变异雪傀儡的雪球
            if (snowball.hasMetadata("mutantSnowmanSnowball")) {
                // 取消默认的雪球命中消息
                // 这里不需要做任何事情，只是为了标识这是我们的雪球

                if (debugMode) {
                    logger.info("变异雪傀儡雪球命中，已取消默认消息");
                }
            }
        }
    }

    /**
     * 处理鲜血猪灵的攻击回血功能
     */
    private void handleBloodPiglinAttackHeal(Piglin piglin, Player player, EntityDamageByEntityEvent event) {
        try {
            // 获取攻击回血配置
            boolean attackHealEnabled = true;
            int attackHealPercentage = 10; // 默认10%
            
            // 尝试从元数据中获取配置（如果有的话）
            if (piglin.hasMetadata("attackHealEnabled")) {
                attackHealEnabled = piglin.getMetadata("attackHealEnabled").get(0).asBoolean();
            }
            if (piglin.hasMetadata("attackHealPercentage")) {
                attackHealPercentage = piglin.getMetadata("attackHealPercentage").get(0).asInt();
            }
            
            if (!attackHealEnabled) {
                return;
            }
            
            // 计算回血量（最大生命值的百分比）
            double maxHealth = piglin.getMaxHealth();
            double currentHealth = piglin.getHealth();
            double healAmount = maxHealth * (attackHealPercentage / 100.0);
            
            // 确保不超过最大生命值
            double newHealth = Math.min(currentHealth + healAmount, maxHealth);
            piglin.setHealth(newHealth);
            
            // 显示治疗效果
            piglin.getWorld().spawnParticle(
                Particle.HEART, 
                piglin.getLocation().add(0, 1, 0), 
                5, 
                0.5, 0.5, 0.5, 
                0
            );
            
            // 播放治疗音效
            piglin.getWorld().playSound(
                piglin.getLocation(), 
                Sound.ENTITY_PLAYER_LEVELUP, 
                0.5f, 1.5f
            );
            
            if (debugMode) {
                logger.info("IDC15鲜血猪灵攻击回血: " + String.format("%.1f", healAmount) + 
                           " 血量 (" + attackHealPercentage + "%), 当前血量: " + 
                           String.format("%.1f", newHealth) + "/" + String.format("%.1f", maxHealth));
            }
            
        } catch (Exception e) {
            logger.warning("处理鲜血猪灵攻击回血时发生错误: " + e.getMessage());
            if (debugMode) {
                e.printStackTrace();
            }
        }
    }
}
