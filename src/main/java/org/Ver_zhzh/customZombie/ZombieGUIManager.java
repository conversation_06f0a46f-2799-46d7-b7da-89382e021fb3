package org.Ver_zhzh.customZombie;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.event.server.PluginDisableEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.plugin.Plugin;

import net.citizensnpcs.api.CitizensAPI;
import net.citizensnpcs.api.npc.NPC;
import net.citizensnpcs.api.npc.NPCRegistry;

/**
 * 管理僵尸GUI界面和NPC的类
 */
public class ZombieGUIManager implements Listener {

    private final CustomZombie customZombie;
    private final Plugin plugin;
    private final String GUI_TITLE = "§6自定义僵尸与NPC菜单";
    // 存储本插件创建的所有NPC的ID，便于关服时清理
    private final List<Integer> createdNPCIds = new ArrayList<>();

    // 当前页码
    private final Map<Player, Integer> currentPage = new HashMap<>();
    // 每页显示的物品数量
    private final int ITEMS_PER_PAGE = 45; // 5行，保留最后一行用于翻页按钮

    /**
     * 僵尸实体ID和名称的映射
     */
    private final Map<String, String> zombieNames = new HashMap<>();

    /**
     * 其他实体ID和名称的映射
     */
    private final Map<String, String> entityNames = new HashMap<>();

    /**
     * NPC ID和名称的映射
     */
    private final Map<String, String> npcNames = new HashMap<>();

    public ZombieGUIManager(CustomZombie customZombie) {
        this.customZombie = customZombie;
        this.plugin = customZombie.getPlugin();

        // 初始化僵尸实体名称映射
        initZombieNames();

        // 初始化其他实体名称映射
        initEntityNames();

        // 初始化NPC名称映射
        initNPCNames();

        // 注册事件监听器
        Bukkit.getPluginManager().registerEvents(this, plugin);
    }

    /**
     * 初始化僵尸实体名称映射
     */
    private void initZombieNames() {
        zombieNames.put("id1", "普通僵尸");
        zombieNames.put("id2", "小僵尸");
        zombieNames.put("id3", "路障僵尸");
        zombieNames.put("id4", "钻斧僵尸");
        zombieNames.put("id5", "剧毒僵尸");
        zombieNames.put("id6", "双生僵尸");
        zombieNames.put("id7", "骷髅僵尸");
        zombieNames.put("id8", "武装僵尸");
        zombieNames.put("id9", "肥胖僵尸");
        zombieNames.put("id10", "法师僵尸");
        zombieNames.put("id11", "自爆僵尸");
        zombieNames.put("id12", "毒箭僵尸");
        zombieNames.put("id13", "电击僵尸");
        zombieNames.put("id14", "冰冻僵尸");
        zombieNames.put("id15", "暗影僵尸");
        zombieNames.put("id16", "毁灭僵尸");
        zombieNames.put("id17", "雷霆僵尸");
        zombieNames.put("id18", "变异科学家");
        zombieNames.put("id19", "变异法师");
        zombieNames.put("id20", "气球僵尸");
        zombieNames.put("id21", "迷雾僵尸");
        zombieNames.put("id22", "变异雷霆僵尸");
        zombieNames.put("id23", "终极毁灭僵尸");
        zombieNames.put("id24", "变异暗影僵尸");
        zombieNames.put("id25", "变异博士");
        zombieNames.put("id01", "变异科学家");
    }

    /**
     * 初始化其他实体名称映射
     */
    private void initEntityNames() {
        // 检查UserCustomEntity系统状态并添加相应标记
        boolean hasUserCustomEntity = customZombie.getEntityIntegration() != null &&
                                     customZombie.getEntityIntegration().isInitialized();

        String customPrefix = hasUserCustomEntity ? "§a[自定义] " : "§7[默认] ";

        entityNames.put("idc1", customPrefix + "变异僵尸01");
        // idc2现在也支持UserCustomEntity系统
        entityNames.put("idc2", customPrefix + "变异僵尸02");
        // idc3现在也支持UserCustomEntity系统
        entityNames.put("idc3", customPrefix + "变异烈焰人");
        // idc4现在也支持UserCustomEntity系统
        entityNames.put("idc4", customPrefix + "变异爬行者");
        entityNames.put("idc5", "§7[默认] 变异末影螨");
        entityNames.put("idc6", "§7[默认] 变异蜘蛛");
        entityNames.put("idc7", "§7[默认] 灾厄卫道士");
        entityNames.put("idc8", "§7[默认] 灾厄唤魔者");
        entityNames.put("idc9", "§7[默认] 灾厄劫掠兽");
        entityNames.put("idc10", "§7[默认] 变异僵尸马");
        entityNames.put("idc11", "§7[默认] 变异岩浆怪");
        entityNames.put("idc12", "§7[默认] 变异尸壳");  // 修复：应该是变异尸壳，不是变异骷髅
        entityNames.put("idc13", "§7[默认] 变异僵尸3");
        entityNames.put("idc14", "§7[默认] 变异僵尸04");
        entityNames.put("idc15", "§7[默认] 鲜血猪灵");  // 修复：idc15对应鲜血猪灵
        entityNames.put("idc16", "§7[默认] 暗影潜影贝");  // 修复：idc16对应暗影潜影贝
        entityNames.put("idc17", "§7[默认] 变异雪傀儡");
        entityNames.put("idc18", "§7[默认] 变异铁傀儡");
        entityNames.put("idc19", "§7[默认] 变异僵尸Max");
        entityNames.put("idc20", "§7[默认] 灵魂坚守者");
        entityNames.put("idc21", "§7[默认] 凋零领主");
        entityNames.put("idc22", "§7[默认] 异变之王");
    }

    /**
     * 初始化NPC名称映射
     */
    private void initNPCNames() {
        npcNames.put("idn1", "感染者1");
        npcNames.put("idn2", "感染者2号");
        npcNames.put("idn3", "感染者农民");
        npcNames.put("idn4", "感染者居民");
        npcNames.put("idn5", "感染猪");
    }

    /**
     * 打开僵尸和NPC选择GUI
     *
     * @param player 打开GUI的玩家
     */
    public void openGUI(Player player) {
        openGUI(player, 1);
    }

    /**
     * 打开僵尸和NPC选择GUI的指定页面
     *
     * @param player 打开GUI的玩家
     * @param page 页码，从1开始
     */
    public void openGUI(Player player, int page) {
        // 创建所有物品的列表
        List<ItemStack> allItems = new ArrayList<>();

        customZombie.getLogger().info("开始创建GUI，僵尸数量: " + zombieNames.size() + ", 实体数量: " + entityNames.size() + ", NPC数量: " + npcNames.size());

        // 添加僵尸实体按钮
        for (Map.Entry<String, String> entry : zombieNames.entrySet()) {
            String id = entry.getKey();
            String name = entry.getValue();

            // 根据僵尸类型选择不同的图标
            Material iconMaterial = Material.ZOMBIE_HEAD;
            if ("id11".equals(id)) { // 自爆僵尸使用TNT图标
                iconMaterial = Material.TNT;
            }

            ItemStack item = new ItemStack(iconMaterial);
            ItemMeta meta = item.getItemMeta();
            meta.setDisplayName("§a" + name);
            meta.setLore(Arrays.asList(
                    "§7点击生成该类型僵尸",
                    "§8ID: " + id,
                    "§8类型: zombie"
            ));
            item.setItemMeta(meta);

            allItems.add(item);
        }

        // 添加其他实体按钮
        customZombie.getLogger().info("开始添加实体按钮，实体映射: " + entityNames.toString());
        for (Map.Entry<String, String> entry : entityNames.entrySet()) {
            String id = entry.getKey();
            String name = entry.getValue();
            customZombie.getLogger().info("处理实体: " + id + " -> " + name);

            // 根据实体类型选择不同的显示头颅
            Material headMaterial = Material.CREEPER_HEAD;
            try {
                // 使用简化的Material映射，避免兼容性问题
                switch (id) {
                    case "idc1": headMaterial = Material.ROTTEN_FLESH; break;
                    case "idc2": headMaterial = Material.BONE; break;
                    case "idc3": headMaterial = Material.BLAZE_POWDER; break;
                    case "idc4": headMaterial = Material.GUNPOWDER; break;
                    case "idc5": headMaterial = Material.ENDER_PEARL; break;
                    case "idc6": headMaterial = Material.SPIDER_EYE; break;
                    case "idc7": headMaterial = Material.IRON_SWORD; break;
                    case "idc8": headMaterial = Material.EMERALD; break;
                    case "idc9": headMaterial = Material.IRON_AXE; break;
                    case "idc10": headMaterial = Material.SADDLE; break;
                    case "idc11": headMaterial = Material.MAGMA_CREAM; break;
                    case "idc12": headMaterial = Material.SAND; break;
                    case "idc13": headMaterial = Material.BOW; break;
                    case "idc14": headMaterial = Material.BONE; break;
                    case "idc15": headMaterial = Material.GOLD_INGOT; break;
                    case "idc16": headMaterial = Material.ENDER_CHEST; break;
                    case "idc17": headMaterial = Material.SNOWBALL; break;
                    case "idc18": headMaterial = Material.IRON_INGOT; break;
                    case "idc19": headMaterial = Material.DIAMOND_SWORD; break;
                    case "idc20": headMaterial = Material.COAL; break;
                    case "idc21": headMaterial = Material.BONE; break;
                    case "idc22": headMaterial = Material.DIAMOND; break;
                    default: headMaterial = Material.CREEPER_HEAD; break;
                }
            } catch (Exception e) {
                customZombie.getLogger().warning("为实体 " + id + " 设置图标时出错: " + e.getMessage());
                headMaterial = Material.CREEPER_HEAD; // 使用默认图标
            }

            try {
                ItemStack item = new ItemStack(headMaterial);
                ItemMeta meta = item.getItemMeta();
                if (meta != null) {
                    meta.setDisplayName("§b" + name);

                    // 检查是否支持UserCustomEntity系统
                    List<String> lore = new ArrayList<>();
                    lore.add("§7点击生成该类型实体");
                    lore.add("§8ID: " + id);
                    lore.add("§8类型: entity");

                    // 添加UserCustomEntity系统状态信息
                    if (customZombie.getEntityIntegration() != null &&
                        customZombie.getEntityIntegration().isInitialized() &&
                        customZombie.getEntityIntegration().isSupportedEntity(id)) {
                        lore.add("§a✓ 支持自定义配置");
                        lore.add("§7使用entity.yml配置文件");

                        // 获取配置信息
                        String configInfo = customZombie.getEntityIntegration().getEntityConfigInfo(id);
                        if (configInfo.contains("启用状态: true")) {
                            lore.add("§a● 自定义配置已启用");
                        } else {
                            lore.add("§7● 使用默认配置");
                        }
                    } else {
                        lore.add("§7○ 使用硬编码配置");
                    }

                    meta.setLore(lore);
                    item.setItemMeta(meta);
                    allItems.add(item);
                    customZombie.getLogger().info("成功添加实体 " + id + " 到GUI");
                } else {
                    customZombie.getLogger().warning("无法为实体 " + id + " 创建ItemMeta");
                }
            } catch (Exception e) {
                customZombie.getLogger().severe("创建实体 " + id + " 的GUI物品时出错: " + e.getMessage());
                e.printStackTrace();
                // 即使出错也要继续处理下一个实体，不要中断整个循环
                continue;
            }
        }

        customZombie.getLogger().info("实体按钮添加完成，总物品数: " + allItems.size());

        // 添加NPC按钮
        for (Map.Entry<String, String> entry : npcNames.entrySet()) {
            String id = entry.getKey();
            String name = entry.getValue();

            ItemStack item = new ItemStack(Material.PLAYER_HEAD);
            ItemMeta meta = item.getItemMeta();
            meta.setDisplayName("§d" + name);
            meta.setLore(Arrays.asList(
                    "§7点击生成该类型NPC",
                    "§8ID: " + id,
                    "§8类型: npc"
            ));
            item.setItemMeta(meta);

            allItems.add(item);
        }

        // 计算总页数
        int totalItems = allItems.size();
        int totalPages = (int) Math.ceil((double) totalItems / ITEMS_PER_PAGE);

        // 确保页码在有效范围内
        if (page < 1) {
            page = 1;
        }
        if (page > totalPages) {
            page = totalPages;
        }

        // 保存当前页码
        currentPage.put(player, page);

        // 创建GUI界面 (固定为6行，54个槽位)
        Inventory gui = Bukkit.createInventory(null, 54, GUI_TITLE + " - 第 " + page + "/" + totalPages + " 页");

        // 计算当前页的起始和结束索引
        int startIndex = (page - 1) * ITEMS_PER_PAGE;
        int endIndex = Math.min(startIndex + ITEMS_PER_PAGE, totalItems);

        // 添加当前页的物品
        for (int i = startIndex; i < endIndex; i++) {
            gui.addItem(allItems.get(i));
        }

        // 添加翻页按钮
        if (page > 1) {
            // 上一页按钮
            ItemStack prevButton = new ItemStack(Material.ARROW);
            ItemMeta prevMeta = prevButton.getItemMeta();
            prevMeta.setDisplayName("§e上一页");
            prevMeta.setLore(Arrays.asList("§7点击查看上一页"));
            prevButton.setItemMeta(prevMeta);
            gui.setItem(45, prevButton);
        }

        if (page < totalPages) {
            // 下一页按钮
            ItemStack nextButton = new ItemStack(Material.ARROW);
            ItemMeta nextMeta = nextButton.getItemMeta();
            nextMeta.setDisplayName("§e下一页");
            nextMeta.setLore(Arrays.asList("§7点击查看下一页"));
            nextButton.setItemMeta(nextMeta);
            gui.setItem(53, nextButton);
        }

        // 添加返回按钮
        ItemStack backButton = new ItemStack(Material.BARRIER);
        ItemMeta backMeta = backButton.getItemMeta();
        backMeta.setDisplayName("§c关闭菜单");
        backMeta.setLore(Arrays.asList("§7点击关闭此菜单"));
        backButton.setItemMeta(backMeta);
        gui.setItem(49, backButton);

        // 打开GUI界面
        player.openInventory(gui);
    }

    /**
     * 处理GUI点击事件
     */
    @EventHandler
    public void onInventoryClick(InventoryClickEvent event) {
        String title = event.getView().getTitle();
        if (title.startsWith(GUI_TITLE)) {
            event.setCancelled(true); // 取消点击事件

            if (!(event.getWhoClicked() instanceof Player)) {
                return;
            }

            Player player = (Player) event.getWhoClicked();
            ItemStack clickedItem = event.getCurrentItem();

            if (clickedItem == null || !clickedItem.hasItemMeta()) {
                return;
            }

            // 获取当前页码
            int page = currentPage.getOrDefault(player, 1);

            // 检查是否点击了翻页按钮
            if (clickedItem.getType() == Material.ARROW) {
                String buttonName = clickedItem.getItemMeta().getDisplayName();
                if (buttonName.equals("§e上一页")) {
                    // 打开上一页
                    openGUI(player, page - 1);
                    return;
                } else if (buttonName.equals("§e下一页")) {
                    // 打开下一页
                    openGUI(player, page + 1);
                    return;
                }
            }

            // 检查是否点击了关闭按钮
            if (clickedItem.getType() == Material.BARRIER) {
                player.closeInventory();
                return;
            }

            // 检查是否有Lore
            if (!clickedItem.getItemMeta().hasLore()) {
                return;
            }

            // 获取物品的Lore
            List<String> lore = clickedItem.getItemMeta().getLore();
            if (lore == null || lore.size() < 3) {
                return;
            }

            // 获取ID（格式为 "§8ID: xxxx"）
            String idLine = lore.get(1);
            String id = idLine.substring(idLine.indexOf(":") + 2);

            // 获取类型（格式为 "§8类型: xxxx"）
            String typeLine = lore.get(2);
            String type = typeLine.substring(typeLine.indexOf(":") + 2);

            // 根据类型判断处理方式
            switch (type) {
                case "zombie":
                    // 生成僵尸
                    customZombie.spawnCustomZombie(player, id);
                    player.sendMessage("§a已生成 " + clickedItem.getItemMeta().getDisplayName() + "§a!");
                    break;
                case "entity":
                    // 生成其他实体
                    customZombie.spawnOtherEntity(player, id);
                    player.sendMessage("§a已生成 " + clickedItem.getItemMeta().getDisplayName() + "§a!");
                    break;
                case "npc":
                    // 生成NPC并记录ID便于清理
                    NPC npc = createNPC(player, id);
                    if (npc != null) {
                        createdNPCIds.add(npc.getId());
                        player.sendMessage("§a已生成 " + clickedItem.getItemMeta().getDisplayName() + "§a!");
                    }
                    break;
                default:
                    player.sendMessage("§c未知的实体类型: " + type);
                    break;
            }

            // 关闭菜单
            player.closeInventory();
        }
    }

    /**
     * 创建NPC并返回NPC对象
     *
     * @param player 玩家
     * @param npcId NPC ID
     * @return 创建的NPC对象，如果创建失败则返回null
     */
    private NPC createNPC(Player player, String npcId) {
        // 检查Citizens插件是否可用
        if (Bukkit.getPluginManager().getPlugin("Citizens") == null) {
            player.sendMessage("§c无法生成NPC! 服务器未安装Citizens插件。");
            return null;
        }

        // 记录NPC生成前Citizens注册表中的NPC ID集合
        NPCRegistry registry = CitizensAPI.getNPCRegistry();
        List<Integer> npcIdsBefore = new ArrayList<>();
        for (NPC npc : registry) {
            npcIdsBefore.add(npc.getId());
        }

        // 生成NPC
        switch (npcId) {
            case "idn1":
                // 感染者1
                customZombie.createInfectedNPC1(player);
                break;
            case "idn2":
                // 感染者2号
                customZombie.createInfectedNPC2(player);
                break;
            case "idn3":
                // 感染者农民
                customZombie.createInfectedFarmer(player);
                break;
            case "idn4":
                // 感染者居民
                customZombie.createInfectedResident(player);
                break;
            case "idn5":
                // 感染猪
                customZombie.createInfectedPig(player);
                break;
            default:
                player.sendMessage("§c未知的NPC ID: " + npcId);
                return null;
        }

        // 获取所有NPC ID，找出新增的NPC
        NPC newNPC = null;
        for (NPC npc : registry) {
            if (!npcIdsBefore.contains(npc.getId())) {
                // 找到了一个新的NPC
                newNPC = npc;
                // 记录NPC ID
                createdNPCIds.add(npc.getId());
                customZombie.getLogger().info("已记录NPC ID: " + npc.getId() + ", 名称: " + npc.getName());
            }
        }

        return newNPC;
    }

    /**
     * 在服务器关闭时清理所有NPC
     */
    @EventHandler
    public void onPluginDisable(PluginDisableEvent event) {
        if (event.getPlugin().equals(plugin)) {
            cleanupNPCs();
        }
    }

    /**
     * 清理所有由此插件创建的NPC
     */
    public void cleanupNPCs() {
        // 检查Citizens插件是否可用
        if (Bukkit.getPluginManager().getPlugin("Citizens") == null) {
            customZombie.getLogger().warning("无法清理NPC: Citizens插件不可用");
            return;
        }

        NPCRegistry registry = CitizensAPI.getNPCRegistry();

        // 遍历所有NPC ID并尝试移除对应的NPC
        for (Integer npcId : createdNPCIds) {
            NPC npc = registry.getById(npcId);
            if (npc != null) {
                customZombie.getLogger().info("清理NPC: " + npc.getName() + " (ID: " + npcId + ")");
                npc.destroy();
            }
        }

        createdNPCIds.clear();
        customZombie.getLogger().info("已清理所有由CustomZombie生成的NPC");
    }

    /**
     * 记录由CustomZombie插件生成的NPC ID
     *
     * @param npcId 要记录的NPC ID
     */
    public void recordNPCId(int npcId) {
        if (!createdNPCIds.contains(npcId)) {
            createdNPCIds.add(npcId);
        }
    }

    /**
     * 安全地获取Material，如果指定的Material不存在则使用备用Material
     *
     * @param materialName 要获取的Material名称
     * @param fallback 备用Material
     * @return 可用的Material
     */
    private Material getSafeMaterial(String materialName, Material fallback) {
        try {
            return Material.valueOf(materialName);
        } catch (IllegalArgumentException e) {
            customZombie.getLogger().warning("Material " + materialName + " 不存在，使用备用Material: " + fallback.name());
            return fallback;
        }
    }

    /**
     * 调试方法：打印所有实体映射
     */
    public void debugPrintEntityMappings() {
        customZombie.getLogger().info("=== 实体映射调试信息 ===");
        customZombie.getLogger().info("僵尸映射 (" + zombieNames.size() + " 个):");
        for (Map.Entry<String, String> entry : zombieNames.entrySet()) {
            customZombie.getLogger().info("  " + entry.getKey() + " -> " + entry.getValue());
        }

        customZombie.getLogger().info("实体映射 (" + entityNames.size() + " 个):");
        for (Map.Entry<String, String> entry : entityNames.entrySet()) {
            customZombie.getLogger().info("  " + entry.getKey() + " -> " + entry.getValue());
        }

        customZombie.getLogger().info("NPC映射 (" + npcNames.size() + " 个):");
        for (Map.Entry<String, String> entry : npcNames.entrySet()) {
            customZombie.getLogger().info("  " + entry.getKey() + " -> " + entry.getValue());
        }
        customZombie.getLogger().info("=== 调试信息结束 ===");
    }
}
