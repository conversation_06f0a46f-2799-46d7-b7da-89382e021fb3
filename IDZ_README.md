# IDZ自定义怪物系统 - 使用指南

## 🎯 系统概述

IDZ（ID-Z系列）自定义怪物系统是DeathZombieV4插件的全新功能模块，允许用户通过可视化界面创建完全自定义的怪物实体。该系统整合了现有ID和IDC系列怪物的所有技能，并提供了强大的扩展能力。

## ✨ 核心特性

### 🔧 技能模板库
- **完整技能提取**：从ID1-ID20和IDC1-IDC22中提取所有技能模板
- **技能分类**：攻击类、召唤类、移动类、特效类、防御类、负面类、增益类、环境类
- **参数自定义**：每个技能都支持详细的参数配置
- **兼容性检查**：自动验证技能组合的兼容性

### 🎨 可视化编辑器
- **现代化WebUI**：基于Vue 3 + Bootstrap 5的响应式界面
- **拖拽式操作**：直观的技能添加和配置方式
- **实时预览**：即时查看怪物属性和技能配置
- **配置验证**：实时验证配置的有效性

### 🛠️ 强大的自定义能力
- **基础属性**：生命值、攻击力、移动速度等完全自定义
- **装备系统**：支持主手、副手、护甲的完整配置
- **药水效果**：永久或临时的状态效果
- **外观定制**：发光、隐身、静音、缩放等视觉效果

## 🚀 快速开始

### 1. 基础命令

```bash
# 查看所有IDZ命令
/idz

# 创建新的IDZ实体
/idz create <实体ID> <显示名称>

# 列出所有IDZ实体
/idz list

# 查看实体详细信息
/idz info <实体ID>

# 生成IDZ实体
/idz spawn <实体ID> [数量]

# 打开网页编辑器
/idz gui

# 查看系统统计
/idz stats
```

### 2. 网页编辑器使用

1. **启动Web服务器**
   - 确保config.yml中`web.enabled: true`
   - 默认端口：8080

2. **访问编辑器**
   - 浏览器打开：`http://localhost:8080/idz-creator-vue.html`
   - 使用配置的密码登录

3. **创建怪物**
   - 点击"新建"按钮创建IDZ实体
   - 配置基础属性（名称、类型、生命值等）
   - 从技能库中拖拽或添加技能
   - 调整技能参数
   - 保存配置

## 📚 技能系统详解

### 可用技能列表

#### 攻击类技能
- **剧毒攻击** (poison_attack)：攻击时有概率造成中毒效果
- **电击攻击** (electric_attack)：对范围内玩家造成电击伤害
- **烈焰攻击** (blaze_attack)：发射烈焰弹攻击玩家
- **远程射击** (ranged_attack)：发射箭矢进行远程攻击
- **雷霆攻击** (thunder_attack)：召唤闪电攻击玩家
- **爆炸攻击** (explosion_attack)：死亡时产生爆炸
- **生命汲取** (life_drain)：攻击时汲取玩家生命值
- **骷髅射击** (skeleton_shooting)：高频率的箭矢攻击
- **召唤风暴** (summon_storm)：召唤雷电风暴攻击大范围敌人

#### 召唤类技能
- **召唤仆从** (summon_minion)：定时召唤普通僵尸作为仆从
- **法师召唤** (mage_summon)：召唤双生僵尸作为法师仆从
- **分裂召唤** (split_summon)：死亡时分裂成多个小怪物
- **岩浆怪分裂** (magma_cube_split)：死亡时分裂成小岩浆怪
- **僵尸群体召唤** (zombie_horde_summon)：召唤大量僵尸仆从

#### 移动类技能
- **跳跃攻击** (jump_attack)：跳向玩家进行攻击
- **传送攻击** (teleport_attack)：传送到玩家身后进行攻击
- **末影传送** (ender_teleport)：随机传送并进行电流攻击

#### 防御类技能
- **护盾反射** (shield_reflect)：反射部分受到的伤害

#### 负面类技能
- **冰冻攻击** (freeze_attack)：对玩家造成缓慢效果
- **虚弱光环** (weakness_aura)：持续对周围玩家施加虚弱效果

#### 增益类技能
- **狂暴模式** (berserk_mode)：血量低时进入狂暴状态

#### 特效类技能
- **剧毒烟雾** (poison_smoke)：持续生成黑色烟雾粒子效果
- **螺旋粒子** (spiral_particles)：生成螺旋上升的紫色粒子效果

#### 环境类技能
- **蜘蛛网陷阱** (web_trap)：在周围生成蜘蛛网并喷射毒液

#### 基础技能
- **基础僵尸** (basic_zombie)：标准的僵尸实体，无特殊技能
- **基础僵尸变种** (basic_zombie_variant)：基础僵尸的变种
- **强化僵尸** (enhanced_zombie)：属性增强的僵尸
- **精英僵尸** (elite_zombie)：装备精良的僵尸

### 技能参数配置

每个技能都有详细的参数配置选项：

```json
{
  "skillId": "electric_attack",
  "enabled": true,
  "parameters": {
    "attack_interval": 60,    // 攻击间隔（tick）
    "attack_range": 8.0,      // 攻击范围（格）
    "electric_damage": 6.0    // 电击伤害（点）
  },
  "priority": 1
}
```

## 🔧 配置文件结构

### IDZ实体配置示例

```json
{
  "entityId": "idz_example_1",
  "displayName": "§c烈焰战士",
  "description": "一个强大的烈焰战士",
  "entityType": "BLAZE",
  "health": 50.0,
  "damage": 8.0,
  "speed": 1.2,
  "skills": [
    {
      "skillId": "blaze_attack",
      "enabled": true,
      "parameters": {
        "fireball_interval": 40,
        "fireball_speed": 2.5,
        "explosion_power": 1.5
      }
    }
  ]
}
```

## 🌐 API接口

### RESTful API端点

- `GET /api/idz/entities` - 获取所有IDZ实体
- `POST /api/idz/entities` - 创建/更新IDZ实体
- `DELETE /api/idz/entities/{id}` - 删除IDZ实体
- `GET /api/idz/skill-templates` - 获取技能模板库
- `POST /api/idz/spawn` - 生成IDZ实体
- `POST /api/idz/validate` - 验证配置
- `GET /api/idz/statistics` - 获取系统统计

### 使用示例

```javascript
// 获取所有IDZ实体
fetch('/api/idz/entities')
  .then(response => response.json())
  .then(data => console.log(data.entities));

// 生成IDZ实体
fetch('/api/idz/spawn', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    entityId: 'idz_example_1',
    location: 'player'
  })
});
```

## 🎮 游戏内集成

### 与现有系统的兼容性

- **元数据标记**：IDZ实体使用标准的游戏实体元数据
- **事件监听**：完全兼容现有的攻击、死亡等事件系统
- **配置热重载**：支持运行时配置更新
- **权限系统**：细粒度的权限控制

### 性能优化

- **任务管理**：智能的BukkitTask管理，避免内存泄漏
- **技能缓存**：技能模板缓存机制，提高执行效率
- **批量操作**：支持批量生成和管理
- **资源清理**：自动清理无效的技能任务

## 🔒 权限配置

```yaml
permissions:
  deathzombie.idz:
    description: IDZ系统完整权限
    children:
      deathzombie.idz.spawn: true
      deathzombie.idz.create: true
      deathzombie.idz.delete: true
      deathzombie.idz.gui: true
    default: op
```

## 🐛 故障排除

### 常见问题

1. **Web服务器无法启动**
   - 检查端口是否被占用
   - 确认config.yml中web配置正确

2. **技能不生效**
   - 验证技能ID是否正确
   - 检查技能参数配置
   - 查看控制台错误日志

3. **实体无法生成**
   - 确认实体配置有效
   - 检查生成位置是否合适
   - 验证权限设置

### 调试模式

启用详细日志记录：
```yaml
# config.yml
debug:
  idz_system: true
  skill_execution: true
```

## 🔄 更新日志

### v1.0.0 (当前版本)
- ✅ 完整的技能模板库系统 (25+ 技能模板)
- ✅ 可视化WebUI编辑器
- ✅ RESTful API接口
- ✅ 游戏内命令支持
- ✅ 配置验证和错误处理
- ✅ 权限系统集成
- ✅ IDZ事件监听器系统
- ✅ 被动技能支持（剧毒攻击、生命汲取、护盾反射等）
- ✅ 主动技能支持（传送攻击、跳跃攻击、电击攻击等）
- ✅ 死亡技能支持（爆炸攻击、分裂召唤等）
- ⚠️ 部分高级技能仍在开发中
- ⚠️ 性能优化持续进行中

## 🤝 贡献指南

欢迎为IDZ系统贡献代码和建议！

1. Fork项目仓库
2. 创建功能分支
3. 提交更改
4. 发起Pull Request

## 📞 技术支持

如有问题或建议，请联系：
- 作者：Ver_zhzh
- 项目：DeathZombieV4
- 版本：1.2+

---

**IDZ自定义怪物系统** - 让你的Minecraft服务器拥有无限可能的怪物创造力！ 🎮✨
