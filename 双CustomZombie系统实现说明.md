# DeathZombieV4 双CustomZombie系统实现说明

## 概述

本文档记录了DeathZombieV4插件双CustomZombie生成系统的实现，该系统允许在保持原有CustomZombie功能的同时，通过配置文件自定义僵尸属性。

## 实现方案

采用**方案B：技能共享的混合系统**

### 核心特点
- 保持原有CustomZombie的所有技能逻辑不变
- 通过配置文件覆盖关键属性（生命值、伤害、装备等）
- 支持灵活的优先级策略
- 目前支持ID1-ID4僵尸的双系统生成

## 系统架构

### 1. 核心类结构

```
DualZombieSystemManager (双系统管理器)
├── CustomZombie (原有系统)
└── UserCustomZombie (用户自定义系统)
    ├── ZombieOverrideConfig (覆盖配置类)
    └── PotionEffectConfig (药水效果配置类)
```

### 2. 文件结构

```
DeathZombieV4/
├── src/main/java/org/Ver_zhzh/
│   ├── customZombie/
│   │   ├── CustomZombie.java (原有系统)
│   │   ├── DualZombieSystemManager.java (双系统管理器)
│   │   └── UserCustomZombie/
│   │       └── UserCustomZombie.java (用户自定义系统)
│   └── deathZombieV4/
│       ├── utils/ZombieHelper.java (已修改，集成双系统)
│       └── commands/CommandManager.java (已修改，添加管理命令)
└── src/main/resources/
    └── zombie.yml (配置文件)
```

## 配置文件说明

### zombie.yml 主要配置节

#### 1. 系统总开关
```yaml
system_settings:
  use_default_settings: true          # 是否启用原有CustomZombie
  use_user_custom_settings: false     # 是否启用用户自定义系统
  priority_strategy: "default_first"  # 优先级策略
  debug_mode: false                   # 调试模式
```

#### 2. 优先级策略
- `default_first`: 优先使用原有系统，失败时使用用户自定义
- `user_first`: 优先使用用户自定义，失败时使用原有系统  
- `both`: 同时使用两套系统（实验性功能）

#### 3. 僵尸覆盖配置示例
```yaml
user_custom_overrides:
  specific_overrides:
    id1:  # 普通僵尸
      enabled: true
      health_override: 25.0           # 覆盖生命值
      damage_override: 6.0            # 覆盖伤害值
      custom_name_override: "§a§l强化普通僵尸"
      weapon_override: "STONE_AXE"    # 覆盖武器
      speed_multiplier: 1.2           # 速度倍数
```

## 使用方法

### 1. 管理命令

```bash
# 查看双系统状态
/dzs dualsystem status

# 重载双系统配置
/dzs dualsystem reload

# 测试生成僵尸 (支持id1-id4)
/dzs dualsystem test id1
```

### 2. 配置步骤

1. **启用用户自定义系统**
   ```yaml
   system_settings:
     use_user_custom_settings: true
   ```

2. **配置特定僵尸覆盖**
   ```yaml
   user_custom_overrides:
     specific_overrides:
       id1:
         enabled: true
         health_override: 30.0
         # ... 其他配置
   ```

3. **重载配置**
   ```bash
   /dzs dualsystem reload
   ```

4. **测试生成**
   ```bash
   /dzs dualsystem test id1
   ```

## 技术实现细节

### 1. 生成流程

```
ZombieHelper.spawnCustomZombie()
    ↓
DualZombieSystemManager.spawnCustomZombieDirect()
    ↓
根据优先级策略选择系统:
    ├── CustomZombie.spawnCustomZombieDirect() (原有系统)
    └── UserCustomZombie.spawnUserCustomZombieDirect() (用户自定义)
        ↓
        应用配置覆盖:
        ├── 生命值覆盖
        ├── 伤害值覆盖
        ├── 装备覆盖
        ├── 药水效果覆盖
        └── 启用原有技能
```

### 2. 配置覆盖机制

UserCustomZombie系统支持以下覆盖：
- **基础属性**: 生命值、伤害值、移动速度
- **外观**: 自定义名称、武器、护甲
- **效果**: 药水效果（类型、等级、持续时间）
- **技能**: 保持原有CustomZombie的技能逻辑

### 3. 元数据标记

生成的僵尸会添加以下元数据：
- `userCustomZombie`: 标记为用户自定义僵尸
- `zombieId`: 记录僵尸ID
- `dualZombieSystem`: 标记双系统来源

## 当前支持范围

### 已实现功能
- ✅ ID1-ID4僵尸的双系统支持
- ✅ 配置文件热重载
- ✅ 管理命令接口
- ✅ 调试模式和详细日志
- ✅ 优先级策略切换

### 计划扩展
- 🔄 ID5-ID17僵尸支持
- 🔄 IDC系列实体支持
- 🔄 完全自定义新僵尸类型
- 🔄 技能参数覆盖
- 🔄 游戏级别的配置开关

## 兼容性说明

### 向后兼容
- 原有CustomZombie的所有功能保持不变
- 现有游戏配置无需修改
- 默认情况下只使用原有系统

### 性能影响
- 配置缓存机制减少文件读取
- 双系统管理器采用懒加载
- 调试模式可关闭以提升性能

## 故障排除

### 常见问题

1. **双系统未初始化**
   - 检查CustomZombie是否正常加载
   - 查看控制台初始化日志

2. **配置不生效**
   - 确认`use_user_custom_settings: true`
   - 检查僵尸ID的`enabled: true`
   - 使用`/dzs dualsystem reload`重载配置

3. **生成失败**
   - 查看控制台错误日志
   - 使用`/dzs dualsystem status`检查系统状态
   - 尝试`/dzs dualsystem test`命令测试

### 调试模式

启用调试模式获取详细日志：
```yaml
system_settings:
  debug_mode: true
  verbose_logging: true
```

## 开发规范

本实现遵循阿里巴巴编码规范：
- 类名使用大驼峰命名
- 方法名使用小驼峰命名
- 常量使用全大写下划线分隔
- 详细的中文注释
- 异常处理和日志记录

## 版本信息

- **实现版本**: 1.0
- **实现日期**: 2025-07-22
- **作者**: Ver_zhzh
- **支持的僵尸ID**: id1, id2, id3, id4

## Git提交记录

建议的提交信息格式：
```
feat: 实现双CustomZombie系统基础框架

- 添加UserCustomZombie类和配置系统
- 实现DualZombieSystemManager双系统管理器
- 集成到ZombieHelper和CommandManager
- 支持ID1-ID4僵尸的配置覆盖
- 添加管理命令和调试功能

符合阿里巴巴编码规范，包含详细中文注释
```
